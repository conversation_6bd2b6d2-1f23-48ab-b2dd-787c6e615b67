msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2023-11-24 12:51+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: es_ES\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"

msgctxt "ironing_inset description"
msgid "A distance to keep from the edges of the model. Ironing all the way to the edge of the mesh may result in a jagged edge on your print."
msgstr "Distancia que debe guardarse desde el borde del modelo. Si se alisa hasta el borde de la malla, puede quedar un borde irregular."

msgctxt "material_no_load_move_factor description"
msgid "A factor indicating how much the filament gets compressed between the feeder and the nozzle chamber, used to determine how far to move the material for a filament switch."
msgstr "Un factor que indica cuánto se comprime el filamento entre el alimentador y la cámara de la boquilla. Se utiliza para determinar cuán lejos debe avanzar el material para cambiar el filamento."

msgctxt "roofing_angles description"
msgid "A list of integer line directions to use when the top surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Una lista de los valores enteros de las direcciones de línea si las capas de la superficie superior del forro utilizan líneas o el patrón en zigzag. Los elementos de esta lista se utilizan de forma secuencial a medida que las capas se utilizan y, cuando se alcanza el final, la lista vuelve a comenzar desde el principio. Los elementos de la lista están separados por comas y toda la lista aparece entre corchetes. El valor predeterminado es una lista vacía que utiliza los ángulos predeterminados típicos (45 y 135 grados)."

msgctxt "skin_angles description"
msgid "A list of integer line directions to use when the top/bottom layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Una lista de los valores enteros de las direcciones de línea si las capas superiores e inferiores utilizan líneas o el patrón en zigzag. Los elementos de esta lista se utilizan de forma secuencial a medida que las capas se utilizan y, cuando se alcanza el final, la lista vuelve a comenzar desde el principio. Los elementos de la lista están separados por comas y toda la lista aparece entre corchetes. El valor predeterminado es una lista vacía que utiliza los ángulos predeterminados típicos (45 y 135 grados)."

msgctxt "support_infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angle 0 degrees."
msgstr "Una lista de los valores enteros de las direcciones de línea que se van a utilizar. Los elementos de la lista se usan secuencialmente a medida que avanzan las capas y cuando se alcanza el final de la lista, comienza de nuevo desde el principio. Los elementos de la lista están separados por comas y toda la lista aparece entre corchetes. El valor predeterminado es una lista vacía, lo que significa que se usa el ángulo predeterminado de 0 grados."

msgctxt "support_bottom_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Una lista de los valores enteros de las direcciones de línea que se van a utilizar. Los elementos de la lista se usan secuencialmente a medida que avanzan las capas y cuando se alcanza el final de la lista, comienza de nuevo desde el principio. Los elementos de la lista están separados por comas y toda la lista aparece entre corchetes. El valor predeterminado es una lista vacía, lo que significa que se utilizan los ángulos estándar (que varían entre 45 y 135 grados si las interfaces son bastante gruesas o de 90 grados en otro caso)."

msgctxt "support_interface_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Una lista de los valores enteros de las direcciones de línea que se van a utilizar. Los elementos de la lista se usan secuencialmente a medida que avanzan las capas y cuando se alcanza el final de la lista, comienza de nuevo desde el principio. Los elementos de la lista están separados por comas y toda la lista aparece entre corchetes. El valor predeterminado es una lista vacía, lo que significa que se utilizan los ángulos estándar (que varían entre 45 y 135 grados si las interfaces son bastante gruesas o de 90 grados en otro caso)."

msgctxt "support_roof_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Una lista de los valores enteros de las direcciones de línea que se van a utilizar. Los elementos de la lista se usan secuencialmente a medida que avanzan las capas y cuando se alcanza el final de la lista, comienza de nuevo desde el principio. Los elementos de la lista están separados por comas y toda la lista aparece entre corchetes. El valor predeterminado es una lista vacía, lo que significa que se utilizan los ángulos estándar (que varían entre 45 y 135 grados si las interfaces son bastante gruesas o de 90 grados en otro caso)."

msgctxt "infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees for the lines and zig zag patterns and 45 degrees for all other patterns)."
msgstr "Una lista de los valores enteros de las direcciones de línea. Los elementos de esta lista se utilizan de forma secuencial a medida que las capas se utilizan y, cuando se alcanza el final, la lista vuelve a comenzar desde el principio. Los elementos de la lista están separados por comas y toda la lista aparece entre corchetes. El valor predeterminado es una lista vacía que utiliza los ángulos predeterminados típicos (45 y 135 grados para las líneas y los patrones en zigzag y 45 grados para el resto de patrones)."

msgctxt "nozzle_disallowed_areas description"
msgid "A list of polygons with areas the nozzle is not allowed to enter."
msgstr "Lista de polígonos con áreas en las que la tobera no tiene permitido entrar."

msgctxt "machine_disallowed_areas description"
msgid "A list of polygons with areas the print head is not allowed to enter."
msgstr "Lista de polígonos con áreas que el cabezal de impresión no tiene permitido introducir."

msgctxt "brim_inside_margin description"
msgid "A part fully enclosed inside another part can generate an outer brim that touches the inside of the other part. This removes all brim within this distance from internal holes."
msgstr "Una pieza completamente encerrada dentro de otra puede generar un borde exterior que toque el interior de la pieza exterior. Esto elimina cualquier borde dentro de esta distancia de los orificios internos."

msgctxt "support_tree_branch_reach_limit description"
msgid "A recomendation to how far branches can move from the points they support. Branches can violate this value to reach their destination (buildplate or a flat part of the model). Lowering this value will make the support more sturdy, but increase the amount of branches (and because of that material usage/print time) "
msgstr "Una recomendación de la distancia a la que pueden situarse las ramas de los puntos que sostienen. Las ramas pueden infringir este valor para llegar a su destino (placa de impresión o una pieza plana del modelo). Reducir este valor hará que el soporte sea más resistente, pero aumentará la cantidad de ramas (y, con ello, el uso de material y el tiempo de impresión)"

msgctxt "extruder_prime_pos_abs label"
msgid "Absolute Extruder Prime Position"
msgstr "Posición de preparación absoluta del extrusor"

msgctxt "adaptive_layer_height_variation label"
msgid "Adaptive Layers Maximum Variation"
msgstr "Variación máxima de las capas de adaptación"

msgctxt "adaptive_layer_height_threshold label"
msgid "Adaptive Layers Topography Size"
msgstr "Tamaño de la topografía de las capas de adaptación"

msgctxt "adaptive_layer_height_variation_step label"
msgid "Adaptive Layers Variation Step Size"
msgstr "Tamaño de pasos de variación de las capas de adaptación"

msgctxt "adaptive_layer_height_enabled description"
msgid "Adaptive layers computes the layer heights depending on the shape of the model."
msgstr "Las capas de adaptación calculan las alturas de las capas dependiendo de la forma del modelo."

msgctxt "infill_wall_line_count description"
msgid ""
"Add extra walls around the infill area. Such walls can make top/bottom skin lines sag down less which means you need less top/bottom skin layers for the same quality at the cost of some extra material.\n"
"This feature can combine with the Connect Infill Polygons to connect all the infill into a single extrusion path without the need for travels or retractions if configured right."
msgstr ""
"Agregar paredes adicionales alrededor del área de relleno. Estas paredes pueden hacer que las líneas del forro superior/inferior se aflojen menos, lo que significa que necesitaría menos capas de forro superior/inferior para obtener la misma calidad utilizando algo más de material.\n"
"Puede utilizar esta función junto a la de Conectar polígonos de relleno para conectar todo el relleno en una única trayectoria de extrusión sin necesidad de desplazamientos ni retracciones si se configura correctamente."

msgctxt "platform_adhesion description"
msgid "Adhesion"
msgstr "Adherencia"

msgctxt "material_adhesion_tendency label"
msgid "Adhesion Tendency"
msgstr "Tendencia de adherencia"

msgctxt "skin_overlap description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines, as a percentage of the line widths of the skin lines and the innermost wall. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any percentage over 50% may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "Ajuste la cantidad de superposición entre las paredes y (los extremos de) las líneas centrales del forro, como un porcentaje de los anchos de las líneas del forro y la pared más profunda. Una ligera superposición permite que las paredes estén firmemente unidas al forro. Tenga en cuenta que, con un mismo ancho de la línea del forro y la pared, cualquier porcentaje superior al 50 % ya puede provocar que cualquier forro sobrepase la pared, debido a que en ese punto la posición de la tobera del extrusor del forro ya puede sobrepasar la mitad de la pared."

msgctxt "skin_overlap_mm description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any value over half the width of the wall may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "Ajuste la cantidad de superposición entre las paredes y (los extremos de) las líneas centrales del forro. Una ligera superposición permite que las paredes estén firmemente unidas al forro. Tenga en cuenta que, con un mismo ancho de la línea del forro y la pared, cualquier valor superior a la mitad del ancho de la pared ya puede provocar que cualquier forro sobrepase la pared, debido a que en ese punto la posición de la tobera del extrusor del forro ya puede sobrepasar la mitad de la pared."

msgctxt "infill_sparse_density description"
msgid "Adjusts the density of infill of the print."
msgstr "Ajusta la densidad del relleno de la impresión."

msgctxt "support_interface_density description"
msgid "Adjusts the density of the roofs and floors of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Ajusta la densidad de los techos y suelos de la estructura de soporte. Un valor superior da como resultado mejores voladizos pero los soportes son más difíciles de retirar."

msgctxt "support_tree_top_rate description"
msgid "Adjusts the density of the support structure used to generate the tips of the branches. A higher value results in better overhangs, but the supports are harder to remove. Use Support Roof for very high values or ensure support density is similarly high at the top."
msgstr "Ajusta la densidad de la estructura de soporte utilizada para generar las puntas de las ramas. Un valor más alto da como resultado mejores voladizos, pero los soportes son más difíciles de eliminar. Utilice el techo de soporte para valores muy altos o asegúrese de que la densidad del soporte sea igual de alta en la parte superior."

msgctxt "support_infill_rate description"
msgid "Adjusts the density of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Ajusta la densidad de la estructura del soporte. Un valor superior da como resultado mejores voladizos pero los soportes son más difíciles de retirar."

msgctxt "material_diameter description"
msgid "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament."
msgstr "Ajusta el diámetro del filamento utilizado. Este valor debe coincidir con el diámetro del filamento utilizado."

msgctxt "support_type description"
msgid "Adjusts the placement of the support structures. The placement can be set to touching build plate or everywhere. When set to everywhere the support structures will also be printed on the model."
msgstr "Ajusta la colocación de las estructuras del soporte. La colocación se puede establecer tocando la placa de impresión o en todas partes. Cuando se establece en todas partes, las estructuras del soporte también se imprimirán en el modelo."

msgctxt "prime_tower_wipe_enabled description"
msgid "After printing the prime tower with one nozzle, wipe the oozed material from the other nozzle off on the prime tower."
msgstr "Tras imprimir la torre auxiliar con una tobera, limpie el material rezumado de la otra tobera de la torre auxiliar."

msgctxt "retraction_hop_after_extruder_switch description"
msgid "After the machine switched from one extruder to the other, the build plate is lowered to create clearance between the nozzle and the print. This prevents the nozzle from leaving oozed material on the outside of a print."
msgstr "Cuando la máquina cambia de un extrusor a otro, la placa de impresión se baja para crear holgura entre la tobera y la impresión. Esto impide que el material rezumado quede fuera de la impresión."

msgctxt "retraction_combing option all"
msgid "All"
msgstr "Todo"

msgctxt "print_sequence option all_at_once"
msgid "All at Once"
msgstr "Todos a la vez"

msgctxt "resolution description"
msgid "All settings that influence the resolution of the print. These settings have a large impact on the quality (and print time)"
msgstr "Todos los ajustes que influyen en la resolución de la impresión. Estos ajustes tienen una gran repercusión en la calidad (y en el tiempo de impresión)."

msgctxt "alternate_extra_perimeter label"
msgid "Alternate Extra Wall"
msgstr "Alternar pared adicional"

msgctxt "alternate_carve_order label"
msgid "Alternate Mesh Removal"
msgstr "Alternar la retirada de las mallas"

msgctxt "material_alternate_walls label"
msgid "Alternate Wall Directions"
msgstr "Alternar direcciones de pared"

msgctxt "material_alternate_walls description"
msgid "Alternate wall directions every other layer and inset. Useful for materials that can build up stress, like for metal printing."
msgstr "Le permite alternar las direcciones de las paredes entre capas o insertos. Útil para materiales que pueden acumular tensión, como para imprimir en metal."

msgctxt "machine_buildplate_type option aluminum"
msgid "Aluminum"
msgstr "Aluminio"

msgctxt "machine_always_write_active_tool label"
msgid "Always Write Active Tool"
msgstr "Escriba siempre la herramienta activa"

msgctxt "travel_retract_before_outer_wall description"
msgid "Always retract when moving to start an outer wall."
msgstr "Retraer siempre al desplazarse para empezar una pared exterior."

msgctxt "xy_offset description"
msgid "Amount of offset applied to all polygons in each layer. Positive values can compensate for too big holes; negative values can compensate for too small holes."
msgstr "Cantidad de desplazamiento aplicado a todos los polígonos de cada capa. Los valores positivos pueden compensar agujeros demasiado grandes; los valores negativos pueden compensar agujeros demasiado pequeños."

msgctxt "xy_offset_layer_0 description"
msgid "Amount of offset applied to all polygons in the first layer. A negative value can compensate for squishing of the first layer known as \"elephant's foot\"."
msgstr "Cantidad de desplazamiento aplicado a todos los polígonos de la primera capa. Un valor negativo puede compensar el aplastamiento de la primera capa, lo que se conoce como «pie de elefante»."

msgctxt "support_offset description"
msgid "Amount of offset applied to all support polygons in each layer. Positive values can smooth out the support areas and result in more sturdy support."
msgstr "Cantidad de desplazamiento aplicado a todos los polígonos de cada capa. Los valores positivos pueden suavizar las áreas del soporte y producir un soporte más robusto."

msgctxt "support_bottom_offset description"
msgid "Amount of offset applied to the floors of the support."
msgstr "Cantidad de desplazamiento aplicado a los suelos del soporte."

msgctxt "support_roof_offset description"
msgid "Amount of offset applied to the roofs of the support."
msgstr "Cantidad de desplazamiento aplicado a los techos del soporte."

msgctxt "support_interface_offset description"
msgid "Amount of offset applied to the support interface polygons."
msgstr "Cantidad de desplazamiento aplicado a los polígonos de la interfaz de soporte."

msgctxt "wipe_retraction_amount description"
msgid "Amount to retract the filament so it does not ooze during the wipe sequence."
msgstr "Cantidad para retraer el filamento para que no rezume durante la secuencia de limpieza."

msgctxt "sub_div_rad_add description"
msgid "An addition to the radius from the center of each cube to check for the boundary of the model, as to decide whether this cube should be subdivided. Larger values lead to a thicker shell of small cubes near the boundary of the model."
msgstr "Un suplemento al radio desde el centro de cada cubo cuyo fin es comprobar el contorno del modelo para decidir si este cubo debería subdividirse. Cuanto mayor sea su valor, más grueso será el perímetro de cubos pequeños junto al contorno del modelo."

msgctxt "anti_overhang_mesh label"
msgid "Anti Overhang Mesh"
msgstr "Malla antivoladizo"

msgctxt "material_anti_ooze_retracted_position label"
msgid "Anti-ooze Retracted Position"
msgstr "Velocidad de retracción antirrezumado"

msgctxt "material_anti_ooze_retraction_speed label"
msgid "Anti-ooze Retraction Speed"
msgstr "Velocidad de retracción antirrezumado"

msgctxt "machine_use_extruder_offset_to_offset_coords description"
msgid "Apply the extruder offset to the coordinate system. Affects all extruders."
msgstr "Aplicar el desplazamiento del extrusor al sistema de coordenadas. Influye en todos los extrusores."

msgctxt "interlocking_enable description"
msgid "At the locations where models touch, generate an interlocking beam structure. This improves the adhesion between models, especially models printed in different materials."
msgstr "En las ubicaciones donde se tocan los modelos, genere una estructura de haz entrelazado. Esto mejora la adhesión entre los modelos, especialmente de aquellos impresos en materiales diferentes."

msgctxt "travel_avoid_other_parts label"
msgid "Avoid Printed Parts When Traveling"
msgstr "Evitar partes impresas al desplazarse"

msgctxt "travel_avoid_supports label"
msgid "Avoid Supports When Traveling"
msgstr "Evitar soportes al desplazarse"

msgctxt "z_seam_position option back"
msgid "Back"
msgstr "Posterior"

msgctxt "z_seam_position option backleft"
msgid "Back Left"
msgstr "Posterior izquierda"

msgctxt "z_seam_position option backright"
msgid "Back Right"
msgstr "Posterior derecha"

msgctxt "machine_gcode_flavor option BFB"
msgid "Bits from Bytes"
msgstr "Bits from Bytes"

msgctxt "magic_mesh_surface_mode option both"
msgid "Both"
msgstr "Ambos"

msgctxt "support_interface_priority option nothing"
msgid "Both overlap"
msgstr "Ambos se solapan"

msgctxt "bottom_layers label"
msgid "Bottom Layers"
msgstr "Capas inferiores"

msgctxt "top_bottom_pattern_0 label"
msgid "Bottom Pattern Initial Layer"
msgstr "Patrón inferior de la capa inicial"

msgctxt "bottom_skin_expand_distance label"
msgid "Bottom Skin Expand Distance"
msgstr "Distancia de expansión del forro inferior"

msgctxt "bottom_skin_preshrink label"
msgid "Bottom Skin Removal Width"
msgstr "Anchura de retirada del forro inferior"

msgctxt "bottom_thickness label"
msgid "Bottom Thickness"
msgstr "Grosor inferior"

msgctxt "support_tree_top_rate label"
msgid "Branch Density"
msgstr "Densidad de la rama"

msgctxt "support_tree_branch_diameter label"
msgid "Branch Diameter"
msgstr "Diámetro de la rama"

msgctxt "support_tree_branch_diameter_angle label"
msgid "Branch Diameter Angle"
msgstr "Ángulo del diámetro de la rama"

msgctxt "material_break_preparation_retracted_position label"
msgid "Break Preparation Retracted Position"
msgstr "Posición retraída de preparación de rotura"

msgctxt "material_break_preparation_speed label"
msgid "Break Preparation Retraction Speed"
msgstr "Velocidad de retracción de preparación de rotura"

msgctxt "material_break_preparation_temperature label"
msgid "Break Preparation Temperature"
msgstr "Temperatura de preparación de rotura"

msgctxt "material_break_retracted_position label"
msgid "Break Retracted Position"
msgstr "Posición retraída de rotura"

msgctxt "material_break_speed label"
msgid "Break Retraction Speed"
msgstr "Velocidad de retracción de rotura"

msgctxt "material_break_temperature label"
msgid "Break Temperature"
msgstr "Temperatura de rotura"

msgctxt "support_skip_some_zags label"
msgid "Break Up Support In Chunks"
msgstr "Descomponer el soporte en pedazos"

msgctxt "bridge_fan_speed label"
msgid "Bridge Fan Speed"
msgstr "Velocidad del ventilador del puente"

msgctxt "bridge_enable_more_layers label"
msgid "Bridge Has Multiple Layers"
msgstr "Puente con varias capas"

msgctxt "bridge_skin_density_2 label"
msgid "Bridge Second Skin Density"
msgstr "Densidad del segundo forro del puente"

msgctxt "bridge_fan_speed_2 label"
msgid "Bridge Second Skin Fan Speed"
msgstr "Velocidad del ventilador del segundo forro del puente"

msgctxt "bridge_skin_material_flow_2 label"
msgid "Bridge Second Skin Flow"
msgstr "Flujo del segundo forro del puente"

msgctxt "bridge_skin_speed_2 label"
msgid "Bridge Second Skin Speed"
msgstr "Velocidad del segundo forro del puente"

msgctxt "bridge_skin_density label"
msgid "Bridge Skin Density"
msgstr "Densidad de forro del puente"

msgctxt "bridge_skin_material_flow label"
msgid "Bridge Skin Flow"
msgstr "Flujo de forro del puente"

msgctxt "bridge_skin_speed label"
msgid "Bridge Skin Speed"
msgstr "Velocidad de forro del puente"

msgctxt "bridge_skin_support_threshold label"
msgid "Bridge Skin Support Threshold"
msgstr "Umbral del soporte del forro del puente"

msgctxt "bridge_sparse_infill_max_density label"
msgid "Bridge Sparse Infill Max Density"
msgstr "Densidad máxima de relleno de puente escaso"

msgctxt "bridge_skin_density_3 label"
msgid "Bridge Third Skin Density"
msgstr "Densidad del tercer forro del puente"

msgctxt "bridge_fan_speed_3 label"
msgid "Bridge Third Skin Fan Speed"
msgstr "Velocidad del ventilador del tercer forro del puente"

msgctxt "bridge_skin_material_flow_3 label"
msgid "Bridge Third Skin Flow"
msgstr "Flujo del tercer forro del puente"

msgctxt "bridge_skin_speed_3 label"
msgid "Bridge Third Skin Speed"
msgstr "Velocidad del tercer forro del puente"

msgctxt "bridge_wall_coast label"
msgid "Bridge Wall Coasting"
msgstr "Depósito por inercia de la pared del puente"

msgctxt "bridge_wall_material_flow label"
msgid "Bridge Wall Flow"
msgstr "Flujo de pared del puente"

msgctxt "bridge_wall_speed label"
msgid "Bridge Wall Speed"
msgstr "Velocidad de pared del puente"

msgctxt "adhesion_type option brim"
msgid "Brim"
msgstr "Borde"

msgctxt "brim_gap label"
msgid "Brim Distance"
msgstr "Distancia del borde"

msgctxt "brim_inside_margin label"
msgid "Brim Inside Avoid Margin"
msgstr "Margen de distancia del borde interior"

msgctxt "brim_line_count label"
msgid "Brim Line Count"
msgstr "Recuento de líneas de borde"

msgctxt "brim_outside_only label"
msgid "Brim Only on Outside"
msgstr "Borde solo en el exterior"

msgctxt "brim_replaces_support label"
msgid "Brim Replaces Support"
msgstr "Sustituir soporte por borde"

msgctxt "brim_width label"
msgid "Brim Width"
msgstr "Ancho del borde"

msgctxt "platform_adhesion label"
msgid "Build Plate Adhesion"
msgstr "Adherencia de la placa de impresión"

msgctxt "adhesion_extruder_nr label"
msgid "Build Plate Adhesion Extruder"
msgstr "Extrusor de adherencia de la placa de impresión"

msgctxt "adhesion_type label"
msgid "Build Plate Adhesion Type"
msgstr "Tipo adherencia de la placa de impresión"

msgctxt "machine_buildplate_type label"
msgid "Build Plate Material"
msgstr "Material de placa de impresión"

msgctxt "machine_shape label"
msgid "Build Plate Shape"
msgstr "Forma de la placa de impresión"

msgctxt "material_bed_temperature label"
msgid "Build Plate Temperature"
msgstr "Temperatura de la placa de impresión"

msgctxt "material_bed_temperature_layer_0 label"
msgid "Build Plate Temperature Initial Layer"
msgstr "Temperatura de la placa de impresión en la capa inicial"

msgctxt "build_volume_temperature label"
msgid "Build Volume Temperature"
msgstr "Temperatura de volumen de impresión"

msgctxt "prime_tower_brim_enable description"
msgid "By enabling this setting, your prime-tower will get a brim, even if the model doesn't. If you want a sturdier base for a high tower, you can increase the base height."
msgstr "Al habilitar esta configuración, tu torre de cebado tendrá un borde, incluso si el modelo no lo tiene. Si quieres una base más robusta para una torre alta, puedes aumentar la altura de la base."

msgctxt "center_object label"
msgid "Center Object"
msgstr "Centrar objeto"

msgctxt "conical_overhang_enabled description"
msgid "Change the geometry of the printed model such that minimal support is required. Steep overhangs will become shallow overhangs. Overhanging areas will drop down to become more vertical."
msgstr "Cambiar la geometría del modelo impreso de modo que se necesite un soporte mínimo. Los voladizos descendentes se convertirán en voladizos llanos y las áreas inclinadas caerán para ser más verticales."

msgctxt "support_structure description"
msgid "Chooses between the techniques available to generate support. \"Normal\" support creates a support structure directly below the overhanging parts and drops those areas straight down. \"Tree\" support creates branches towards the overhanging areas that support the model on the tips of those branches, and allows the branches to crawl around the model to support it from the build plate as much as possible."
msgstr "Elige entre las técnicas disponibles para generar soporte. El soporte \"Normal\" crea una estructura de soporte directamente debajo de las partes en voladizo y lleva estas áreas hacia abajo. El soporte en \"Árbol\" crea ramas en las áreas en voladizo que sostienen el modelo al final de estas ramas y permite que las ramas se arrastren alrededor del modelo para sostenerlo tanto como sea posible en la placa de impresión."

msgctxt "coasting_speed label"
msgid "Coasting Speed"
msgstr "Velocidad de depósito por inercia"

msgctxt "coasting_volume label"
msgid "Coasting Volume"
msgstr "Volumen de depósito por inercia"

msgctxt "coasting_enable description"
msgid "Coasting replaces the last part of an extrusion path with a travel path. The oozed material is used to print the last piece of the extrusion path in order to reduce stringing."
msgstr "Depósito por inercia sustituye la última parte de una trayectoria de extrusión por una trayectoria de desplazamiento. El material rezumado se utiliza para imprimir la última parte de la trayectoria de extrusión con el fin de reducir el encordado."

msgctxt "retraction_combing label"
msgid "Combing Mode"
msgstr "Modo Peinada"

msgctxt "retraction_combing description"
msgid "Combing keeps the nozzle within already printed areas when traveling. This results in slightly longer travel moves but reduces the need for retractions. If combing is off, the material will retract and the nozzle moves in a straight line to the next point. It is also possible to avoid combing over top/bottom skin areas or to only comb within the infill."
msgstr "La opción de peinada mantiene la tobera dentro de las áreas ya impresas al desplazarse. Esto ocasiona movimientos de desplazamiento ligeramente más largos, pero reduce la necesidad de realizar retracciones. Si se desactiva la opción de peinada, el material se retraerá y la tobera se moverá en línea recta hasta el siguiente punto. Otra posibilidad es evitar la peinada en áreas de forro superiores/inferiores o peinar solo en el relleno."

msgctxt "command_line_settings label"
msgid "Command Line Settings"
msgstr "Ajustes de la línea de comandos"

msgctxt "infill_pattern option concentric"
msgid "Concentric"
msgstr "Concéntrico"

msgctxt "ironing_pattern option concentric"
msgid "Concentric"
msgstr "Concéntrico"

msgctxt "roofing_pattern option concentric"
msgid "Concentric"
msgstr "Concéntrico"

msgctxt "support_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Concéntrico"

msgctxt "support_interface_pattern option concentric"
msgid "Concentric"
msgstr "Concéntrico"

msgctxt "support_pattern option concentric"
msgid "Concentric"
msgstr "Concéntrico"

msgctxt "support_roof_pattern option concentric"
msgid "Concentric"
msgstr "Concéntrico"

msgctxt "top_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Concéntrico"

msgctxt "top_bottom_pattern_0 option concentric"
msgid "Concentric"
msgstr "Concéntrico"

msgctxt "support_conical_angle label"
msgid "Conical Support Angle"
msgstr "Ángulo del soporte cónico"

msgctxt "support_conical_min_width label"
msgid "Conical Support Minimum Width"
msgstr "Anchura mínima del soporte cónico"

msgctxt "zig_zaggify_infill label"
msgid "Connect Infill Lines"
msgstr "Conectar líneas de relleno"

msgctxt "connect_infill_polygons label"
msgid "Connect Infill Polygons"
msgstr "Conectar polígonos de relleno"

msgctxt "zig_zaggify_support label"
msgid "Connect Support Lines"
msgstr "Conectar líneas del soporte"

msgctxt "support_connect_zigzags label"
msgid "Connect Support ZigZags"
msgstr "Conectar zigzags del soporte"

msgctxt "connect_skin_polygons label"
msgid "Connect Top/Bottom Polygons"
msgstr "Conectar polígonos superiores/inferiores"

msgctxt "connect_infill_polygons description"
msgid "Connect infill paths where they run next to each other. For infill patterns which consist of several closed polygons, enabling this setting greatly reduces the travel time."
msgstr "Conectar las trayectorias de polígonos cuando están próximas entre sí. Habilitar esta opción reduce considerablemente el tiempo de desplazamiento en los patrones de relleno que constan de varios polígonos cerrados."

msgctxt "support_connect_zigzags description"
msgid "Connect the ZigZags. This will increase the strength of the zig zag support structure."
msgstr "Conectar los zigzags. Esto aumentará la resistencia de la estructura del soporte de zigzag."

msgctxt "zig_zaggify_support description"
msgid "Connect the ends of the support lines together. Enabling this setting can make your support more sturdy and reduce underextrusion, but it will cost more material."
msgstr "Unión de los extremos de las líneas de soporte. Al habilitar este ajuste, puede conseguir que el soporte sea más sólido y reducir la infraextrusión, pero se necesitará más material."

msgctxt "zig_zaggify_infill description"
msgid "Connect the ends where the infill pattern meets the inner wall using a line which follows the shape of the inner wall. Enabling this setting can make the infill adhere to the walls better and reduce the effects of infill on the quality of vertical surfaces. Disabling this setting reduces the amount of material used."
msgstr "Conectar los extremos donde los patrones de relleno se juntan con la pared interior usando una línea que siga la forma de esta. Habilitar este ajuste puede lograr que el relleno se adhiera mejor a las paredes y se reduzca el efecto del relleno sobre la calidad de las superficies verticales. Deshabilitar este ajuste reduce la cantidad de material utilizado."

msgctxt "connect_skin_polygons description"
msgid "Connect top/bottom skin paths where they run next to each other. For the concentric pattern enabling this setting greatly reduces the travel time, but because the connections can happen midway over infill this feature can reduce the top surface quality."
msgstr "Conecta las trayectorias de forro superior/inferior cuando están próximas entre sí. Al habilitar este ajuste, en el patrón concéntrico se reduce considerablemente el tiempo de desplazamiento, pero las conexiones pueden producirse en mitad del relleno, con lo que bajaría la calidad de la superficie superior."

msgctxt "z_seam_corner description"
msgid "Control whether corners on the model outline influence the position of the seam. None means that corners have no influence on the seam position. Hide Seam makes the seam more likely to occur on an inside corner. Expose Seam makes the seam more likely to occur on an outside corner. Hide or Expose Seam makes the seam more likely to occur at an inside or outside corner. Smart Hiding allows both inside and outside corners, but chooses inside corners more frequently, if appropriate."
msgstr "Controlar si las esquinas del contorno del modelo influyen en la posición de la costura. «Ninguno» significa que las esquinas no influyen en la posición de la costura. «Ocultar costura» significa que es probable que la costura se realice en una esquina interior. «Mostrar costura» significa que es probable que la costura se realice en una esquina exterior. «Ocultar o mostrar costura» significa que es probable que la costura se realice en una esquina interior o exterior. «Costura inteligente» permite realizar la costura en ambas esquinas, pero opta con más frecuencia por las esquinas interiores, si resulta oportuno."

msgctxt "infill_multiplier description"
msgid "Convert each infill line to this many lines. The extra lines do not cross over each other, but avoid each other. This makes the infill stiffer, but increases print time and material usage."
msgstr "Multiplicar cada línea de relleno. Las líneas adicionales no se cruzan entre sí, sino que se evitan entre ellas. Esto consigue un relleno más rígido, pero incrementa el tiempo de impresión y el uso de material."

msgctxt "machine_nozzle_cool_down_speed label"
msgid "Cool Down Speed"
msgstr "Velocidad de enfriamiento"

msgctxt "cooling description"
msgid "Cooling"
msgstr "Refrigeración"

msgctxt "cooling label"
msgid "Cooling"
msgstr "Refrigeración"

msgctxt "infill_pattern option cross"
msgid "Cross"
msgstr "Cruz"

msgctxt "support_pattern option cross"
msgid "Cross"
msgstr "Cruz"

msgctxt "infill_pattern option cross_3d"
msgid "Cross 3D"
msgstr "Cruz 3D"

msgctxt "cross_infill_pocket_size label"
msgid "Cross 3D Pocket Size"
msgstr "Tamaño de las bolsas 3D en cruces"

msgctxt "cross_support_density_image label"
msgid "Cross Fill Density Image for Support"
msgstr "Imagen de densidad de relleno cruzada para soporte"

msgctxt "cross_infill_density_image label"
msgid "Cross Infill Density Image"
msgstr "Imagen de densidad de relleno cruzada"

msgctxt "material_crystallinity label"
msgid "Crystalline Material"
msgstr "Material cristalino"

msgctxt "infill_pattern option cubic"
msgid "Cubic"
msgstr "Cúbico"

msgctxt "infill_pattern option cubicsubdiv"
msgid "Cubic Subdivision"
msgstr "Subdivisión cúbica"

msgctxt "sub_div_rad_add label"
msgid "Cubic Subdivision Shell"
msgstr "Perímetro de la subdivisión cúbica"

msgctxt "cutting_mesh label"
msgid "Cutting Mesh"
msgstr "Cortar malla"

msgctxt "material_flow_temp_graph description"
msgid "Data linking material flow (in mm3 per second) to temperature (degrees Celsius)."
msgstr "Datos que vinculan el flujo de materiales (en 3 mm por segundo) a la temperatura (grados centígrados)."

msgctxt "machine_acceleration label"
msgid "Default Acceleration"
msgstr "Aceleración predeterminada"

msgctxt "default_material_bed_temperature label"
msgid "Default Build Plate Temperature"
msgstr "Temperatura predeterminada de la placa de impresión"

msgctxt "machine_max_jerk_e label"
msgid "Default Filament Jerk"
msgstr "Impulso de filamento predeterminado"

msgctxt "default_material_print_temperature label"
msgid "Default Printing Temperature"
msgstr "Temperatura de impresión predeterminada"

msgctxt "machine_max_jerk_xy label"
msgid "Default X-Y Jerk"
msgstr "Impulso X-Y predeterminado"

msgctxt "machine_max_jerk_z label"
msgid "Default Z Jerk"
msgstr "Impulso Z predeterminado"

msgctxt "machine_max_jerk_xy description"
msgid "Default jerk for movement in the horizontal plane."
msgstr "Impulso predeterminado para el movimiento en el plano horizontal."

msgctxt "machine_max_jerk_z description"
msgid "Default jerk for the motor of the Z-direction."
msgstr "Impulso predeterminado del motor de la dirección Z."

msgctxt "machine_max_jerk_e description"
msgid "Default jerk for the motor of the filament."
msgstr "Impulso predeterminado del motor del filamento."

msgctxt "bridge_settings_enabled description"
msgid "Detect bridges and modify print speed, flow and fan settings while bridges are printed."
msgstr "Detección de puentes y modificación de los ajustes de velocidad de impresión, flujo y ventilador durante la impresión de puentes."

msgctxt "inset_direction description"
msgid "Determines the order in which walls are printed. Printing outer walls earlier helps with dimensional accuracy, as faults from inner walls cannot propagate to the outside. However printing them later allows them to stack better when overhangs are printed. When there is an uneven amount of total innner walls, the 'center last line' is always printed last."
msgstr "Determina el orden de impresión de las paredes. Empezar imprimiendo las paredes exteriores ayuda a la precisión dimensional, ya que evita que los defectos de las paredes interiores se propaguen al exterior. Sin embargo, si las imprime más tarde, podrá apilarlas mejor cuando se impriman los voladizos. Cuando hay una cantidad impar de paredes interiores totales, la «última línea central» siempre se imprime en último lugar."

msgctxt "infill_mesh_order description"
msgid "Determines the priority of this mesh when considering multiple overlapping infill meshes. Areas where multiple infill meshes overlap will take on the settings of the mesh with the highest rank. An infill mesh with a higher rank will modify the infill of infill meshes with lower rank and normal meshes."
msgstr "Determina la prioridad de esta malla al tener en cuenta varias mallas de relleno superpuestas. Las áreas en las que se superponen varias mallas de relleno tomarán la configuración de la malla con el rango más alto. Una malla de relleno con un rango superior modificará el relleno de las mallas de relleno con un rango inferior y mallas normales."

msgctxt "lightning_infill_support_angle description"
msgid "Determines when a lightning infill layer has to support anything above it. Measured in the angle given the thickness of a layer."
msgstr "Determina cuándo una capa de relleno de rayos tiene que soportar algo por encima de ella. Medido en el ángulo dado el espesor de una capa."

msgctxt "lightning_infill_overhang_angle description"
msgid "Determines when a lightning infill layer has to support the model above it. Measured in the angle given the thickness."
msgstr "Determina cuándo una capa de relleno de rayos tiene que soportar el modelo que está por encima. Medido en el ángulo dado el espesor."

msgctxt "material_diameter label"
msgid "Diameter"
msgstr "Diámetro"

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model label"
msgid "Diameter Increase To Model"
msgstr "Aumento del diámetro para el modelo"

msgctxt "support_tree_bp_diameter description"
msgid "Diameter every branch tries to achieve when reaching the buildplate. Improves bed adhesion."
msgstr "Diámetro que cada rama trata de alcanzar al llegar a la placa de impresión. Mejora la adherencia a la plataforma."

msgctxt "adhesion_type description"
msgid "Different options that help to improve both priming your extrusion and adhesion to the build plate. Brim adds a single layer flat area around the base of your model to prevent warping. Raft adds a thick grid with a roof below the model. Skirt is a line printed around the model, but not connected to the model."
msgstr "Opciones diferentes que ayudan a mejorar tanto la extrusión como la adherencia a la placa de impresión. El borde agrega una zona plana de una sola capa alrededor de la base del modelo para impedir que se deforme. La balsa agrega una rejilla gruesa con un techo por debajo del modelo. La falda es una línea impresa alrededor del modelo, pero que no está conectada al modelo."

msgctxt "machine_disallowed_areas label"
msgid "Disallowed Areas"
msgstr "Áreas no permitidas"

msgctxt "infill_line_distance description"
msgid "Distance between the printed infill lines. This setting is calculated by the infill density and the infill line width."
msgstr "Distancia entre las líneas de relleno impresas. Este ajuste se calcula por la densidad del relleno y el ancho de la línea de relleno."

msgctxt "support_initial_layer_line_distance description"
msgid "Distance between the printed initial layer support structure lines. This setting is calculated by the support density."
msgstr "Distancia entre las líneas de estructuras del soporte de la capa inicial impresas. Este ajuste se calcula por la densidad del soporte."

msgctxt "support_bottom_line_distance description"
msgid "Distance between the printed support floor lines. This setting is calculated by the Support Floor Density, but can be adjusted separately."
msgstr "Distancia entre las líneas de suelo de soporte impresas. Este ajuste se calcula por la densidad del suelo del soporte pero se puede ajustar de forma independiente."

msgctxt "support_roof_line_distance description"
msgid "Distance between the printed support roof lines. This setting is calculated by the Support Roof Density, but can be adjusted separately."
msgstr "Distancia entre las líneas de techo de soporte impresas. Este ajuste se calcula por la densidad del techo del soporte pero se puede ajustar de forma independiente."

msgctxt "support_line_distance description"
msgid "Distance between the printed support structure lines. This setting is calculated by the support density."
msgstr "Distancia entre las líneas de estructuras del soporte impresas. Este ajuste se calcula por la densidad del soporte."

msgctxt "support_bottom_distance description"
msgid "Distance from the print to the bottom of the support. Note that this is rounded up to the next layer height."
msgstr "Distancia de la impresión hasta la parte inferior del soporte. Ten en cuenta que esto se redondea al siguiente altura de capa."

msgctxt "support_top_distance description"
msgid "Distance from the top of the support to the print."
msgstr "Distancia desde la parte superior del soporte a la impresión."

msgctxt "support_z_distance description"
msgid "Distance from the top/bottom of the support structure to the print. This gap provides clearance to remove the supports after the model is printed. The topmost support layer below the model might be a fraction of regular layers."
msgstr "Distancia desde la parte superior/inferior de la estructura de soporte hasta la impresión. Este espacio proporciona la holgura necesaria para remover los soportes después de imprimir el modelo. La capa de soporte más cercana al modelo podría ser una fracción de las capas regulares."

msgctxt "infill_wipe_dist description"
msgid "Distance of a travel move inserted after every infill line, to make the infill stick to the walls better. This option is similar to infill overlap, but without extrusion and only on one end of the infill line."
msgstr "Distancia de un desplazamiento insertado después de cada línea de relleno, para que el relleno se adhiera mejor a las paredes. Esta opción es similar a la superposición del relleno, pero sin extrusión y solo en un extremo de la línea de relleno."

msgctxt "wall_0_wipe_dist description"
msgid "Distance of a travel move inserted after the outer wall, to hide the Z seam better."
msgstr "Distancia de un movimiento de desplazamiento insertado tras la pared exterior con el fin de ocultar mejor la costura sobre el eje Z."

msgctxt "draft_shield_dist description"
msgid "Distance of the draft shield from the print, in the X/Y directions."
msgstr "Distancia entre el parabrisas y la impresión, en las direcciones X/Y."

msgctxt "ooze_shield_dist description"
msgid "Distance of the ooze shield from the print, in the X/Y directions."
msgstr "Distancia entre la placa de rezumado y la impresión, en las direcciones X/Y."

msgctxt "support_xy_distance_overhang description"
msgid "Distance of the support structure from the overhang in the X/Y directions."
msgstr "Distancia de la estructura de soporte desde el voladizo en las direcciones X/Y."

msgctxt "support_xy_distance description"
msgid "Distance of the support structure from the print in the X/Y directions."
msgstr "Distancia de la estructura del soporte desde la impresión en las direcciones X/Y."

msgctxt "meshfix_fluid_motion_shift_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "Los puntos de distancia se desplazan para suavizar la trayectoria"

msgctxt "meshfix_fluid_motion_small_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "Los puntos de distancia se desplazan para suavizar la trayectoria"

msgctxt "min_infill_area description"
msgid "Don't generate areas of infill smaller than this (use skin instead)."
msgstr "No genere áreas con un relleno inferior a este (utilice forro)."

msgctxt "draft_shield_height label"
msgid "Draft Shield Height"
msgstr "Altura del parabrisas"

msgctxt "draft_shield_height_limitation label"
msgid "Draft Shield Limitation"
msgstr "Limitación del parabrisas"

msgctxt "draft_shield_dist label"
msgid "Draft Shield X/Y Distance"
msgstr "Distancia X/Y del parabrisas"

msgctxt "support_mesh_drop_down label"
msgid "Drop Down Support Mesh"
msgstr "Malla de soporte desplegable"

msgctxt "dual label"
msgid "Dual Extrusion"
msgstr "Extrusión doble"

msgctxt "machine_shape option elliptic"
msgid "Elliptic"
msgstr "Elíptica"

msgctxt "acceleration_enabled label"
msgid "Enable Acceleration Control"
msgstr "Activar control de aceleración"

msgctxt "bridge_settings_enabled label"
msgid "Enable Bridge Settings"
msgstr "Habilitar ajustes del puente"

msgctxt "coasting_enable label"
msgid "Enable Coasting"
msgstr "Habilitar depósito por inercia"

msgctxt "support_conical_enabled label"
msgid "Enable Conical Support"
msgstr "Activar soporte cónico"

msgctxt "draft_shield_enabled label"
msgid "Enable Draft Shield"
msgstr "Habilitar parabrisas"

msgctxt "meshfix_fluid_motion_enabled label"
msgid "Enable Fluid Motion"
msgstr "Activar movimiento fluido"

msgctxt "ironing_enabled label"
msgid "Enable Ironing"
msgstr "Habilitar alisado"

msgctxt "jerk_enabled label"
msgid "Enable Jerk Control"
msgstr "Activar control de impulso"

msgctxt "machine_nozzle_temp_enabled label"
msgid "Enable Nozzle Temperature Control"
msgstr "Habilitar control de temperatura de la tobera"

msgctxt "ooze_shield_enabled label"
msgid "Enable Ooze Shield"
msgstr "Activar placa de rezumado"

msgctxt "prime_blob_enable label"
msgid "Enable Prime Blob"
msgstr "Activar gotas de cebado"

msgctxt "prime_tower_enable label"
msgid "Enable Prime Tower"
msgstr "Activar la torre auxiliar"

msgctxt "cool_fan_enabled label"
msgid "Enable Print Cooling"
msgstr "Activar refrigeración de impresión"

msgctxt "retraction_enable label"
msgid "Enable Retraction"
msgstr "Habilitar la retracción"

msgctxt "support_brim_enable label"
msgid "Enable Support Brim"
msgstr "Habilitar borde de soporte"

msgctxt "support_bottom_enable label"
msgid "Enable Support Floor"
msgstr "Habilitar suelo del soporte"

msgctxt "support_interface_enable label"
msgid "Enable Support Interface"
msgstr "Habilitar interfaz del soporte"

msgctxt "support_roof_enable label"
msgid "Enable Support Roof"
msgstr "Habilitar techo del soporte"

msgctxt "acceleration_travel_enabled label"
msgid "Enable Travel Acceleration"
msgstr "Habilitar aceleración de desplazamiento"

msgctxt "jerk_travel_enabled label"
msgid "Enable Travel Jerk"
msgstr "Activar impulso de desplazamiento"

msgctxt "ooze_shield_enabled description"
msgid "Enable exterior ooze shield. This will create a shell around the model which is likely to wipe a second nozzle if it's at the same height as the first nozzle."
msgstr "Activar la placa de rezumado exterior. Esto crea un perímetro alrededor del modelo que suele limpiar una segunda tobera si se encuentra a la misma altura que la primera."

msgctxt "small_skin_on_surface description"
msgid "Enable small (up to 'Small Top/Bottom Width') regions on the topmost skinned layer (exposed to air) to be filled with walls instead of the default pattern."
msgstr "Permite que las zonas pequeñas (hasta \"Ancho superior/inferior pequeño\") de la capa más superficial (expuestas al aire) se rellenen con paredes en lugar de con el patrón predeterminado."

msgctxt "jerk_enabled description"
msgid "Enables adjusting the jerk of print head when the velocity in the X or Y axis changes. Increasing the jerk can reduce printing time at the cost of print quality."
msgstr "Permite ajustar el impulso del cabezal de impresión cuando la velocidad del eje X o Y cambia. Aumentar el impulso puede reducir el tiempo de impresión a costa de la calidad de impresión."

msgctxt "acceleration_enabled description"
msgid "Enables adjusting the print head acceleration. Increasing the accelerations can reduce printing time at the cost of print quality."
msgstr "Permite ajustar la aceleración del cabezal de impresión. Aumentar las aceleraciones puede reducir el tiempo de impresión a costa de la calidad de impresión."

msgctxt "cool_fan_enabled description"
msgid "Enables the print cooling fans while printing. The fans improve print quality on layers with short layer times and bridging / overhangs."
msgstr "Habilita ventiladores de refrigeración mientras se imprime. Los ventiladores mejoran la calidad de la impresión en capas con menores tiempos de capas y puentes o voladizos."

msgctxt "machine_end_gcode label"
msgid "End G-code"
msgstr "Finalizar GCode"

msgctxt "material_end_of_filament_purge_length label"
msgid "End of Filament Purge Length"
msgstr "Longitud de purga del extremo del filamento"

msgctxt "material_end_of_filament_purge_speed label"
msgid "End of Filament Purge Speed"
msgstr "Velocidad de purga del extremo del filamento"

msgctxt "brim_replaces_support description"
msgid "Enforce brim to be printed around the model even if that space would otherwise be occupied by support. This replaces some regions of the first layer of support by brim regions."
msgstr "Aplica la impresión de un borde alrededor del modelo, aunque en esa posición debiera estar el soporte. Sustituye algunas áreas de la primera capa de soporte por áreas de borde."

msgctxt "support_type option everywhere"
msgid "Everywhere"
msgstr "En todos sitios"

msgctxt "slicing_tolerance option exclusive"
msgid "Exclusive"
msgstr "Exclusiva"

msgctxt "experimental label"
msgid "Experimental"
msgstr "Experimental"

msgctxt "z_seam_corner option z_seam_corner_outer"
msgid "Expose Seam"
msgstr "Mostrar costura"

msgctxt "meshfix_extensive_stitching label"
msgid "Extensive Stitching"
msgstr "Cosido amplio"

msgctxt "meshfix_extensive_stitching description"
msgid "Extensive stitching tries to stitch up open holes in the mesh by closing the hole with touching polygons. This option can introduce a lot of processing time."
msgstr "Cosido amplio intenta coser los agujeros abiertos en la malla cerrando el agujero con polígonos que se tocan. Esta opción puede agregar una gran cantidad de tiempo de procesamiento."

msgctxt "infill_wall_line_count label"
msgid "Extra Infill Wall Count"
msgstr "Recuento de líneas de pared adicional"

msgctxt "skin_outline_count label"
msgid "Extra Skin Wall Count"
msgstr "Recuento de paredes adicionales de forro"

msgctxt "switch_extruder_extra_prime_amount description"
msgid "Extra material to prime after nozzle switching."
msgstr "Material adicional que debe cebarse tras el cambio de tobera."

msgctxt "extruder_prime_pos_x label"
msgid "Extruder Prime X Position"
msgstr "Posición de preparación del extrusor sobre el eje X"

msgctxt "extruder_prime_pos_y label"
msgid "Extruder Prime Y Position"
msgstr "Posición de preparación del extrusor sobre el eje Y"

msgctxt "extruder_prime_pos_z label"
msgid "Extruder Prime Z Position"
msgstr "Posición de preparación del extrusor sobre el eje Z"

msgctxt "machine_extruders_share_heater label"
msgid "Extruders Share Heater"
msgstr "Calentador compartido de extrusores"

msgctxt "machine_extruders_share_nozzle label"
msgid "Extruders Share Nozzle"
msgstr "Los extrusores comparten la tobera"

msgctxt "material_extrusion_cool_down_speed label"
msgid "Extrusion Cool Down Speed Modifier"
msgstr "Modificador de la velocidad de enfriamiento de la extrusión"

msgctxt "speed_equalize_flow_width_factor description"
msgid "Extrusion width based correction factor on the speed. At 0% the movement speed is kept constant at the Print Speed. At 100% the movement speed is adjusted so that the flow (in mm³/s) is kept constant, i.e. lines half the normal Line Width are printed twice as fast and lines twice as wide are printed half as fast. A value larger than 100% can help to compensate for the higher pressure required to extrude wide lines."
msgstr "Factor de corrección del ancho de extrusión basado en la velocidad. Al 0 % de velocidad de movimiento se mantiene constante a la velocidad de impresión. Al 100 % de velocidad de movimiento se ajusta para mantener el flujo constante (en mm³/s), es decir, las líneas cuyo ancho es la mitad del ancho normal se imprimen el doble de rápido y las líneas que tienen el doble de ancho se imprimen a la mitad de la velocidad. Un valor superior al 100 % puede ayudar a compensar la mayor presión necesaria para extruir líneas anchas."

msgctxt "cool_fan_speed label"
msgid "Fan Speed"
msgstr "Velocidad del ventilador"

msgctxt "support_fan_enable label"
msgid "Fan Speed Override"
msgstr "Alteración de velocidad del ventilador"

msgctxt "small_feature_max_length description"
msgid "Feature outlines that are shorter than this length will be printed using Small Feature Speed."
msgstr "Los contornos de las partes que sean más cortos que esta longitud se imprimen utilizando la función Velocidad de pequeñas partes."

msgctxt "experimental description"
msgid "Features that haven't completely been fleshed out yet."
msgstr "Características que aún no se han desarrollado por completo."

msgctxt "machine_feeder_wheel_diameter label"
msgid "Feeder Wheel Diameter"
msgstr "Diámetro de la rueda del alimentador"

msgctxt "material_final_print_temperature label"
msgid "Final Printing Temperature"
msgstr "Temperatura de impresión final"

msgctxt "machine_firmware_retract label"
msgid "Firmware Retraction"
msgstr "Retracción de firmware"

msgctxt "support_extruder_nr_layer_0 label"
msgid "First Layer Support Extruder"
msgstr "Extrusor del soporte de la primera capa"

msgctxt "material_flow label"
msgid "Flow"
msgstr "Flujo"

msgctxt "speed_equalize_flow_width_factor label"
msgid "Flow Equalization Ratio"
msgstr "Proporción de ecualización de flujo"

msgctxt "flow_rate_extrusion_offset_factor label"
msgid "Flow Rate Compensation Factor"
msgstr "Factor de compensación del caudal"

msgctxt "flow_rate_max_extrusion_offset label"
msgid "Flow Rate Compensation Max Extrusion Offset"
msgstr "Desplazamiento de extrusión máximo del factor de compensación del caudal"

msgctxt "material_flow_temp_graph label"
msgid "Flow Temperature Graph"
msgstr "Gráfico de flujo y temperatura"

msgctxt "material_flow_layer_0 description"
msgid "Flow compensation for the first layer: the amount of material extruded on the initial layer is multiplied by this value."
msgstr "Compensación de flujo de la primera capa: la cantidad de material extruido de la capa inicial se multiplica por este valor."

msgctxt "skin_material_flow_layer_0 description"
msgid "Flow compensation on bottom lines of the first layer"
msgstr "Compensación de flujo en las líneas inferiores de la primera capa"

msgctxt "infill_material_flow description"
msgid "Flow compensation on infill lines."
msgstr "Compensación de flujo en líneas de relleno."

msgctxt "support_interface_material_flow description"
msgid "Flow compensation on lines of support roof or floor."
msgstr "Compensación de flujo en líneas de techo o suelo de soporte."

msgctxt "roofing_material_flow description"
msgid "Flow compensation on lines of the areas at the top of the print."
msgstr "Compensación de flujo en líneas de las áreas superiores de la impresión."

msgctxt "prime_tower_flow description"
msgid "Flow compensation on prime tower lines."
msgstr "Compensación de flujo en líneas de la torre auxiliar."

msgctxt "skirt_brim_material_flow description"
msgid "Flow compensation on skirt or brim lines."
msgstr "Compensación de flujo en líneas de falda o borde."

msgctxt "support_bottom_material_flow description"
msgid "Flow compensation on support floor lines."
msgstr "Compensación de flujo en líneas de suelo de soporte."

msgctxt "support_roof_material_flow description"
msgid "Flow compensation on support roof lines."
msgstr "Compensación de flujo en líneas de techo de soporte."

msgctxt "support_material_flow description"
msgid "Flow compensation on support structure lines."
msgstr "Compensación de flujo en líneas de estructura de soporte."

msgctxt "wall_0_material_flow_layer_0 description"
msgid "Flow compensation on the outermost wall line of the first layer."
msgstr "Compensación de flujo en la línea de pared más externa de la primera capa."

msgctxt "wall_0_material_flow description"
msgid "Flow compensation on the outermost wall line."
msgstr "Compensación de flujo en la línea de pared más externa."

msgctxt "wall_0_material_flow_roofing description"
msgid "Flow compensation on the top surface outermost wall line."
msgstr "Compensación de flujo en la línea de la pared exterior más externa de la superficie superior."

msgctxt "wall_x_material_flow_roofing description"
msgid "Flow compensation on top surface wall lines for all wall lines except the outermost one."
msgstr "Compensación de flujo en las líneas de pared de la superficie superior para todas las líneas de pared excepto la más externa."

msgctxt "skin_material_flow description"
msgid "Flow compensation on top/bottom lines."
msgstr "Compensación de flujo en las líneas superiores o inferiores."

msgctxt "wall_x_material_flow_layer_0 description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one, but only for the first layer"
msgstr "Compensación de caudal en líneas de pared para todas, excepto la más exterior, pero solo para la primera capa."

msgctxt "wall_x_material_flow description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one."
msgstr "Compensación de flujo en líneas de pared para todas las líneas excepto la más externa."

msgctxt "wall_material_flow description"
msgid "Flow compensation on wall lines."
msgstr "Compensación de flujo en líneas de pared."

msgctxt "material_flow description"
msgid "Flow compensation: the amount of material extruded is multiplied by this value."
msgstr "Compensación de flujo: la cantidad de material extruido se multiplica por este valor."

msgctxt "meshfix_fluid_motion_angle label"
msgid "Fluid Motion Angle"
msgstr "Ángulo de movimiento fluido"

msgctxt "meshfix_fluid_motion_shift_distance label"
msgid "Fluid Motion Shift Distance"
msgstr "Cambio de distancia del movimiento fluido"

msgctxt "meshfix_fluid_motion_small_distance label"
msgid "Fluid Motion Small Distance"
msgstr "Pequeña distancia del movimiento fluido"

msgctxt "material_flush_purge_length label"
msgid "Flush Purge Length"
msgstr "Longitud de purga de descarga"

msgctxt "material_flush_purge_speed label"
msgid "Flush Purge Speed"
msgstr "Velocidad de purga de descarga"

msgctxt "min_wall_line_width description"
msgid "For thin structures around once or twice the nozzle size, the line widths need to be altered to adhere to the thickness of the model. This setting controls the minimum line width allowed for the walls. The minimum line widths inherently also determine the maximum line widths, since we transition from N to N+1 walls at some geometry thickness where the N walls are wide and the N+1 walls are narrow. The widest possible wall line is twice the Minimum Wall Line Width."
msgstr "Para estructuras delgadas, aproximadamente una o dos veces el tamaño de la boquilla, los anchos de línea deben cambiarse para que coincidan con el grosor del modelo. Esta configuración controla el ancho de línea mínimo permitido para las paredes. Los anchos de línea mínimos también determinan de forma inherente los anchos de línea máximos, ya que la transición de N a N + 1 paredes se realiza con un grosor geométrico donde N paredes son anchas y N + 1 paredes son estrechas. La línea perimetral más ancha posible es el doble del ancho mínimo de la línea perimetral."

msgctxt "z_seam_position option front"
msgid "Front"
msgstr "Delantera"

msgctxt "z_seam_position option frontleft"
msgid "Front Left"
msgstr "Delantera izquierda"

msgctxt "z_seam_position option frontright"
msgid "Front Right"
msgstr "Delantera derecha"

msgctxt "draft_shield_height_limitation option full"
msgid "Full"
msgstr "Completo"

msgctxt "magic_fuzzy_skin_enabled label"
msgid "Fuzzy Skin"
msgstr "Forro difuso"

msgctxt "magic_fuzzy_skin_point_density label"
msgid "Fuzzy Skin Density"
msgstr "Densidad del forro difuso"

msgctxt "magic_fuzzy_skin_outside_only label"
msgid "Fuzzy Skin Outside Only"
msgstr "Forro difuso exterior únicamente"

msgctxt "magic_fuzzy_skin_point_dist label"
msgid "Fuzzy Skin Point Distance"
msgstr "Distancia de punto del forro difuso"

msgctxt "magic_fuzzy_skin_thickness label"
msgid "Fuzzy Skin Thickness"
msgstr "Grosor del forro difuso"

msgctxt "machine_gcode_flavor label"
msgid "G-code Flavor"
msgstr "Tipo de GCode"

msgctxt "machine_end_gcode description"
msgid ""
"G-code commands to be executed at the very end - separated by \n"
"."
msgstr ""
"Los comandos de GCode que se ejecutarán justo al final separados por -\n"
"."

msgctxt "machine_start_gcode description"
msgid ""
"G-code commands to be executed at the very start - separated by \n"
"."
msgstr ""
"Los comandos de GCode que se ejecutarán justo al inicio separados por - \n"
"."

msgctxt "material_guid description"
msgid "GUID of the material. This is set automatically."
msgstr "GUID del material. Este valor se define de forma automática."

msgctxt "gantry_height label"
msgid "Gantry Height"
msgstr "Altura del puente"

msgctxt "interlocking_enable label"
msgid "Generate Interlocking Structure"
msgstr "Generar estructura entrelazada"

msgctxt "support_enable label"
msgid "Generate Support"
msgstr "Generar soporte"

msgctxt "support_brim_enable description"
msgid "Generate a brim within the support infill regions of the first layer. This brim is printed underneath the support, not around it. Enabling this setting increases the adhesion of support to the build plate."
msgstr "Genera un borde dentro de las zonas de relleno del soporte de la primera capa. Este borde se imprime por debajo del soporte y no a su alrededor. Si habilita esta configuración aumentará la adhesión del soporte a la placa de impresión."

msgctxt "support_interface_enable description"
msgid "Generate a dense interface between the model and the support. This will create a skin at the top of the support on which the model is printed and at the bottom of the support, where it rests on the model."
msgstr "Genera una gruesa interfaz entre el modelo y el soporte. De esta forma, se crea un forro en la parte superior del soporte, donde se imprime el modelo, y en la parte inferior del soporte, donde se apoya el modelo."

msgctxt "support_bottom_enable description"
msgid "Generate a dense slab of material between the bottom of the support and the model. This will create a skin between the model and support."
msgstr "Genere una placa densa de material entre la parte inferior del soporte y el modelo. Esto creará un forro entre el modelo y el soporte."

msgctxt "support_roof_enable description"
msgid "Generate a dense slab of material between the top of support and the model. This will create a skin between the model and support."
msgstr "Genere una placa densa de material entre la parte superior del soporte y el modelo. Esto creará un forro entre el modelo y el soporte."

msgctxt "support_enable description"
msgid "Generate structures to support parts of the model which have overhangs. Without these structures, such parts would collapse during printing."
msgstr "Generar estructuras para soportar piezas del modelo que tengan voladizos. Sin estas estructuras, estas piezas se romperían durante la impresión."

msgctxt "machine_buildplate_type option glass"
msgid "Glass"
msgstr "Vidrio"

msgctxt "ironing_enabled description"
msgid "Go over the top surface one additional time, but this time extruding very little material. This is meant to melt the plastic on top further, creating a smoother surface. The pressure in the nozzle chamber is kept high so that the creases in the surface are filled with material."
msgstr "Pasar por la superficie superior una vez más, pero esta vez extruyendo muy poco material, para derretir la capa superior del plástico y crear una superficie más lisa. La presión de la cámara en la boquilla se mantiene alta para que los pliegues de la superficie se llenen de material."

msgctxt "gradual_infill_step_height label"
msgid "Gradual Infill Step Height"
msgstr "Altura necesaria de los pasos de relleno"

msgctxt "gradual_infill_steps label"
msgid "Gradual Infill Steps"
msgstr "Pasos de relleno necesarios"

msgctxt "gradual_support_infill_step_height label"
msgid "Gradual Support Infill Step Height"
msgstr "Altura necesaria de los escalones de relleno de soporte"

msgctxt "gradual_support_infill_steps label"
msgid "Gradual Support Infill Steps"
msgstr "Escalones de relleno de soporte"

msgctxt "cool_min_temperature description"
msgid "Gradually reduce to this temperature when printing at reduced speeds because of minimum layer time."
msgstr "Reduzca gradualmente a esta temperatura cuando imprima a velocidades bajas debido al tiempo mínimo de capa."

msgctxt "infill_pattern option grid"
msgid "Grid"
msgstr "Rejilla"

msgctxt "support_bottom_pattern option grid"
msgid "Grid"
msgstr "Rejilla"

msgctxt "support_interface_pattern option grid"
msgid "Grid"
msgstr "Rejilla"

msgctxt "support_pattern option grid"
msgid "Grid"
msgstr "Rejilla"

msgctxt "support_roof_pattern option grid"
msgid "Grid"
msgstr "Rejilla"

msgctxt "machine_gcode_flavor option Griffin"
msgid "Griffin"
msgstr "Griffin"

msgctxt "group_outer_walls label"
msgid "Group Outer Walls"
msgstr "Agrupar las paredes exteriores"

msgctxt "infill_pattern option gyroid"
msgid "Gyroid"
msgstr "Giroide"

msgctxt "support_pattern option gyroid"
msgid "Gyroid"
msgstr "Giroide"

msgctxt "machine_heated_build_volume label"
msgid "Has Build Volume Temperature Stabilization"
msgstr "Tiene estabilización de temperatura del volumen de impresión"

msgctxt "machine_heated_bed label"
msgid "Has Heated Build Plate"
msgstr "Tiene una placa de impresión caliente"

msgctxt "machine_nozzle_heat_up_speed label"
msgid "Heat Up Speed"
msgstr "Velocidad de calentamiento"

msgctxt "machine_heat_zone_length label"
msgid "Heat Zone Length"
msgstr "Longitud de la zona térmica"

msgctxt "draft_shield_height description"
msgid "Height limitation of the draft shield. Above this height no draft shield will be printed."
msgstr "Limitación de la altura del parabrisas. Por encima de esta altura, no se imprimirá ningún parabrisas."

msgctxt "z_seam_corner option z_seam_corner_inner"
msgid "Hide Seam"
msgstr "Ocultar costura"

msgctxt "z_seam_corner option z_seam_corner_any"
msgid "Hide or Expose Seam"
msgstr "Ocultar o mostrar costura"

msgctxt "hole_xy_offset label"
msgid "Hole Horizontal Expansion"
msgstr "Expansión horizontal de orificios"

msgctxt "hole_xy_offset_max_diameter label"
msgid "Hole Horizontal Expansion Max Diameter"
msgstr "Diámetro máximo de la expansión horizontal de los orificios"

msgctxt "small_hole_max_size description"
msgid "Holes and part outlines with a diameter smaller than this will be printed using Small Feature Speed."
msgstr "Los agujeros y contornos de las piezas con un diámetro menor que estos se imprimen utilizando la función Velocidad de pequeñas partes."

msgctxt "xy_offset label"
msgid "Horizontal Expansion"
msgstr "Expansión horizontal"

msgctxt "material_shrinkage_percentage_xy label"
msgid "Horizontal Scaling Factor Shrinkage Compensation"
msgstr "Factor de escala horizontal para la compensación de la contracción"

msgctxt "material_break_preparation_retracted_position description"
msgid "How far the filament can be stretched before it breaks, while heated."
msgstr "Hasta dónde puede estirarse el filamento antes de que se rompa mientras se calienta."

msgctxt "material_anti_ooze_retracted_position description"
msgid "How far the material needs to be retracted before it stops oozing."
msgstr "Hasta dónde tiene que retraerse el material antes de detener el rezumado."

msgctxt "flow_rate_extrusion_offset_factor description"
msgid "How far to move the filament in order to compensate for changes in flow rate, as a percentage of how far the filament would move in one second of extrusion."
msgstr "La distancia para mover el filamento con el fin de compensar los cambios en el caudal, como porcentaje de la distancia a la que se movería el filamento en un segundo de extrusión."

msgctxt "material_break_retracted_position description"
msgid "How far to retract the filament in order to break it cleanly."
msgstr "Hasta dónde debe retraerse el filamento para que se rompa limpiamente."

msgctxt "material_break_preparation_speed description"
msgid "How fast the filament needs to be retracted just before breaking it off in a retraction."
msgstr "Con qué velocidad debe retraerse el filamento justo antes de romperse en una retracción."

msgctxt "material_anti_ooze_retraction_speed description"
msgid "How fast the material needs to be retracted during a filament switch to prevent oozing."
msgstr "Con qué velocidad tiene que retraerse el material durante un cambio de filamento para evitar el rezumado."

msgctxt "material_end_of_filament_purge_speed description"
msgid "How fast to prime the material after replacing an empty spool with a fresh spool of the same material."
msgstr "La velocidad de cebado del material después de sustituir una bobina vacía por una bobina nueva del mismo material."

msgctxt "material_flush_purge_speed description"
msgid "How fast to prime the material after switching to a different material."
msgstr "La velocidad de cebado del material después de cambiar a otro material."

msgctxt "material_maximum_park_duration description"
msgid "How long the material can be kept out of dry storage safely."
msgstr "La cantidad de tiempo que el material puede mantenerse seco de forma segura."

msgctxt "machine_steps_per_mm_x description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the X direction."
msgstr "Número de pasos que tiene que dar el motor para abarcar un milímetro de movimiento en la dirección X."

msgctxt "machine_steps_per_mm_y description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Y direction."
msgstr "Número de pasos que tiene que dar el motor para abarcar un milímetro de movimiento en la dirección Y."

msgctxt "machine_steps_per_mm_z description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Z direction."
msgstr "Número de pasos que tiene que dar el motor para abarcar un milímetro de movimiento en la dirección Z."

msgctxt "machine_steps_per_mm_e description"
msgid "How many steps of the stepper motors will result in moving the feeder wheel by one millimeter around its circumference."
msgstr "El número de pasos en un motor paso a paso que mueve la rueda de alimentación en incrementos de 1 milímetro alrededor de su circunferencia."

msgctxt "material_end_of_filament_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when replacing an empty spool with a fresh spool of the same material."
msgstr "La cantidad de material que se va a utilizará para purgar el material que había antes en la tobera (en longitud del filamento) al sustituir una bobina vacía por una bobina nueva del mismo material."

msgctxt "material_flush_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when switching to a different material."
msgstr "La cantidad de material que se va a utilizar para purgar el material que había antes en la tobera (en longitud del filamento) cuando se cambia a otro material."

msgctxt "machine_extruders_shared_nozzle_initial_retraction description"
msgid "How much the filament of each extruder is assumed to have been retracted from the shared nozzle tip at the completion of the printer-start gcode script; the value should be equal to or greater than the length of the common part of the nozzle's ducts."
msgstr "La cantidad de filamento de cada extrusor que se supone que se ha retirado de la punta de la tobera compartida al final de la secuencia de comandos gcode de inicio de la impresora; el valor debe ser igual o mayor que la longitud de la parte común de los conductos de la tobera."

msgctxt "support_interface_priority description"
msgid "How support interface and support will interact when they overlap. Currently only implemented for support roof."
msgstr "Cómo interactuarán la interfaz y el soporte en caso de superposición. Actualmente, solo implantado para techos de soporte."

msgctxt "support_tree_min_height_to_model description"
msgid "How tall a branch has to be if it is placed on the model. Prevents small blobs of support. This setting is ignored when a branch is supporting a support roof."
msgstr "Qué altura debe tener una rama si se coloca sobre el modelo. Evita la formación de pequeñas gotas de soporte. Esta configuración se ignora cuando una rama está aguantando un techo de soporte."

msgctxt "bridge_skin_support_threshold description"
msgid "If a skin region is supported for less than this percentage of its area, print it using the bridge settings. Otherwise it is printed using the normal skin settings."
msgstr "Si un área de forro es compatible con un porcentaje inferior de su área, se imprime utilizando los ajustes de puente. De lo contrario, se imprimirá utilizando los ajustes de forro habituales."

msgctxt "meshfix_fluid_motion_angle description"
msgid "If a toolpath-segment deviates more than this angle from the general motion it is smoothed."
msgstr "Si un segmento de la trayectoria de la herramienta se desvía más de este ángulo del movimiento general, se suaviza."

msgctxt "bridge_enable_more_layers description"
msgid "If enabled, the second and third layers above the air are printed using the following settings. Otherwise, those layers are printed using the normal settings."
msgstr "Si esta opción está habilitada, la segunda y tercera capa por encima del aire se imprimen utilizando los siguientes ajustes. De lo contrario, estas capas se imprimen utilizando los ajustes habituales."

msgctxt "wall_transition_filter_distance description"
msgid "If it would be transitioning back and forth between different numbers of walls in quick succession, don't transition at all. Remove transitions if they are closer together than this distance."
msgstr "Si planea pasar de un lado a otro entre diferentes números de pared en rápida sucesión, no realice ninguna transición. Elimine las transiciones si están más cerca que esta distancia."

msgctxt "raft_margin description"
msgid "If the raft is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Si la balsa está habilitada, esta es el área adicional de la balsa alrededor del modelo que también tiene una balsa. El aumento de este margen creará una balsa más resistente mientras que usará más material y dejará menos área para la impresión."

msgctxt "meshfix_union_all description"
msgid "Ignore the internal geometry arising from overlapping volumes within a mesh and print the volumes as one. This may cause unintended internal cavities to disappear."
msgstr "Ignora la geometría interna que surge de los volúmenes de superposición dentro de una malla e imprime los volúmenes como si fuera uno. Esto puede hacer que desaparezcan cavidades internas que no se hayan previsto."

msgctxt "material_bed_temp_prepend label"
msgid "Include Build Plate Temperature"
msgstr "Incluir temperatura de la placa de impresión"

msgctxt "material_print_temp_prepend label"
msgid "Include Material Temperatures"
msgstr "Incluir temperaturas del material"

msgctxt "slicing_tolerance option inclusive"
msgid "Inclusive"
msgstr "Inclusiva"

msgctxt "infill description"
msgid "Infill"
msgstr "Relleno"

msgctxt "infill label"
msgid "Infill"
msgstr "Relleno"

msgctxt "acceleration_infill label"
msgid "Infill Acceleration"
msgstr "Aceleración del relleno"

msgctxt "infill_before_walls label"
msgid "Infill Before Walls"
msgstr "Relleno antes que las paredes"

msgctxt "infill_sparse_density label"
msgid "Infill Density"
msgstr "Densidad de relleno"

msgctxt "infill_extruder_nr label"
msgid "Infill Extruder"
msgstr "Extrusor del relleno"

msgctxt "infill_material_flow label"
msgid "Infill Flow"
msgstr "Flujo de relleno"

msgctxt "jerk_infill label"
msgid "Infill Jerk"
msgstr "Impulso de relleno"

msgctxt "infill_sparse_thickness label"
msgid "Infill Layer Thickness"
msgstr "Grosor de la capa de relleno"

msgctxt "infill_angles label"
msgid "Infill Line Directions"
msgstr "Direcciones de línea de relleno"

msgctxt "infill_line_distance label"
msgid "Infill Line Distance"
msgstr "Distancia de línea de relleno"

msgctxt "infill_multiplier label"
msgid "Infill Line Multiplier"
msgstr "Multiplicador de línea de relleno"

msgctxt "infill_line_width label"
msgid "Infill Line Width"
msgstr "Ancho de línea de relleno"

msgctxt "infill_mesh label"
msgid "Infill Mesh"
msgstr "Malla de relleno"

msgctxt "infill_support_angle label"
msgid "Infill Overhang Angle"
msgstr "Ángulo de voladizo de relleno"

msgctxt "infill_overlap_mm label"
msgid "Infill Overlap"
msgstr "Superposición del relleno"

msgctxt "infill_overlap label"
msgid "Infill Overlap Percentage"
msgstr "Porcentaje de superposición del relleno"

msgctxt "infill_pattern label"
msgid "Infill Pattern"
msgstr "Patrón de relleno"

msgctxt "speed_infill label"
msgid "Infill Speed"
msgstr "Velocidad de relleno"

msgctxt "infill_support_enabled label"
msgid "Infill Support"
msgstr "Soporte de relleno"

msgctxt "infill_enable_travel_optimization label"
msgid "Infill Travel Optimization"
msgstr "Optimización del desplazamiento del relleno"

msgctxt "infill_wipe_dist label"
msgid "Infill Wipe Distance"
msgstr "Distancia de pasada de relleno"

msgctxt "infill_offset_x label"
msgid "Infill X Offset"
msgstr "Desplazamiento del relleno sobre el eje X"

msgctxt "infill_offset_y label"
msgid "Infill Y Offset"
msgstr "Desplazamiento del relleno sobre el eje Y"

msgctxt "initial_bottom_layers label"
msgid "Initial Bottom Layers"
msgstr "Capas inferiores iniciales"

msgctxt "cool_fan_speed_0 label"
msgid "Initial Fan Speed"
msgstr "Velocidad inicial del ventilador"

msgctxt "acceleration_layer_0 label"
msgid "Initial Layer Acceleration"
msgstr "Aceleración de la capa inicial"

msgctxt "skin_material_flow_layer_0 label"
msgid "Initial Layer Bottom Flow"
msgstr "Flujo inferior de la capa inicial"

msgctxt "support_tree_bp_diameter label"
msgid "Initial Layer Diameter"
msgstr "Diámetro de la capa inicial"

msgctxt "material_flow_layer_0 label"
msgid "Initial Layer Flow"
msgstr "Flujo de capa inicial"

msgctxt "layer_height_0 label"
msgid "Initial Layer Height"
msgstr "Altura de capa inicial"

msgctxt "xy_offset_layer_0 label"
msgid "Initial Layer Horizontal Expansion"
msgstr "Expansión horizontal de la capa inicial"

msgctxt "wall_x_material_flow_layer_0 label"
msgid "Initial Layer Inner Wall Flow"
msgstr "Flujo de pared interior de la capa inicial"

msgctxt "jerk_layer_0 label"
msgid "Initial Layer Jerk"
msgstr "Impulso de capa inicial"

msgctxt "initial_layer_line_width_factor label"
msgid "Initial Layer Line Width"
msgstr "Ancho de línea de la capa inicial"

msgctxt "wall_0_material_flow_layer_0 label"
msgid "Initial Layer Outer Wall Flow"
msgstr "Flujo de pared exterior de la capa inicial"

msgctxt "acceleration_print_layer_0 label"
msgid "Initial Layer Print Acceleration"
msgstr "Aceleración de impresión de la capa inicial"

msgctxt "jerk_print_layer_0 label"
msgid "Initial Layer Print Jerk"
msgstr "Impulso de impresión de capa inicial"

msgctxt "speed_print_layer_0 label"
msgid "Initial Layer Print Speed"
msgstr "Velocidad de impresión de la capa inicial"

msgctxt "speed_layer_0 label"
msgid "Initial Layer Speed"
msgstr "Velocidad de capa inicial"

msgctxt "support_initial_layer_line_distance label"
msgid "Initial Layer Support Line Distance"
msgstr "Distancia de línea del soporte de la capa inicial"

msgctxt "acceleration_travel_layer_0 label"
msgid "Initial Layer Travel Acceleration"
msgstr "Aceleración de desplazamiento de la capa inicial"

msgctxt "jerk_travel_layer_0 label"
msgid "Initial Layer Travel Jerk"
msgstr "Impulso de desplazamiento de capa inicial"

msgctxt "speed_travel_layer_0 label"
msgid "Initial Layer Travel Speed"
msgstr "Velocidad de desplazamiento de la capa inicial"

msgctxt "layer_0_z_overlap label"
msgid "Initial Layer Z Overlap"
msgstr "Superposición de las capas iniciales en Z"

msgctxt "material_initial_print_temperature label"
msgid "Initial Printing Temperature"
msgstr "Temperatura de impresión inicial"

msgctxt "acceleration_wall_x label"
msgid "Inner Wall Acceleration"
msgstr "Aceleración de pared interior"

msgctxt "wall_x_extruder_nr label"
msgid "Inner Wall Extruder"
msgstr "Extrusor de pared interior"

msgctxt "jerk_wall_x label"
msgid "Inner Wall Jerk"
msgstr "Impulso de pared interior"

msgctxt "speed_wall_x label"
msgid "Inner Wall Speed"
msgstr "Velocidad de pared interior"

msgctxt "wall_x_material_flow label"
msgid "Inner Wall(s) Flow"
msgstr "Flujo de pared o paredes interiores"

msgctxt "wall_line_width_x label"
msgid "Inner Wall(s) Line Width"
msgstr "Ancho de línea de pared(es) interna(s)"

msgctxt "wall_0_inset description"
msgid "Inset applied to the path of the outer wall. If the outer wall is smaller than the nozzle, and printed after the inner walls, use this offset to get the hole in the nozzle to overlap with the inner walls instead of the outside of the model."
msgstr "Entrante aplicado a la trayectoria de la pared exterior. Si la pared exterior es más pequeña que la tobera y se imprime a continuación de las paredes interiores, utilice este valor de desplazamiento para hacer que el agujero de la tobera se superponga a las paredes interiores del modelo en lugar de a las exteriores."

msgctxt "inset_direction option inside_out"
msgid "Inside To Outside"
msgstr "Del interior al exterior"

msgctxt "support_interface_priority option interface_lines_overwrite_support_area"
msgid "Interface lines preferred"
msgstr "Líneas de interfaz preferidas"

msgctxt "support_interface_priority option interface_area_overwrite_support_area"
msgid "Interface preferred"
msgstr "Interfaz preferida"

msgctxt "interlocking_beam_layer_count label"
msgid "Interlocking Beam Layer Count"
msgstr "Recuento de capas de haz entrelazado"

msgctxt "interlocking_beam_width label"
msgid "Interlocking Beam Width"
msgstr "Ancho del haz entrelazado"

msgctxt "interlocking_boundary_avoidance label"
msgid "Interlocking Boundary Avoidance"
msgstr "Distancia a los límites de entrelazado"

msgctxt "interlocking_depth label"
msgid "Interlocking Depth"
msgstr "Profundidad del entrelazado"

msgctxt "interlocking_orientation label"
msgid "Interlocking Structure Orientation"
msgstr "Orientación de estructura entrelazada"

msgctxt "ironing_only_highest_layer label"
msgid "Iron Only Highest Layer"
msgstr "Planchar solo la capa superior"

msgctxt "acceleration_ironing label"
msgid "Ironing Acceleration"
msgstr "Aceleración del alisado"

msgctxt "ironing_flow label"
msgid "Ironing Flow"
msgstr "Flujo de alisado"

msgctxt "ironing_inset label"
msgid "Ironing Inset"
msgstr "Inserción de alisado"

msgctxt "jerk_ironing label"
msgid "Ironing Jerk"
msgstr "Impulso de alisado"

msgctxt "ironing_line_spacing label"
msgid "Ironing Line Spacing"
msgstr "Espaciado de líneas del alisado"

msgctxt "ironing_pattern label"
msgid "Ironing Pattern"
msgstr "Patrón de alisado"

msgctxt "speed_ironing label"
msgid "Ironing Speed"
msgstr "Velocidad de alisado"

msgctxt "machine_center_is_zero label"
msgid "Is Center Origin"
msgstr "El origen está centrado"

msgctxt "material_is_support_material label"
msgid "Is support material"
msgstr "Es material de soporte"

msgctxt "material_crystallinity description"
msgid "Is this material the type that breaks off cleanly when heated (crystalline), or is it the type that produces long intertwined polymer chains (non-crystalline)?"
msgstr "¿Es este el tipo de material que se desprende limpiamente cuando se calienta (cristalino) o el que produce largas cadenas de polímeros entrelazadas (no cristalino)?"

msgctxt "material_is_support_material description"
msgid "Is this material typically used as a support material during printing."
msgstr "Este material se utiliza normalmente como material de soporte durante la impresión."

msgctxt "magic_fuzzy_skin_outside_only description"
msgid "Jitter only the parts' outlines and not the parts' holes."
msgstr "Use solo los contornos de las piezas, no los orificios de las piezas."

msgctxt "meshfix_keep_open_polygons label"
msgid "Keep Disconnected Faces"
msgstr "Mantener caras desconectadas"

msgctxt "layer_height label"
msgid "Layer Height"
msgstr "Altura de capa"

msgctxt "layer_start_x label"
msgid "Layer Start X"
msgstr "X de inicio de capa"

msgctxt "layer_start_y label"
msgid "Layer Start Y"
msgstr "Y de inicio de capa"

msgctxt "raft_base_thickness description"
msgid "Layer thickness of the base raft layer. This should be a thick layer which sticks firmly to the printer build plate."
msgstr "Grosor de la capa base de la balsa. Esta debe ser una capa gruesa que se adhiera firmemente a la placa de impresión de la impresora."

msgctxt "raft_interface_thickness description"
msgid "Layer thickness of the middle raft layer."
msgstr "Grosor de la capa intermedia de la balsa."

msgctxt "raft_surface_thickness description"
msgid "Layer thickness of the top raft layers."
msgstr "Grosor de capa de las capas superiores de la balsa."

msgctxt "support_skip_zag_per_mm description"
msgid "Leave out a connection between support lines once every N millimeter to make the support structure easier to break away."
msgstr "Omitir una conexión entre líneas de soporte una vez cada N milímetros a fin de lograr que la estructura de soporte resulte más fácil de descomponer."

msgctxt "z_seam_position option left"
msgid "Left"
msgstr "Izquierda"

msgctxt "cool_lift_head label"
msgid "Lift Head"
msgstr "Levantar el cabezal"

msgctxt "infill_pattern option lightning"
msgid "Lightning"
msgstr "Rayos"

msgctxt "lightning_infill_overhang_angle label"
msgid "Lightning Infill Overhang Angle"
msgstr "Ángulo del voladizo de relleno de rayos"

msgctxt "lightning_infill_prune_angle label"
msgid "Lightning Infill Prune Angle"
msgstr "Ángulo de recorte de relleno de rayos"

msgctxt "lightning_infill_straightening_angle label"
msgid "Lightning Infill Straightening Angle"
msgstr "Ángulo de enderezamiento de rayos"

msgctxt "lightning_infill_support_angle label"
msgid "Lightning Infill Support Angle"
msgstr "Ángulo de sujeción de relleno de rayos"

msgctxt "support_tree_limit_branch_reach label"
msgid "Limit Branch Reach"
msgstr "Alcance límite de la rama"

msgctxt "support_tree_limit_branch_reach description"
msgid "Limit how far each branch should travel from the point it supports. This can make the support more sturdy, but will increase the amount of branches (and because of that material usage/print time)"
msgstr "Limite la distancia que debe recorrer cada rama desde el punto que soporta. Esto puede hacer que el soporte sea más resistente, pero aumentará la cantidad de ramas (y, con ello, el uso de material y el tiempo de impresión)"

msgctxt "cutting_mesh description"
msgid "Limit the volume of this mesh to within other meshes. You can use this to make certain areas of one mesh print with different settings and with a whole different extruder."
msgstr "Limite el volumen de esta malla a lo que está dentro de otras mallas. Puede usar esto para hacer que determinadas áreas de una malla se impriman con ajustes diferentes y con un extrusor totalmente diferente."

msgctxt "draft_shield_height_limitation option limited"
msgid "Limited"
msgstr "Limitado"

msgctxt "line_width label"
msgid "Line Width"
msgstr "Ancho de línea"

msgctxt "infill_pattern option lines"
msgid "Lines"
msgstr "Líneas"

msgctxt "roofing_pattern option lines"
msgid "Lines"
msgstr "Líneas"

msgctxt "support_bottom_pattern option lines"
msgid "Lines"
msgstr "Líneas"

msgctxt "support_interface_pattern option lines"
msgid "Lines"
msgstr "Líneas"

msgctxt "support_pattern option lines"
msgid "Lines"
msgstr "Líneas"

msgctxt "support_roof_pattern option lines"
msgid "Lines"
msgstr "Líneas"

msgctxt "top_bottom_pattern option lines"
msgid "Lines"
msgstr "Líneas"

msgctxt "top_bottom_pattern_0 option lines"
msgid "Lines"
msgstr "Líneas"

msgctxt "machine_gcode_flavor option MACH3"
msgid "Mach3"
msgstr "Mach3"

msgctxt "machine_settings label"
msgid "Machine"
msgstr "Máquina"

msgctxt "machine_depth label"
msgid "Machine Depth"
msgstr "Profundidad de la máquina"

msgctxt "machine_head_with_fans_polygon label"
msgid "Machine Head & Fan Polygon"
msgstr "Polígono del cabezal de la máquina y del ventilador"

msgctxt "machine_height label"
msgid "Machine Height"
msgstr "Altura de la máquina"

msgctxt "machine_name label"
msgid "Machine Type"
msgstr "Tipo de máquina"

msgctxt "machine_width label"
msgid "Machine Width"
msgstr "Ancho de la máquina"

msgctxt "machine_settings description"
msgid "Machine specific settings"
msgstr "Ajustes específicos de la máquina"

msgctxt "conical_overhang_enabled label"
msgid "Make Overhang Printable"
msgstr "Convertir voladizo en imprimible"

msgctxt "multiple_mesh_overlap description"
msgid "Make meshes which are touching each other overlap a bit. This makes them bond together better."
msgstr "Hace que las mallas que se tocan las unas a las otras se superpongan ligeramente. Esto mejora la conexión entre ellas."

msgctxt "support_conical_enabled description"
msgid "Make support areas smaller at the bottom than at the overhang."
msgstr "Hace que las áreas de soporte sean más pequeñas en la parte inferior que en el voladizo."

msgctxt "support_mesh_drop_down description"
msgid "Make support everywhere below the support mesh, so that there's no overhang in the support mesh."
msgstr "Disponga un soporte en todas partes por debajo de la malla de soporte, para que no haya voladizo en la malla de soporte."

msgctxt "extruder_prime_pos_abs description"
msgid "Make the extruder prime position absolute rather than relative to the last-known location of the head."
msgstr "La posición de preparación del extrusor se considera absoluta, en lugar de relativa a la última ubicación conocida del cabezal."

msgctxt "layer_0_z_overlap description"
msgid "Make the first and second layer of the model overlap in the Z direction to compensate for the filament lost in the airgap. All models above the first model layer will be shifted down by this amount."
msgstr "La superposición entre la primera y segunda capa del modelo para compensar la pérdida de material en el hueco de aire. Todas las capas por encima de la primera capa se desplazan hacia abajo por esta cantidad."

msgctxt "meshfix description"
msgid "Make the meshes more suited for 3D printing."
msgstr "Consiga las mallas más adecuadas para la impresión 3D."

msgctxt "machine_gcode_flavor option Makerbot"
msgid "Makerbot"
msgstr "Makerbot"

msgctxt "machine_gcode_flavor option RepRap (Marlin/Sprinter)"
msgid "Marlin"
msgstr "Marlin"

msgctxt "machine_gcode_flavor option RepRap (Volumetric)"
msgid "Marlin (Volumetric)"
msgstr "Marlin (Volumetric)"

msgctxt "material description"
msgid "Material"
msgstr "Material"

msgctxt "material label"
msgid "Material"
msgstr "Material"

msgctxt "material_guid label"
msgid "Material GUID"
msgstr "GUID del material"

msgctxt "max_extrusion_before_wipe label"
msgid "Material Volume Between Wipes"
msgstr "Volumen de material entre limpiezas"

msgctxt "retraction_combing_max_distance label"
msgid "Max Comb Distance With No Retract"
msgstr "Distancia de peinada máxima sin retracción"

msgctxt "machine_max_acceleration_x label"
msgid "Maximum Acceleration X"
msgstr "Aceleración máxima sobre el eje X"

msgctxt "machine_max_acceleration_y label"
msgid "Maximum Acceleration Y"
msgstr "Aceleración máxima de Y"

msgctxt "machine_max_acceleration_z label"
msgid "Maximum Acceleration Z"
msgstr "Aceleración máxima de Z"

msgctxt "support_tree_angle label"
msgid "Maximum Branch Angle"
msgstr "Ángulo máximo de la rama"

msgctxt "meshfix_maximum_deviation label"
msgid "Maximum Deviation"
msgstr "Desviación máxima"

msgctxt "meshfix_maximum_extrusion_area_deviation label"
msgid "Maximum Extrusion Area Deviation"
msgstr "Desviación máxima del área de extrusión"

msgctxt "cool_fan_speed_max label"
msgid "Maximum Fan Speed"
msgstr "Velocidad máxima del ventilador"

msgctxt "machine_max_acceleration_e label"
msgid "Maximum Filament Acceleration"
msgstr "Aceleración máxima del filamento"

msgctxt "conical_overhang_angle label"
msgid "Maximum Model Angle"
msgstr "Ángulo máximo del modelo"

msgctxt "conical_overhang_hole_size label"
msgid "Maximum Overhang Hole Area"
msgstr "Área máxima del agujero en voladizo"

msgctxt "material_maximum_park_duration label"
msgid "Maximum Park Duration"
msgstr "Duración máxima de estacionamiento"

msgctxt "meshfix_maximum_resolution label"
msgid "Maximum Resolution"
msgstr "Resolución máxima"

msgctxt "retraction_count_max label"
msgid "Maximum Retraction Count"
msgstr "Recuento máximo de retracciones"

msgctxt "max_skin_angle_for_expansion label"
msgid "Maximum Skin Angle for Expansion"
msgstr "Ángulo máximo de expansión del forro"

msgctxt "machine_max_feedrate_e label"
msgid "Maximum Speed E"
msgstr "Velocidad máxima E"

msgctxt "machine_max_feedrate_x label"
msgid "Maximum Speed X"
msgstr "Velocidad máxima sobre el eje X"

msgctxt "machine_max_feedrate_y label"
msgid "Maximum Speed Y"
msgstr "Velocidad máxima sobre el eje Y"

msgctxt "machine_max_feedrate_z label"
msgid "Maximum Speed Z"
msgstr "Velocidad máxima sobre el eje Z"

msgctxt "support_tower_maximum_supported_diameter label"
msgid "Maximum Tower-Supported Diameter"
msgstr "Diámetro máximo soportado por la torre"

msgctxt "meshfix_maximum_travel_resolution label"
msgid "Maximum Travel Resolution"
msgstr "Resolución de desplazamiento máximo"

msgctxt "machine_max_acceleration_x description"
msgid "Maximum acceleration for the motor of the X-direction"
msgstr "Aceleración máxima del motor de la dirección X"

msgctxt "machine_max_acceleration_y description"
msgid "Maximum acceleration for the motor of the Y-direction."
msgstr "Aceleración máxima del motor de la dirección Y."

msgctxt "machine_max_acceleration_z description"
msgid "Maximum acceleration for the motor of the Z-direction."
msgstr "Aceleración máxima del motor de la dirección Z."

msgctxt "machine_max_acceleration_e description"
msgid "Maximum acceleration for the motor of the filament."
msgstr "Aceleración máxima del motor del filamento."

msgctxt "bridge_sparse_infill_max_density description"
msgid "Maximum density of infill considered to be sparse. Skin over sparse infill is considered to be unsupported and so may be treated as a bridge skin."
msgstr "La máxima densidad de relleno que se considera escasa. El forro sobre el relleno escaso se considera sin soporte y, por lo tanto, se puede tratar como un forro de puente."

msgctxt "support_tower_maximum_supported_diameter description"
msgid "Maximum diameter in the X/Y directions of a small area which is to be supported by a specialized support tower."
msgstr "Diámetro máximo en las direcciones X/Y de una pequeña área que debe ser soportada por una torre de soporte especializada."

msgctxt "max_extrusion_before_wipe description"
msgid "Maximum material that can be extruded before another nozzle wipe is initiated. If this value is less than the volume of material required in a layer, the setting has no effect in this layer, i.e. it is limited to one wipe per layer."
msgstr "Material máximo que puede extruirse antes de que se inicie otra limpieza de la tobera. Si este valor es inferior al volumen de material necesario en una capa, el ajuste no tiene efecto en esa capa, es decir, se limita a una limpieza por capa."

msgctxt "multiple_mesh_overlap label"
msgid "Merged Meshes Overlap"
msgstr "Superponer mallas combinadas"

msgctxt "meshfix label"
msgid "Mesh Fixes"
msgstr "Correcciones de malla"

msgctxt "mesh_position_x label"
msgid "Mesh Position X"
msgstr "Posición X en la malla"

msgctxt "mesh_position_y label"
msgid "Mesh Position Y"
msgstr "Posición Y en la malla"

msgctxt "mesh_position_z label"
msgid "Mesh Position Z"
msgstr "Posición Z en la malla"

msgctxt "infill_mesh_order label"
msgid "Mesh Processing Rank"
msgstr "Rango de procesamiento de la malla"

msgctxt "mesh_rotation_matrix label"
msgid "Mesh Rotation Matrix"
msgstr "Matriz de rotación de la malla"

msgctxt "slicing_tolerance option middle"
msgid "Middle"
msgstr "Media"

msgctxt "mold_width label"
msgid "Minimal Mold Width"
msgstr "Ancho de molde mínimo"

msgctxt "machine_min_cool_heat_time_window label"
msgid "Minimal Time Standby Temperature"
msgstr "Temperatura mínima en modo de espera"

msgctxt "bridge_wall_min_length label"
msgid "Minimum Bridge Wall Length"
msgstr "Longitud mínima de la pared del puente"

msgctxt "min_even_wall_line_width label"
msgid "Minimum Even Wall Line Width"
msgstr "Ancho mínimo de la línea perimetral uniforme"

msgctxt "retraction_extrusion_window label"
msgid "Minimum Extrusion Distance Window"
msgstr "Ventana de distancia mínima de extrusión"

msgctxt "min_feature_size label"
msgid "Minimum Feature Size"
msgstr "Tamaño mínimo de la característica"

msgctxt "machine_minimum_feedrate label"
msgid "Minimum Feedrate"
msgstr "Velocidad de alimentación mínima"

msgctxt "support_tree_min_height_to_model label"
msgid "Minimum Height To Model"
msgstr "Altura mínima para el modelo"

msgctxt "min_infill_area label"
msgid "Minimum Infill Area"
msgstr "Área de relleno mínima"

msgctxt "cool_min_layer_time label"
msgid "Minimum Layer Time"
msgstr "Tiempo mínimo de capa"

msgctxt "min_odd_wall_line_width label"
msgid "Minimum Odd Wall Line Width"
msgstr "Ancho mínimo de la línea perimetral impar"

msgctxt "minimum_polygon_circumference label"
msgid "Minimum Polygon Circumference"
msgstr "Circunferencia mínima de polígono"

msgctxt "min_skin_width_for_expansion label"
msgid "Minimum Skin Width for Expansion"
msgstr "Anchura de expansión mínima del forro"

msgctxt "cool_min_speed label"
msgid "Minimum Speed"
msgstr "Velocidad mínima"

msgctxt "minimum_support_area label"
msgid "Minimum Support Area"
msgstr "Área del soporte mínima"

msgctxt "minimum_bottom_area label"
msgid "Minimum Support Floor Area"
msgstr "Área de los suelos del soporte mínima"

msgctxt "minimum_interface_area label"
msgid "Minimum Support Interface Area"
msgstr "Área de la interfaz de soporte mínima"

msgctxt "minimum_roof_area label"
msgid "Minimum Support Roof Area"
msgstr "Área de los techos del soporte mínima"

msgctxt "support_xy_distance_overhang label"
msgid "Minimum Support X/Y Distance"
msgstr "Distancia X/Y mínima del soporte"

msgctxt "min_bead_width label"
msgid "Minimum Thin Wall Line Width"
msgstr "Ancho mínimo de la línea perimetral delgada"

msgctxt "coasting_min_volume label"
msgid "Minimum Volume Before Coasting"
msgstr "Volumen mínimo antes del depósito por inercia"

msgctxt "min_wall_line_width label"
msgid "Minimum Wall Line Width"
msgstr "Ancho mínimo de la línea perimetral"

msgctxt "minimum_interface_area description"
msgid "Minimum area size for support interface polygons. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Tamaño del área mínima para los polígonos de la interfaz de soporte. Los polígonos que posean un área de menor tamaño que este valor se imprimirán como soporte normal."

msgctxt "minimum_support_area description"
msgid "Minimum area size for support polygons. Polygons which have an area smaller than this value will not be generated."
msgstr "Tamaño del área mínima para los polígonos del soporte. No se generarán polígonos que posean un área de menor tamaño que este valor."

msgctxt "minimum_bottom_area description"
msgid "Minimum area size for the floors of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Tamaño del área mínima para los suelos del soporte. Los polígonos que posean un área de menor tamaño que este valor se imprimirán como soporte normal."

msgctxt "minimum_roof_area description"
msgid "Minimum area size for the roofs of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Tamaño del área mínima para los techos del soporte. Los polígonos que posean un área de menor tamaño que este valor se imprimirán como soporte normal."

msgctxt "min_feature_size description"
msgid "Minimum thickness of thin features. Model features that are thinner than this value will not be printed, while features thicker than the Minimum Feature Size will be widened to the Minimum Wall Line Width."
msgstr "Espesor mínimo de características delgadas. Las características del modelo que sean más delgadas que este valor no se imprimirán, mientras que las características más gruesas que el tamaño mínimo de la característica se estirarán hasta el ancho mínimo de la línea perimetral."

msgctxt "support_conical_min_width description"
msgid "Minimum width to which the base of the conical support area is reduced. Small widths can lead to unstable support structures."
msgstr "Ancho mínimo al que se reduce la base del área de soporte cónico. Las anchuras pequeñas pueden producir estructuras de soporte inestables."

msgctxt "mold_enabled label"
msgid "Mold"
msgstr "Molde"

msgctxt "mold_angle label"
msgid "Mold Angle"
msgstr "Ángulo del molde"

msgctxt "mold_roof_height label"
msgid "Mold Roof Height"
msgstr "Altura del techo del molde"

msgctxt "ironing_monotonic label"
msgid "Monotonic Ironing Order"
msgstr "Orden de planchado monotónico"

msgctxt "roofing_monotonic label"
msgid "Monotonic Top Surface Order"
msgstr "Orden monotónica de la superficie superior"

msgctxt "skin_monotonic label"
msgid "Monotonic Top/Bottom Order"
msgstr "Orden monotónica superior e inferior"

msgctxt "skirt_line_count description"
msgid "Multiple skirt lines help to prime your extrusion better for small models. Setting this to 0 will disable the skirt."
msgstr "Líneas de falda múltiples sirven para preparar la extrusión mejor para modelos pequeños. Con un ajuste de 0 se desactivará la falda."

msgctxt "initial_layer_line_width_factor description"
msgid "Multiplier of the line width on the first layer. Increasing this could improve bed adhesion."
msgstr "Multiplicador del ancho de la línea de la primera capa. Si esta se aumenta, se puede mejorar la adherencia a la plataforma."

msgctxt "material_no_load_move_factor label"
msgid "No Load Move Factor"
msgstr "Factor de desplazamiento sin carga"

msgctxt "skin_no_small_gaps_heuristic label"
msgid "No Skin in Z Gaps"
msgstr "Sin forro en huecos en Z"

msgctxt "blackmagic description"
msgid "Non-traditional ways to print your models."
msgstr "Formas no tradicionales de imprimir sus modelos."

msgctxt "adhesion_type option none"
msgid "None"
msgstr "Ninguno"

msgctxt "z_seam_corner option z_seam_corner_none"
msgid "None"
msgstr "Ninguno"

msgctxt "magic_mesh_surface_mode option normal"
msgid "Normal"
msgstr "Normal"

msgctxt "support_structure option normal"
msgid "Normal"
msgstr "Normal"

msgctxt "meshfix_keep_open_polygons description"
msgid "Normally Cura tries to stitch up small holes in the mesh and remove parts of a layer with big holes. Enabling this option keeps those parts which cannot be stitched. This option should be used as a last resort option when everything else fails to produce proper g-code."
msgstr "Normalmente, Cura intenta coser los pequeños agujeros de la malla y eliminar las partes de una capa con grandes agujeros. Al habilitar esta opción, se mantienen aquellas partes que no puedan coserse. Esta opción se debe utilizar como una opción de último recurso cuando todo lo demás falla para producir un GCode adecuado."

msgctxt "retraction_combing option noskin"
msgid "Not in Skin"
msgstr "No en el forro"

msgctxt "retraction_combing option no_outer_surfaces"
msgid "Not on Outer Surface"
msgstr "No en la superficie exterior"

msgctxt "machine_nozzle_expansion_angle label"
msgid "Nozzle Angle"
msgstr "Ángulo de la tobera"

msgctxt "machine_nozzle_size label"
msgid "Nozzle Diameter"
msgstr "Diámetro de la tobera"

msgctxt "nozzle_disallowed_areas label"
msgid "Nozzle Disallowed Areas"
msgstr "Áreas no permitidas para la tobera"

msgctxt "machine_nozzle_id label"
msgid "Nozzle ID"
msgstr "Id. de la tobera"

msgctxt "machine_nozzle_head_distance label"
msgid "Nozzle Length"
msgstr "Longitud de la tobera"

msgctxt "switch_extruder_extra_prime_amount label"
msgid "Nozzle Switch Extra Prime Amount"
msgstr "Volumen de cebado adicional tras cambio de tobera"

msgctxt "switch_extruder_prime_speed label"
msgid "Nozzle Switch Prime Speed"
msgstr "Velocidad de cebado del cambio de tobera"

msgctxt "switch_extruder_retraction_speed label"
msgid "Nozzle Switch Retract Speed"
msgstr "Velocidad de retracción del cambio de tobera"

msgctxt "switch_extruder_retraction_amount label"
msgid "Nozzle Switch Retraction Distance"
msgstr "Distancia de retracción del cambio de tobera"

msgctxt "switch_extruder_retraction_speeds label"
msgid "Nozzle Switch Retraction Speed"
msgstr "Velocidad de retracción del cambio de tobera"

msgctxt "machine_extruder_count label"
msgid "Number of Extruders"
msgstr "Número de extrusores"

msgctxt "extruders_enabled_count label"
msgid "Number of Extruders That Are Enabled"
msgstr "Número de extrusores habilitados"

msgctxt "speed_slowdown_layers label"
msgid "Number of Slower Layers"
msgstr "Número de capas más lentas"

msgctxt "extruders_enabled_count description"
msgid "Number of extruder trains that are enabled; automatically set in software"
msgstr "Número de trenes extrusores habilitados y configurados en el software de forma automática"

msgctxt "machine_extruder_count description"
msgid "Number of extruder trains. An extruder train is the combination of a feeder, bowden tube, and nozzle."
msgstr "Número de trenes extrusores. Un tren extrusor está formado por un alimentador, un tubo guía y una tobera."

msgctxt "wipe_repeat_count description"
msgid "Number of times to move the nozzle across the brush."
msgstr "Número de movimientos de la tobera a lo largo del cepillo."

msgctxt "gradual_infill_steps description"
msgid "Number of times to reduce the infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Infill Density."
msgstr "Número de veces necesarias para reducir a la mitad la densidad del relleno a medida que se aleja de las superficies superiores. Las zonas más próximas a las superficies superiores tienen una densidad mayor, hasta alcanzar la densidad de relleno."

msgctxt "gradual_support_infill_steps description"
msgid "Number of times to reduce the support infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Support Infill Density."
msgstr "Número de veces necesarias para reducir a la mitad la densidad del relleno de soporte a medida que se aleja de las superficies superiores. Las zonas más próximas a las superficies superiores tienen una densidad mayor, hasta alcanzar la densidad de relleno de soporte."

msgctxt "infill_pattern option tetrahedral"
msgid "Octet"
msgstr "Octeto"

msgctxt "retraction_combing option off"
msgid "Off"
msgstr "Apagado"

msgctxt "mesh_position_x description"
msgid "Offset applied to the object in the x direction."
msgstr "Desplazamiento aplicado al objeto en la dirección x."

msgctxt "mesh_position_y description"
msgid "Offset applied to the object in the y direction."
msgstr "Desplazamiento aplicado al objeto en la dirección y."

msgctxt "mesh_position_z description"
msgid "Offset applied to the object in the z direction. With this you can perform what was used to be called 'Object Sink'."
msgstr "Desplazamiento aplicado al objeto sobre el eje Z. Permite efectuar la operación antes conocida como «Object Sink»."

msgctxt "machine_use_extruder_offset_to_offset_coords label"
msgid "Offset with Extruder"
msgstr "Desplazamiento con extrusor"

msgctxt "support_tree_rest_preference option buildplate"
msgid "On buildplate when possible"
msgstr "En la placa de impresión, cuando sea posible"

msgctxt "support_tree_rest_preference option graceful"
msgid "On model if required"
msgstr "Sobre el modelo, en caso de que sea necesario"

msgctxt "print_sequence option one_at_a_time"
msgid "One at a Time"
msgstr "De uno en uno"

msgctxt "retraction_hop_only_when_collides description"
msgid "Only perform a Z Hop when moving over printed parts which cannot be avoided by horizontal motion by Avoid Printed Parts when Traveling."
msgstr "Realizar un salto en Z solo al desplazarse por las partes impresas que no puede evitar el movimiento horizontal de la opción Evitar partes impresas al desplazarse."

msgctxt "ironing_only_highest_layer description"
msgid "Only perform ironing on the very last layer of the mesh. This saves time if the lower layers don't need a smooth surface finish."
msgstr "Planchar únicamente la última capa de la malla. De este modo se ahorra tiempo si las capas inferiores no requieren un acabado superficial suave."

msgctxt "brim_outside_only description"
msgid "Only print the brim on the outside of the model. This reduces the amount of brim you need to remove afterwards, while it doesn't reduce the bed adhesion that much."
msgstr "Imprimir solo el borde en el exterior del modelo. Esto reduce el número de bordes que deberá retirar después sin que la adherencia a la plataforma se vea muy afectada."

msgctxt "ooze_shield_angle label"
msgid "Ooze Shield Angle"
msgstr "Ángulo de la placa de rezumado"

msgctxt "ooze_shield_dist label"
msgid "Ooze Shield Distance"
msgstr "Distancia de la placa de rezumado"

msgctxt "support_tree_branch_reach_limit label"
msgid "Optimal Branch Range"
msgstr "Espaciado óptimo de ramas"

msgctxt "optimize_wall_printing_order label"
msgid "Optimize Wall Printing Order"
msgstr "Optimizar el orden de impresión de paredes"

msgctxt "optimize_wall_printing_order description"
msgid "Optimize the order in which walls are printed so as to reduce the number of retractions and the distance travelled. Most parts will benefit from this being enabled but some may actually take longer so please compare the print time estimates with and without optimization. First layer is not optimized when choosing brim as build plate adhesion type."
msgstr "Optimizar el orden en el que se imprimen las paredes a fin de reducir el número de retracciones y la distancia recorrida. La mayoría de los componentes se beneficiarán si este ajuste está habilitado pero, en algunos casos, se puede tardar más, por lo que deben compararse las previsiones de tiempo de impresión con y sin optimización. La primera capa no está optimizada al elegir el borde como el tipo de adhesión de la placa de impresión."

msgctxt "machine_nozzle_tip_outer_diameter label"
msgid "Outer Nozzle Diameter"
msgstr "Diámetro exterior de la tobera"

msgctxt "acceleration_wall_0 label"
msgid "Outer Wall Acceleration"
msgstr "Aceleración de pared exterior"

msgctxt "wall_0_extruder_nr label"
msgid "Outer Wall Extruder"
msgstr "Extrusor de pared exterior"

msgctxt "wall_0_material_flow label"
msgid "Outer Wall Flow"
msgstr "Flujo de pared exterior"

msgctxt "wall_0_inset label"
msgid "Outer Wall Inset"
msgstr "Entrante en la pared exterior"

msgctxt "jerk_wall_0 label"
msgid "Outer Wall Jerk"
msgstr "Impulso de pared exterior"

msgctxt "wall_line_width_0 label"
msgid "Outer Wall Line Width"
msgstr "Ancho de línea de la pared exterior"

msgctxt "speed_wall_0 label"
msgid "Outer Wall Speed"
msgstr "Velocidad de pared exterior"

msgctxt "wall_0_wipe_dist label"
msgid "Outer Wall Wipe Distance"
msgstr "Distancia de pasada de la pared exterior"

msgctxt "group_outer_walls description"
msgid "Outer walls of different islands in the same layer are printed in sequence. When enabled the amount of flow changes is limited because walls are printed one type at a time, when disabled the number of travels between islands is reduced because walls in the same islands are grouped."
msgstr "Las paredes exteriores de diferentes islas en la misma capa se imprimen en secuencia. Cuando está habilitado, la cantidad de cambios de flujo se limita porque las paredes se imprimen de a una a la vez; cuando está deshabilitado, se reduce el número de desplazamientos entre islas porque las paredes en las mismas islas se agrupan."

msgctxt "inset_direction option outside_in"
msgid "Outside To Inside"
msgstr "Del exterior al interior"

msgctxt "wall_overhang_angle label"
msgid "Overhanging Wall Angle"
msgstr "Ángulo de voladizo de pared"

msgctxt "wall_overhang_speed_factor label"
msgid "Overhanging Wall Speed"
msgstr "Velocidad de voladizo de pared"

msgctxt "wall_overhang_speed_factor description"
msgid "Overhanging walls will be printed at this percentage of their normal print speed."
msgstr "Los voladizos de pared se imprimirán a este porcentaje de su velocidad de impresión normal."

msgctxt "wipe_pause description"
msgid "Pause after the unretract."
msgstr "Pausa después de no haber retracción."

msgctxt "bridge_fan_speed description"
msgid "Percentage fan speed to use when printing bridge walls and skin."
msgstr "Porcentaje de velocidad del ventilador que se emplea en la impresión de las paredes y el forro del puente."

msgctxt "bridge_fan_speed_2 description"
msgid "Percentage fan speed to use when printing the second bridge skin layer."
msgstr "Velocidad del ventilador en porcentaje que se utiliza para imprimir la segunda capa del forro del puente."

msgctxt "support_supported_skin_fan_speed description"
msgid "Percentage fan speed to use when printing the skin regions immediately above the support. Using a high fan speed can make the support easier to remove."
msgstr "Porcentaje para la velocidad de ventilador que se utiliza al imprimir las áreas del forro que se encuentran inmediatamente encima del soporte. Si utiliza una velocidad alta para el ventilador, será más fácil retirar el soporte."

msgctxt "bridge_fan_speed_3 description"
msgid "Percentage fan speed to use when printing the third bridge skin layer."
msgstr "Velocidad del ventilador en porcentaje que se utiliza para imprimir la tercera capa del forro del puente."

msgctxt "minimum_polygon_circumference description"
msgid "Polygons in sliced layers that have a circumference smaller than this amount will be filtered out. Lower values lead to higher resolution mesh at the cost of slicing time. It is meant mostly for high resolution SLA printers and very tiny 3D models with a lot of details."
msgstr "Se filtran los polígonos en capas segmentadas que tienen una circunferencia más pequeña que esta. Los valores más pequeños suponen una resolución de malla mayor a costa de un tiempo de segmentación. Está indicado, sobre todo, para impresoras SLA y modelos 3D muy pequeños con muchos detalles."

msgctxt "support_tree_angle_slow label"
msgid "Preferred Branch Angle"
msgstr "Ángulo de rama preferido"

msgctxt "wall_transition_filter_deviation description"
msgid "Prevent transitioning back and forth between one extra wall and one less. This margin extends the range of line widths which follow to [Minimum Wall Line Width - Margin, 2 * Minimum Wall Line Width + Margin]. Increasing this margin reduces the number of transitions, which reduces the number of extrusion starts/stops and travel time. However, large line width variation can lead to under- or overextrusion problems."
msgstr "Evite la transición de ida y vuelta entre una pared extra y una menos. Este margen amplía el rango de anchos de línea después de [Ancho mínimo de línea perimetral - Margen, 2 * Ancho mínimo de línea perimetral + Margen]. Aumentar este margen reduce el número de transiciones, lo que reduce el número de arranques y paradas de la extrusión y el tiempo de recorrido. No obstante, las grandes variaciones en el ancho de la línea pueden provocar problemas de subextrusión o sobreextrusión."

msgctxt "acceleration_prime_tower label"
msgid "Prime Tower Acceleration"
msgstr "Aceleración de la torre auxiliar"

msgctxt "prime_tower_brim_enable label"
msgid "Prime Tower Base"
msgstr "Base de la torre de cebado"

msgctxt "prime_tower_base_height label"
msgid "Prime Tower Base Height"
msgstr "Altura de la base de la torre de cebado"

msgctxt "prime_tower_base_size label"
msgid "Prime Tower Base Size"
msgstr "Tamaño de la base de la torre de cebado"

msgctxt "prime_tower_base_curve_magnitude label"
msgid "Prime Tower Base Slope"
msgstr "Pendiente de la Base de la Torre de Cebado"

msgctxt "prime_tower_flow label"
msgid "Prime Tower Flow"
msgstr "Flujo de la torre auxiliar"

msgctxt "jerk_prime_tower label"
msgid "Prime Tower Jerk"
msgstr "Impulso de la torre auxiliar"

msgctxt "prime_tower_line_width label"
msgid "Prime Tower Line Width"
msgstr "Ancho de línea de la torre auxiliar"

msgctxt "prime_tower_min_volume label"
msgid "Prime Tower Minimum Volume"
msgstr "Volumen mínimo de la torre auxiliar"

msgctxt "prime_tower_raft_base_line_spacing label"
msgid "Prime Tower Raft Line Spacing"
msgstr "Espaciado de las líneas del raft de la torre de cebado"

msgctxt "prime_tower_size label"
msgid "Prime Tower Size"
msgstr "Tamaño de la torre auxiliar"

msgctxt "speed_prime_tower label"
msgid "Prime Tower Speed"
msgstr "Velocidad de la torre auxiliar"

msgctxt "prime_tower_position_x label"
msgid "Prime Tower X Position"
msgstr "Posición de la torre auxiliar sobre el eje X"

msgctxt "prime_tower_position_y label"
msgid "Prime Tower Y Position"
msgstr "Posición de la torre auxiliar sobre el eje Y"

msgctxt "acceleration_print label"
msgid "Print Acceleration"
msgstr "Aceleración de la impresión"

msgctxt "jerk_print label"
msgid "Print Jerk"
msgstr "Impulso de impresión"

msgctxt "print_sequence label"
msgid "Print Sequence"
msgstr "Secuencia de impresión"

msgctxt "speed_print label"
msgid "Print Speed"
msgstr "Velocidad de impresión"

msgctxt "fill_outline_gaps label"
msgid "Print Thin Walls"
msgstr "Imprimir paredes finas"

msgctxt "prime_tower_enable description"
msgid "Print a tower next to the print which serves to prime the material after each nozzle switch."
msgstr "Imprimir una torre junto a la impresión que sirve para preparar el material tras cada cambio de tobera."

msgctxt "infill_support_enabled description"
msgid "Print infill structures only where tops of the model should be supported. Enabling this reduces print time and material usage, but leads to ununiform object strength."
msgstr "Imprimir estructuras de relleno solo cuando se deban soportar las partes superiores del modelo. Habilitar esto reduce el tiempo de impresión y el uso de material, pero ocasiona que la resistencia del objeto no sea uniforme."

msgctxt "ironing_monotonic description"
msgid "Print ironing lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Imprime colocando las líneas de planchado de modo que siempre se superpongan a las líneas adyacentes en una dirección. Esto lleva un poco más de tiempo de impresión, pero hace que las superficies planas tengan un aspecto más consistente."

msgctxt "mold_enabled description"
msgid "Print models as a mold, which can be cast in order to get a model which resembles the models on the build plate."
msgstr "Imprimir modelos como un molde que se pueden fundir para obtener un modelo que se parezca a los modelos de la placa de impresión."

msgctxt "fill_outline_gaps description"
msgid "Print pieces of the model which are horizontally thinner than the nozzle size."
msgstr "Imprime las piezas del modelo que son horizontalmente más finas que el tamaño de la tobera."

msgctxt "bridge_skin_speed_2 description"
msgid "Print speed to use when printing the second bridge skin layer."
msgstr "Velocidad de impresión que se utiliza para imprimir la segunda capa del forro del puente."

msgctxt "bridge_skin_speed_3 description"
msgid "Print speed to use when printing the third bridge skin layer."
msgstr "Velocidad de impresión que se utiliza para imprimir la tercera capa del forro del puente."

msgctxt "infill_before_walls description"
msgid "Print the infill before printing the walls. Printing the walls first may lead to more accurate walls, but overhangs print worse. Printing the infill first leads to sturdier walls, but the infill pattern might sometimes show through the surface."
msgstr "Imprime el relleno antes de imprimir las paredes. Si se imprimen primero las paredes, estas serán más precisas, pero los voladizos se imprimirán peor. Si se imprime primero el relleno las paredes serán más resistentes, pero el patrón de relleno a veces se nota a través de la superficie."

msgctxt "roofing_monotonic description"
msgid "Print top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Imprime colocando las líneas de la superficie superior de modo que siempre se superpongan a las líneas adyacentes en una dirección. Esto lleva un poco más de tiempo de impresión, pero hace que las superficies planas tengan un aspecto más consistente."

msgctxt "skin_monotonic description"
msgid "Print top/bottom lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Imprime colocando las líneas superior e inferior de modo que siempre se superpongan a las líneas adyacentes en una dirección. Esto lleva un poco más de tiempo de impresión, pero hace que las superficies planas tengan un aspecto más consistente."

msgctxt "material_print_temperature label"
msgid "Printing Temperature"
msgstr "Temperatura de impresión"

msgctxt "material_print_temperature_layer_0 label"
msgid "Printing Temperature Initial Layer"
msgstr "Temperatura de impresión de la capa inicial"

msgctxt "skirt_height description"
msgid "Printing the innermost skirt line with multiple layers makes it easy to remove the skirt."
msgstr "La impresión de la línea más interna de la falda con varias capas facilita su eliminación."

msgctxt "alternate_extra_perimeter description"
msgid "Prints an extra wall at every other layer. This way infill gets caught between these extra walls, resulting in stronger prints."
msgstr "Imprime una pared adicional cada dos capas. De este modo el relleno se queda atrapado entre estas paredes adicionales, lo que da como resultado impresiones más sólidas."

msgctxt "resolution label"
msgid "Quality"
msgstr "Calidad"

msgctxt "infill_pattern option quarter_cubic"
msgid "Quarter Cubic"
msgstr "Cúbico bitruncado"

msgctxt "adhesion_type option raft"
msgid "Raft"
msgstr "Balsa"

msgctxt "raft_airgap label"
msgid "Raft Air Gap"
msgstr "Cámara de aire de la balsa"

msgctxt "raft_base_extruder_nr label"
msgid "Raft Base Extruder"
msgstr "Extrusor base de la balsa"

msgctxt "raft_base_fan_speed label"
msgid "Raft Base Fan Speed"
msgstr "Velocidad del ventilador de la base de la balsa"

msgctxt "raft_base_line_spacing label"
msgid "Raft Base Line Spacing"
msgstr "Espacio de la línea base de la balsa"

msgctxt "raft_base_line_width label"
msgid "Raft Base Line Width"
msgstr "Ancho de la línea base de la balsa"

msgctxt "raft_base_acceleration label"
msgid "Raft Base Print Acceleration"
msgstr "Aceleración de la impresión de la base de la balsa"

msgctxt "raft_base_jerk label"
msgid "Raft Base Print Jerk"
msgstr "Impulso de impresión de base de la balsa"

msgctxt "raft_base_speed label"
msgid "Raft Base Print Speed"
msgstr "Velocidad de impresión de la base de la balsa"

msgctxt "raft_base_thickness label"
msgid "Raft Base Thickness"
msgstr "Grosor de la base de la balsa"

msgctxt "raft_base_wall_count label"
msgid "Raft Base Wall Count"
msgstr "Recuento de paredes base de balsa"

msgctxt "raft_margin label"
msgid "Raft Extra Margin"
msgstr "Margen adicional de la balsa"

msgctxt "raft_fan_speed label"
msgid "Raft Fan Speed"
msgstr "Velocidad del ventilador de la balsa"

msgctxt "raft_interface_extruder_nr label"
msgid "Raft Middle Extruder"
msgstr "Extrusor medio de la balsa"

msgctxt "raft_interface_fan_speed label"
msgid "Raft Middle Fan Speed"
msgstr "Velocidad del ventilador de balsa intermedia"

msgctxt "raft_interface_layers label"
msgid "Raft Middle Layers"
msgstr "Capas medias de la balsa"

msgctxt "raft_interface_line_width label"
msgid "Raft Middle Line Width"
msgstr "Ancho de la línea intermedia de la balsa"

msgctxt "raft_interface_acceleration label"
msgid "Raft Middle Print Acceleration"
msgstr "Aceleración de la impresión de la balsa intermedia"

msgctxt "raft_interface_jerk label"
msgid "Raft Middle Print Jerk"
msgstr "Impulso de impresión de balsa intermedia"

msgctxt "raft_interface_speed label"
msgid "Raft Middle Print Speed"
msgstr "Velocidad de impresión de la balsa intermedia"

msgctxt "raft_interface_line_spacing label"
msgid "Raft Middle Spacing"
msgstr "Espaciado intermedio de la balsa"

msgctxt "raft_interface_thickness label"
msgid "Raft Middle Thickness"
msgstr "Grosor intermedio de la balsa"

msgctxt "raft_acceleration label"
msgid "Raft Print Acceleration"
msgstr "Aceleración de impresión de la balsa"

msgctxt "raft_jerk label"
msgid "Raft Print Jerk"
msgstr "Impulso de impresión de la balsa"

msgctxt "raft_speed label"
msgid "Raft Print Speed"
msgstr "Velocidad de impresión de la balsa"

msgctxt "raft_smoothing label"
msgid "Raft Smoothing"
msgstr "Suavizado de la balsa"

msgctxt "raft_surface_extruder_nr label"
msgid "Raft Top Extruder"
msgstr "Extrusor superior de la balsa"

msgctxt "raft_surface_fan_speed label"
msgid "Raft Top Fan Speed"
msgstr "Velocidad del ventilador de balsa superior"

msgctxt "raft_surface_thickness label"
msgid "Raft Top Layer Thickness"
msgstr "Grosor de las capas superiores de la balsa"

msgctxt "raft_surface_layers label"
msgid "Raft Top Layers"
msgstr "Capas superiores de la balsa"

msgctxt "raft_surface_line_width label"
msgid "Raft Top Line Width"
msgstr "Ancho de las líneas superiores de la balsa"

msgctxt "raft_surface_acceleration label"
msgid "Raft Top Print Acceleration"
msgstr "Aceleración de la impresión de la balsa superior"

msgctxt "raft_surface_jerk label"
msgid "Raft Top Print Jerk"
msgstr "Impulso de impresión de balsa superior"

msgctxt "raft_surface_speed label"
msgid "Raft Top Print Speed"
msgstr "Velocidad de impresión de la balsa superior"

msgctxt "raft_surface_line_spacing label"
msgid "Raft Top Spacing"
msgstr "Espaciado superior de la balsa"

msgctxt "z_seam_type option random"
msgid "Random"
msgstr "Aleatoria"

msgctxt "infill_randomize_start_location label"
msgid "Randomize Infill Start"
msgstr "Comienzo de relleno aleatorio"

msgctxt "infill_randomize_start_location description"
msgid "Randomize which infill line is printed first. This prevents one segment becoming the strongest, but it does so at the cost of an additional travel move."
msgstr "Determine qué línea de relleno se imprime primero. Esto evita que un segmento se convierta en el más fuerte, pero a expensas de un movimiento adicional."

msgctxt "magic_fuzzy_skin_enabled description"
msgid "Randomly jitter while printing the outer wall, so that the surface has a rough and fuzzy look."
msgstr "Fluctúa aleatoriamente durante la impresión de la pared exterior, de modo que la superficie tiene un aspecto desigual y difuso."

msgctxt "machine_shape option rectangular"
msgid "Rectangular"
msgstr "Rectangular"

msgctxt "cool_fan_speed_min label"
msgid "Regular Fan Speed"
msgstr "Velocidad normal del ventilador"

msgctxt "cool_fan_full_at_height label"
msgid "Regular Fan Speed at Height"
msgstr "Velocidad normal del ventilador a altura"

msgctxt "cool_fan_full_layer label"
msgid "Regular Fan Speed at Layer"
msgstr "Velocidad normal del ventilador por capa"

msgctxt "cool_min_layer_time_fan_speed_max label"
msgid "Regular/Maximum Fan Speed Threshold"
msgstr "Umbral de velocidad normal/máxima del ventilador"

msgctxt "relative_extrusion label"
msgid "Relative Extrusion"
msgstr "Extrusión relativa"

msgctxt "meshfix_union_all_remove_holes label"
msgid "Remove All Holes"
msgstr "Eliminar todos los agujeros"

msgctxt "remove_empty_first_layers label"
msgid "Remove Empty First Layers"
msgstr "Eliminar primeras capas vacías"

msgctxt "carve_multiple_volumes label"
msgid "Remove Mesh Intersection"
msgstr "Eliminar el cruce de mallas"

msgctxt "raft_remove_inside_corners label"
msgid "Remove Raft Inside Corners"
msgstr "Quitar las esquinas internas de la balsa"

msgctxt "carve_multiple_volumes description"
msgid "Remove areas where multiple meshes are overlapping with each other. This may be used if merged dual material objects overlap with each other."
msgstr "Eliminar las zonas en las que se superponen varias mallas. Puede utilizarse esta opción cuando se superponen objetos combinados de dos materiales."

msgctxt "remove_empty_first_layers description"
msgid "Remove empty layers beneath the first printed layer if they are present. Disabling this setting can cause empty first layers if the Slicing Tolerance setting is set to Exclusive or Middle."
msgstr "Eliminar (si las hubiera) las capas vacías por debajo de la primera capa impresa. Deshabilitar este ajuste puede hacer que aparezcan primeras capas vacías si el ajuste de tolerancia de segmentación está establecido en Exclusiva o Medio."

msgctxt "raft_remove_inside_corners description"
msgid "Remove inside corners from the raft, causing the raft to become convex."
msgstr "Le permite eliminar las esquinas internas de la balsa, haciéndola convexa."

msgctxt "meshfix_union_all_remove_holes description"
msgid "Remove the holes in each layer and keep only the outside shape. This will ignore any invisible internal geometry. However, it also ignores layer holes which can be viewed from above or below."
msgstr "Elimina los agujeros en cada capa y mantiene solo la forma exterior. Esto ignorará cualquier geometría interna invisible. Sin embargo, también ignora los agujeros de la capa que pueden verse desde arriba o desde abajo."

msgctxt "machine_gcode_flavor option RepRap (RepRap)"
msgid "RepRap"
msgstr "RepRap"

msgctxt "machine_gcode_flavor option Repetier"
msgid "Repetier"
msgstr "Repetier"

msgctxt "skin_outline_count description"
msgid "Replaces the outermost part of the top/bottom pattern with a number of concentric lines. Using one or two lines improves roofs that start on infill material."
msgstr "Reemplaza la parte más externa del patrón superior/inferior con un número de líneas concéntricas. Mediante el uso de una o dos líneas mejora los techos que comienzan en el material de relleno."

msgctxt "support_tree_rest_preference label"
msgid "Rest Preference"
msgstr "Preferencia de apoyo"

msgctxt "travel_retract_before_outer_wall label"
msgid "Retract Before Outer Wall"
msgstr "Retracción antes de la pared exterior"

msgctxt "retract_at_layer_change label"
msgid "Retract at Layer Change"
msgstr "Retracción en el cambio de capa"

msgctxt "retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "Retrae el filamento cuando la tobera se mueve sobre un área no impresa."

msgctxt "wipe_retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "Retrae el filamento cuando la tobera se mueve sobre un área no impresa."

msgctxt "retract_at_layer_change description"
msgid "Retract the filament when the nozzle is moving to the next layer."
msgstr "Retrae el filamento cuando la tobera se mueve a la siguiente capa."

msgctxt "retraction_amount label"
msgid "Retraction Distance"
msgstr "Distancia de retracción"

msgctxt "retraction_extra_prime_amount label"
msgid "Retraction Extra Prime Amount"
msgstr "Cantidad de cebado adicional de retracción"

msgctxt "retraction_min_travel label"
msgid "Retraction Minimum Travel"
msgstr "Desplazamiento mínimo de retracción"

msgctxt "retraction_prime_speed label"
msgid "Retraction Prime Speed"
msgstr "Velocidad de cebado de retracción"

msgctxt "retraction_retract_speed label"
msgid "Retraction Retract Speed"
msgstr "Velocidad de retracción"

msgctxt "retraction_speed label"
msgid "Retraction Speed"
msgstr "Velocidad de retracción"

msgctxt "z_seam_position option right"
msgid "Right"
msgstr "Derecha"

msgctxt "machine_scale_fan_speed_zero_to_one label"
msgid "Scale Fan Speed To 0-1"
msgstr "Escale la velocidad del ventilador a 0-1"

msgctxt "machine_scale_fan_speed_zero_to_one description"
msgid "Scale the fan speed to be between 0 and 1 instead of between 0 and 256."
msgstr "Escale la velocidad del ventilador para que esté entre 0 y 1 en lugar de entre 0 y 256."

msgctxt "material_shrinkage_percentage label"
msgid "Scaling Factor Shrinkage Compensation"
msgstr "Factor de escala para la compensación de la contracción"

msgctxt "support_meshes_present label"
msgid "Scene Has Support Meshes"
msgstr "La escena tiene mallas de soporte"

msgctxt "z_seam_corner label"
msgid "Seam Corner Preference"
msgstr "Preferencia de esquina de costura"

msgctxt "draft_shield_height_limitation description"
msgid "Set the height of the draft shield. Choose to print the draft shield at the full height of the model or at a limited height."
msgstr "Establece la altura del parabrisas. Seleccione esta opción para imprimir el parabrisas a la altura completa del modelo o a una altura limitada."

msgctxt "dual description"
msgid "Settings used for printing with multiple extruders."
msgstr "Ajustes utilizados en la impresión con varios extrusores."

msgctxt "command_line_settings description"
msgid "Settings which are only used if CuraEngine isn't called from the Cura frontend."
msgstr "Ajustes que únicamente se utilizan si CuraEngine no se ejecuta desde la interfaz de Cura."

msgctxt "machine_extruders_shared_nozzle_initial_retraction label"
msgid "Shared Nozzle Initial Retraction"
msgstr "Retracción inicial de tobera compartida"

msgctxt "z_seam_type option sharpest_corner"
msgid "Sharpest Corner"
msgstr "Esquina más pronunciada"

msgctxt "shell description"
msgid "Shell"
msgstr "Perímetro"

msgctxt "z_seam_type option shortest"
msgid "Shortest"
msgstr "Más corta"

msgctxt "machine_show_variants label"
msgid "Show Machine Variants"
msgstr "Mostrar versiones de la máquina"

msgctxt "skin_edge_support_layers label"
msgid "Skin Edge Support Layers"
msgstr "Capas de soporte de los bordes del forro"

msgctxt "skin_edge_support_thickness label"
msgid "Skin Edge Support Thickness"
msgstr "Espesor de soporte de los bordes del forro"

msgctxt "expand_skins_expand_distance label"
msgid "Skin Expand Distance"
msgstr "Distancia de expansión del forro"

msgctxt "skin_overlap_mm label"
msgid "Skin Overlap"
msgstr "Superposición del forro"

msgctxt "skin_overlap label"
msgid "Skin Overlap Percentage"
msgstr "Porcentaje de superposición del forro"

msgctxt "skin_preshrink label"
msgid "Skin Removal Width"
msgstr "Anchura de retirada del forro"

msgctxt "min_skin_width_for_expansion description"
msgid "Skin areas narrower than this are not expanded. This avoids expanding the narrow skin areas that are created when the model surface has a slope close to the vertical."
msgstr "Las áreas de forro más estrechas que este valor no se expanden. Esto evita la expansión de las áreas de forro estrechas que se crean cuando la superficie del modelo tiene una inclinación casi vertical."

msgctxt "support_zag_skip_count description"
msgid "Skip one in every N connection lines to make the support structure easier to break away."
msgstr "Omitir una de cada N líneas de conexión para que la estructura de soporte se descomponga fácilmente."

msgctxt "support_skip_some_zags description"
msgid "Skip some support line connections to make the support structure easier to break away. This setting is applicable to the Zig Zag support infill pattern."
msgstr "Omitir algunas conexiones de línea de soporte para que la estructura de soporte sea más fácil de descomponer. Este ajuste es aplicable al patrón de relleno del soporte en zigzag."

msgctxt "adhesion_type option skirt"
msgid "Skirt"
msgstr "Falda"

msgctxt "skirt_gap label"
msgid "Skirt Distance"
msgstr "Distancia de falda"

msgctxt "skirt_height label"
msgid "Skirt Height"
msgstr "Altura de la falda"

msgctxt "skirt_line_count label"
msgid "Skirt Line Count"
msgstr "Recuento de líneas de falda"

msgctxt "acceleration_skirt_brim label"
msgid "Skirt/Brim Acceleration"
msgstr "Aceleración de falda/borde"

msgctxt "skirt_brim_extruder_nr label"
msgid "Skirt/Brim Extruder"
msgstr "Extrusor de falda o borde"

msgctxt "skirt_brim_material_flow label"
msgid "Skirt/Brim Flow"
msgstr "Flujo de falda/borde"

msgctxt "jerk_skirt_brim label"
msgid "Skirt/Brim Jerk"
msgstr "Impulso de falda/borde"

msgctxt "skirt_brim_line_width label"
msgid "Skirt/Brim Line Width"
msgstr "Ancho de línea de falda/borde"

msgctxt "skirt_brim_minimal_length label"
msgid "Skirt/Brim Minimum Length"
msgstr "Longitud mínima de falda/borde"

msgctxt "skirt_brim_speed label"
msgid "Skirt/Brim Speed"
msgstr "Velocidad de falda/borde"

msgctxt "slicing_tolerance label"
msgid "Slicing Tolerance"
msgstr "Tolerancia de segmentación"

msgctxt "small_feature_speed_factor_0 label"
msgid "Small Feature Initial Layer Speed"
msgstr "Velocidad de la capa inicial de partes pequeñas"

msgctxt "small_feature_max_length label"
msgid "Small Feature Max Length"
msgstr "Longitud máxima de pequeñas partes"

msgctxt "small_feature_speed_factor label"
msgid "Small Feature Speed"
msgstr "Velocidad de pequeñas partes"

msgctxt "small_hole_max_size label"
msgid "Small Hole Max Size"
msgstr "Tamaño máximo de agujero pequeño"

msgctxt "cool_min_temperature label"
msgid "Small Layer Printing Temperature"
msgstr "Temperatura de impresión de capas pequeñas"

msgctxt "small_skin_on_surface label"
msgid "Small Top/Bottom On Surface"
msgstr "Zonas pequeñas superiores/inferiores en superficie"

msgctxt "small_skin_width label"
msgid "Small Top/Bottom Width"
msgstr "Anchura superior/​inferior pequeña"

msgctxt "small_feature_speed_factor_0 description"
msgid "Small features on the first layer will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "Las pequeñas partes de la primera capa se imprimirán a este porcentaje de su velocidad de impresión normal. Una impresión más lenta puede mejorar la adhesión y la precisión."

msgctxt "small_feature_speed_factor description"
msgid "Small features will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "Las pequeñas partes se imprimirán a este porcentaje de su velocidad de impresión normal. Una impresión más lenta puede mejorar la adhesión y la precisión."

msgctxt "small_skin_width description"
msgid "Small top/bottom regions are filled with walls instead of the default top/bottom pattern. This helps to avoids jerky motions. Off for the topmost (air-exposed) layer by default (see 'Small Top/Bottom On Surface')."
msgstr "Las zonas pequeñas superiores/inferiores se rellenan con paredes en lugar de con el patrón predeterminado superior/inferior. Esto ayuda a evitar movimientos bruscos. Está desactivado para la capa superior (expuesta al aire) por defecto. (Consulte: \"Zonas pequeñas superiores/inferiores en superficie\")."

msgctxt "brim_smart_ordering label"
msgid "Smart Brim"
msgstr "Borde inteligente"

msgctxt "z_seam_corner option z_seam_corner_weighted"
msgid "Smart Hiding"
msgstr "Costura inteligente"

msgctxt "smooth_spiralized_contours label"
msgid "Smooth Spiralized Contours"
msgstr "Contornos espiralizados suaves"

msgctxt "smooth_spiralized_contours description"
msgid "Smooth the spiralized contours to reduce the visibility of the Z seam (the Z seam should be barely visible on the print but will still be visible in the layer view). Note that smoothing will tend to blur fine surface details."
msgstr "Suaviza los contornos espiralizados para reducir la visibilidad de la costura Z (la costura Z debería ser apenas visible en la impresora pero seguirá siendo visible en la vista de capas). Tenga en cuenta que la suavización tenderá a desdibujar detalles finos de la superficie."

msgctxt "retraction_extra_prime_amount description"
msgid "Some material can ooze away during a travel move, which can be compensated for here."
msgstr "Algunos materiales pueden rezumar durante el movimiento de un desplazamiento, lo cual se puede corregir aquí."

msgctxt "wipe_retraction_extra_prime_amount description"
msgid "Some material can ooze away during a wipe travel moves, which can be compensated for here."
msgstr "Algunos materiales pueden rezumar durante el movimiento de un desplazamiento de limpieza, lo cual se puede corregir aquí."

msgctxt "blackmagic label"
msgid "Special Modes"
msgstr "Modos especiales"

msgctxt "speed description"
msgid "Speed"
msgstr "Velocidad"

msgctxt "speed label"
msgid "Speed"
msgstr "Velocidad"

msgctxt "wipe_hop_speed description"
msgid "Speed to move the z-axis during the hop."
msgstr "Velocidad para mover el eje Z durante el salto."

msgctxt "magic_spiralize label"
msgid "Spiralize Outer Contour"
msgstr "Espiralizar el contorno exterior"

msgctxt "magic_spiralize description"
msgid "Spiralize smooths out the Z move of the outer edge. This will create a steady Z increase over the whole print. This feature turns a solid model into a single walled print with a solid bottom. This feature should only be enabled when each layer only contains a single part."
msgstr "La opción de espiralizar suaviza el movimiento en Z del borde exterior. Esto creará un incremento en Z constante durante toda la impresión. Esta función convierte un modelo sólido en una impresión de una sola pared con una parte inferior sólida. Esta función solo se debería habilitar cuando cada capa contenga una única pieza."

msgctxt "material_standby_temperature label"
msgid "Standby Temperature"
msgstr "Temperatura en modo de espera"

msgctxt "machine_start_gcode label"
msgid "Start G-code"
msgstr "Iniciar GCode"

msgctxt "z_seam_type description"
msgid "Starting point of each path in a layer. When paths in consecutive layers start at the same point a vertical seam may show on the print. When aligning these near a user specified location, the seam is easiest to remove. When placed randomly the inaccuracies at the paths' start will be less noticeable. When taking the shortest path the print will be quicker."
msgstr "Punto de partida de cada trayectoria en una capa. Cuando las trayectorias en capas consecutivas comienzan en el mismo punto, puede aparecer una costura vertical en la impresión. Cuando se alinean cerca de una ubicación especificada por el usuario, es más fácil eliminar la costura. Si se colocan aleatoriamente, las inexactitudes del inicio de las trayectorias se notarán menos. Si se toma la trayectoria más corta, la impresión será más rápida."

msgctxt "machine_steps_per_mm_e label"
msgid "Steps per Millimeter (E)"
msgstr "Pasos por milímetro (E)"

msgctxt "machine_steps_per_mm_x label"
msgid "Steps per Millimeter (X)"
msgstr "Pasos por milímetro (X)"

msgctxt "machine_steps_per_mm_y label"
msgid "Steps per Millimeter (Y)"
msgstr "Pasos por milímetro (Y)"

msgctxt "machine_steps_per_mm_z label"
msgid "Steps per Millimeter (Z)"
msgstr "Pasos por milímetro (Z)"

msgctxt "support description"
msgid "Support"
msgstr "Soporte"

msgctxt "support label"
msgid "Support"
msgstr "Soporte"

msgctxt "acceleration_support label"
msgid "Support Acceleration"
msgstr "Aceleración de soporte"

msgctxt "support_bottom_distance label"
msgid "Support Bottom Distance"
msgstr "Distancia inferior del soporte"

msgctxt "support_bottom_wall_count label"
msgid "Support Bottom Wall Line Count"
msgstr "Recuento de líneas de pared de la base de soporte"

msgctxt "support_brim_line_count label"
msgid "Support Brim Line Count"
msgstr "Recuento de líneas del borde de soporte"

msgctxt "support_brim_width label"
msgid "Support Brim Width"
msgstr "Ancho del borde de soporte"

msgctxt "support_zag_skip_count label"
msgid "Support Chunk Line Count"
msgstr "Recuento de líneas de pedazos del soporte"

msgctxt "support_skip_zag_per_mm label"
msgid "Support Chunk Size"
msgstr "Tamaño de los pedazos de soporte"

msgctxt "support_infill_rate label"
msgid "Support Density"
msgstr "Densidad del soporte"

msgctxt "support_xy_overrides_z label"
msgid "Support Distance Priority"
msgstr "Prioridad de las distancias del soporte"

msgctxt "support_extruder_nr label"
msgid "Support Extruder"
msgstr "Extrusor del soporte"

msgctxt "acceleration_support_bottom label"
msgid "Support Floor Acceleration"
msgstr "Aceleración del suelo del soporte"

msgctxt "support_bottom_density label"
msgid "Support Floor Density"
msgstr "Densidad del suelo del soporte"

msgctxt "support_bottom_extruder_nr label"
msgid "Support Floor Extruder"
msgstr "Extrusor del suelo del soporte"

msgctxt "support_bottom_material_flow label"
msgid "Support Floor Flow"
msgstr "Flujo de suelo de soporte"

msgctxt "support_bottom_offset label"
msgid "Support Floor Horizontal Expansion"
msgstr "Expansión horizontal de los suelos de soporte"

msgctxt "jerk_support_bottom label"
msgid "Support Floor Jerk"
msgstr "Impulso del suelo del soporte"

msgctxt "support_bottom_angles label"
msgid "Support Floor Line Directions"
msgstr "Direcciones de línea del suelo de soporte"

msgctxt "support_bottom_line_distance label"
msgid "Support Floor Line Distance"
msgstr "Distancia de línea del suelo de soporte"

msgctxt "support_bottom_line_width label"
msgid "Support Floor Line Width"
msgstr "Ancho de línea del suelo de soporte"

msgctxt "support_bottom_pattern label"
msgid "Support Floor Pattern"
msgstr "Patrón del suelo del soporte"

msgctxt "speed_support_bottom label"
msgid "Support Floor Speed"
msgstr "Velocidad del suelo del soporte"

msgctxt "support_bottom_height label"
msgid "Support Floor Thickness"
msgstr "Grosor del suelo del soporte"

msgctxt "support_material_flow label"
msgid "Support Flow"
msgstr "Flujo de soporte"

msgctxt "support_offset label"
msgid "Support Horizontal Expansion"
msgstr "Expansión horizontal del soporte"

msgctxt "acceleration_support_infill label"
msgid "Support Infill Acceleration"
msgstr "Aceleración de relleno de soporte"

msgctxt "support_infill_extruder_nr label"
msgid "Support Infill Extruder"
msgstr "Extrusor del relleno de soporte"

msgctxt "jerk_support_infill label"
msgid "Support Infill Jerk"
msgstr "Impulso de relleno de soporte"

msgctxt "support_infill_sparse_thickness label"
msgid "Support Infill Layer Thickness"
msgstr "Grosor de la capa de relleno de soporte"

msgctxt "support_infill_angles label"
msgid "Support Infill Line Directions"
msgstr "Dirección de línea de relleno de soporte"

msgctxt "speed_support_infill label"
msgid "Support Infill Speed"
msgstr "Velocidad de relleno del soporte"

msgctxt "acceleration_support_interface label"
msgid "Support Interface Acceleration"
msgstr "Aceleración de interfaz de soporte"

msgctxt "support_interface_density label"
msgid "Support Interface Density"
msgstr "Densidad de la interfaz de soporte"

msgctxt "support_interface_extruder_nr label"
msgid "Support Interface Extruder"
msgstr "Extrusor de la interfaz de soporte"

msgctxt "support_interface_material_flow label"
msgid "Support Interface Flow"
msgstr "Flujo de interfaz de soporte"

msgctxt "support_interface_offset label"
msgid "Support Interface Horizontal Expansion"
msgstr "Expansión horizontal de la interfaz de soporte"

msgctxt "jerk_support_interface label"
msgid "Support Interface Jerk"
msgstr "Impulso de interfaz de soporte"

msgctxt "support_interface_angles label"
msgid "Support Interface Line Directions"
msgstr "Direcciones de línea de interfaz de soporte"

msgctxt "support_interface_line_width label"
msgid "Support Interface Line Width"
msgstr "Ancho de línea de interfaz de soporte"

msgctxt "support_interface_pattern label"
msgid "Support Interface Pattern"
msgstr "Patrón de la interfaz de soporte"

msgctxt "support_interface_priority label"
msgid "Support Interface Priority"
msgstr "Prioridad de la interfaz de soporte"

msgctxt "support_interface_skip_height label"
msgid "Support Interface Resolution"
msgstr "Resolución de la interfaz de soporte"

msgctxt "speed_support_interface label"
msgid "Support Interface Speed"
msgstr "Velocidad de interfaz del soporte"

msgctxt "support_interface_height label"
msgid "Support Interface Thickness"
msgstr "Grosor de la interfaz del soporte"

msgctxt "support_interface_wall_count label"
msgid "Support Interface Wall Line Count"
msgstr "Recuento de líneas de pared de la interfaz de soporte"

msgctxt "jerk_support label"
msgid "Support Jerk"
msgstr "Impulso de soporte"

msgctxt "support_join_distance label"
msgid "Support Join Distance"
msgstr "Distancia de unión del soporte"

msgctxt "support_line_distance label"
msgid "Support Line Distance"
msgstr "Distancia de línea del soporte"

msgctxt "support_line_width label"
msgid "Support Line Width"
msgstr "Ancho de línea de soporte"

msgctxt "support_mesh label"
msgid "Support Mesh"
msgstr "Malla de soporte"

msgctxt "support_angle label"
msgid "Support Overhang Angle"
msgstr "Ángulo de voladizo del soporte"

msgctxt "support_pattern label"
msgid "Support Pattern"
msgstr "Patrón del soporte"

msgctxt "support_type label"
msgid "Support Placement"
msgstr "Colocación del soporte"

msgctxt "acceleration_support_roof label"
msgid "Support Roof Acceleration"
msgstr "Aceleración del techo del soporte"

msgctxt "support_roof_density label"
msgid "Support Roof Density"
msgstr "Densidad del techo del soporte"

msgctxt "support_roof_extruder_nr label"
msgid "Support Roof Extruder"
msgstr "Extrusor del techo del soporte"

msgctxt "support_roof_material_flow label"
msgid "Support Roof Flow"
msgstr "Flujo de techo de soporte"

msgctxt "support_roof_offset label"
msgid "Support Roof Horizontal Expansion"
msgstr "Expansión horizontal de los techos del soporte"

msgctxt "jerk_support_roof label"
msgid "Support Roof Jerk"
msgstr "Impulso del techo del soporte"

msgctxt "support_roof_angles label"
msgid "Support Roof Line Directions"
msgstr "Direcciones de línea del techo de soporte"

msgctxt "support_roof_line_distance label"
msgid "Support Roof Line Distance"
msgstr "Distancia de línea del techo del soporte"

msgctxt "support_roof_line_width label"
msgid "Support Roof Line Width"
msgstr "Ancho de línea del techo de soporte"

msgctxt "support_roof_pattern label"
msgid "Support Roof Pattern"
msgstr "Patrón del techo del soporte"

msgctxt "speed_support_roof label"
msgid "Support Roof Speed"
msgstr "Velocidad del techo del soporte"

msgctxt "support_roof_height label"
msgid "Support Roof Thickness"
msgstr "Grosor del techo del soporte"

msgctxt "support_roof_wall_count label"
msgid "Support Roof Wall Line Count"
msgstr "Recuento de líneas de pared del techo de soporte"

msgctxt "speed_support label"
msgid "Support Speed"
msgstr "Velocidad de soporte"

msgctxt "support_bottom_stair_step_height label"
msgid "Support Stair Step Height"
msgstr "Altura del escalón de la escalera del soporte"

msgctxt "support_bottom_stair_step_width label"
msgid "Support Stair Step Maximum Width"
msgstr "Ancho máximo del escalón de la escalera del soporte"

msgctxt "support_bottom_stair_step_min_slope label"
msgid "Support Stair Step Minimum Slope Angle"
msgstr "Ángulo de pendiente mínimo del escalón de la escalera de soporte"

msgctxt "support_structure label"
msgid "Support Structure"
msgstr "Estructura de soporte"

msgctxt "support_top_distance label"
msgid "Support Top Distance"
msgstr "Distancia superior del soporte"

msgctxt "support_wall_count label"
msgid "Support Wall Line Count"
msgstr "Recuento de líneas de pared del soporte"

msgctxt "support_xy_distance label"
msgid "Support X/Y Distance"
msgstr "Distancia X/Y del soporte"

msgctxt "support_z_distance label"
msgid "Support Z Distance"
msgstr "Distancia en Z del soporte"

msgctxt "support_interface_priority option support_lines_overwrite_interface_area"
msgid "Support lines preferred"
msgstr "Líneas de soporte preferidas"

msgctxt "support_interface_priority option support_area_overwrite_interface_area"
msgid "Support preferred"
msgstr "Soporte preferido"

msgctxt "support_supported_skin_fan_speed label"
msgid "Supported Skin Fan Speed"
msgstr "Velocidad del ventilador para forro con soporte"

msgctxt "magic_mesh_surface_mode option surface"
msgid "Surface"
msgstr "Superficie"

msgctxt "material_surface_energy label"
msgid "Surface Energy"
msgstr "Energía de la superficie"

msgctxt "magic_mesh_surface_mode label"
msgid "Surface Mode"
msgstr "Modo de superficie"

msgctxt "material_adhesion_tendency description"
msgid "Surface adhesion tendency."
msgstr "Tendencia de adherencia de la superficie."

msgctxt "material_surface_energy description"
msgid "Surface energy."
msgstr "Energía de la superficie."

msgctxt "brim_smart_ordering description"
msgid "Swap print order of the innermost and second innermost brim lines. This improves brim removal."
msgstr "Intercambie el orden de impresión de las líneas más internas y del segundo borde más interno. De ese modo se mejora la eliminación del borde."

msgctxt "alternate_carve_order description"
msgid "Switch to which mesh intersecting volumes will belong with every layer, so that the overlapping meshes become interwoven. Turning this setting off will cause one of the meshes to obtain all of the volume in the overlap, while it is removed from the other meshes."
msgstr "Cambiar la malla a la que pertenecerán los volúmenes que se cruzan en cada capa, de forma que las mallas superpuestas se entrelacen. Desactivar esta opción dará lugar a que una de las mallas reciba todo el volumen de la superposición y que este se elimine de las demás mallas."

msgctxt "adaptive_layer_height_threshold description"
msgid "Target horizontal distance between two adjacent layers. Reducing this setting causes thinner layers to be used to bring the edges of the layers closer together."
msgstr "Distancia horizontal objetivo entre dos capas adyacentes. Si se reduce este ajuste, se tendrán que utilizar capas más finas para acercar más los bordes de las capas."

msgctxt "layer_start_x description"
msgid "The X coordinate of the position near where to find the part to start printing each layer."
msgstr "Coordenada X de la posición cerca de donde se encuentra la pieza para comenzar a imprimir cada capa."

msgctxt "z_seam_x description"
msgid "The X coordinate of the position near where to start printing each part in a layer."
msgstr "Coordenada X de la posición cerca de donde se comienza a imprimir cada parte en una capa."

msgctxt "extruder_prime_pos_x description"
msgid "The X coordinate of the position where the nozzle primes at the start of printing."
msgstr "Coordenada X de la posición en la que la tobera se coloca al inicio de la impresión."

msgctxt "layer_start_y description"
msgid "The Y coordinate of the position near where to find the part to start printing each layer."
msgstr "Coordenada Y de la posición cerca de donde se encuentra la pieza para comenzar a imprimir cada capa."

msgctxt "z_seam_y description"
msgid "The Y coordinate of the position near where to start printing each part in a layer."
msgstr "Coordenada Y de la posición cerca de donde se comienza a imprimir cada parte en una capa."

msgctxt "extruder_prime_pos_y description"
msgid "The Y coordinate of the position where the nozzle primes at the start of printing."
msgstr "Coordenada Y de la posición en la que la tobera se coloca al inicio de la impresión."

msgctxt "extruder_prime_pos_z description"
msgid "The Z coordinate of the position where the nozzle primes at the start of printing."
msgstr "Coordenada Z de la posición en la que la tobera queda preparada al inicio de la impresión."

msgctxt "acceleration_print_layer_0 description"
msgid "The acceleration during the printing of the initial layer."
msgstr "Aceleración durante la impresión de la capa inicial."

msgctxt "acceleration_layer_0 description"
msgid "The acceleration for the initial layer."
msgstr "Aceleración de la capa inicial."

msgctxt "acceleration_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "Aceleración de los movimientos de desplazamiento de la capa inicial."

msgctxt "jerk_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "Aceleración de los movimientos de desplazamiento de la capa inicial."

msgctxt "acceleration_wall_x description"
msgid "The acceleration with which all inner walls are printed."
msgstr "Aceleración a la que se imprimen las paredes interiores."

msgctxt "acceleration_infill description"
msgid "The acceleration with which infill is printed."
msgstr "Aceleración a la que se imprime el relleno."

msgctxt "acceleration_ironing description"
msgid "The acceleration with which ironing is performed."
msgstr "La aceleración a la que se produce el alisado."

msgctxt "acceleration_print description"
msgid "The acceleration with which printing happens."
msgstr "Aceleración a la que se realiza la impresión."

msgctxt "raft_base_acceleration description"
msgid "The acceleration with which the base raft layer is printed."
msgstr "Aceleración a la que se imprime la capa base de la balsa."

msgctxt "acceleration_support_bottom description"
msgid "The acceleration with which the floors of support are printed. Printing them at lower acceleration can improve adhesion of support on top of your model."
msgstr "Aceleración a la que se imprimen los suelos del soporte. Imprimirlos a una aceleración inferior puede mejorar la adhesión de soporte en la parte superior del modelo."

msgctxt "acceleration_support_infill description"
msgid "The acceleration with which the infill of support is printed."
msgstr "Aceleración a la que se imprime el relleno de soporte."

msgctxt "raft_interface_acceleration description"
msgid "The acceleration with which the middle raft layer is printed."
msgstr "Aceleración a la que se imprime la capa intermedia de la balsa."

msgctxt "acceleration_wall_0 description"
msgid "The acceleration with which the outermost walls are printed."
msgstr "Aceleración a la que se imprimen las paredes exteriores."

msgctxt "acceleration_prime_tower description"
msgid "The acceleration with which the prime tower is printed."
msgstr "Aceleración a la que se imprime la torre auxiliar."

msgctxt "raft_acceleration description"
msgid "The acceleration with which the raft is printed."
msgstr "Aceleración a la que se imprime la balsa."

msgctxt "acceleration_support_interface description"
msgid "The acceleration with which the roofs and floors of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "Aceleración a la que se imprimen los techos y suelos del soporte. Imprimirlos a una aceleración inferior puede mejorar la calidad del voladizo."

msgctxt "acceleration_support_roof description"
msgid "The acceleration with which the roofs of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "Aceleración a la que se imprimen los techos del soporte. Imprimirlos a una aceleración inferior puede mejorar la calidad del voladizo."

msgctxt "acceleration_skirt_brim description"
msgid "The acceleration with which the skirt and brim are printed. Normally this is done with the initial layer acceleration, but sometimes you might want to print the skirt or brim at a different acceleration."
msgstr "Aceleración a la que se imprimen la falda y el borde. Normalmente, esto se hace a la aceleración de la capa inicial, pero a veces es posible que se prefiera imprimir la falda o el borde a una aceleración diferente."

msgctxt "acceleration_support description"
msgid "The acceleration with which the support structure is printed."
msgstr "Aceleración a la que se imprime la estructura de soporte."

msgctxt "raft_surface_acceleration description"
msgid "The acceleration with which the top raft layers are printed."
msgstr "Aceleración a la que se imprimen las capas superiores de la balsa."

msgctxt "acceleration_wall_x_roofing description"
msgid "The acceleration with which the top surface inner walls are printed."
msgstr "La aceleración con la que se imprimen las paredes internas de la superficie superior."

msgctxt "acceleration_wall_0_roofing description"
msgid "The acceleration with which the top surface outermost walls are printed."
msgstr "La aceleración con la que se imprimen las paredes más externas de la superficie superior."

msgctxt "acceleration_wall description"
msgid "The acceleration with which the walls are printed."
msgstr "Aceleración a la que se imprimen las paredes."

msgctxt "acceleration_roofing description"
msgid "The acceleration with which top surface skin layers are printed."
msgstr "Aceleración a la que se imprimen las capas de la superficie superior del forro."

msgctxt "acceleration_topbottom description"
msgid "The acceleration with which top/bottom layers are printed."
msgstr "Aceleración a la que se imprimen las capas superiores/inferiores."

msgctxt "acceleration_travel description"
msgid "The acceleration with which travel moves are made."
msgstr "Aceleración a la que se realizan los movimientos de desplazamiento."

msgctxt "ironing_flow description"
msgid "The amount of material, relative to a normal skin line, to extrude during ironing. Keeping the nozzle filled helps filling some of the crevices of the top surface, but too much results in overextrusion and blips on the side of the surface."
msgstr "Cantidad de material (relativa a la línea del forro normal) que se extruye durante el alisado. Dejar la tobera llena permite rellenar algunas grietas de la superficie, pero llenarla demasiado puede provocar la sobrextrusión y afectar a la superficie."

msgctxt "infill_overlap description"
msgid "The amount of overlap between the infill and the walls as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "La cantidad de superposición entre el relleno y las paredes son un porcentaje del ancho de la línea de relleno. Una ligera superposición permite que las paredes estén firmemente unidas al relleno."

msgctxt "infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Cantidad de superposición entre el relleno y las paredes. Una ligera superposición permite que las paredes conecten firmemente con el relleno."

msgctxt "switch_extruder_retraction_amount description"
msgid "The amount of retraction when switching extruders. Set to 0 for no retraction at all. This should generally be the same as the length of the heat zone."
msgstr "Distancia de la retracción al cambiar los extrusores. Utilice el valor 0 para que no haya retracción. Por norma general, este valor debe ser igual a la longitud de la zona de calentamiento."

msgctxt "machine_nozzle_expansion_angle description"
msgid "The angle between the horizontal plane and the conical part right above the tip of the nozzle."
msgstr "Ángulo entre el plano horizontal y la parte cónica que hay justo encima de la punta de la tobera."

msgctxt "support_tower_roof_angle description"
msgid "The angle of a rooftop of a tower. A higher value results in pointed tower roofs, a lower value results in flattened tower roofs."
msgstr "Ángulo del techo superior de una torre. Un valor más alto da como resultado techos de torre en punta, un valor más bajo da como resultado techos de torre planos."

msgctxt "mold_angle description"
msgid "The angle of overhang of the outer walls created for the mold. 0° will make the outer shell of the mold vertical, while 90° will make the outside of the model follow the contour of the model."
msgstr "Ángulo del voladizo de las paredes exteriores creado para el molde. 0º hará el perímetro exterior del molde vertical, mientras que 90º hará el exterior del modelo seguido del contorno del modelo."

msgctxt "support_tree_branch_diameter_angle description"
msgid "The angle of the branches' diameter as they gradually become thicker towards the bottom. An angle of 0 will cause the branches to have uniform thickness over their length. A bit of an angle can increase stability of the tree support."
msgstr "El ángulo del diámetro de las ramas es gradualmente más alto según se acercan a la base. Un ángulo de 0 ocasionará que las ramas tengan un grosor uniforme en toda su longitud. Un poco de ángulo puede aumentar la estabilidad del soporte en árbol."

msgctxt "support_conical_angle description"
msgid "The angle of the tilt of conical support. With 0 degrees being vertical, and 90 degrees being horizontal. Smaller angles cause the support to be more sturdy, but consist of more material. Negative angles cause the base of the support to be wider than the top."
msgstr "Ángulo de inclinación del soporte cónico. Donde 0 grados es vertical y 90 grados es horizontal. Cuanto más pequeños son los ángulos, más robusto es el soporte, pero consta de más material. Los ángulos negativos hacen que la base del soporte sea más ancha que la parte superior."

msgctxt "magic_fuzzy_skin_point_density description"
msgid "The average density of points introduced on each polygon in a layer. Note that the original points of the polygon are discarded, so a low density results in a reduction of the resolution."
msgstr "Densidad media de los puntos introducidos en cada polígono en una capa. Tenga en cuenta que los puntos originales del polígono se descartan, así que una baja densidad produce una reducción de la resolución."

msgctxt "magic_fuzzy_skin_point_dist description"
msgid "The average distance between the random points introduced on each line segment. Note that the original points of the polygon are discarded, so a high smoothness results in a reduction of the resolution. This value must be higher than half the Fuzzy Skin Thickness."
msgstr "Distancia media entre los puntos aleatorios introducidos en cada segmento de línea. Tenga en cuenta que los puntos originales del polígono se descartan, así que un suavizado alto produce una reducción de la resolución. Este valor debe ser mayor que la mitad del grosor del forro difuso."

msgctxt "machine_acceleration description"
msgid "The default acceleration of print head movement."
msgstr "Aceleración predeterminada del movimiento del cabezal de impresión."

msgctxt "default_material_print_temperature description"
msgid "The default temperature used for printing. This should be the \"base\" temperature of a material. All other print temperatures should use offsets based on this value"
msgstr "La temperatura predeterminada que se utiliza para imprimir. Debería ser la temperatura básica del material. Las demás temperaturas de impresión deberían calcularse a partir de este valor"

msgctxt "default_material_bed_temperature description"
msgid "The default temperature used for the heated build plate. This should be the \"base\" temperature of a build plate. All other print temperatures should use offsets based on this value"
msgstr "La temperatura predeterminada que se utiliza en placa de impresión caliente. Debería ser la temperatura básica de una placa de impresión. Las demás temperaturas de impresión deberían calcularse a partir de este valor"

msgctxt "bridge_skin_density description"
msgid "The density of the bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "Densidad de la capa de forro del puente. Un valor inferior a 100 aumentará los huecos entre las líneas del forro."

msgctxt "support_bottom_density description"
msgid "The density of the floors of the support structure. A higher value results in better adhesion of the support on top of the model."
msgstr "Densidad de los suelos de la estructura de soporte. Un valor superior da como resultado una mejor adhesión del soporte en la parte superior del modelo."

msgctxt "support_roof_density description"
msgid "The density of the roofs of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Densidad de los techos de la estructura de soporte. Un valor superior da como resultado mejores voladizos pero los soportes son más difíciles de retirar."

msgctxt "bridge_skin_density_2 description"
msgid "The density of the second bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "Densidad de la segunda capa de forro del puente. Un valor inferior a 100 aumentará los huecos entre las líneas del forro."

msgctxt "bridge_skin_density_3 description"
msgid "The density of the third bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "Densidad de la tercera capa de forro del puente. Un valor inferior a 100 aumentará los huecos entre las líneas del forro."

msgctxt "machine_depth description"
msgid "The depth (Y-direction) of the printable area."
msgstr "Profundidad (dimensión sobre el eje Y) del área de impresión."

msgctxt "support_tower_diameter description"
msgid "The diameter of a special tower."
msgstr "Diámetro de una torre especial."

msgctxt "support_tree_branch_diameter description"
msgid "The diameter of the thinnest branches of tree support. Thicker branches are more sturdy. Branches towards the base will be thicker than this."
msgstr "El diámetro de las ramas más finas del soporte en árbol. Cuanto más gruesas sean las ramas, más robustas serán. Las ramas que estén cerca de la base serán más gruesas que esto."

msgctxt "support_tree_tip_diameter description"
msgid "The diameter of the top of the tip of the branches of tree support."
msgstr "El diámetro de la parte superior de la punta de las ramas de soporte en árbol."

msgctxt "machine_feeder_wheel_diameter description"
msgid "The diameter of the wheel that drives the material in the feeder."
msgstr "El diámetro de la rueda que dirige el material hacia el alimentador."

msgctxt "support_tree_max_diameter description"
msgid "The diameter of the widest branches of tree support. A thicker trunk is more sturdy; a thinner trunk takes up less space on the build plate."
msgstr "El diámetro de las ramas más anchas del soporte en árbol. Un tronco más grueso es más resistente, pero uno más delgado ocupa menos espacio en la placa de impresión."

msgctxt "adaptive_layer_height_variation_step description"
msgid "The difference in height of the next layer height compared to the previous one."
msgstr "La diferencia de altura de la siguiente altura de capa en comparación con la anterior."

msgctxt "ironing_line_spacing description"
msgid "The distance between the lines of ironing."
msgstr "Distancia entre las líneas del alisado."

msgctxt "travel_avoid_distance description"
msgid "The distance between the nozzle and already printed parts when avoiding during travel moves."
msgstr "Distancia entre la tobera y las partes ya impresas, cuando se evita durante movimientos de desplazamiento."

msgctxt "raft_base_line_spacing description"
msgid "The distance between the raft lines for the base raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "Distancia entre las líneas de balsa para la capa base de la balsa. Un amplio espaciado facilita la retirada de la balsa de la placa de impresión."

msgctxt "raft_interface_line_spacing description"
msgid "The distance between the raft lines for the middle raft layer. The spacing of the middle should be quite wide, while being dense enough to support the top raft layers."
msgstr "Distancia entre las líneas de la balsa para la capa intermedia de la balsa. La espaciado del centro debería ser bastante amplio, pero lo suficientemente denso como para soportar las capas superiores de la balsa."

msgctxt "raft_surface_line_spacing description"
msgid "The distance between the raft lines for the top raft layers. The spacing should be equal to the line width, so that the surface is solid."
msgstr "Distancia entre las líneas de la balsa para las capas superiores de la balsa. La separación debe ser igual a la ancho de línea para producir una superficie sólida."

msgctxt "prime_tower_raft_base_line_spacing description"
msgid "The distance between the raft lines for the unique prime tower raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "La distancia entre las líneas del raft para la capa única del raft de la torre de cebado. Un espaciado amplio facilita la eliminación del raft de la placa de construcción."

msgctxt "interlocking_depth description"
msgid "The distance from the boundary between models to generate interlocking structure, measured in cells. Too few cells will result in poor adhesion."
msgstr "La distancia del límite entre los modelos para generar una estructura entrelazada, medida en celdas. Un número demasiado bajo de celdas provocará una adhesión deficiente."

msgctxt "brim_width description"
msgid "The distance from the model to the outermost brim line. A larger brim enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "Distancia desde el modelo hasta la línea del borde exterior. Un borde mayor mejora la adhesión a la plataforma de impresión, pero también reduce el área de impresión efectiva."

msgctxt "interlocking_boundary_avoidance description"
msgid "The distance from the outside of a model where interlocking structures will not be generated, measured in cells."
msgstr "La distancia desde el exterior de un modelo en el que no se generarán estructuras entrelazadas, medida en celdas."

msgctxt "machine_heat_zone_length description"
msgid "The distance from the tip of the nozzle in which heat from the nozzle is transferred to the filament."
msgstr "Distancia desde la punta de la tobera que transfiere calor al filamento."

msgctxt "bottom_skin_expand_distance description"
msgid "The distance the bottom skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the skin adhere better to the walls on the layer below. Lower values save amount of material used."
msgstr "La distancia a la que los forros inferiores se expanden en el relleno. Los valores superiores hacen que el forro se adhiera mejor al patrón de relleno y que el forro se adhiera mejor a las paredes de la capa inferior. Los valores inferiores ahorran material."

msgctxt "expand_skins_expand_distance description"
msgid "The distance the skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on neighboring layers adhere better to the skin. Lower values save amount of material used."
msgstr "La distancia a la que los forros se expanden en el relleno. Los valores superiores hacen que el forro se adhiera mejor al patrón de relleno y que las paredes de las capas vecinas se adhieran mejor al forro. Los valores inferiores ahorran material."

msgctxt "top_skin_expand_distance description"
msgid "The distance the top skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on the layer above adhere better to the skin. Lower values save amount of material used."
msgstr "La distancia a la que los forros superiores se expanden en el relleno. Los valores superiores hacen que el forro se adhiera mejor al patrón de relleno y que las paredes de la capa superior se adhieran mejor al forro. Los valores inferiores ahorran material."

msgctxt "wipe_move_distance description"
msgid "The distance to move the head back and forth across the brush."
msgstr "La distancia para mover el cabezal hacia adelante y hacia atrás a lo largo del cepillo."

msgctxt "lightning_infill_prune_angle description"
msgid "The endpoints of infill lines are shortened to save on material. This setting is the angle of overhang of the endpoints of these lines."
msgstr "Los extremos de las líneas de relleno se acortan para ahorrar material. Esta configuración es el ángulo de voladizo de los extremos de estas líneas."

msgctxt "material_extrusion_cool_down_speed description"
msgid "The extra speed by which the nozzle cools while extruding. The same value is used to signify the heat up speed lost when heating up while extruding."
msgstr "Velocidad adicional a la que se enfría la tobera durante la extrusión. El mismo valor se utiliza para indicar la velocidad de calentamiento perdido cuando se calienta durante la extrusión."

msgctxt "support_extruder_nr_layer_0 description"
msgid "The extruder train to use for printing the first layer of support infill. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir la primera capa del relleno de soporte. Se emplea en la extrusión múltiple."

msgctxt "raft_base_extruder_nr description"
msgid "The extruder train to use for printing the first layer of the raft. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir la primera capa de la balsa. Se emplea en la extrusión múltiple."

msgctxt "support_bottom_extruder_nr description"
msgid "The extruder train to use for printing the floors of the support. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir los suelos del soporte. Se emplea en la extrusión múltiple."

msgctxt "support_infill_extruder_nr description"
msgid "The extruder train to use for printing the infill of the support. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir el relleno del soporte. Se emplea en la extrusión múltiple."

msgctxt "raft_interface_extruder_nr description"
msgid "The extruder train to use for printing the middle layer of the raft. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir la capa media de la balsa. Se emplea en la extrusión múltiple."

msgctxt "support_interface_extruder_nr description"
msgid "The extruder train to use for printing the roofs and floors of the support. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir los techos y suelos del soporte. Se emplea en la extrusión múltiple."

msgctxt "support_roof_extruder_nr description"
msgid "The extruder train to use for printing the roofs of the support. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir los techos del soporte. Se emplea en la extrusión múltiple."

msgctxt "skirt_brim_extruder_nr description"
msgid "The extruder train to use for printing the skirt or brim. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir la falda o el borde. Se emplea en la extrusión múltiple."

msgctxt "adhesion_extruder_nr description"
msgid "The extruder train to use for printing the skirt/brim/raft. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir la falda/borde/balsa. Se emplea en la extrusión múltiple."

msgctxt "support_extruder_nr description"
msgid "The extruder train to use for printing the support. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir el soporte. Se emplea en la extrusión múltiple."

msgctxt "raft_surface_extruder_nr description"
msgid "The extruder train to use for printing the top layer(s) of the raft. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir la capa o capas superiores de la balsa. Se emplea en la extrusión múltiple."

msgctxt "infill_extruder_nr description"
msgid "The extruder train used for printing infill. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir el relleno. Se emplea en la extrusión múltiple."

msgctxt "wall_x_extruder_nr description"
msgid "The extruder train used for printing the inner walls. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir las paredes interiores. Se emplea en la extrusión múltiple."

msgctxt "wall_0_extruder_nr description"
msgid "The extruder train used for printing the outer wall. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir la pared exterior. Se emplea en la extrusión múltiple."

msgctxt "top_bottom_extruder_nr description"
msgid "The extruder train used for printing the top and bottom skin. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir el forro superior e inferior. Se emplea en la extrusión múltiple."

msgctxt "roofing_extruder_nr description"
msgid "The extruder train used for printing the top most skin. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir el nivel superior del forro. Se emplea en la extrusión múltiple."

msgctxt "wall_extruder_nr description"
msgid "The extruder train used for printing the walls. This is used in multi-extrusion."
msgstr "El tren extrusor que se utiliza para imprimir paredes. Se emplea en la extrusión múltiple."

msgctxt "raft_base_fan_speed description"
msgid "The fan speed for the base raft layer."
msgstr "Velocidad del ventilador para la capa base de la balsa."

msgctxt "raft_interface_fan_speed description"
msgid "The fan speed for the middle raft layer."
msgstr "Velocidad del ventilador para la capa intermedia de la balsa."

msgctxt "raft_fan_speed description"
msgid "The fan speed for the raft."
msgstr "Velocidad del ventilador para la balsa."

msgctxt "raft_surface_fan_speed description"
msgid "The fan speed for the top raft layers."
msgstr "Velocidad del ventilador para las capas superiores de la balsa."

msgctxt "cross_infill_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the infill of the print."
msgstr "La ubicación del archivo de una imagen de la que los valores de brillo determinan la densidad mínima en la ubicación correspondiente en el relleno de la impresión."

msgctxt "cross_support_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the support."
msgstr "La ubicación del archivo de una imagen de la que los valores de brillo determinan la densidad mínima en la ubicación correspondiente del soporte."

msgctxt "speed_slowdown_layers description"
msgid "The first few layers are printed slower than the rest of the model, to get better adhesion to the build plate and improve the overall success rate of prints. The speed is gradually increased over these layers."
msgstr "Las primeras capas se imprimen más lentamente que el resto del modelo para obtener una mejor adhesión a la placa de impresión y mejorar la tasa de éxito global de las impresiones. La velocidad aumenta gradualmente en estas capas."

msgctxt "raft_airgap description"
msgid "The gap between the final raft layer and the first layer of the model. Only the first layer is raised by this amount to lower the bonding between the raft layer and the model. Makes it easier to peel off the raft."
msgstr "Hueco entre la capa final de la balsa y la primera capa del modelo. Solo la primera capa se eleva según este valor para reducir la unión entre la capa de la balsa y el modelo y que sea más fácil despegar la balsa."

msgctxt "machine_height description"
msgid "The height (Z-direction) of the printable area."
msgstr "Altura (dimensión sobre el eje Z) del área de impresión."

msgctxt "mold_roof_height description"
msgid "The height above horizontal parts in your model which to print mold."
msgstr "Altura por encima de las piezas horizontales del modelo del que imprimir el molde."

msgctxt "cool_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "Altura a la que giran los ventiladores en la velocidad normal del ventilador. En las capas más bajas, la velocidad del ventilador aumenta gradualmente desde la velocidad inicial del ventilador hasta la velocidad normal del ventilador."

msgctxt "gantry_height description"
msgid "The height difference between the tip of the nozzle and the gantry system (X and Y axes)."
msgstr "Diferencia de altura entre la punta de la tobera y el sistema del puente (ejes X e Y)."

msgctxt "machine_nozzle_head_distance description"
msgid "The height difference between the tip of the nozzle and the lowest part of the print head."
msgstr "Diferencia de altura entre la punta de la tobera y la parte más baja del cabezal de impresión."

msgctxt "retraction_hop_after_extruder_switch_height description"
msgid "The height difference when performing a Z Hop after extruder switch."
msgstr "Diferencia de altura cuando se realiza un salto en Z después de un cambio de extrusor."

msgctxt "retraction_hop description"
msgid "The height difference when performing a Z Hop."
msgstr "Diferencia de altura cuando se realiza un salto en Z."

msgctxt "wipe_hop_amount description"
msgid "The height difference when performing a Z Hop."
msgstr "Diferencia de altura cuando se realiza un salto en Z."

msgctxt "layer_height description"
msgid "The height of each layer in mm. Higher values produce faster prints in lower resolution, lower values produce slower prints in higher resolution."
msgstr "Altura de cada capa en mm. Los valores más altos producen impresiones más rápidas con una menor resolución, los valores más bajos producen impresiones más lentas con una mayor resolución."

msgctxt "gradual_infill_step_height description"
msgid "The height of infill of a given density before switching to half the density."
msgstr "Altura de un relleno de determinada densidad antes de cambiar a la mitad de la densidad."

msgctxt "gradual_support_infill_step_height description"
msgid "The height of support infill of a given density before switching to half the density."
msgstr "Altura del relleno de soporte de una determinada densidad antes de cambiar a la mitad de la densidad."

msgctxt "interlocking_beam_layer_count description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "La altura de los haces de la estructura entrelazada, medida en número de capas. Cuantas menos capas, mayor resistencia, pero más susceptible a los defectos."

msgctxt "interlocking_orientation description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "La altura de los haces de la estructura entrelazada, medida en número de capas. Cuantas menos capas, mayor resistencia, pero más susceptible a los defectos."

msgctxt "layer_height_0 description"
msgid "The height of the initial layer in mm. A thicker initial layer makes adhesion to the build plate easier."
msgstr "Altura de capa inicial en mm. Una capa inicial más gruesa se adhiere a la placa de impresión con mayor facilidad."

msgctxt "prime_tower_base_height description"
msgid "The height of the prime tower base. Increasing this value will result in a more sturdy prime tower because the base will be wider. If this setting is too low, the prime tower will not have a sturdy base."
msgstr "La altura de la base de la torre de cebado. Aumentar este valor resultará en una torre de cebado más robusta porque la base será más ancha. Si esta configuración es demasiado baja, la torre de cebado no tendrá una base resistente."

msgctxt "support_bottom_stair_step_height description"
msgid "The height of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures. Set to zero to turn off the stair-like behaviour."
msgstr "Altura de los escalones de la parte inferior de la escalera del soporte que descansa sobre el modelo. Un valor más bajo hace que el soporte sea difícil de retirar pero valores demasiado altos pueden producir estructuras del soporte inestables. Configúrelo en cero para desactivar el comportamiento de escalera."

msgctxt "brim_gap description"
msgid "The horizontal distance between the first brim line and the outline of the first layer of the print. A small gap can make the brim easier to remove while still providing the thermal benefits."
msgstr "La distancia horizontal entre la primera línea de borde y el contorno de la primera capa de la impresión. Un pequeño orificio puede facilitar la eliminación del borde al tiempo que proporciona ventajas térmicas."

msgctxt "skirt_gap description"
msgid ""
"The horizontal distance between the skirt and the first layer of the print.\n"
"This is the minimum distance. Multiple skirt lines will extend outwards from this distance."
msgstr ""
"La distancia horizontal entre la falda y la primera capa de la impresión.\n"
"Se trata de la distancia mínima. Múltiples líneas de falda se extenderán hacia el exterior a partir de esta distancia."

msgctxt "lightning_infill_straightening_angle description"
msgid "The infill lines are straightened out to save on printing time. This is the maximum angle of overhang allowed across the length of the infill line."
msgstr "Las líneas de relleno se simplifican para ahorrar tiempo al imprimir. Este es el ángulo máximo permitido del voladizo sobre la longitud de la línea de relleno."

msgctxt "infill_offset_x description"
msgid "The infill pattern is moved this distance along the X axis."
msgstr "El patrón de relleno se mueve esta distancia a lo largo del eje X."

msgctxt "infill_offset_y description"
msgid "The infill pattern is moved this distance along the Y axis."
msgstr "El patrón de relleno se mueve esta distancia a lo largo del eje Y."

msgctxt "machine_nozzle_size description"
msgid "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size."
msgstr "Diámetro interior de la tobera. Cambie este ajuste cuando utilice un tamaño de tobera no estándar."

msgctxt "raft_base_jerk description"
msgid "The jerk with which the base raft layer is printed."
msgstr "Impulso con el que se imprime la capa base de la balsa."

msgctxt "raft_interface_jerk description"
msgid "The jerk with which the middle raft layer is printed."
msgstr "Impulso con el que se imprime la capa intermedia de la balsa."

msgctxt "raft_jerk description"
msgid "The jerk with which the raft is printed."
msgstr "Impulso con el que se imprime la balsa."

msgctxt "raft_surface_jerk description"
msgid "The jerk with which the top raft layers are printed."
msgstr "Impulso con el que se imprimen las capas superiores de la balsa."

msgctxt "bottom_skin_preshrink description"
msgid "The largest width of bottom skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing bottom skin at slanted surfaces in the model."
msgstr "Anchura máxima de las áreas inferiores de forro que se deben retirar. Todas las áreas de forro inferiores a este valor desaparecerán. Esto puede contribuir a limitar el tiempo y el material empleados en imprimir el forro inferior en las superficies inclinadas del modelo."

msgctxt "skin_preshrink description"
msgid "The largest width of skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top/bottom skin at slanted surfaces in the model."
msgstr "Anchura máxima de las áreas de forro que se deben retirar. Todas las áreas de forro inferiores a este valor desaparecerán. Esto puede contribuir a limitar el tiempo y el material empleados en imprimir el forro superior/inferior en las superficies inclinadas del modelo."

msgctxt "top_skin_preshrink description"
msgid "The largest width of top skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top skin at slanted surfaces in the model."
msgstr "Anchura máxima de las áreas superiores de forro que se deben retirar. Todas las áreas de forro inferiores a este valor desaparecerán. Esto puede contribuir a limitar el tiempo y el material empleados en imprimir el forro superior en las superficies inclinadas del modelo."

msgctxt "cool_fan_full_layer description"
msgid "The layer at which the fans spin on regular fan speed. If regular fan speed at height is set, this value is calculated and rounded to a whole number."
msgstr "Capa en la que los ventiladores giran a velocidad normal del ventilador. Si la velocidad normal del ventilador a altura está establecida, este valor se calcula y redondea a un número entero."

msgctxt "cool_min_layer_time_fan_speed_max description"
msgid "The layer time which sets the threshold between regular fan speed and maximum fan speed. Layers that print slower than this time use regular fan speed. For faster layers the fan speed gradually increases towards the maximum fan speed."
msgstr "Tiempo de capa que establece el umbral entre la velocidad normal y la máxima del ventilador. Las capas que se imprimen más despacio que este tiempo utilizan la velocidad de ventilador regular. Para las capas más rápidas el ventilador aumenta la velocidad gradualmente hacia la velocidad máxima del ventilador."

msgctxt "retraction_amount description"
msgid "The length of material retracted during a retraction move."
msgstr "Longitud del material retraído durante un movimiento de retracción."

msgctxt "prime_tower_base_curve_magnitude description"
msgid "The magnitude factor used for the slope of the prime tower base. If you increase this value, the base will become slimmer. If you decrease it, the base will become thicker."
msgstr "El factor de magnitud utilizado para la pendiente de la base de la torre de cebado. Si aumentas este valor, la base se volverá más delgada. Si lo disminuyes, la base se volverá más gruesa."

msgctxt "machine_buildplate_type description"
msgid "The material of the build plate installed on the printer."
msgstr "Material de la placa de impresión colocado en la impresora."

msgctxt "adaptive_layer_height_variation description"
msgid "The maximum allowed height different from the base layer height."
msgstr "La diferencia de altura máxima permitida en comparación con la altura de la capa base."

msgctxt "ooze_shield_angle description"
msgid "The maximum angle a part in the ooze shield will have. With 0 degrees being vertical, and 90 degrees being horizontal. A smaller angle leads to less failed ooze shields, but more material."
msgstr "Ángulo de separación máximo de la placa de rezumado. Un valor 0° significa vertical y un valor de 90°, horizontal. Un ángulo más pequeño resultará en menos placas de rezumado con errores, pero más material."

msgctxt "conical_overhang_angle description"
msgid "The maximum angle of overhangs after the they have been made printable. At a value of 0° all overhangs are replaced by a piece of model connected to the build plate, 90° will not change the model in any way."
msgstr "Ángulo máximo de los voladizos una vez que se han hecho imprimibles. Un valor de 0º hace que todos los voladizos sean reemplazados por una pieza del modelo conectada a la placa de impresión y un valor de 90º no cambiará el modelo."

msgctxt "support_tree_angle description"
msgid "The maximum angle of the branches while they grow around the model. Use a lower angle to make them more vertical and more stable. Use a higher angle to be able to have more reach."
msgstr "El ángulo máximo de las ramas mientras crecen alrededor del modelo. Utilice un ángulo más pequeño para hacerlas más verticales y más estables. Utilice un ángulo mayor para abarcar más."

msgctxt "conical_overhang_hole_size description"
msgid "The maximum area of a hole in the base of the model before it's removed by Make Overhang Printable.  Holes smaller than this will be retained.  A value of 0 mm² will fill all holes in the models base."
msgstr "El área máxima de un agujero en la base del modelo antes de que se elimine mediante la herramienta Convertir voladizo en imprimible.  Se conservarán los agujeros más pequeños.  Con un valor de 0 mm² se rellenan todos los agujeros de la base del modelo."

msgctxt "meshfix_maximum_deviation description"
msgid "The maximum deviation allowed when reducing the resolution for the Maximum Resolution setting. If you increase this, the print will be less accurate, but the g-code will be smaller. Maximum Deviation is a limit for Maximum Resolution, so if the two conflict the Maximum Deviation will always be held true."
msgstr "La desviación máxima permitida al reducir la resolución en el ajuste de la resolución máxima. Si se aumenta el valor, la impresión será menos precisa pero el GCode será más pequeño. La desviación máxima es un límite para la resolución máxima, por lo que si las dos entran en conflicto, la desviación máxima siempre tendrá prioridad."

msgctxt "support_join_distance description"
msgid "The maximum distance between support structures in the X/Y directions. When separate structures are closer together than this value, the structures merge into one."
msgstr "Distancia máxima entre las estructuras del soporte en las direcciones X/Y. Cuando las estructuras separadas están más cerca entre sí que este valor, se combinan en una."

msgctxt "flow_rate_max_extrusion_offset description"
msgid "The maximum distance in mm to move the filament to compensate for changes in flow rate."
msgstr "La distancia máxima en mm para mover el filamento con el fin de compensar los cambios en el caudal."

msgctxt "meshfix_maximum_extrusion_area_deviation description"
msgid "The maximum extrusion area deviation allowed when removing intermediate points from a straight line. An intermediate point may serve as width-changing point in a long straight line. Therefore, if it is removed, it will cause the line to have a uniform width and, as a result, lose (or gain) a bit of extrusion area. If you increase this you may notice slight under- (or over-) extrusion in between straight parallel walls, as more intermediate width-changing points will be allowed to be removed. Your print will be less accurate, but the g-code will be smaller."
msgstr "La desviación máxima del área de extrusión permitida al eliminar puntos intermedios de una línea recta. Un punto intermedio puede actuar como un punto de cambio de ancho en una línea recta larga. Por lo tanto, si se elimina, la línea tendrá un ancho uniforme y, como resultado, perderá (o ganará) área de extrusión. En caso de incremento, es posible que observe una extrusión leve por debajo (o por encima) entre paredes paralelas rectas, ya que será posible eliminar múltiples puntos de cambio de ancho intermedio. La impresión será menos precisa, pero el GCode será más pequeño."

msgctxt "jerk_print_layer_0 description"
msgid "The maximum instantaneous velocity change during the printing of the initial layer."
msgstr "Cambio en la velocidad instantánea máxima durante la impresión de la capa inicial."

msgctxt "jerk_print description"
msgid "The maximum instantaneous velocity change of the print head."
msgstr "Cambio en la velocidad instantánea máxima del cabezal de impresión."

msgctxt "jerk_ironing description"
msgid "The maximum instantaneous velocity change while performing ironing."
msgstr "Cambio en la velocidad instantánea máxima durante el alisado."

msgctxt "jerk_wall_x description"
msgid "The maximum instantaneous velocity change with which all inner walls are printed."
msgstr "Cambio en la velocidad instantánea máxima a la que se imprimen las paredes interiores."

msgctxt "jerk_infill description"
msgid "The maximum instantaneous velocity change with which infill is printed."
msgstr "Cambio en la velocidad instantánea máxima a la que se imprime el relleno."

msgctxt "jerk_support_bottom description"
msgid "The maximum instantaneous velocity change with which the floors of support are printed."
msgstr "Cambio en la velocidad instantánea máxima a la que se imprimen los suelos del soporte."

msgctxt "jerk_support_infill description"
msgid "The maximum instantaneous velocity change with which the infill of support is printed."
msgstr "Cambio en la velocidad instantánea máxima a la que se imprime el relleno de soporte."

msgctxt "jerk_wall_0 description"
msgid "The maximum instantaneous velocity change with which the outermost walls are printed."
msgstr "Cambio en la velocidad instantánea máxima a la que se imprimen las paredes exteriores."

msgctxt "jerk_prime_tower description"
msgid "The maximum instantaneous velocity change with which the prime tower is printed."
msgstr "Cambio en la velocidad instantánea máxima a la que se imprime la torre auxiliar."

msgctxt "jerk_support_interface description"
msgid "The maximum instantaneous velocity change with which the roofs and floors of support are printed."
msgstr "Cambio en la velocidad instantánea máxima a la que se imprimen los techos y suelos del soporte."

msgctxt "jerk_support_roof description"
msgid "The maximum instantaneous velocity change with which the roofs of support are printed."
msgstr "Cambio en la velocidad instantánea máxima a la que se imprimen los techos del soporte."

msgctxt "jerk_skirt_brim description"
msgid "The maximum instantaneous velocity change with which the skirt and brim are printed."
msgstr "Cambio en la velocidad instantánea máxima a la que se imprimen la falta y el borde."

msgctxt "jerk_support description"
msgid "The maximum instantaneous velocity change with which the support structure is printed."
msgstr "Cambio en la velocidad instantánea máxima a la que se imprime la estructura de soporte."

msgctxt "jerk_wall_x_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface inner walls are printed."
msgstr "El cambio máximo de velocidad instantánea con el que se imprimen las paredes exteriores más externas de la superficie superior."

msgctxt "jerk_wall_0_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface outermost walls are printed."
msgstr "El cambio máximo de velocidad instantánea con el que se imprimen las paredes interiores de la superficie superior."

msgctxt "jerk_wall description"
msgid "The maximum instantaneous velocity change with which the walls are printed."
msgstr "Cambio en la velocidad instantánea máxima a la que se imprimen las paredes."

msgctxt "jerk_roofing description"
msgid "The maximum instantaneous velocity change with which top surface skin layers are printed."
msgstr "Cambio en la velocidad instantánea máxima a la que se imprime la capa de la superficie superior del forro."

msgctxt "jerk_topbottom description"
msgid "The maximum instantaneous velocity change with which top/bottom layers are printed."
msgstr "Cambio en la velocidad instantánea máxima a la que se imprimen las capas superiores/inferiores."

msgctxt "jerk_travel description"
msgid "The maximum instantaneous velocity change with which travel moves are made."
msgstr "Cambio en la velocidad instantánea máxima a la que realizan los movimientos de desplazamiento."

msgctxt "machine_max_feedrate_x description"
msgid "The maximum speed for the motor of the X-direction."
msgstr "Velocidad máxima del motor de la dirección X."

msgctxt "machine_max_feedrate_y description"
msgid "The maximum speed for the motor of the Y-direction."
msgstr "Velocidad máxima del motor de la dirección Y."

msgctxt "machine_max_feedrate_z description"
msgid "The maximum speed for the motor of the Z-direction."
msgstr "Velocidad máxima del motor de la dirección Z."

msgctxt "machine_max_feedrate_e description"
msgid "The maximum speed of the filament."
msgstr "Velocidad máxima del filamento."

msgctxt "support_bottom_stair_step_width description"
msgid "The maximum width of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures."
msgstr "Ancho máximo de los escalones de la parte inferior de la escalera del soporte que descansa sobre el modelo. Un valor más bajo hace que el soporte sea difícil de retirar pero valores demasiado altos pueden producir estructuras del soporte inestables."

msgctxt "mold_width description"
msgid "The minimal distance between the outside of the mold and the outside of the model."
msgstr "Distancia mínima entre la parte exterior del molde y la parte exterior del modelo."

msgctxt "machine_minimum_feedrate description"
msgid "The minimal movement speed of the print head."
msgstr "Velocidad mínima de movimiento del cabezal de impresión."

msgctxt "material_initial_print_temperature description"
msgid "The minimal temperature while heating up to the Printing Temperature at which printing can already start."
msgstr "La temperatura mínima durante el calentamiento hasta alcanzar la temperatura de impresión a la cual puede comenzar la impresión."

msgctxt "machine_min_cool_heat_time_window description"
msgid "The minimal time an extruder has to be inactive before the nozzle is cooled. Only when an extruder is not used for longer than this time will it be allowed to cool down to the standby temperature."
msgstr "Tiempo mínimo que un extrusor debe permanecer inactivo antes de que la tobera se enfríe. Para que pueda enfriarse hasta alcanzar la temperatura en modo de espera, el extrusor deberá permanecer inactivo durante un tiempo superior al establecido."

msgctxt "infill_support_angle description"
msgid "The minimum angle of internal overhangs for which infill is added. At a value of 0° objects are totally filled with infill, 90° will not provide any infill."
msgstr "El ángulo mínimo de los voladizos internos para los que se agrega relleno. A partir de un valor de 0 º todos los objetos estarán totalmente rellenos, a 90 º no se proporcionará ningún relleno."

msgctxt "support_angle description"
msgid "The minimum angle of overhangs for which support is added. At a value of 0° all overhangs are supported, 90° will not provide any support."
msgstr "Ángulo mínimo de los voladizos para los que se agrega soporte. A partir de un valor de 0º todos los voladizos tendrán soporte, a 90º no se proporcionará ningún soporte."

msgctxt "retraction_min_travel description"
msgid "The minimum distance of travel needed for a retraction to happen at all. This helps to get fewer retractions in a small area."
msgstr "Distancia mínima de desplazamiento necesario para que no se produzca retracción alguna. Esto ayuda a conseguir un menor número de retracciones en un área pequeña."

msgctxt "skirt_brim_minimal_length description"
msgid "The minimum length of the skirt or brim. If this length is not reached by all skirt or brim lines together, more skirt or brim lines will be added until the minimum length is reached. Note: If the line count is set to 0 this is ignored."
msgstr "La longitud mínima de la falda o el borde. Si el número de líneas de falda o borde no alcanza esta longitud, se agregarán más líneas de falda o borde hasta alcanzar esta longitud mínima. Nota: Si el número de líneas está establecido en 0, esto se ignora."

msgctxt "min_odd_wall_line_width description"
msgid "The minimum line width for middle line gap filler polyline walls. This setting determines at which model thickness we switch from printing two wall lines, to printing two outer walls and a single central wall in the middle. A higher Minimum Odd Wall Line Width leads to a higher maximum even wall line width. The maximum odd wall line width is calculated as 2 * Minimum Even Wall Line Width."
msgstr "La anchura mínima de línea para las paredes tipo polilínea para rellenar el hueco de la línea central. Este parámetro determina a partir de qué grosor de modelo pasamos de imprimir dos líneas de pared a imprimir dos paredes exteriores y una sola pared central en el medio. Un ancho mínimo más alto de la línea de pared impar conduce a un ancho máximo más alto de la línea de pared par. El ancho máximo de línea de pared impar se calcula como el doble del ancho mínimo de línea de pared par."

msgctxt "min_even_wall_line_width description"
msgid "The minimum line width for normal polygonal walls. This setting determines at which model thickness we switch from printing a single thin wall line, to printing two wall lines. A higher Minimum Even Wall Line Width leads to a higher maximum odd wall line width. The maximum even wall line width is calculated as Outer Wall Line Width + 0.5 * Minimum Odd Wall Line Width."
msgstr "El ancho de línea mínimo para paredes poligonales normales. Este ajuste determina a qué espesor de modelo pasamos de imprimir una sola línea de perímetro delgada a imprimir dos líneas de perímetro. Un ancho mínimo más alto de la línea perimetral par conduce a un ancho máximo más alto de la línea perimetral impar. El ancho máximo de la línea perimetral par se calcula como el ancho de la línea perimetral exterior + 0,5 * el ancho mínimo de la línea perimetral impar."

msgctxt "cool_min_speed description"
msgid "The minimum print speed, despite slowing down due to the minimum layer time. When the printer would slow down too much, the pressure in the nozzle would be too low and result in bad print quality."
msgstr "Velocidad de impresión mínima, a pesar de ir más despacio debido al tiempo mínimo de capa. Cuando la impresora vaya demasiado despacio, la presión de la tobera puede ser demasiado baja y resultar en una impresión de mala calidad."

msgctxt "meshfix_maximum_resolution description"
msgid "The minimum size of a line segment after slicing. If you increase this, the mesh will have a lower resolution. This may allow the printer to keep up with the speed it has to process g-code and will increase slice speed by removing details of the mesh that it can't process anyway."
msgstr "El tamaño mínimo de un segmento de línea tras la segmentación. Si se aumenta, la resolución de la malla será menor. Esto puede permitir a la impresora mantener la velocidad que necesita para procesar GCode y aumentará la velocidad de segmentación al eliminar detalles de la malla que, de todas formas, no puede procesar."

msgctxt "meshfix_maximum_travel_resolution description"
msgid "The minimum size of a travel line segment after slicing. If you increase this, the travel moves will have less smooth corners. This may allow the printer to keep up with the speed it has to process g-code, but it may cause model avoidance to become less accurate."
msgstr "El tamaño mínimo de un segmento de línea de desplazamiento tras la segmentación. Si se aumenta, los movimientos de desplazamiento tendrán esquinas menos suavizadas. Esto puede le permite a la impresora mantener la velocidad que necesita para procesar GCode pero puede ocasionar que evitar el modelo sea menos preciso."

msgctxt "support_bottom_stair_step_min_slope description"
msgid "The minimum slope of the area for stair-stepping to take effect. Low values should make support easier to remove on shallower slopes, but really low values may result in some very counter-intuitive results on other parts of the model."
msgstr "La pendiente mínima de la zona para un efecto del escalón de la escalera de soporte. Los valores más bajos deberían facilitar la extracción del soporte en pendientes poco profundas, pero los valores muy bajos pueden dar resultados realmente contradictorios en otras partes del modelo."

msgctxt "cool_min_layer_time description"
msgid "The minimum time spent in a layer. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "Tiempo mínimo empleado en una capa. Esto fuerza a la impresora a ir más despacio, para emplear al menos el tiempo establecido aquí en una capa. Esto permite que el material impreso se enfríe adecuadamente antes de imprimir la siguiente capa. Es posible que el tiempo para cada capa sea inferior al tiempo mínimo si se desactiva Levantar el cabezal o si la velocidad mínima se ve modificada de otro modo."

msgctxt "prime_tower_min_volume description"
msgid "The minimum volume for each layer of the prime tower in order to purge enough material."
msgstr "El volumen mínimo de cada capa de la torre auxiliar que permite purgar suficiente material."

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model description"
msgid "The most the diameter of a branch that has to connect to the model may increase by merging with branches that could reach the buildplate. Increasing this reduces print time, but increases the area of support that rests on model"
msgstr "El diámetro máximo de una rama que debe conectarse al modelo puede aumentar al fusionarse con ramas que podrían llegar a la placa de impresión. Al aumentarlo, se reduce el tiempo de impresión, pero el área de soporte que descansa sobre el modelo aumenta"

msgctxt "machine_name description"
msgid "The name of your 3D printer model."
msgstr "Nombre del modelo de la impresora 3D."

msgctxt "machine_nozzle_id description"
msgid "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\"."
msgstr "Id. de la tobera de un tren extrusor, como \"AA 0.4\" y \"BB 0.8\"."

msgctxt "travel_avoid_other_parts description"
msgid "The nozzle avoids already printed parts when traveling. This option is only available when combing is enabled."
msgstr "La tobera evita las partes ya impresas al desplazarse. Esta opción solo está disponible cuando se ha activado la opción de peinada."

msgctxt "travel_avoid_supports description"
msgid "The nozzle avoids already printed supports when traveling. This option is only available when combing is enabled."
msgstr "La tobera evita los soportes ya impresos al desplazarse. Esta opción solo está disponible cuando se ha activado la opción de peinada."

msgctxt "bottom_layers description"
msgid "The number of bottom layers. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "Número de capas inferiores. Al calcularlo por el grosor inferior, este valor se redondea a un número entero."

msgctxt "raft_base_wall_count description"
msgid "The number of contours to print around the linear pattern in the base layer of the raft."
msgstr "El número de contornos que se imprimirán alrededor del patrón lineal en la capa base de la balsa."

msgctxt "skin_edge_support_layers description"
msgid "The number of infill layers that supports skin edges."
msgstr "El número de capas de relleno que soportan los bordes del forro."

msgctxt "initial_bottom_layers description"
msgid "The number of initial bottom layers, from the build-plate upwards. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "El número de capas inferiores iniciales, desde la capa de impresión hacia arriba. Al calcularlo por el grosor inferior, este valor se redondea a un número entero."

msgctxt "raft_interface_layers description"
msgid "The number of layers between the base and the surface of the raft. These comprise the main thickness of the raft. Increasing this creates a thicker, sturdier raft."
msgstr "El número de capas entre la base y la superficie de la balsa. Estas comprenden el espesor principal de la balsa. Al aumentar este número se crea una balsa más gruesa y resistente."

msgctxt "brim_line_count description"
msgid "The number of lines used for a brim. More brim lines enhance adhesion to the build plate, but also reduces the effective print area."
msgstr "Número de líneas utilizadas para un borde. Más líneas de borde mejoran la adhesión a la plataforma de impresión, pero también reducen el área de impresión efectiva."

msgctxt "support_brim_line_count description"
msgid "The number of lines used for the support brim. More brim lines enhance adhesion to the build plate, at the cost of some extra material."
msgstr "Número de líneas utilizadas para el borde de soporte. Más líneas de borde mejoran la adhesión a la placa de impresión, pero requieren material adicional."

msgctxt "raft_surface_layers description"
msgid "The number of top layers on top of the 2nd raft layer. These are fully filled layers that the model sits on. 2 layers result in a smoother top surface than 1."
msgstr "Número de capas superiores encima de la segunda capa de la balsa. Estas son las capas en las que se asienta el modelo. Dos capas producen una superficie superior más lisa que una."

msgctxt "top_layers description"
msgid "The number of top layers. When calculated by the top thickness, this value is rounded to a whole number."
msgstr "Número de capas superiores. Al calcularlo por el grosor superior, este valor se redondea a un número entero."

msgctxt "roofing_layer_count description"
msgid "The number of top most skin layers. Usually only one top most layer is sufficient to generate higher quality top surfaces."
msgstr "El número de capas del nivel superior del forro. Normalmente es suficiente con una sola capa para generar superficies superiores con mayor calidad."

msgctxt "support_wall_count description"
msgid "The number of walls with which to surround support infill. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "El número de paredes con las que el soporte rodea el relleno. Añadir una pared puede hacer que la impresión de soporte sea más fiable y pueda soportar mejor los voladizos pero aumenta el tiempo de impresión y el material utilizado."

msgctxt "support_bottom_wall_count description"
msgid "The number of walls with which to surround support interface floor. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "El número de paredes con las que rodear el suelo de la interfaz de soporte. Añadir una pared puede hacer que la copia impresa del soporte sea más fiable y pueda sostener mejor los voladizos, pero aumentará el tiempo de impresión y el material utilizado."

msgctxt "support_roof_wall_count description"
msgid "The number of walls with which to surround support interface roof. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "El número de paredes con las que rodear el techo de la interfaz de soporte. Añadir una pared puede hacer que la copia impresa del soporte sea más fiable y pueda sostener mejor los voladizos, pero aumentará el tiempo de impresión y el material utilizado."

msgctxt "support_interface_wall_count description"
msgid "The number of walls with which to surround support interface. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "El número de paredes con las que rodear la interfaz de soporte. Añadir una pared puede hacer que la copia impresa del soporte sea más fiable y pueda sostener mejor los voladizos, pero aumentará el tiempo de impresión y el material utilizado."

msgctxt "wall_distribution_count description"
msgid "The number of walls, counted from the center, over which the variation needs to be spread. Lower values mean that the outer walls don't change in width."
msgstr "El número de paredes, contadas desde el centro, en las que se distribuirá la variación. Los valores más bajos indican que el ancho de las paredes externas no cambia."

msgctxt "wall_line_count description"
msgid "The number of walls. When calculated by the wall thickness, this value is rounded to a whole number."
msgstr "Número de paredes. Al calcularlo por el grosor de las paredes, este valor se redondea a un número entero."

msgctxt "machine_nozzle_tip_outer_diameter description"
msgid "The outer diameter of the tip of the nozzle."
msgstr "Diámetro exterior de la punta de la tobera."

msgctxt "infill_pattern description"
msgid "The pattern of the infill material of the print. The line and zig zag infill swap direction on alternate layers, reducing material cost. The grid, triangle, tri-hexagon, cubic, octet, quarter cubic, cross and concentric patterns are fully printed every layer. Gyroid, cubic, quarter cubic and octet infill change with every layer to provide a more equal distribution of strength over each direction. Lightning infill tries to minimize the infill, by only supporting the ceiling of the object."
msgstr "Patrón del material de relleno de la impresión. El método de llenado en línea y en zigzag cambia de dirección en capas alternas para reducir los costes de material. Los patrones de rejilla, triángulo, trihexágono, cubo, octaédrico, cubo bitruncado, transversal y concéntrico se imprimen en todas las capas por completo. Los rellenos de giroide, cúbico, cúbico bitruncado y octaédrico se alternan en cada capa para lograr una distribución más uniforme de la fuerza en todas las direcciones. El relleno de rayos intenta minimizar el relleno apoyando solo la parte superior del objeto."

msgctxt "support_pattern description"
msgid "The pattern of the support structures of the print. The different options available result in sturdy or easy to remove support."
msgstr "Patrón de las estructuras del soporte de la impresión. Las diferentes opciones disponibles dan como resultado un soporte robusto o fácil de retirar."

msgctxt "roofing_pattern description"
msgid "The pattern of the top most layers."
msgstr "El patrón de las capas de nivel superior."

msgctxt "top_bottom_pattern description"
msgid "The pattern of the top/bottom layers."
msgstr "Patrón de las capas superiores/inferiores."

msgctxt "top_bottom_pattern_0 description"
msgid "The pattern on the bottom of the print on the first layer."
msgstr "El patrón que aparece en la parte inferior de la impresión de la primera capa."

msgctxt "ironing_pattern description"
msgid "The pattern to use for ironing top surfaces."
msgstr "El patrón que se usará para el alisado de las superficies superiores."

msgctxt "support_bottom_pattern description"
msgid "The pattern with which the floors of the support are printed."
msgstr "Patrón con el que se imprimen los suelos del soporte."

msgctxt "support_interface_pattern description"
msgid "The pattern with which the interface of the support with the model is printed."
msgstr "Patrón con el que se imprime la interfaz de soporte con el modelo."

msgctxt "support_roof_pattern description"
msgid "The pattern with which the roofs of the support are printed."
msgstr "Patrón con el que se imprimen los techos del soporte."

msgctxt "z_seam_position description"
msgid "The position near where to start printing each part in a layer."
msgstr "La posición cerca de donde comenzará la impresión de cada parte de una capa."

msgctxt "support_tree_angle_slow description"
msgid "The preferred angle of the branches, when they do not have to avoid the model. Use a lower angle to make them more vertical and more stable. Use a higher angle for branches to merge faster."
msgstr "El ángulo para las ramas de preferencia cuando no tienen que evitar el modelo. Utilice un ángulo más pequeño para hacerlas más verticales y más estables. Utilice un ángulo mayor para que las ramas se fusionen más rápido."

msgctxt "support_tree_rest_preference description"
msgid "The preferred placement of the support structures. If structures can't be placed at the preferred location, they will be place elsewhere, even if that means placing them on the model."
msgstr "La colocación preferida para las estructuras de soporte. Si las estructuras no se pueden poner en la colocación preferida, se pondrán en otro lugar, incluso si eso significa situarlas sobre el modelo."

msgctxt "jerk_layer_0 description"
msgid "The print maximum instantaneous velocity change for the initial layer."
msgstr "Cambio en la velocidad instantánea máxima de la capa inicial."

msgctxt "machine_shape description"
msgid "The shape of the build plate without taking unprintable areas into account."
msgstr "La forma de la placa de impresión sin tener en cuenta las zonas externas al área de impresión."

msgctxt "machine_head_with_fans_polygon description"
msgid "The shape of the print head. These are coordinates relative to the position of the print head, which is usually the position of its first extruder. The dimensions left and in front of the print head must be negative coordinates."
msgstr "La forma del cabezal de impresión. Estas son las coordenadas relativas a la posición del cabezal de impresión, que generalmente es la posición de su primer extrusor. Las dimensiones de la izquierda y de la parte delantera del cabezal de impresión deben ser coordenadas negativas."

msgctxt "cross_infill_pocket_size description"
msgid "The size of pockets at four-way crossings in the cross 3D pattern at heights where the pattern is touching itself."
msgstr "Tamaño de las bolsas en cruces del patrón de cruz 3D en las alturas en las que el patrón coincide consigo mismo."

msgctxt "coasting_min_volume description"
msgid "The smallest volume an extrusion path should have before allowing coasting. For smaller extrusion paths, less pressure has been built up in the bowden tube and so the coasted volume is scaled linearly. This value should always be larger than the Coasting Volume."
msgstr "Menor Volumen que deberá tener una trayectoria de extrusión antes de permitir el depósito por inercia. Para trayectorias de extrusión más pequeñas, se acumula menos presión en el tubo guía y, por tanto, el volumen depositado por inercia se escala linealmente. Este valor debe ser siempre mayor que el Volumen de depósito por inercia."

msgctxt "machine_nozzle_cool_down_speed description"
msgid "The speed (°C/s) by which the nozzle cools down averaged over the window of normal printing temperatures and the standby temperature."
msgstr "Velocidad (°C/s) de enfriamiento de la tobera calculada como una media a partir de las temperaturas de impresión habituales y la temperatura en modo de espera."

msgctxt "machine_nozzle_heat_up_speed description"
msgid "The speed (°C/s) by which the nozzle heats up averaged over the window of normal printing temperatures and the standby temperature."
msgstr "Velocidad (°C/s) de calentamiento de la tobera calculada como una media a partir de las temperaturas de impresión habituales y la temperatura en modo de espera."

msgctxt "speed_wall_x description"
msgid "The speed at which all inner walls are printed. Printing the inner wall faster than the outer wall will reduce printing time. It works well to set this in between the outer wall speed and the infill speed."
msgstr "Velocidad a la que se imprimen todas las paredes interiores. Imprimir la pared interior más rápido que la exterior reduce el tiempo de impresión. Ajustar este valor entre la velocidad de la pared exterior y la velocidad a la que se imprime el relleno puede ir bien."

msgctxt "bridge_skin_speed description"
msgid "The speed at which bridge skin regions are printed."
msgstr "Velocidad a la que se imprimen las áreas de forro del puente."

msgctxt "speed_infill description"
msgid "The speed at which infill is printed."
msgstr "Velocidad a la que se imprime el relleno."

msgctxt "speed_print description"
msgid "The speed at which printing happens."
msgstr "Velocidad a la que se realiza la impresión."

msgctxt "raft_base_speed description"
msgid "The speed at which the base raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "Velocidad a la que se imprime la capa de base de la balsa. Esta debe imprimirse con bastante lentitud, ya que el volumen de material que sale de la tobera es bastante alto."

msgctxt "bridge_wall_speed description"
msgid "The speed at which the bridge walls are printed."
msgstr "Velocidad a la que se imprimen las paredes del puente."

msgctxt "cool_fan_speed_0 description"
msgid "The speed at which the fans spin at the start of the print. In subsequent layers the fan speed is gradually increased up to the layer corresponding to Regular Fan Speed at Height."
msgstr "Velocidad a la que giran los ventiladores al comienzo de la impresión. En las capas posteriores, la velocidad del ventilador aumenta gradualmente hasta la capa correspondiente a la velocidad normal del ventilador a altura."

msgctxt "cool_fan_speed_min description"
msgid "The speed at which the fans spin before hitting the threshold. When a layer prints faster than the threshold, the fan speed gradually inclines towards the maximum fan speed."
msgstr "Velocidad a la que giran los ventiladores antes de alcanzar el umbral. Cuando una capa se imprime más rápido que el umbral, la velocidad del ventilador se inclina gradualmente hacia la velocidad máxima del ventilador."

msgctxt "cool_fan_speed_max description"
msgid "The speed at which the fans spin on the minimum layer time. The fan speed gradually increases between the regular fan speed and maximum fan speed when the threshold is hit."
msgstr "Velocidad a la que giran los ventiladores en el tiempo mínimo de capa. La velocidad del ventilador aumenta gradualmente entre la velocidad normal y máxima del ventilador cuando se alcanza el umbral."

msgctxt "retraction_prime_speed description"
msgid "The speed at which the filament is primed during a retraction move."
msgstr "Velocidad a la que se prepara el filamento durante un movimiento de retracción."

msgctxt "wipe_retraction_prime_speed description"
msgid "The speed at which the filament is primed during a wipe retraction move."
msgstr "Velocidad a la que se prepara el filamento durante un movimiento de retracción de limpieza."

msgctxt "switch_extruder_prime_speed description"
msgid "The speed at which the filament is pushed back after a nozzle switch retraction."
msgstr "Velocidad a la que se retrae el filamento durante una retracción del cambio de tobera."

msgctxt "retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a retraction move."
msgstr "Velocidad a la que se retrae el filamento y se prepara durante un movimiento de retracción."

msgctxt "wipe_retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a wipe retraction move."
msgstr "Velocidad a la que se retrae el filamento y se prepara durante un movimiento de retracción de limpieza."

msgctxt "switch_extruder_retraction_speed description"
msgid "The speed at which the filament is retracted during a nozzle switch retract."
msgstr "Velocidad a la que se retrae el filamento durante una retracción del cambio de tobera."

msgctxt "retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a retraction move."
msgstr "Velocidad a la que se retrae el filamento durante un movimiento de retracción."

msgctxt "wipe_retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a wipe retraction move."
msgstr "Velocidad a la que se retrae el filamento durante un movimiento de retracción de limpieza."

msgctxt "switch_extruder_retraction_speeds description"
msgid "The speed at which the filament is retracted. A higher retraction speed works better, but a very high retraction speed can lead to filament grinding."
msgstr "Velocidad de retracción del filamento. Se recomienda una velocidad de retracción alta, pero si es demasiado alta, podría hacer que el filamento se aplaste."

msgctxt "speed_support_bottom description"
msgid "The speed at which the floor of support is printed. Printing it at lower speed can improve adhesion of support on top of your model."
msgstr "Velocidad a la que se imprimen los suelos del soporte. Imprimirlos a una velocidad inferior puede mejorar la adhesión del soporte en la parte superior del modelo."

msgctxt "speed_support_infill description"
msgid "The speed at which the infill of support is printed. Printing the infill at lower speeds improves stability."
msgstr "Velocidad a la que se rellena el soporte. Imprimir el relleno a una velocidad inferior mejora la estabilidad."

msgctxt "raft_interface_speed description"
msgid "The speed at which the middle raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "Velocidad a la que se imprime la capa intermedia de la balsa. Esta debe imprimirse con bastante lentitud, ya que el volumen de material que sale de la tobera es bastante alto."

msgctxt "speed_wall_0 description"
msgid "The speed at which the outermost walls are printed. Printing the outer wall at a lower speed improves the final skin quality. However, having a large difference between the inner wall speed and the outer wall speed will affect quality in a negative way."
msgstr "Velocidad a la que se imprimen las paredes exteriores. Imprimir la pared exterior a una velocidad inferior mejora la calidad final del forro. Sin embargo, una gran diferencia entre la velocidad de la pared interior y de la pared exterior afectará negativamente a la calidad."

msgctxt "speed_prime_tower description"
msgid "The speed at which the prime tower is printed. Printing the prime tower slower can make it more stable when the adhesion between the different filaments is suboptimal."
msgstr "Velocidad a la que se imprime la torre auxiliar. Imprimir la torre auxiliar a una velocidad inferior puede conseguir más estabilidad si la adherencia entre los diferentes filamentos es insuficiente."

msgctxt "cool_fan_speed description"
msgid "The speed at which the print cooling fans spin."
msgstr "Velocidad a la que giran los ventiladores de refrigeración de impresión."

msgctxt "raft_speed description"
msgid "The speed at which the raft is printed."
msgstr "Velocidad a la que se imprime la balsa."

msgctxt "speed_support_interface description"
msgid "The speed at which the roofs and floors of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "Velocidad a la que se imprimen los techos y suelos del soporte. Imprimirlos a una velocidad inferior puede mejorar la calidad del voladizo."

msgctxt "speed_support_roof description"
msgid "The speed at which the roofs of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "Velocidad a la que se imprimen los techos del soporte. Imprimirlos a una velocidad inferior puede mejorar la calidad del voladizo."

msgctxt "skirt_brim_speed description"
msgid "The speed at which the skirt and brim are printed. Normally this is done at the initial layer speed, but sometimes you might want to print the skirt or brim at a different speed."
msgstr "Velocidad a la que se imprimen la falda y el borde. Normalmente, esto se hace a la velocidad de la capa inicial, pero a veces es posible que se prefiera imprimir la falda o el borde a una velocidad diferente."

msgctxt "speed_support description"
msgid "The speed at which the support structure is printed. Printing support at higher speeds can greatly reduce printing time. The surface quality of the support structure is not important since it is removed after printing."
msgstr "Velocidad a la que se imprime la estructura de soporte. Imprimir el soporte a una mayor velocidad puede reducir considerablemente el tiempo de impresión. La calidad de superficie de la estructura de soporte no es importante, ya que se elimina después de la impresión."

msgctxt "raft_surface_speed description"
msgid "The speed at which the top raft layers are printed. These should be printed a bit slower, so that the nozzle can slowly smooth out adjacent surface lines."
msgstr "Velocidad a la que se imprimen las capas superiores de la balsa. Estas deben imprimirse un poco más lento para permitir que la tobera pueda suavizar lentamente las líneas superficiales adyacentes."

msgctxt "speed_wall_x_roofing description"
msgid "The speed at which the top surface inner walls are printed."
msgstr "La velocidad a la que se imprimen las paredes internas de la superficie superior."

msgctxt "speed_wall_0_roofing description"
msgid "The speed at which the top surface outermost wall is printed."
msgstr "La velocidad con la que se imprimen las paredes más externas de la superficie superior."

msgctxt "speed_z_hop description"
msgid "The speed at which the vertical Z movement is made for Z Hops. This is typically lower than the print speed since the build plate or machine's gantry is harder to move."
msgstr "Velocidad a la que se realiza el movimiento vertical en la dirección Z para los saltos en Z. Suele ser inferior a la velocidad de impresión porque la placa de impresión o el puente de la máquina es más difícil de desplazar."

msgctxt "speed_wall description"
msgid "The speed at which the walls are printed."
msgstr "Velocidad a la que se imprimen las paredes."

msgctxt "speed_ironing description"
msgid "The speed at which to pass over the top surface."
msgstr "Velocidad a la que pasa por encima de la superficie superior."

msgctxt "material_break_speed description"
msgid "The speed at which to retract the filament in order to break it cleanly."
msgstr "Velocidad a la que debe retraerse el filamento para que se rompa limpiamente."

msgctxt "speed_roofing description"
msgid "The speed at which top surface skin layers are printed."
msgstr "Velocidad a la que se imprimen las capas de la superficie superior del forro."

msgctxt "speed_topbottom description"
msgid "The speed at which top/bottom layers are printed."
msgstr "Velocidad a la que se imprimen las capas superiores/inferiores."

msgctxt "speed_travel description"
msgid "The speed at which travel moves are made."
msgstr "Velocidad a la que tienen lugar los movimientos de desplazamiento."

msgctxt "coasting_speed description"
msgid "The speed by which to move during coasting, relative to the speed of the extrusion path. A value slightly under 100% is advised, since during the coasting move the pressure in the bowden tube drops."
msgstr "Velocidad a la que se desplaza durante el depósito por inercia con relación a la velocidad de la trayectoria de extrusión. Se recomienda un valor ligeramente por debajo del 100%, ya que la presión en el tubo guía disminuye durante el movimiento depósito por inercia."

msgctxt "speed_layer_0 description"
msgid "The speed for the initial layer. A lower value is advised to improve adhesion to the build plate. Does not affect the build plate adhesion structures themselves, like brim and raft."
msgstr "La velocidad de la capa inicial. Se recomienda un valor más bajo para mejorar la adherencia a la placa de impresión. No influye en las estructuras de adhesión de la placa de impresión en sí, como el borde y la balsa."

msgctxt "speed_print_layer_0 description"
msgid "The speed of printing for the initial layer. A lower value is advised to improve adhesion to the build plate."
msgstr "Velocidad de impresión de la capa inicial. Se recomienda un valor más bajo para mejorar la adherencia a la placa de impresión."

msgctxt "speed_travel_layer_0 description"
msgid "The speed of travel moves in the initial layer. A lower value is advised to prevent pulling previously printed parts away from the build plate. The value of this setting can automatically be calculated from the ratio between the Travel Speed and the Print Speed."
msgstr "Velocidad de impresión de la capa inicial. Se recomienda un valor más bajo para evitar que las partes ya impresas se separen de la placa de impresión. El valor de este ajuste se puede calcular automáticamente a partir del ratio entre la velocidad de desplazamiento y la velocidad de impresión."

msgctxt "material_break_temperature description"
msgid "The temperature at which the filament is broken for a clean break."
msgstr "Temperatura a la que se rompe el filamento de forma limpia."

msgctxt "build_volume_temperature description"
msgid "The temperature of the environment to print in. If this is 0, the build volume temperature will not be adjusted."
msgstr "La temperatura del entorno de impresión. Si el valor es 0, la temperatura de volumen de impresión no se ajustará."

msgctxt "material_standby_temperature description"
msgid "The temperature of the nozzle when another nozzle is currently used for printing."
msgstr "Temperatura de la tobera cuando otra se está utilizando en la impresión."

msgctxt "material_final_print_temperature description"
msgid "The temperature to which to already start cooling down just before the end of printing."
msgstr "La temperatura a la que se puede empezar a enfriar justo antes de finalizar la impresión."

msgctxt "material_print_temperature_layer_0 description"
msgid "The temperature used for printing the first layer."
msgstr "La temperatura utilizada para imprimir la primera capa."

msgctxt "material_print_temperature description"
msgid "The temperature used for printing."
msgstr "Temperatura de la impresión."

msgctxt "material_bed_temperature_layer_0 description"
msgid "The temperature used for the heated build plate at the first layer. If this is 0, the build plate is left unheated during the first layer."
msgstr "Temperatura de la placa de impresión una vez caliente en la primera capa. Si el valor es 0, la placa de impresión no se calentará en la primera capa."

msgctxt "material_bed_temperature description"
msgid "The temperature used for the heated build plate. If this is 0, the build plate is left unheated."
msgstr "La temperatura utilizada para la placa de impresión caliente. Si el valor es 0, la placa de impresión no se calentará."

msgctxt "material_break_preparation_temperature description"
msgid "The temperature used to purge material, should be roughly equal to the highest possible printing temperature."
msgstr "La temperatura utilizada para purgar el material. Debería ser aproximadamente igual a la temperatura de impresión más alta posible."

msgctxt "bottom_thickness description"
msgid "The thickness of the bottom layers in the print. This value divided by the layer height defines the number of bottom layers."
msgstr "Grosor de las capas inferiores en la impresión. Este valor dividido por la altura de capa define el número de capas inferiores."

msgctxt "skin_edge_support_thickness description"
msgid "The thickness of the extra infill that supports skin edges."
msgstr "El grosor del relleno extra que soporta los bordes del forro."

msgctxt "support_interface_height description"
msgid "The thickness of the interface of the support where it touches with the model on the bottom or the top."
msgstr "Grosor de la interfaz del soporte donde toca con el modelo, ya sea en la parte superior o inferior."

msgctxt "support_bottom_height description"
msgid "The thickness of the support floors. This controls the number of dense layers that are printed on top of places of a model on which support rests."
msgstr "Grosor de los suelos del soporte. Este valor controla el número de capas densas que se imprimen en las partes superiores de un modelo, donde apoya el soporte."

msgctxt "support_roof_height description"
msgid "The thickness of the support roofs. This controls the amount of dense layers at the top of the support on which the model rests."
msgstr "Grosor de los techos del soporte. Este valor controla el número de capas densas en la parte superior del soporte, donde apoya el modelo."

msgctxt "top_thickness description"
msgid "The thickness of the top layers in the print. This value divided by the layer height defines the number of top layers."
msgstr "Grosor de las capas superiores en la impresión. Este valor dividido por la altura de capa define el número de capas superiores."

msgctxt "top_bottom_thickness description"
msgid "The thickness of the top/bottom layers in the print. This value divided by the layer height defines the number of top/bottom layers."
msgstr "Grosor de las capas superiores/inferiores en la impresión. Este valor dividido por la altura de la capa define el número de capas superiores/inferiores."

msgctxt "wall_thickness description"
msgid "The thickness of the walls in the horizontal direction. This value divided by the wall line width defines the number of walls."
msgstr "Grosor de las paredes en dirección horizontal. Este valor dividido por el ancho de la línea de pared define el número de paredes."

msgctxt "infill_sparse_thickness description"
msgid "The thickness per layer of infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "Grosor por capa de material de relleno. Este valor siempre debe ser un múltiplo de la altura de la capa y, de lo contrario, se redondea."

msgctxt "support_infill_sparse_thickness description"
msgid "The thickness per layer of support infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "Grosor por capa de material de relleno de soporte. Este valor siempre debe ser un múltiplo de la altura de la capa; de lo contrario, se redondea."

msgctxt "machine_gcode_flavor description"
msgid "The type of g-code to be generated."
msgstr "Tipo de GCode que se va a generar."

msgctxt "coasting_volume description"
msgid "The volume otherwise oozed. This value should generally be close to the nozzle diameter cubed."
msgstr "Volumen que de otro modo rezumaría. Este valor generalmente debería ser próximo al cubicaje del diámetro de la tobera."

msgctxt "machine_width description"
msgid "The width (X-direction) of the printable area."
msgstr "Ancho (dimensión sobre el eje X) del área de impresión."

msgctxt "support_brim_width description"
msgid "The width of the brim to print underneath the support. A larger brim enhances adhesion to the build plate, at the cost of some extra material."
msgstr "Anchura del borde de impresión que se imprime por debajo del soporte. Una anchura de soporte amplia mejora la adhesión a la placa de impresión, pero requieren material adicional."

msgctxt "interlocking_beam_width description"
msgid "The width of the interlocking structure beams."
msgstr "El ancho de los haces de la estructura entrelazada."

msgctxt "prime_tower_base_size description"
msgid "The width of the prime tower brim/base. A larger base enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "El ancho del borde/base de la torre de cebado. Una base más grande mejora la adhesión a la placa de construcción, pero también reduce el área efectiva de impresión."

msgctxt "prime_tower_size description"
msgid "The width of the prime tower."
msgstr "Anchura de la torre auxiliar."

msgctxt "magic_fuzzy_skin_thickness description"
msgid "The width within which to jitter. It's advised to keep this below the outer wall width, since the inner walls are unaltered."
msgstr "Ancho dentro de la cual se fluctúa. Se recomienda mantener este valor por debajo del ancho de la pared exterior, ya que las paredes interiores permanecen inalteradas."

msgctxt "retraction_extrusion_window description"
msgid "The window in which the maximum retraction count is enforced. This value should be approximately the same as the retraction distance, so that effectively the number of times a retraction passes the same patch of material is limited."
msgstr "Ventana en la que se aplica el recuento máximo de retracciones. Este valor debe ser aproximadamente el mismo que la distancia de retracción, lo que limita efectivamente el número de veces que una retracción pasa por el mismo parche de material."

msgctxt "prime_tower_position_x description"
msgid "The x coordinate of the position of the prime tower."
msgstr "Coordenada X de la posición de la torre auxiliar."

msgctxt "prime_tower_position_y description"
msgid "The y coordinate of the position of the prime tower."
msgstr "Coordenada Y de la posición de la torre auxiliar."

msgctxt "support_meshes_present description"
msgid "There are support meshes present in the scene. This setting is controlled by Cura."
msgstr "Hay mallas de soporte presentes en la escena. Esta configuración está controlada por Cura."

msgctxt "bridge_wall_coast description"
msgid "This controls the distance the extruder should coast immediately before a bridge wall begins. Coasting before the bridge starts can reduce the pressure in the nozzle and may produce a flatter bridge."
msgstr "Controla la distancia del depósito por inercia del extrusor justo antes de empezar un puente. Un depósito por inercia antes del inicio del puente puede reducir la presión en la tobera y dar como resultado un puente más horizontal."

msgctxt "raft_smoothing description"
msgid "This setting controls how much inner corners in the raft outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Este ajuste controla la medida en que se redondean las esquinas interiores en el contorno de la balsa. Las esquinas hacia el interior se redondean en semicírculo con un radio equivalente al valor aquí indicado. Este ajuste también elimina los orificios del contorno de la balsa que sean más pequeños que dicho círculo."

msgctxt "retraction_count_max description"
msgid "This setting limits the number of retractions occurring within the minimum extrusion distance window. Further retractions within this window will be ignored. This avoids retracting repeatedly on the same piece of filament, as that can flatten the filament and cause grinding issues."
msgstr "Este ajuste limita el número de retracciones que ocurren dentro de la ventana de distancia mínima de extrusión. Dentro de esta ventana se ignorarán las demás retracciones. Esto evita retraer repetidamente el mismo trozo de filamento, ya que esto podría aplanar el filamento y causar problemas de desmenuzamiento."

msgctxt "draft_shield_enabled description"
msgid "This will create a wall around the model, which traps (hot) air and shields against exterior airflow. Especially useful for materials which warp easily."
msgstr "Esto creará una pared alrededor del modelo que atrapa el aire (caliente) y lo protege contra flujos de aire exterior. Es especialmente útil para materiales que se deforman fácilmente."

msgctxt "support_tree_tip_diameter label"
msgid "Tip Diameter"
msgstr "Diámetro de la punta"

msgctxt "material_shrinkage_percentage_xy description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the XY-direction (horizontally)."
msgstr "Para compensar la contracción del material al enfriarse, el modelo se escala con este factor en la dirección XY (horizontalmente)."

msgctxt "material_shrinkage_percentage_z description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the Z-direction (vertically)."
msgstr "Para compensar la contracción del material al enfriarse, el modelo se escala con este factor en la dirección Z (verticalmente)."

msgctxt "material_shrinkage_percentage description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor."
msgstr "Para compensar la contracción del material a medida que se enfría, el modelo se escala con este factor."

msgctxt "top_layers label"
msgid "Top Layers"
msgstr "Capas superiores"

msgctxt "top_skin_expand_distance label"
msgid "Top Skin Expand Distance"
msgstr "Distancia de expansión del forro superior"

msgctxt "top_skin_preshrink label"
msgid "Top Skin Removal Width"
msgstr "Anchura de retirada del forro superior"

msgctxt "acceleration_wall_x_roofing label"
msgid "Top Surface Inner Wall Acceleration"
msgstr "Aceleración de la superficie interna superior de la pared"

msgctxt "jerk_wall_x_roofing label"
msgid "Top Surface Inner Wall Jerk"
msgstr "Sacudida de la pared exterior más externa de la superficie superior"

msgctxt "speed_wall_x_roofing label"
msgid "Top Surface Inner Wall Speed"
msgstr "Velocidad de la pared interna de la superficie superior"

msgctxt "wall_x_material_flow_roofing label"
msgid "Top Surface Inner Wall(s) Flow"
msgstr "Flujo de la pared interior de la superficie superior"

msgctxt "acceleration_wall_0_roofing label"
msgid "Top Surface Outer Wall Acceleration"
msgstr "Aceleración de la superficie externa superior de la pared"

msgctxt "wall_0_material_flow_roofing label"
msgid "Top Surface Outer Wall Flow"
msgstr "Flujo de la pared exterior de la superficie superior"

msgctxt "jerk_wall_0_roofing label"
msgid "Top Surface Outer Wall Jerk"
msgstr "Sacudida de la pared interior de la superficie superior"

msgctxt "speed_wall_0_roofing label"
msgid "Top Surface Outer Wall Speed"
msgstr "Velocidad de la pared externa de la superficie superior"

msgctxt "acceleration_roofing label"
msgid "Top Surface Skin Acceleration"
msgstr "Aceleración de la superficie superior del forro"

msgctxt "roofing_extruder_nr label"
msgid "Top Surface Skin Extruder"
msgstr "Extrusor de la superficie superior del forro"

msgctxt "roofing_material_flow label"
msgid "Top Surface Skin Flow"
msgstr "Flujo de forro de superficie superior"

msgctxt "jerk_roofing label"
msgid "Top Surface Skin Jerk"
msgstr "Impulso de la superficie superior del forro"

msgctxt "roofing_layer_count label"
msgid "Top Surface Skin Layers"
msgstr "Capas de la superficie superior del forro"

msgctxt "roofing_angles label"
msgid "Top Surface Skin Line Directions"
msgstr "Direcciones de línea de la superficie superior del forro"

msgctxt "roofing_line_width label"
msgid "Top Surface Skin Line Width"
msgstr "Ancho de línea de la superficie superior del forro"

msgctxt "roofing_pattern label"
msgid "Top Surface Skin Pattern"
msgstr "Patrón de la superficie superior del forro"

msgctxt "speed_roofing label"
msgid "Top Surface Skin Speed"
msgstr "Velocidad de la superficie superior del forro"

msgctxt "top_thickness label"
msgid "Top Thickness"
msgstr "Grosor superior"

msgctxt "max_skin_angle_for_expansion description"
msgid "Top and/or bottom surfaces of your object with an angle larger than this setting, won't have their top/bottom skin expanded. This avoids expanding the narrow skin areas that are created when the model surface has a near vertical slope. An angle of 0° is horizontal and will cause no skin to be expanded, while an angle of 90° is vertical and will cause all skin to be expanded."
msgstr "El revestimiento superior e inferior no se expandirá cuando las superficies superior e inferior del objeto tengan un ángulo mayor que este valor. Esto evita la expansión de las pequeñas áreas de revestimiento que se crean cuando la superficie del modelo tiene una pendiente casi vertical. Un ángulo de 0° es horizontal y no provoca la extensión de ningún revestimiento exterior, mientras que un ángulo de 90 ° es vertical y provoca la extensión de todo el revestimiento exterior."

msgctxt "top_bottom description"
msgid "Top/Bottom"
msgstr "Superior o inferior"

msgctxt "top_bottom label"
msgid "Top/Bottom"
msgstr "Superior o inferior"

msgctxt "acceleration_topbottom label"
msgid "Top/Bottom Acceleration"
msgstr "Aceleración superior/inferior"

msgctxt "top_bottom_extruder_nr label"
msgid "Top/Bottom Extruder"
msgstr "Extrusor superior/inferior"

msgctxt "skin_material_flow label"
msgid "Top/Bottom Flow"
msgstr "Flujo superior o inferior"

msgctxt "jerk_topbottom label"
msgid "Top/Bottom Jerk"
msgstr "Impulso superior/inferior"

msgctxt "skin_angles label"
msgid "Top/Bottom Line Directions"
msgstr "Direcciones de línea superior/inferior"

msgctxt "skin_line_width label"
msgid "Top/Bottom Line Width"
msgstr "Ancho de línea superior/inferior"

msgctxt "top_bottom_pattern label"
msgid "Top/Bottom Pattern"
msgstr "Patrón superior/inferior"

msgctxt "speed_topbottom label"
msgid "Top/Bottom Speed"
msgstr "Velocidad superior/inferior"

msgctxt "top_bottom_thickness label"
msgid "Top/Bottom Thickness"
msgstr "Grosor superior/inferior"

msgctxt "support_type option buildplate"
msgid "Touching Buildplate"
msgstr "Tocando la placa de impresión"

msgctxt "support_tower_diameter label"
msgid "Tower Diameter"
msgstr "Diámetro de la torre"

msgctxt "support_tower_roof_angle label"
msgid "Tower Roof Angle"
msgstr "Ángulo del techo de la torre"

msgctxt "mesh_rotation_matrix description"
msgid "Transformation matrix to be applied to the model when loading it from file."
msgstr "Matriz de transformación que se aplicará al modelo cuando se cargue desde el archivo."

msgctxt "travel label"
msgid "Travel"
msgstr "Desplazamiento"

msgctxt "acceleration_travel label"
msgid "Travel Acceleration"
msgstr "Aceleración de desplazamiento"

msgctxt "travel_avoid_distance label"
msgid "Travel Avoid Distance"
msgstr "Distancia para evitar al desplazarse"

msgctxt "jerk_travel label"
msgid "Travel Jerk"
msgstr "Impulso de desplazamiento"

msgctxt "speed_travel label"
msgid "Travel Speed"
msgstr "Velocidad de desplazamiento"

msgctxt "magic_mesh_surface_mode description"
msgid "Treat the model as a surface only, a volume, or volumes with loose surfaces. The normal print mode only prints enclosed volumes. \"Surface\" prints a single wall tracing the mesh surface with no infill and no top/bottom skin. \"Both\" prints enclosed volumes like normal and any remaining polygons as surfaces."
msgstr "Tratar el modelo como una superficie solo, un volumen o volúmenes con superficies sueltas. El modo de impresión normal solo imprime volúmenes cerrados. «Superficie» imprime una sola pared trazando la superficie de la malla sin relleno ni forro superior/inferior. «Ambos» imprime volúmenes cerrados de la forma habitual y cualquier polígono restante como superficies."

msgctxt "support_structure option tree"
msgid "Tree"
msgstr "Árbol"

msgctxt "infill_pattern option trihexagon"
msgid "Tri-Hexagon"
msgstr "Trihexagonal"

msgctxt "infill_pattern option triangles"
msgid "Triangles"
msgstr "Triángulos"

msgctxt "support_bottom_pattern option triangles"
msgid "Triangles"
msgstr "Triángulos"

msgctxt "support_interface_pattern option triangles"
msgid "Triangles"
msgstr "Triángulos"

msgctxt "support_pattern option triangles"
msgid "Triangles"
msgstr "Triángulos"

msgctxt "support_roof_pattern option triangles"
msgid "Triangles"
msgstr "Triángulos"

msgctxt "support_tree_max_diameter label"
msgid "Trunk Diameter"
msgstr "Diámetro del tronco"

msgctxt "machine_gcode_flavor option UltiGCode"
msgid "Ultimaker 2"
msgstr "Ultimaker 2"

msgctxt "meshfix_union_all label"
msgid "Union Overlapping Volumes"
msgstr "Volúmenes de superposiciones de uniones"

msgctxt "bridge_wall_min_length description"
msgid "Unsupported walls shorter than this will be printed using the normal wall settings. Longer unsupported walls will be printed using the bridge wall settings."
msgstr "Las paredes no compatibles menores que este valor se imprimirán utilizando los ajustes de pared habituales. Las paredes no compatibles mayores se imprimirán utilizando los ajustes de pared de puente."

msgctxt "adaptive_layer_height_enabled label"
msgid "Use Adaptive Layers"
msgstr "Utilizar capas de adaptación"

msgctxt "support_use_towers label"
msgid "Use Towers"
msgstr "Usar torres"

msgctxt "acceleration_travel_enabled description"
msgid "Use a separate acceleration rate for travel moves. If disabled, travel moves will use the acceleration value of the printed line at their destination."
msgstr "Utilice una tasa de aceleración independiente para los movimientos de desplazamiento. Si está deshabilitada, los movimientos de desplazamiento utilizarán el valor de aceleración de la línea impresa en su destino."

msgctxt "jerk_travel_enabled description"
msgid "Use a separate jerk rate for travel moves. If disabled, travel moves will use the jerk value of the printed line at their destination."
msgstr "Utilice una tasa de impulso independiente para los movimientos de desplazamiento. Si está deshabilitada, los movimientos de desplazamiento utilizarán el valor de impulso de la línea impresa en su destino."

msgctxt "relative_extrusion description"
msgid "Use relative extrusion rather than absolute extrusion. Using relative E-steps makes for easier post-processing of the g-code. However, it's not supported by all printers and it may produce very slight deviations in the amount of deposited material compared to absolute E-steps. Irrespective of this setting, the extrusion mode will always be set to absolute before any g-code script is output."
msgstr "Utilizar la extrusión relativa en lugar de la extrusión absoluta. El uso de pasos de extrusión relativos permite un procesamiento posterior más sencillo del GCode. Sin embargo, no es compatible con todas las impresoras y puede causar ligeras desviaciones en la cantidad de material depositado si se compara con los pasos de extrusión absolutos. Con independencia de este ajuste, el modo de extrusión se ajustará siempre en absoluto antes de la salida de cualquier secuencia GCode."

msgctxt "support_use_towers description"
msgid "Use specialized towers to support tiny overhang areas. These towers have a larger diameter than the region they support. Near the overhang the towers' diameter decreases, forming a roof."
msgstr "Usa torres especializadas como soporte de pequeñas áreas de voladizo. Estas torres tienen un diámetro mayor que la región que soportan. El diámetro de las torres disminuye cerca del voladizo, formando un techo."

msgctxt "infill_mesh description"
msgid "Use this mesh to modify the infill of other meshes with which it overlaps. Replaces infill regions of other meshes with regions for this mesh. It's suggested to only print one Wall and no Top/Bottom Skin for this mesh."
msgstr "Utilice esta malla para modificar el relleno de otras mallas con las que se superpone. Reemplaza las zonas de relleno de otras mallas con zonas de esta malla. Se sugiere imprimir una pared y no un forro superior/inferior para esta malla."

msgctxt "support_mesh description"
msgid "Use this mesh to specify support areas. This can be used to generate support structure."
msgstr "Utilice esta malla para especificar las áreas de soporte. Esta opción puede utilizarse para generar estructuras de soporte."

msgctxt "anti_overhang_mesh description"
msgid "Use this mesh to specify where no part of the model should be detected as overhang. This can be used to remove unwanted support structure."
msgstr "Utilice esta malla para especificar los lugares del modelo en los que no debería detectarse ningún voladizo. Esta opción puede utilizarse para eliminar estructuras de soporte no deseadas."

msgctxt "z_seam_type option back"
msgid "User Specified"
msgstr "Especificada por el usuario"

msgctxt "material_shrinkage_percentage_z label"
msgid "Vertical Scaling Factor Shrinkage Compensation"
msgstr "Factor de escala vertical para la compensación de la contracción"

msgctxt "slicing_tolerance description"
msgid "Vertical tolerance in the sliced layers. The contours of a layer are normally generated by taking cross sections through the middle of each layer's thickness (Middle). Alternatively each layer can have the areas which fall inside of the volume throughout the entire thickness of the layer (Exclusive) or a layer has the areas which fall inside anywhere within the layer (Inclusive). Inclusive retains the most details, Exclusive makes for the best fit and Middle stays closest to the original surface."
msgstr "Tolerancia vertical en las capas cortadas. Los contornos de una capa se generan normalmente pasando las secciones entrecruzadas a través del medio de cada espesor de capa (Media). Alternativamente, cada capa puede tener áreas ubicadas dentro del volumen a través de todo el grosor de la capa (Exclusiva) o una capa puede tener áreas ubicadas dentro en cualquier lugar de la capa (Inclusiva). La opción Inclusiva permite conservar la mayoría de los detalles, la opción Exclusiva permite obtener una adaptación óptima y la opción Media permite permanecer cerca de la superficie original."

msgctxt "material_bed_temp_wait label"
msgid "Wait for Build Plate Heatup"
msgstr "Esperar a que la placa de impresión se caliente"

msgctxt "material_print_temp_wait label"
msgid "Wait for Nozzle Heatup"
msgstr "Esperar a la que la tobera se caliente"

msgctxt "acceleration_wall label"
msgid "Wall Acceleration"
msgstr "Aceleración de la pared"

msgctxt "wall_distribution_count label"
msgid "Wall Distribution Count"
msgstr "Recuento de distribución de pared"

msgctxt "wall_extruder_nr label"
msgid "Wall Extruder"
msgstr "Extrusor de pared"

msgctxt "wall_material_flow label"
msgid "Wall Flow"
msgstr "Flujo de pared"

msgctxt "jerk_wall label"
msgid "Wall Jerk"
msgstr "Impulso de pared"

msgctxt "wall_line_count label"
msgid "Wall Line Count"
msgstr "Recuento de líneas de pared"

msgctxt "wall_line_width label"
msgid "Wall Line Width"
msgstr "Ancho de línea de pared"

msgctxt "inset_direction label"
msgid "Wall Ordering"
msgstr "Orden de paredes"

msgctxt "speed_wall label"
msgid "Wall Speed"
msgstr "Velocidad de pared"

msgctxt "wall_thickness label"
msgid "Wall Thickness"
msgstr "Grosor de la pared"

msgctxt "wall_transition_length label"
msgid "Wall Transition Length"
msgstr "Longitud de transición de la pared"

msgctxt "wall_transition_filter_distance label"
msgid "Wall Transitioning Filter Distance"
msgstr "Distancia del filtro de transición a la pared"

msgctxt "wall_transition_filter_deviation label"
msgid "Wall Transitioning Filter Margin"
msgstr "Margen del filtro de transición de pared"

msgctxt "wall_transition_angle label"
msgid "Wall Transitioning Threshold Angle"
msgstr "Ángulo de umbral de transición de pared"

msgctxt "shell label"
msgid "Walls"
msgstr "Paredes"

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either."
msgstr "Las paredes con un ángulo de voladizo mayor que este se imprimirán con los ajustes de voladizo de pared. Cuando el valor sea 90, no se aplicará la condición de voladizo a la pared. El voladizo que se apoya en el soporte tampoco se tratará como voladizo."

msgctxt "support_interface_skip_height description"
msgid "When checking where there's model above and below the support, take steps of the given height. Lower values will slice slower, while higher values may cause normal support to be printed in some places where there should have been support interface."
msgstr "A la hora de comprobar si existe un modelo por encima y por debajo del soporte, tome las medidas de la altura determinada. Reducir los valores hará que se segmente más despacio, mientras que valores más altos pueden provocar que el soporte normal se imprima en lugares en los que debería haber una interfaz de soporte."

msgctxt "meshfix_fluid_motion_enabled description"
msgid "When enabled tool paths are corrected for printers with smooth motion planners. Small movements that deviate from the general tool path direction are smoothed to improve fluid motions."
msgstr "Cuando está activado, las trayectorias de las herramientas se corrigen para impresoras con planificadores de movimiento suave. Los pequeños movimientos que se desvían de la dirección general de la trayectoria de la herramienta se suavizan para mejorar los movimientos fluidos."

msgctxt "infill_enable_travel_optimization description"
msgid "When enabled, the order in which the infill lines are printed is optimized to reduce the distance travelled. The reduction in travel time achieved very much depends on the model being sliced, infill pattern, density, etc. Note that, for some models that have many small areas of infill, the time to slice the model may be greatly increased."
msgstr "Cuando está habilitado, se optimiza el orden en el que se imprimen las líneas de relleno para reducir la distancia de desplazamiento. La reducción del tiempo de desplazamiento obtenido depende en gran parte del modelo que se está fragmentando, el patrón de relleno, la densidad, etc. Tenga en cuenta que, para algunos modelos que tienen pequeñas áreas de relleno, el tiempo para fragmentar el modelo se puede aumentar en gran medida."

msgctxt "support_fan_enable description"
msgid "When enabled, the print cooling fan speed is altered for the skin regions immediately above the support."
msgstr "Al habilitar esta opción, la velocidad del ventilador de enfriamiento de impresión cambia para las áreas de forro que se encuentran inmediatamente encima del soporte."

msgctxt "z_seam_relative description"
msgid "When enabled, the z seam coordinates are relative to each part's centre. When disabled, the coordinates define an absolute position on the build plate."
msgstr "Cuando se habilita, las coordenadas de la costura en z son relativas al centro de cada pieza. De lo contrario, las coordenadas definen una posición absoluta en la placa de impresión."

msgctxt "retraction_combing_max_distance description"
msgid "When greater than zero, combing travel moves that are longer than this distance will use retraction. If set to zero, there is no maximum and combing moves will not use retraction."
msgstr "Si es mayor que cero, los movimientos de desplazamiento de peinada que sean superiores a esta distancia utilizarán retracción. Si se establece como cero, no hay un máximo y los movimientos de peinada no utilizarán la retracción."

msgctxt "hole_xy_offset_max_diameter description"
msgid "When greater than zero, the Hole Horizontal Expansion is gradually applied on small holes (small holes are expanded more). When set to zero the Hole Horizontal Expansion will be applied to all holes. Holes larger than the Hole Horizontal Expansion Max Diameter are not expanded."
msgstr "Cuando es mayor que cero, la expansión horizontal de los orificios se aplica gradualmente en orificios pequeños (los orificios pequeños se expanden más). Cuando se establezca en cero, la expansión horizontal de los orificios se aplicará a todos ellos. Los orificios mayores que el diámetro máximo de expansión horizontal de los orificios no se expanden."

msgctxt "hole_xy_offset description"
msgid "When greater than zero, the Hole Horizontal Expansion is the amount of offset applied to all holes in each layer. Positive values increase the size of the holes, negative values reduce the size of the holes. When this setting is enabled it can be further tuned with Hole Horizontal Expansion Max Diameter."
msgstr "Cuando es mayor que cero, la Expansión horizontal de agujeros es la cantidad de desplazamiento aplicada a todos los agujeros de cada capa. Los valores positivos aumentan el tamaño de los agujeros y los negativos lo reducen. Cuando esta configuración está activada, se puede ajustar aún más con el Diámetro máximo de expansión horizontal de agujeros."

msgctxt "bridge_skin_material_flow description"
msgid "When printing bridge skin regions, the amount of material extruded is multiplied by this value."
msgstr "Cuando se imprimen las áreas de forro del puente; la cantidad de material extruido se multiplica por este valor."

msgctxt "bridge_wall_material_flow description"
msgid "When printing bridge walls, the amount of material extruded is multiplied by this value."
msgstr "Cuando se imprimen las paredes del puente; la cantidad de material extruido se multiplica por este valor."

msgctxt "bridge_skin_material_flow_2 description"
msgid "When printing the second bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "Cuando se imprime la segunda capa del forro del puente; la cantidad de material extruido se multiplica por este valor."

msgctxt "bridge_skin_material_flow_3 description"
msgid "When printing the third bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "Cuando se imprime la tercera capa del forro del puente; la cantidad de material extruido se multiplica por este valor."

msgctxt "cool_lift_head description"
msgid "When the minimum speed is hit because of minimum layer time, lift the head away from the print and wait the extra time until the minimum layer time is reached."
msgstr "Cuando se alcanza la velocidad mínima debido al tiempo mínimo de capa, levante el cabezal de la impresión y espere el tiempo adicional hasta que se alcance el tiempo mínimo de capa."

msgctxt "skin_no_small_gaps_heuristic description"
msgid "When the model has small vertical gaps of only a few layers, there should normally be skin around those layers in the narrow space. Enable this setting to not generate skin if the vertical gap is very small. This improves printing time and slicing time, but technically leaves infill exposed to the air."
msgstr "Cuando el modelo tiene pequeños huecos verticales de solo unas pocas capas, normalmente suele haber forro alrededor de ellas en el espacio estrecho. Active este ajuste para no generar forro si el hueco vertical es muy pequeño. Esto mejora el tiempo de impresión y de segmentación, pero deja el relleno expuesto al aire."

msgctxt "wall_transition_angle description"
msgid "When to create transitions between even and odd numbers of walls. A wedge shape with an angle greater than this setting will not have transitions and no walls will be printed in the center to fill the remaining space. Reducing this setting reduces the number and length of these center walls, but may leave gaps or overextrude."
msgstr "Cuándo crear transiciones entre números de pared pares e impares. Una forma de cuña con un ángulo mayor que esta configuración no tiene transacciones y no se imprimirán paredes en el centro para rellenar el espacio restante. Reducir esta configuración reduce el número y la longitud de estas paredes centrales, pero puede dejar espacios o sobreextrusión."

msgctxt "wall_transition_length description"
msgid "When transitioning between different numbers of walls as the part becomes thinner, a certain amount of space is allotted to split or join the wall lines."
msgstr "Cuando se pasa de un número de paredes a otro a medida que la pieza se hace más delgada, se asigna una determinada cantidad de espacio para dividir o unir las líneas de contorno."

msgctxt "wipe_hop_enable description"
msgid "When wiping, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "Siempre que se limpia, la placa de impresión se baja para crear holgura entre la tobera y la impresión. Impide que la tobera golpee la impresión durante los movimientos de desplazamiento, reduciendo las posibilidades de golpear la impresión desde la placa de impresión."

msgctxt "retraction_hop_enabled description"
msgid "Whenever a retraction is done, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "Siempre que se realiza una retracción, la placa de impresión se baja para crear holgura entre la tobera y la impresión. Impide que la tobera golpee la impresión durante movimientos de desplazamiento, reduciendo las posibilidades de alcanzar la impresión de la placa de impresión."

msgctxt "support_xy_overrides_z description"
msgid "Whether the Support X/Y Distance overrides the Support Z Distance or vice versa. When X/Y overrides Z the X/Y distance can push away the support from the model, influencing the actual Z distance to the overhang. We can disable this by not applying the X/Y distance around overhangs."
msgstr "Elija si quiere que la distancia X/Y del soporte prevalezca sobre la distancia Z del soporte o viceversa. Si X/Y prevalece sobre Z, la distancia X/Y puede separar el soporte del modelo, lo que afectaría a la distancia Z real con respecto al voladizo. Esta opción puede desactivarse si la distancia X/Y no se aplica a los voladizos."

msgctxt "machine_center_is_zero description"
msgid "Whether the X/Y coordinates of the zero position of the printer is at the center of the printable area."
msgstr "Indica si las coordenadas X/Y de la posición inicial del cabezal de impresión se encuentran en el centro del área de impresión."

msgctxt "machine_endstop_positive_direction_x description"
msgid "Whether the endstop of the X axis is in the positive direction (high X coordinate) or negative (low X coordinate)."
msgstr "Si el tope del eje X se encuentra en la dirección positiva (coordenada X hacia arriba) o negativa (coordenada X hacia abajo)."

msgctxt "machine_endstop_positive_direction_y description"
msgid "Whether the endstop of the Y axis is in the positive direction (high Y coordinate) or negative (low Y coordinate)."
msgstr "Si el tope del eje Y se encuentra en la dirección positiva (coordenada Y hacia arriba) o negativa (coordenada Y hacia abajo)."

msgctxt "machine_endstop_positive_direction_z description"
msgid "Whether the endstop of the Z axis is in the positive direction (high Z coordinate) or negative (low Z coordinate)."
msgstr "Si el tope del eje Z se encuentra en la dirección positiva (coordenada Z hacia arriba) o negativa (coordenada Z hacia abajo)."

msgctxt "machine_extruders_share_heater description"
msgid "Whether the extruders share a single heater rather than each extruder having its own heater."
msgstr "Si los extrusores comparten un único calentador en lugar de que cada extrusor tenga el suyo propio."

msgctxt "machine_extruders_share_nozzle description"
msgid "Whether the extruders share a single nozzle rather than each extruder having its own nozzle. When set to true, it is expected that the printer-start gcode script properly sets up all extruders in an initial retraction state that is known and mutually compatible (either zero or one filament not retracted); in that case the initial retraction status is described, per extruder, by the 'machine_extruders_shared_nozzle_initial_retraction' parameter."
msgstr "Indica si los extrusores comparten una única tobera en lugar de que cada uno tenga la suya propia. Cuando se establece en true, se espera que la secuencia de comandos gcode de inicio de la impresora establezca todos los extrusores en un estado de retracción inicial conocido y mutuamente compatible (ninguno o un solo filamento que no se retrae); en este caso, el estado de retracción inicial se describe, por extrusor, mediante el parámetro \"machine_extruders_shared_nozzle_initial_retraction\"."

msgctxt "machine_heated_bed description"
msgid "Whether the machine has a heated build plate present."
msgstr "Indica si la máquina tiene una placa de impresión caliente."

msgctxt "machine_heated_build_volume description"
msgid "Whether the machine is able to stabilize the build volume temperature."
msgstr "Si la máquina puede estabilizar la temperatura del volumen de impresión."

msgctxt "center_object description"
msgid "Whether to center the object on the middle of the build platform (0,0), instead of using the coordinate system in which the object was saved."
msgstr "Centrar o no el objeto en el centro de la plataforma de impresión (0, 0), en vez de utilizar el sistema de coordenadas con el que se guardó el objeto."

msgctxt "machine_nozzle_temp_enabled description"
msgid "Whether to control temperature from Cura. Turn this off to control nozzle temperature from outside of Cura."
msgstr "Para controlar la temperatura desde Cura. Si va a controlar la temperatura de la tobera desde fuera de Cura, desactive esta opción."

msgctxt "material_bed_temp_prepend description"
msgid "Whether to include build plate temperature commands at the start of the gcode. When the start_gcode already contains build plate temperature commands Cura frontend will automatically disable this setting."
msgstr "Elija si desea incluir comandos de temperatura de la placa de impresión al iniciar el Gcode. Si start_gcode ya contiene comandos de temperatura de la placa de impresión, la interfaz de Cura desactivará este ajuste de forma automática."

msgctxt "material_print_temp_prepend description"
msgid "Whether to include nozzle temperature commands at the start of the gcode. When the start_gcode already contains nozzle temperature commands Cura frontend will automatically disable this setting."
msgstr "Elija si desea incluir comandos de temperatura de la tobera al inicio del Gcode. Si start_gcode ya contiene comandos de temperatura de la tobera, la interfaz de Cura desactivará este ajuste de forma automática."

msgctxt "clean_between_layers description"
msgid "Whether to include nozzle wipe G-Code between layers (maximum 1 per layer). Enabling this setting could influence behavior of retract at layer change. Please use Wipe Retraction settings to control retraction at layers where the wipe script will be working."
msgstr "Posibilidad de incluir GCode de limpieza de tobera entre capas (máximo 1 por capa). Habilitar este ajuste puede influir en el comportamiento de retracción en el cambio de capa. Utilice los ajustes de retracción de limpieza para controlar la retracción en las capas donde la secuencia de limpieza estará en curso."

msgctxt "material_bed_temp_wait description"
msgid "Whether to insert a command to wait until the build plate temperature is reached at the start."
msgstr "Elija si desea escribir un comando para esperar a que la temperatura de la placa de impresión se alcance al inicio."

msgctxt "prime_blob_enable description"
msgid "Whether to prime the filament with a blob before printing. Turning this setting on will ensure that the extruder will have material ready at the nozzle before printing. Printing Brim or Skirt can act like priming too, in which case turning this setting off saves some time."
msgstr "Si cebar el filamento con una gota antes de imprimir. Al activar este ajuste se garantiza que el extrusor tendrá material listo en la tobera antes de imprimir. La impresión de borde o falda puede actuar como cebado también, en este caso ahorrará tiempo al desactivar este ajuste."

msgctxt "print_sequence description"
msgid "Whether to print all models one layer at a time or to wait for one model to finish, before moving on to the next. One at a time mode is possible if a) only one extruder is enabled and b) all models are separated in such a way that the whole print head can move in between and all models are lower than the distance between the nozzle and the X/Y axes."
msgstr "Con esta opción se decide si imprimir todos los modelos al mismo tiempo capa por capa o esperar a terminar un modelo antes de pasar al siguiente. El modo de uno en uno solo es posible si todos los modelos están lo suficientemente separados para que el cabezal de impresión pase entre ellos y todos estén a menos de la distancia entre la boquilla y los ejes X/Y."

msgctxt "machine_show_variants description"
msgid "Whether to show the different variants of this machine, which are described in separate json files."
msgstr "Elija si desea mostrar las diferentes versiones de esta máquina, las cuales están descritas en archivos .json independientes."

msgctxt "machine_firmware_retract description"
msgid "Whether to use firmware retract commands (G10/G11) instead of using the E property in G1 commands to retract the material."
msgstr "Utilizar o no los comandos de retracción de firmware (G10/G11) en lugar de utilizar la propiedad E en comandos G1 para retraer el material."

msgctxt "material_print_temp_wait description"
msgid "Whether to wait until the nozzle temperature is reached at the start."
msgstr "Elija si desea esperar a que la temperatura de la tobera se alcance al inicio."

msgctxt "infill_line_width description"
msgid "Width of a single infill line."
msgstr "Ancho de una sola línea de relleno."

msgctxt "support_interface_line_width description"
msgid "Width of a single line of support roof or floor."
msgstr "Ancho de una sola línea de techo o suelo de soporte."

msgctxt "roofing_line_width description"
msgid "Width of a single line of the areas at the top of the print."
msgstr "Ancho de una sola línea de las áreas superiores de la impresión."

msgctxt "line_width description"
msgid "Width of a single line. Generally, the width of each line should correspond to the width of the nozzle. However, slightly reducing this value could produce better prints."
msgstr "Ancho de una única línea. Generalmente, el ancho de cada línea se debería corresponder con el ancho de la tobera. Sin embargo, reducir este valor ligeramente podría producir mejores impresiones."

msgctxt "prime_tower_line_width description"
msgid "Width of a single prime tower line."
msgstr "Ancho de una sola línea de la torre auxiliar."

msgctxt "skirt_brim_line_width description"
msgid "Width of a single skirt or brim line."
msgstr "Ancho de una sola línea de falda o borde."

msgctxt "support_bottom_line_width description"
msgid "Width of a single support floor line."
msgstr "Ancho de una sola línea de suelo de soporte."

msgctxt "support_roof_line_width description"
msgid "Width of a single support roof line."
msgstr "Ancho de una sola línea de techo de soporte."

msgctxt "support_line_width description"
msgid "Width of a single support structure line."
msgstr "Ancho de una sola línea de estructura de soporte."

msgctxt "skin_line_width description"
msgid "Width of a single top/bottom line."
msgstr "Ancho de una sola línea superior/inferior."

msgctxt "wall_line_width_x description"
msgid "Width of a single wall line for all wall lines except the outermost one."
msgstr "Ancho de una sola línea de pared para todas las líneas de pared excepto la más externa."

msgctxt "wall_line_width description"
msgid "Width of a single wall line."
msgstr "Ancho de una sola línea de pared."

msgctxt "raft_base_line_width description"
msgid "Width of the lines in the base raft layer. These should be thick lines to assist in build plate adhesion."
msgstr "Ancho de las líneas de la capa base de la balsa. Estas deben ser líneas gruesas para facilitar la adherencia a la placa e impresión."

msgctxt "raft_interface_line_width description"
msgid "Width of the lines in the middle raft layer. Making the second layer extrude more causes the lines to stick to the build plate."
msgstr "Ancho de las líneas de la capa intermedia de la balsa. Haciendo la segunda capa con mayor extrusión las líneas se adhieren a la placa de impresión."

msgctxt "raft_surface_line_width description"
msgid "Width of the lines in the top surface of the raft. These can be thin lines so that the top of the raft becomes smooth."
msgstr "Ancho de las líneas de la superficie superior de la balsa. Estas pueden ser líneas finas para que la parte superior de la balsa sea lisa."

msgctxt "wall_line_width_0 description"
msgid "Width of the outermost wall line. By lowering this value, higher levels of detail can be printed."
msgstr "Ancho de la línea de pared más externa. Reduciendo este valor se puede imprimir con un mayor nivel de detalle."

msgctxt "min_bead_width description"
msgid "Width of the wall that will replace thin features (according to the Minimum Feature Size) of the model. If the Minimum Wall Line Width is thinner than the thickness of the feature, the wall will become as thick as the feature itself."
msgstr "Ancho de la pared que reemplazará las características delgadas (según el tamaño mínimo de la característica) del modelo. Si el ancho mínimo de la línea perimetral es más delgado que el grosor de la característica, la pared se volverá tan gruesa como la propia característica."

msgctxt "wipe_brush_pos_x label"
msgid "Wipe Brush X Position"
msgstr "Limpiar posición X de cepillo"

msgctxt "wipe_hop_speed label"
msgid "Wipe Hop Speed"
msgstr "Limpiar velocidad de salto"

msgctxt "prime_tower_wipe_enabled label"
msgid "Wipe Inactive Nozzle on Prime Tower"
msgstr "Limpiar tobera inactiva de la torre auxiliar"

msgctxt "wipe_move_distance label"
msgid "Wipe Move Distance"
msgstr "Distancia de movimiento de limpieza"

msgctxt "clean_between_layers label"
msgid "Wipe Nozzle Between Layers"
msgstr "Limpiar tobera entre capas"

msgctxt "wipe_pause label"
msgid "Wipe Pause"
msgstr "Pausar limpieza"

msgctxt "wipe_repeat_count label"
msgid "Wipe Repeat Count"
msgstr "Recuento de repeticiones de limpieza"

msgctxt "wipe_retraction_amount label"
msgid "Wipe Retraction Distance"
msgstr "Distancia de retracción de limpieza"

msgctxt "wipe_retraction_enable label"
msgid "Wipe Retraction Enable"
msgstr "Habilitación de retracción de limpieza"

msgctxt "wipe_retraction_extra_prime_amount label"
msgid "Wipe Retraction Extra Prime Amount"
msgstr "Cantidad de cebado adicional de retracción de limpieza"

msgctxt "wipe_retraction_prime_speed label"
msgid "Wipe Retraction Prime Speed"
msgstr "Velocidad de cebado de retracción de limpieza"

msgctxt "wipe_retraction_retract_speed label"
msgid "Wipe Retraction Retract Speed"
msgstr "Velocidad de retracción en retracción de limpieza"

msgctxt "wipe_retraction_speed label"
msgid "Wipe Retraction Speed"
msgstr "Velocidad de retracción de limpieza"

msgctxt "wipe_hop_enable label"
msgid "Wipe Z Hop"
msgstr "Limpiar salto en Z"

msgctxt "wipe_hop_amount label"
msgid "Wipe Z Hop Height"
msgstr "Limpiar altura del salto en Z"

msgctxt "retraction_combing option infill"
msgid "Within Infill"
msgstr "Sobre el relleno"

msgctxt "machine_always_write_active_tool description"
msgid "Write active tool after sending temp commands to inactive tool. Required for Dual Extruder printing with Smoothie or other firmware with modal tool commands."
msgstr "Escriba la herramienta activa después de enviar comandos temporales a la herramienta inactiva. Requerido para la impresión de extrusión dual con Smoothie u otro firmware con comandos de herramientas modales."

msgctxt "machine_endstop_positive_direction_x label"
msgid "X Endstop in Positive Direction"
msgstr "Tope de X en dirección positiva"

msgctxt "wipe_brush_pos_x description"
msgid "X location where wipe script will start."
msgstr "Ubicación X donde se iniciará la secuencia de limpieza."

msgctxt "support_xy_overrides_z option xy_overrides_z"
msgid "X/Y overrides Z"
msgstr "X/Y sobre Z"

msgctxt "machine_endstop_positive_direction_y label"
msgid "Y Endstop in Positive Direction"
msgstr "Tope de Y en dirección positiva"

msgctxt "machine_endstop_positive_direction_z label"
msgid "Z Endstop in Positive Direction"
msgstr "Tope de Z en dirección positiva"

msgctxt "retraction_hop_after_extruder_switch label"
msgid "Z Hop After Extruder Switch"
msgstr "Salto en Z tras cambio de extrusor"

msgctxt "retraction_hop_after_extruder_switch_height label"
msgid "Z Hop After Extruder Switch Height"
msgstr "Salto en Z tras altura de cambio de extrusor"

msgctxt "retraction_hop label"
msgid "Z Hop Height"
msgstr "Altura del salto en Z"

msgctxt "retraction_hop_only_when_collides label"
msgid "Z Hop Only Over Printed Parts"
msgstr "Salto en Z solo en las partes impresas"

msgctxt "speed_z_hop label"
msgid "Z Hop Speed"
msgstr "Velocidad del salto en Z"

msgctxt "retraction_hop_enabled label"
msgid "Z Hop When Retracted"
msgstr "Salto en Z en la retracción"

msgctxt "z_seam_type label"
msgid "Z Seam Alignment"
msgstr "Alineación de costuras en Z"

msgctxt "z_seam_position label"
msgid "Z Seam Position"
msgstr "Posición de costura en Z"

msgctxt "z_seam_relative label"
msgid "Z Seam Relative"
msgstr "Costuras relativas en Z"

msgctxt "z_seam_x label"
msgid "Z Seam X"
msgstr "X de la costura Z"

msgctxt "z_seam_y label"
msgid "Z Seam Y"
msgstr "Y de la costura Z"

msgctxt "support_xy_overrides_z option z_overrides_xy"
msgid "Z overrides X/Y"
msgstr "Z sobre X/Y"

msgctxt "infill_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "ironing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "roofing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "support_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "support_interface_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "support_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "support_roof_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "top_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "top_bottom_pattern_0 option zigzag"
msgid "Zig Zag"
msgstr "Zigzag"

msgctxt "travel description"
msgid "travel"
msgstr "desplazamiento"

msgctxt "gradual_flow_discretisation_step_size description"
msgid "Duration of each step in the gradual flow change"
msgstr "Duración de cada paso en el cambio de flujo gradual"

msgctxt "gradual_flow_enabled description"
msgid "Enable gradual flow changes. When enabled, the flow is gradually increased/decreased to the target flow. This is useful for printers with a bowden tube where the flow is not immediately changed when the extruder motor starts/stops."
msgstr "Activar cambios graduales de flujo. Cuando está activado, el flujo aumenta/disminuye gradualmente hasta el flujo objetivo. Esto es útil para impresoras con un tubo bowden donde el flujo no cambia inmediatamente cuando el motor del extrusor se enciende/apaga."

msgctxt "reset_flow_duration description"
msgid "For any travel move longer than this value, the material flow is reset to the paths target flow"
msgstr "Para cualquier movimiento de desplazamiento superior a este valor, el flujo de material se restablece con el flujo objetivo de las trayectorias"

msgctxt "gradual_flow_discretisation_step_size label"
msgid "Gradual flow discretisation step size"
msgstr "Tamaño del paso de discretización del flujo gradual"

msgctxt "gradual_flow_enabled label"
msgid "Gradual flow enabled"
msgstr "Flujo gradual activado"

msgctxt "max_flow_acceleration label"
msgid "Gradual flow max acceleration"
msgstr "Aceleración máxima del flujo gradual"

msgctxt "layer_0_max_flow_acceleration label"
msgid "Initial layer max flow acceleration"
msgstr "Aceleración máxima del flujo de la capa inicial"

msgctxt "max_flow_acceleration description"
msgid "Maximum acceleration for gradual flow changes"
msgstr "Aceleración máxima para los cambios de flujo graduales"

msgctxt "layer_0_max_flow_acceleration description"
msgid "Minimum speed for gradual flow changes for the first layer"
msgstr "Velocidad mínima para los cambios de flujo graduales de la primera capa"

msgctxt "reset_flow_duration label"
msgid "Reset flow duration"
msgstr "Restablecer duración del flujo"
