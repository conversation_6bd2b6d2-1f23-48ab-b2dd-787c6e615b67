[general]
definition = deltacomb_base
name = Accurate
version = 4

[metadata]
intent_category = engineering
material = generic_pla
quality_type = D010
setting_version = 22
type = intent
variant = FBE 0.40mm

[values]
jerk_wall = 5
jerk_wall_0 = =jerk_wall
jerk_wall_x = =jerk_wall
top_bottom_thickness = =wall_thickness
wall_thickness = =line_width * 3
xy_offset = =-layer_height * 0.2
xy_offset_layer_0 = =((-0.2 + layer_height * 0.2) if adhesion_type == "skirt" or adhesion_type == "none" else 0) + xy_offset

