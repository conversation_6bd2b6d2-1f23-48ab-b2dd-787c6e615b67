########## MACROS ###########################################################################
#############################################################################################

# Requires CMake > 3.15
if(${CMAKE_VERSION} VERSION_LESS "3.15")
    message(FATAL_ERROR "The 'CMakeDeps' generator only works with CMake >= 3.15")
endif()

if(mapbox-wagyu_FIND_QUIETLY)
    set(mapbox-wagyu_MESSAGE_MODE VERBOSE)
else()
    set(mapbox-wagyu_MESSAGE_MODE STATUS)
endif()

include(${CMAKE_CURRENT_LIST_DIR}/cmakedeps_macros.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/mapbox-wagyuTargets.cmake)
include(CMakeFindDependencyMacro)

check_build_type_defined()

foreach(_DEPENDENCY ${mapbox-wagyu_FIND_DEPENDENCY_NAMES} )
    # Check that we have not already called a find_package with the transitive dependency
    if(NOT ${_DEPENDENCY}_FOUND)
        find_dependency(${_DEPENDENCY} REQUIRED ${${_DEPENDENCY}_FIND_MODE})
    endif()
endforeach()

set(mapbox-wagyu_VERSION_STRING "0.5.0")
set(mapbox-wagyu_INCLUDE_DIRS ${mapbox-wagyu_INCLUDE_DIRS_RELEASE} )
set(mapbox-wagyu_INCLUDE_DIR ${mapbox-wagyu_INCLUDE_DIRS_RELEASE} )
set(mapbox-wagyu_LIBRARIES ${mapbox-wagyu_LIBRARIES_RELEASE} )
set(mapbox-wagyu_DEFINITIONS ${mapbox-wagyu_DEFINITIONS_RELEASE} )


# Only the last installed configuration BUILD_MODULES are included to avoid the collision
foreach(_BUILD_MODULE ${mapbox-wagyu_BUILD_MODULES_PATHS_RELEASE} )
    message(${mapbox-wagyu_MESSAGE_MODE} "Conan: Including build module from '${_BUILD_MODULE}'")
    include(${_BUILD_MODULE})
endforeach()


