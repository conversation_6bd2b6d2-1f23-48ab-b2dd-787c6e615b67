# Cura和Uranium并行开发环境 - PyCharm配置指南

## 环境概述

✅ **已成功配置的环境：**
- Cura项目：`C:\Mac\Home\Desktop\CuraProject\Cura`
- Uranium项目：`C:\Mac\Home\Desktop\CuraProject\Uranium` (已设置为editable模式)
- 虚拟环境：`C:\Mac\Home\Desktop\CuraProject\Cura\build_windows\build_windows\generators\cura_venv`
- 构建目录：`C:\Mac\Home\Desktop\CuraProject\Cura\build_windows` (带Windows系统后缀)
- PyCharm运行配置：`C:\Mac\Home\Desktop\CuraProject\Cura\.run\`

## PyCharm项目设置

### 1. 打开项目
1. 启动PyCharm
2. 选择 "Open" 
3. 导航到 `C:\Mac\Home\Desktop\CuraProject\Cura`
4. 点击 "OK"

### 2. 配置Python解释器
1. 打开 `File` → `Settings` (或 `Ctrl+Alt+S`)
2. 导航到 `Project: Cura` → `Python Interpreter`
3. 点击齿轮图标 → `Add...`
4. 选择 `Existing environment`
5. 设置解释器路径为：
   ```
   C:\Mac\Home\Desktop\CuraProject\Cura\build_windows\build_windows\generators\cura_venv\Scripts\python.exe
   ```
6. 点击 "OK"

### 3. 添加Uranium作为附加项目
1. 在PyCharm中，右键点击项目根目录
2. 选择 `Mark Directory as` → `Sources Root`
3. 打开 `File` → `Settings`
4. 导航到 `Project: Cura` → `Project Structure`
5. 点击 "Add Content Root"
6. 选择 `C:\Mac\Home\Desktop\CuraProject\Uranium`
7. 将Uranium目录标记为 "Sources"

### 4. 配置运行配置
PyCharm运行配置已自动生成在 `.run` 目录中：

**主要运行配置：**
- `cura.run.xml` - 启动Cura应用程序
- `cura_external_engine.run.xml` - 使用外部引擎启动Cura
- 各种pytest配置用于运行测试

### 5. 验证环境
1. 在PyCharm终端中运行：
   ```bash
   python -c "import UM; print('Uranium imported successfully')"
   python -c "import cura; print('Cura imported successfully')"
   ```

## 开发工作流

### 启动Cura
1. 在PyCharm中选择运行配置 "cura"
2. 点击运行按钮或按 `Shift+F10`

### 调试
1. 在代码中设置断点
2. 选择运行配置 "cura"
3. 点击调试按钮或按 `Shift+F9`

### 运行测试
- 使用生成的pytest配置运行特定测试
- 或在终端中运行：`python -m pytest tests/`

## 重要说明

1. **Uranium Editable模式**：Uranium已设置为editable模式，对Uranium源码的修改会立即反映在Cura中
2. **系统后缀**：所有构建文件和虚拟环境都使用了`_windows`后缀，避免与macOS环境冲突
3. **CuraEngine已排除**：按要求排除了CuraEngine的构建，您可以单独开发
4. **依赖管理**：所有Python依赖已安装在虚拟环境中

## 故障排除

如果遇到导入错误：
1. 确认Python解释器指向正确的虚拟环境
2. 检查Uranium是否正确添加到项目结构中
3. 重新激活虚拟环境：
   ```bash
   C:\Mac\Home\Desktop\CuraProject\Cura\build_windows\build_windows\generators\cura_venv\Scripts\activate.bat
   ```

## 下一步

环境已准备就绪！您现在可以：
1. 在PyCharm中同时开发Cura和Uranium
2. 使用调试器进行调试
3. 运行测试验证更改
4. 享受高效的并行开发体验！
