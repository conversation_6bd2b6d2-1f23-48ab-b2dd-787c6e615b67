[{"classes": [{"className": "LocalClientIo", "lineNumber": 25, "object": true, "qualifiedClassName": "LocalClientIo", "slots": [{"access": "public", "arguments": [{"name": "error", "type": "QLocalSocket::LocalSocketError"}], "index": 0, "name": "onError", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QLocalSocket::LocalSocketState"}], "index": 1, "name": "onStateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtROClientIoDevice"}]}, {"className": "LocalServerIo", "lineNumber": 59, "object": true, "qualifiedClassName": "LocalServerIo", "superClasses": [{"access": "public", "name": "QtROServerIoDevice"}]}, {"className": "LocalServerImpl", "lineNumber": 73, "object": true, "qualifiedClassName": "LocalServerImpl", "superClasses": [{"access": "public", "name": "QConnectionAbstractServer"}]}], "inputFile": "qconnection_local_backend_p.h", "outputRevision": 69}, {"classes": [{"className": "TcpClientIo", "lineNumber": 25, "object": true, "qualifiedClassName": "TcpClientIo", "slots": [{"access": "public", "arguments": [{"name": "error", "type": "QAbstractSocket::SocketError"}], "index": 0, "name": "onError", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QAbstractSocket::SocketState"}], "index": 1, "name": "onStateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtROClientIoDevice"}]}, {"className": "TcpServerIo", "lineNumber": 49, "object": true, "qualifiedClassName": "TcpServerIo", "superClasses": [{"access": "public", "name": "QtROServerIoDevice"}]}, {"className": "TcpServerImpl", "lineNumber": 63, "object": true, "qualifiedClassName": "TcpServerImpl", "superClasses": [{"access": "public", "name": "QConnectionAbstractServer"}]}], "inputFile": "qconnection_tcpip_backend_p.h", "outputRevision": 69}, {"classes": [{"className": "QtROIoDeviceBase", "lineNumber": 29, "object": true, "qualifiedClassName": "QtROIoDeviceBase", "signals": [{"access": "public", "index": 0, "name": "readyRead", "returnType": "void"}, {"access": "public", "index": 1, "name": "disconnected", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QtROServerIoDevice", "lineNumber": 68, "object": true, "qualifiedClassName": "QtROServerIoDevice", "superClasses": [{"access": "public", "name": "QtROIoDeviceBase"}]}, {"className": "QConnectionAbstractServer", "lineNumber": 80, "object": true, "qualifiedClassName": "QConnectionAbstractServer", "signals": [{"access": "public", "index": 0, "name": "newConnection", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QtROClientIoDevice", "lineNumber": 103, "object": true, "qualifiedClassName": "QtROClientIoDevice", "signals": [{"access": "public", "arguments": [{"type": "QtROClientIoDevice*"}], "index": 0, "name": "shouldReconnect", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QRemoteObjectNode::ErrorCode"}], "index": 1, "name": "setError", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QtROIoDeviceBase"}]}], "inputFile": "qconnectionfactories.h", "outputRevision": 69}, {"classes": [{"className": "QtROExternalIoDevice", "lineNumber": 38, "object": true, "qualifiedClassName": "QtROExternalIoDevice", "superClasses": [{"access": "public", "name": "QtROIoDeviceBase"}]}], "inputFile": "qconnectionfactories_p.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractItemModelSourceAdapter", "constructors": [{"access": "public", "arguments": [{"name": "object", "type": "QAbstractItemModel*"}, {"name": "sel", "type": "QItemSelectionModel*"}, {"name": "roles", "type": "QList<int>"}], "index": 0, "name": "QAbstractItemModelSourceAdapter", "returnType": ""}, {"access": "public", "arguments": [{"name": "object", "type": "QAbstractItemModel*"}, {"name": "sel", "type": "QItemSelectionModel*"}], "index": 1, "isCloned": true, "name": "QAbstractItemModelSourceAdapter", "returnType": ""}], "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "availableRoles", "notify": "availableRolesChanged", "read": "availableRoles", "required": false, "scriptable": true, "stored": true, "type": "QList<int>", "user": false, "write": "setAvailableRoles"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "roleNames", "read": "roleNames", "required": false, "scriptable": true, "stored": true, "type": "QIntHash", "user": false}], "qualifiedClassName": "QAbstractItemModelSourceAdapter", "signals": [{"access": "public", "index": 0, "name": "availableRolesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QtPrivate::IndexList"}, {"name": "bottomRight", "type": "QtPrivate::IndexList"}, {"name": "roles", "type": "QList<int>"}], "index": 1, "isConst": true, "name": "dataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QtPrivate::IndexList"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "index": 2, "isConst": true, "name": "rowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QtPrivate::IndexList"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "index": 3, "isConst": true, "name": "rowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceParent", "type": "QtPrivate::IndexList"}, {"name": "sourceRow", "type": "int"}, {"name": "count", "type": "int"}, {"name": "destinationParent", "type": "QtPrivate::IndexList"}, {"name": "destinationChild", "type": "int"}], "index": 4, "isConst": true, "name": "rowsMoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "current", "type": "QtPrivate::IndexList"}, {"name": "previous", "type": "QtPrivate::IndexList"}], "index": 5, "name": "currentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QtPrivate::IndexList"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "index": 6, "isConst": true, "name": "columnsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parents", "type": "QtPrivate::IndexList"}, {"name": "hint", "type": "QAbstractItemModel::LayoutChangeHint"}], "index": 7, "name": "layoutChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 8, "isConst": true, "name": "availableRoles", "returnType": "QList<int>"}, {"access": "public", "arguments": [{"name": "availableRoles", "type": "QList<int>"}], "index": 9, "name": "setAvailableRoles", "returnType": "void"}, {"access": "public", "index": 10, "isConst": true, "name": "roleNames", "returnType": "QIntHash"}, {"access": "public", "arguments": [{"name": "parentList", "type": "QtPrivate::IndexList"}], "index": 11, "name": "replicaSizeRequest", "returnType": "QSize"}, {"access": "public", "arguments": [{"name": "start", "type": "QtPrivate::IndexList"}, {"name": "end", "type": "QtPrivate::IndexList"}, {"name": "roles", "type": "QList<int>"}], "index": 12, "name": "replicaRowRequest", "returnType": "QtPrivate::DataEntries"}, {"access": "public", "arguments": [{"name": "orientations", "type": "QList<Qt::Orientation>"}, {"name": "sections", "type": "QList<int>"}, {"name": "roles", "type": "QList<int>"}], "index": 13, "name": "replicaHeaderRequest", "returnType": "QVariantList"}, {"access": "public", "arguments": [{"name": "index", "type": "QtPrivate::IndexList"}, {"name": "command", "type": "QItemSelectionModel::SelectionFlags"}], "index": 14, "name": "replicaSetCurrentIndex", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "QtPrivate::IndexList"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}, {"name": "role", "type": "int"}], "index": 15, "name": "replicaSetData", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "size_t"}, {"name": "roles", "type": "QList<int>"}], "index": 16, "name": "replicaCacheRequest", "returnType": "QtPrivate::MetaAndDataEntries"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}, {"name": "roles", "type": "QList<int>"}], "index": 17, "isConst": true, "name": "sourceDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "index": 18, "isCloned": true, "isConst": true, "name": "sourceDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "index": 19, "name": "sourceRowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "index": 20, "name": "sourceColumnsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "index": 21, "name": "sourceRowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceParent", "type": "QModelIndex"}, {"name": "sourceRow", "type": "int"}, {"name": "count", "type": "int"}, {"name": "destinationParent", "type": "QModelIndex"}, {"name": "destinationChild", "type": "int"}], "index": 22, "isConst": true, "name": "sourceRowsMoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "current", "type": "QModelIndex"}, {"name": "previous", "type": "QModelIndex"}], "index": 23, "name": "sourceCurrentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parents", "type": "QList<QPersistentModelIndex>"}, {"name": "hint", "type": "QAbstractItemModel::LayoutChangeHint"}], "index": 24, "name": "sourceLayoutChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qremoteobjectabstractitemmodeladapter_p.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractItemModelReplica", "lineNumber": 16, "object": true, "qualifiedClassName": "QAbstractItemModelReplica", "signals": [{"access": "public", "index": 0, "name": "initialized", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractItemModel"}]}], "inputFile": "qremoteobjectabstractitemmodelreplica.h", "outputRevision": 69}, {"classes": [{"className": "SizeWatcher", "lineNumber": 251, "object": true, "qualifiedClassName": "SizeWatcher", "superClasses": [{"access": "public", "name": "QRemoteObjectPendingCallWatcher"}]}, {"className": "RowWatcher", "lineNumber": 261, "object": true, "qualifiedClassName": "RowWatcher", "superClasses": [{"access": "public", "name": "QRemoteObjectPendingCallWatcher"}]}, {"className": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lineNumber": 274, "object": true, "qualifiedClassName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "superClasses": [{"access": "public", "name": "QRemoteObjectPendingCallWatcher"}]}, {"classInfos": [{"name": "RemoteObject Type", "value": "ServerModelAdapter"}], "className": "QAbstractItemModelReplicaImplementation", "lineNumber": 287, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "availableRoles", "notify": "availableRolesChanged", "read": "availableRoles", "required": false, "scriptable": true, "stored": true, "type": "QList<int>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "roleNames", "read": "roleNames", "required": false, "scriptable": true, "stored": true, "type": "QIntHash", "user": false}], "qualifiedClassName": "QAbstractItemModelReplicaImplementation", "signals": [{"access": "public", "index": 0, "name": "availableRolesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QtPrivate::IndexList"}, {"name": "bottomRight", "type": "QtPrivate::IndexList"}, {"name": "roles", "type": "QList<int>"}], "index": 1, "name": "dataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QtPrivate::IndexList"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 2, "name": "rowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QtPrivate::IndexList"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 3, "name": "rowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QtPrivate::IndexList"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}, {"name": "destination", "type": "QtPrivate::IndexList"}, {"name": "row", "type": "int"}], "index": 4, "name": "rowsMoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "current", "type": "QtPrivate::IndexList"}, {"name": "previous", "type": "QtPrivate::IndexList"}], "index": 5, "name": "currentChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "modelReset", "returnType": "void"}, {"access": "public", "arguments": [{"type": "Qt::Orientation"}, {"type": "int"}, {"type": "int"}], "index": 7, "name": "headerDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QtPrivate::IndexList"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 8, "name": "columnsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parents", "type": "QtPrivate::IndexList"}, {"name": "hint", "type": "QAbstractItemModel::LayoutChangeHint"}], "index": 9, "name": "layoutChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "parentList", "type": "QtPrivate::IndexList"}], "index": 10, "name": "replicaSizeRequest", "returnType": "QRemoteObjectPendingReply<QSize>"}, {"access": "public", "arguments": [{"name": "start", "type": "QtPrivate::IndexList"}, {"name": "end", "type": "QtPrivate::IndexList"}, {"name": "roles", "type": "QList<int>"}], "index": 11, "name": "replicaRowRequest", "returnType": "QRemoteObjectPendingReply<QtPrivate::DataEntries>"}, {"access": "public", "arguments": [{"name": "orientations", "type": "QList<Qt::Orientation>"}, {"name": "sections", "type": "QList<int>"}, {"name": "roles", "type": "QList<int>"}], "index": 12, "name": "replicaHeaderRequest", "returnType": "QRemoteObjectPendingReply<QVariantList>"}, {"access": "public", "arguments": [{"name": "index", "type": "QtPrivate::IndexList"}, {"name": "command", "type": "QItemSelectionModel::SelectionFlags"}], "index": 13, "name": "replicaSetCurrentIndex", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "QtPrivate::IndexList"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}, {"name": "role", "type": "int"}], "index": 14, "name": "replicaSetData", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "size_t"}, {"name": "roles", "type": "QList<int>"}], "index": 15, "name": "replicaCacheRequest", "returnType": "QRemoteObjectPendingReply<QtPrivate::MetaAndDataEntries>"}, {"access": "public", "arguments": [{"name": "orientation", "type": "Qt::Orientation"}, {"name": "first", "type": "int"}, {"name": "last", "type": "int"}], "index": 16, "name": "onHeaderDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "start", "type": "QtPrivate::IndexList"}, {"name": "end", "type": "QtPrivate::IndexList"}, {"name": "roles", "type": "QList<int>"}], "index": 17, "name": "onDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QtPrivate::IndexList"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "index": 18, "name": "onRowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QtPrivate::IndexList"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "index": 19, "name": "onRowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QtPrivate::IndexList"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "index": 20, "name": "onColumnsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "srcParent", "type": "QtPrivate::IndexList"}, {"name": "srcRow", "type": "int"}, {"name": "count", "type": "int"}, {"name": "dest<PERSON>arent", "type": "QtPrivate::IndexList"}, {"name": "destRow", "type": "int"}], "index": 21, "name": "onRowsMoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "current", "type": "QtPrivate::IndexList"}, {"name": "previous", "type": "QtPrivate::IndexList"}], "index": 22, "name": "onCurrentChanged", "returnType": "void"}, {"access": "public", "index": 23, "name": "onModelReset", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QRemoteObjectPendingCallWatcher*"}], "index": 24, "name": "requestedData", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QRemoteObjectPendingCallWatcher*"}], "index": 25, "name": "requestedHeaderData", "returnType": "void"}, {"access": "public", "index": 26, "name": "init", "returnType": "void"}, {"access": "public", "index": 27, "name": "fetchPendingData", "returnType": "void"}, {"access": "public", "index": 28, "name": "fetchPendingHeaderData", "returnType": "void"}, {"access": "public", "arguments": [{"name": "watcher", "type": "QRemoteObjectPendingCallWatcher*"}], "index": 29, "name": "handleInitDone", "returnType": "void"}, {"access": "public", "arguments": [{"name": "watcher", "type": "QRemoteObjectPendingCallWatcher*"}], "index": 30, "name": "handleModelResetDone", "returnType": "void"}, {"access": "public", "arguments": [{"name": "watcher", "type": "QRemoteObjectPendingCallWatcher*"}], "index": 31, "name": "handleSizeDone", "returnType": "void"}, {"access": "public", "arguments": [{"name": "current", "type": "QModelIndex"}, {"name": "previous", "type": "QModelIndex"}], "index": 32, "name": "onReplicaCurrentChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pair", "type": "QtPrivate::IndexValuePair"}, {"name": "roles", "type": "QList<int>"}], "index": 33, "name": "fillCache", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parents", "type": "QtPrivate::IndexList"}, {"name": "hint", "type": "QAbstractItemModel::LayoutChangeHint"}], "index": 34, "name": "onLayoutChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QRemoteObjectReplica"}]}], "inputFile": "qremoteobjectabstractitemmodelreplica_p.h", "outputRevision": 69}, {"classes": [{"className": "ProxyInfo", "lineNumber": 72, "object": true, "qualifiedClassName": "ProxyInfo", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qremoteobjectnode_p.h", "outputRevision": 69}, {"classes": [{"className": "QRemoteObjectPackets", "enums": [{"isClass": true, "isFlag": false, "name": "ObjectType", "type": "quint8", "values": ["CLASS", "MODEL", "GADGET"]}], "lineNumber": 41, "namespace": true, "qualifiedClassName": "QRemoteObjectPackets"}], "inputFile": "qremoteobjectpacket_p.h", "outputRevision": 69}, {"classes": [{"className": "QRemoteObjectPendingCallWatcherHelper", "lineNumber": 47, "object": true, "qualifiedClassName": "QRemoteObjectPendingCallWatcherHelper", "signals": [{"access": "public", "index": 0, "name": "finished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qremoteobjectpendingcall_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "RemoteObject Type", "value": "Registry"}], "className": "QRemoteObjectRegistry", "lineNumber": 14, "object": true, "properties": [{"bindable": "bindableSourceLocations", "constant": false, "designable": true, "final": false, "index": 0, "name": "sourceLocations", "read": "sourceLocations", "required": false, "scriptable": true, "stored": false, "type": "QRemoteObjectSourceLocations", "user": false}], "qualifiedClassName": "QRemoteObjectRegistry", "signals": [{"access": "public", "arguments": [{"name": "entry", "type": "QRemoteObjectSourceLocation"}], "index": 0, "name": "remoteObjectAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "entry", "type": "QRemoteObjectSourceLocation"}], "index": 1, "name": "remoteObjectRemoved", "returnType": "void"}], "slots": [{"access": "protected", "arguments": [{"name": "entry", "type": "QRemoteObjectSourceLocation"}], "index": 2, "name": "addSource", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "entry", "type": "QRemoteObjectSourceLocation"}], "index": 3, "name": "removeSource", "returnType": "void"}, {"access": "protected", "index": 4, "name": "pushToRegistryIfNeeded", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QRemoteObjectReplica"}]}], "inputFile": "qremoteobjectregistry.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "RemoteObject Type", "value": "Registry"}], "className": "QRegistrySource", "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "sourceLocations", "read": "sourceLocations", "required": false, "scriptable": true, "stored": true, "type": "QRemoteObjectSourceLocations", "user": false}], "qualifiedClassName": "QRegistrySource", "signals": [{"access": "public", "arguments": [{"name": "entry", "type": "QRemoteObjectSourceLocation"}], "index": 0, "name": "remoteObjectAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "entry", "type": "QRemoteObjectSourceLocation"}], "index": 1, "name": "remoteObjectRemoved", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "entry", "type": "QRemoteObjectSourceLocation"}], "index": 2, "name": "addSource", "returnType": "void"}, {"access": "public", "arguments": [{"name": "entry", "type": "QRemoteObjectSourceLocation"}], "index": 3, "name": "removeSource", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "index": 4, "name": "removeServer", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qremoteobjectregistrysource_p.h", "outputRevision": 69}, {"classes": [{"className": "QRemoteObjectReplica", "enums": [{"isClass": false, "isFlag": false, "name": "State", "values": ["Uninitialized", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Suspect", "SignatureMismatch"]}], "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "node", "read": "node", "required": false, "scriptable": true, "stored": true, "type": "QRemoteObjectNode*", "user": false, "write": "setNode"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "state", "notify": "stateChanged", "read": "state", "required": false, "scriptable": true, "stored": true, "type": "State", "user": false}], "qualifiedClassName": "QRemoteObjectReplica", "signals": [{"access": "public", "index": 0, "name": "initialized", "returnType": "void"}, {"access": "public", "index": 1, "name": "notified", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "State"}, {"name": "oldState", "type": "State"}], "index": 2, "name": "stateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qremoteobjectreplica.h", "outputRevision": 69}, {"classes": [{"className": "QRemoteObjectSettingsStore", "lineNumber": 13, "object": true, "qualifiedClassName": "QRemoteObjectSettingsStore", "superClasses": [{"access": "public", "name": "QRemoteObjectAbstractPersistedStore"}]}], "inputFile": "qremoteobjectsettingsstore.h", "outputRevision": 69}, {"classes": [{"className": "QRemoteObjectSourceIo", "lineNumber": 33, "object": true, "qualifiedClassName": "QRemoteObjectSourceIo", "signals": [{"access": "public", "arguments": [{"type": "QRemoteObjectSourceLocation"}], "index": 0, "name": "remoteObjectAdded", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QRemoteObjectSourceLocation"}], "index": 1, "name": "remoteObjectRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "index": 2, "name": "serverRemoved", "returnType": "void"}], "slots": [{"access": "public", "index": 3, "name": "handleConnection", "returnType": "void"}, {"access": "public", "arguments": [{"name": "obj", "type": "QObject*"}], "index": 4, "name": "onServerDisconnect", "returnType": "void"}, {"access": "public", "index": 5, "isCloned": true, "name": "onServerDisconnect", "returnType": "void"}, {"access": "public", "arguments": [{"name": "obj", "type": "QObject*"}], "index": 6, "name": "onServerRead", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qremoteobjectsourceio_p.h", "outputRevision": 69}, {"classes": [{"className": "QtRemoteObjects", "enums": [{"isClass": false, "isFlag": false, "name": "QRemoteObjectPacketTypeEnum", "values": ["Invalid", "Handshake", "InitPacket", "InitDynamicPacket", "AddObject", "RemoveObject", "InvokePacket", "InvokeReplyPacket", "PropertyChangePacket", "ObjectList", "<PERSON>", "Pong"]}, {"isClass": false, "isFlag": false, "name": "InitialAction", "values": ["FetchRootSize", "PrefetchData"]}], "lineNumber": 83, "namespace": true, "qualifiedClassName": "QtRemoteObjects"}], "inputFile": "qtremoteobjectglobal.h", "outputRevision": 69}, {"classes": [{"className": "QRemoteObjectAbstractPersistedStore", "lineNumber": 29, "object": true, "qualifiedClassName": "QRemoteObjectAbstractPersistedStore", "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QRemoteObjectNode", "enums": [{"isClass": false, "isFlag": false, "name": "ErrorCode", "values": ["NoError", "RegistryNotAcquired", "RegistryAlreadyHosted", "NodeIsNoServer", "ServerAlreadyCreated", "UnintendedRegistryHosting", "OperationNotValidOnClientNode", "SourceNotRegistered", "MissingObjectName", "HostUrlInvalid", "ProtocolMismatch", "ListenFailed", "SocketAccessError"]}], "lineNumber": 45, "methods": [{"access": "public", "arguments": [{"name": "address", "type": "QUrl"}], "index": 4, "name": "connectToNode", "returnType": "bool"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "registryUrl", "read": "registryUrl", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setRegistryUrl"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "persistedStore", "read": "persistedStore", "required": false, "scriptable": true, "stored": true, "type": "QRemoteObjectAbstractPersistedStore*", "user": false, "write": "setPersistedStore"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "heartbeatInterval", "notify": "heartbeatIntervalChanged", "read": "heartbeatInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setHeartbeatInterval"}], "qualifiedClassName": "QRemoteObjectNode", "signals": [{"access": "public", "arguments": [{"type": "QRemoteObjectSourceLocation"}], "index": 0, "name": "remoteObjectAdded", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QRemoteObjectSourceLocation"}], "index": 1, "name": "remoteObjectRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "errorCode", "type": "QRemoteObjectNode::ErrorCode"}], "index": 2, "name": "error", "returnType": "void"}, {"access": "public", "arguments": [{"name": "heartbeatInterval", "type": "int"}], "index": 3, "name": "heartbeatIntervalChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QRemoteObjectHostBase", "enums": [{"isClass": false, "isFlag": false, "name": "AllowedSchemas", "values": ["BuiltInSchemasOnly", "AllowExternalRegistration"]}], "lineNumber": 136, "methods": [{"access": "public", "arguments": [{"name": "object", "type": "QObject*"}, {"name": "name", "type": "QString"}], "index": 0, "name": "enableRemoting", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "object", "type": "QObject*"}], "index": 1, "isCloned": true, "name": "enableRemoting", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "remoteObject", "type": "QObject*"}], "index": 2, "name": "disableRemoting", "returnType": "bool"}], "object": true, "qualifiedClassName": "QRemoteObjectHostBase", "superClasses": [{"access": "public", "name": "QRemoteObjectNode"}]}, {"className": "QRemoteObjectHost", "lineNumber": 173, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "hostUrl", "notify": "hostUrlChanged", "read": "hostUrl", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setHostUrl"}], "qualifiedClassName": "QRemoteObjectHost", "signals": [{"access": "public", "index": 0, "name": "hostUrlChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QRemoteObjectHostBase"}]}, {"className": "QRemoteObjectRegistryHost", "lineNumber": 198, "object": true, "qualifiedClassName": "QRemoteObjectRegistryHost", "superClasses": [{"access": "public", "name": "QRemoteObjectHostBase"}]}], "inputFile": "qremoteobjectnode.h", "outputRevision": 69}, {"classes": [{"className": "QRemoteObjectPendingCallWatcher", "lineNumber": 53, "object": true, "qualifiedClassName": "QRemoteObjectPendingCallWatcher", "signals": [{"access": "public", "arguments": [{"name": "self", "type": "QRemoteObjectPendingCallWatcher*"}], "index": 0, "name": "finished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QRemoteObjectPendingCall"}]}], "inputFile": "qremoteobjectpendingcall.h", "outputRevision": 69}]