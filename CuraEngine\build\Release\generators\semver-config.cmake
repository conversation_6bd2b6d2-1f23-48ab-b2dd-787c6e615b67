########## MACROS ###########################################################################
#############################################################################################

# Requires CMake > 3.15
if(${CMAKE_VERSION} VERSION_LESS "3.15")
    message(FATAL_ERROR "The 'CMakeDeps' generator only works with CMake >= 3.15")
endif()

if(semver_FIND_QUIETLY)
    set(semver_MESSAGE_MODE VERBOSE)
else()
    set(semver_MESSAGE_MODE STATUS)
endif()

include(${CMAKE_CURRENT_LIST_DIR}/cmakedeps_macros.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/semverTargets.cmake)
include(CMakeFindDependencyMacro)

check_build_type_defined()

foreach(_DEPENDENCY ${neargye-semver_FIND_DEPENDENCY_NAMES} )
    # Check that we have not already called a find_package with the transitive dependency
    if(NOT ${_DEPENDENCY}_FOUND)
        find_dependency(${_DEPENDENCY} REQUIRED ${${_DEPENDENCY}_FIND_MODE})
    endif()
endforeach()

set(semver_VERSION_STRING "0.3.0")
set(semver_INCLUDE_DIRS ${neargye-semver_INCLUDE_DIRS_RELEASE} )
set(semver_INCLUDE_DIR ${neargye-semver_INCLUDE_DIRS_RELEASE} )
set(semver_LIBRARIES ${neargye-semver_LIBRARIES_RELEASE} )
set(semver_DEFINITIONS ${neargye-semver_DEFINITIONS_RELEASE} )


# Only the last installed configuration BUILD_MODULES are included to avoid the collision
foreach(_BUILD_MODULE ${neargye-semver_BUILD_MODULES_PATHS_RELEASE} )
    message(${semver_MESSAGE_MODE} "Conan: Including build module from '${_BUILD_MODULE}'")
    include(${_BUILD_MODULE})
endforeach()


