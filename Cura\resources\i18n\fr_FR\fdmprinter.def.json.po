msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2023-11-24 12:51+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: fr_FR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n>1;\n"

msgctxt "ironing_inset description"
msgid "A distance to keep from the edges of the model. Ironing all the way to the edge of the mesh may result in a jagged edge on your print."
msgstr "Distance à garder à partir des bords du modèle. Étirer jusqu'au bord de la maille peut entraîner l'apparition d'un bord denté sur votre impression."

msgctxt "material_no_load_move_factor description"
msgid "A factor indicating how much the filament gets compressed between the feeder and the nozzle chamber, used to determine how far to move the material for a filament switch."
msgstr "Un facteur indiquant la quantité de filament compressée entre le chargeur et la chambre de la buse ; utilisé pour déterminer jusqu'où faire avancer le matériau pour changer de filament."

msgctxt "roofing_angles description"
msgid "A list of integer line directions to use when the top surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Une liste de sens de ligne (exprimés en nombres entiers) à utiliser lorsque les couches extérieures de la surface supérieure utilisent le motif en lignes ou en zig zag. Les éléments de la liste sont utilisés de manière séquentielle à mesure de l'avancement des couches. La liste reprend depuis le début lorsque la fin est atteinte. Les éléments de la liste sont séparés par des virgules et la liste entière est encadrée entre crochets. La valeur par défaut est une liste vide, ce qui signifie que les angles traditionnels par défaut seront utilisés (45 et 135 degrés)."

msgctxt "skin_angles description"
msgid "A list of integer line directions to use when the top/bottom layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Une liste de sens de ligne (exprimés en nombres entiers) à utiliser lorsque les couches du haut / bas utilisent le motif en lignes ou en zig zag. Les éléments de la liste sont utilisés de manière séquentielle à mesure de l'avancement des couches. La liste reprend depuis le début lorsque la fin est atteinte. Les éléments de la liste sont séparés par des virgules et la liste entière est encadrée entre crochets. La valeur par défaut est une liste vide, ce qui signifie que les angles traditionnels par défaut seront utilisés (45 et 135 degrés)."

msgctxt "support_infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angle 0 degrees."
msgstr "Une liste de sens de ligne (exprimés en nombres entiers) à utiliser. Les éléments de la liste sont utilisés de manière séquentielle à mesure de l'avancement des couches. La liste reprend depuis le début lorsque la fin est atteinte. Les éléments de la liste sont séparés par des virgules et la liste entière est encadrée entre crochets. La valeur par défaut est une liste vide, ce qui signifie que l'angle par défaut est utilisé (0 degré)."

msgctxt "support_bottom_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Une liste de sens de ligne (exprimés en nombres entiers) à utiliser. Les éléments de la liste sont utilisés de manière séquentielle à mesure de l'avancement des couches. La liste reprend depuis le début lorsque la fin est atteinte. Les éléments de la liste sont séparés par des virgules et la liste entière est encadrée entre crochets. La valeur par défaut est une liste vide, ce qui signifie que les angles par défaut sont utilisés (alternative entre 45 et 135 degrés si les interfaces sont assez épaisses ou 90 degrés)."

msgctxt "support_interface_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Une liste de sens de ligne (exprimés en nombres entiers) à utiliser. Les éléments de la liste sont utilisés de manière séquentielle à mesure de l'avancement des couches. La liste reprend depuis le début lorsque la fin est atteinte. Les éléments de la liste sont séparés par des virgules et la liste entière est encadrée entre crochets. La valeur par défaut est une liste vide, ce qui signifie que les angles par défaut sont utilisés (alternative entre 45 et 135 degrés si les interfaces sont assez épaisses ou 90 degrés)."

msgctxt "support_roof_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Une liste de sens de ligne (exprimés en nombres entiers) à utiliser. Les éléments de la liste sont utilisés de manière séquentielle à mesure de l'avancement des couches. La liste reprend depuis le début lorsque la fin est atteinte. Les éléments de la liste sont séparés par des virgules et la liste entière est encadrée entre crochets. La valeur par défaut est une liste vide, ce qui signifie que les angles par défaut sont utilisés (alternative entre 45 et 135 degrés si les interfaces sont assez épaisses ou 90 degrés)."

msgctxt "infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees for the lines and zig zag patterns and 45 degrees for all other patterns)."
msgstr "Une liste de sens de ligne (exprimés en nombres entiers) à utiliser. Les éléments de la liste sont utilisés de manière séquentielle à mesure de l'avancement des couches. La liste reprend depuis le début lorsque la fin est atteinte. Les éléments de la liste sont séparés par des virgules et la liste entière est encadrée entre crochets. La valeur par défaut est une liste vide, ce qui signifie que les angles traditionnels par défaut seront utilisés (45 et 135 degrés pour les motifs en lignes et en zig zag et 45 degrés pour tout autre motif)."

msgctxt "nozzle_disallowed_areas description"
msgid "A list of polygons with areas the nozzle is not allowed to enter."
msgstr "Une liste de polygones comportant les zones dans lesquelles le bec n'a pas le droit de pénétrer."

msgctxt "machine_disallowed_areas description"
msgid "A list of polygons with areas the print head is not allowed to enter."
msgstr "Une liste de polygones comportant les zones dans lesquelles la tête d'impression n'a pas le droit de pénétrer."

msgctxt "brim_inside_margin description"
msgid "A part fully enclosed inside another part can generate an outer brim that touches the inside of the other part. This removes all brim within this distance from internal holes."
msgstr "Une pièce entièrement contenue à l'intérieur d'une autre peut générer une bordure extérieure qui vient en contact avec l'intérieur de la pièce extérieure. Cette fonction supprime à cette distance toutes les bordures situées dans des vides intérieurs."

msgctxt "support_tree_branch_reach_limit description"
msgid "A recomendation to how far branches can move from the points they support. Branches can violate this value to reach their destination (buildplate or a flat part of the model). Lowering this value will make the support more sturdy, but increase the amount of branches (and because of that material usage/print time) "
msgstr "Il s'agit de la distance recommandée à laquelle les branches peuvent s'éloigner des points qu'elles soutiennent. Les branches peuvent ne pas respecter cette valeur pour atteindre leur emplacement cible (plateau ou partie plate du modèle). L'abaissement de cette valeur rendra le support plus solide, mais le nombre de branches augmentera (tout comme la quantité de matériau utilisée et le temps d'impression)."

msgctxt "extruder_prime_pos_abs label"
msgid "Absolute Extruder Prime Position"
msgstr "Position d'amorçage absolue de l'extrudeuse"

msgctxt "adaptive_layer_height_variation label"
msgid "Adaptive Layers Maximum Variation"
msgstr "Variation maximale des couches adaptatives"

msgctxt "adaptive_layer_height_threshold label"
msgid "Adaptive Layers Topography Size"
msgstr "Taille de la topographie des couches adaptatives"

msgctxt "adaptive_layer_height_variation_step label"
msgid "Adaptive Layers Variation Step Size"
msgstr "Taille des étapes de variation des couches adaptatives"

msgctxt "adaptive_layer_height_enabled description"
msgid "Adaptive layers computes the layer heights depending on the shape of the model."
msgstr "Cette option calcule la hauteur des couches en fonction de la forme du modèle."

msgctxt "infill_wall_line_count description"
msgid ""
"Add extra walls around the infill area. Such walls can make top/bottom skin lines sag down less which means you need less top/bottom skin layers for the same quality at the cost of some extra material.\n"
"This feature can combine with the Connect Infill Polygons to connect all the infill into a single extrusion path without the need for travels or retractions if configured right."
msgstr ""
"Ajoutez des parois supplémentaires autour de la zone de remplissage. De telles parois peuvent réduire l'affaissement des lignes de couche extérieure supérieure / inférieure, réduisant le nombre de couches extérieures supérieures / inférieures nécessaires pour obtenir la même qualité, au prix d'un peu de matériau supplémentaire.\n"
"Configurée correctement, cette fonctionnalité peut être combinée avec « Relier les polygones de remplissage » pour relier tous les remplissages en un seul mouvement d'extrusion sans avoir besoin de déplacements ou de rétractions."

msgctxt "platform_adhesion description"
msgid "Adhesion"
msgstr "Adhérence"

msgctxt "material_adhesion_tendency label"
msgid "Adhesion Tendency"
msgstr "Tendance à l'adhérence"

msgctxt "skin_overlap description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines, as a percentage of the line widths of the skin lines and the innermost wall. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any percentage over 50% may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "Ajuster le degré de chevauchement entre les parois et les (extrémités des) lignes centrales de la couche extérieure, en pourcentage de la largeur des lignes de la couche extérieure et de la paroi intérieure. Un chevauchement léger permet de relier fermement les parois à la couche extérieure. Notez que, si la largeur de la couche extérieure est égale à celle de la ligne de la paroi, un pourcentage supérieur à 50 % peut déjà faire dépasser la couche extérieure de la paroi, car dans ce cas la position de la buse de l'extrudeuse peut déjà atteindre le milieu de la paroi."

msgctxt "skin_overlap_mm description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any value over half the width of the wall may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "Ajuster le degré de chevauchement entre les parois et les (extrémités des) lignes centrales de la couche extérieure. Un chevauchement léger permet de relier fermement les parois à la couche extérieure. Notez que, si la largeur de la couche extérieure est égale à celle de la ligne de la paroi, une valeur supérieure à la moitié de la largeur de la paroi peut déjà faire dépasser la couche extérieure de la paroi, car dans ce cas la position de la buse de l'extrudeuse peut déjà atteindre le milieu de la paroi."

msgctxt "infill_sparse_density description"
msgid "Adjusts the density of infill of the print."
msgstr "Adapte la densité de remplissage de l'impression."

msgctxt "support_interface_density description"
msgid "Adjusts the density of the roofs and floors of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Ajuste la densité des plafonds et bas de la structure de support. Une valeur plus élevée résulte en de meilleurs porte-à-faux, mais les supports sont plus difficiles à enlever."

msgctxt "support_tree_top_rate description"
msgid "Adjusts the density of the support structure used to generate the tips of the branches. A higher value results in better overhangs, but the supports are harder to remove. Use Support Roof for very high values or ensure support density is similarly high at the top."
msgstr "Ce paramètre ajuste la densité de la structure de support utilisée pour générer les extrémités des branches. Une valeur plus élevée permet d'obtenir de meilleurs porte-à-faux, mais les supports seront plus difficiles à retirer. Utilisez un plafond de support en cas de valeurs très élevées ou veillez à ce que la densité du support soit tout aussi élevée aux extrémités."

msgctxt "support_infill_rate description"
msgid "Adjusts the density of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Ajuste la densité du support. Une valeur plus élevée résulte en de meilleurs porte-à-faux, mais les supports sont plus difficiles à enlever."

msgctxt "material_diameter description"
msgid "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament."
msgstr "Ajuste le diamètre du filament utilisé. Faites correspondre cette valeur au diamètre du filament utilisé."

msgctxt "support_type description"
msgid "Adjusts the placement of the support structures. The placement can be set to touching build plate or everywhere. When set to everywhere the support structures will also be printed on the model."
msgstr "Ajuste le positionnement des supports. Le positionnement peut être défini pour toucher le plateau ou n'importe où. Lorsqu'il est défini sur n'importe où, les supports seront également imprimés sur le modèle."

msgctxt "prime_tower_wipe_enabled description"
msgid "After printing the prime tower with one nozzle, wipe the oozed material from the other nozzle off on the prime tower."
msgstr "Après l'impression de la tour d'amorçage à l'aide d'une buse, nettoyer le matériau qui suinte de l'autre buse sur la tour d'amorçage."

msgctxt "retraction_hop_after_extruder_switch description"
msgid "After the machine switched from one extruder to the other, the build plate is lowered to create clearance between the nozzle and the print. This prevents the nozzle from leaving oozed material on the outside of a print."
msgstr "Une fois que la machine est passée d'une extrudeuse à l'autre, le plateau s'abaisse pour créer un dégagement entre la buse et l'impression. Cela évite que la buse ne ressorte avec du matériau suintant sur l'extérieur d'une impression."

msgctxt "retraction_combing option all"
msgid "All"
msgstr "Tout"

msgctxt "print_sequence option all_at_once"
msgid "All at Once"
msgstr "Tout en même temps"

msgctxt "resolution description"
msgid "All settings that influence the resolution of the print. These settings have a large impact on the quality (and print time)"
msgstr "Tous les paramètres qui influent sur la résolution de l'impression. Ces paramètres ont un impact conséquent sur la qualité (et la durée d'impression)."

msgctxt "alternate_extra_perimeter label"
msgid "Alternate Extra Wall"
msgstr "Alterner les parois supplémentaires"

msgctxt "alternate_carve_order label"
msgid "Alternate Mesh Removal"
msgstr "Alterner le retrait des maillages"

msgctxt "material_alternate_walls label"
msgid "Alternate Wall Directions"
msgstr "Alterner les directions des parois"

msgctxt "material_alternate_walls description"
msgid "Alternate wall directions every other layer and inset. Useful for materials that can build up stress, like for metal printing."
msgstr "Alternez les directions des parois, une couche et un insert sur deux. Utile pour les matériaux qui peuvent accumuler des contraintes, comme pour l'impression de métal."

msgctxt "machine_buildplate_type option aluminum"
msgid "Aluminum"
msgstr "Aluminium"

msgctxt "machine_always_write_active_tool label"
msgid "Always Write Active Tool"
msgstr "Toujours écrire l'outil actif"

msgctxt "travel_retract_before_outer_wall description"
msgid "Always retract when moving to start an outer wall."
msgstr "Toujours rétracter lors du déplacement pour commencer une paroi externe."

msgctxt "xy_offset description"
msgid "Amount of offset applied to all polygons in each layer. Positive values can compensate for too big holes; negative values can compensate for too small holes."
msgstr "Le décalage appliqué à tous les polygones dans chaque couche. Une valeur positive peut compenser les trous trop gros ; une valeur négative peut compenser les trous trop petits."

msgctxt "xy_offset_layer_0 description"
msgid "Amount of offset applied to all polygons in the first layer. A negative value can compensate for squishing of the first layer known as \"elephant's foot\"."
msgstr "Le décalage appliqué à tous les polygones dans la première couche. Une valeur négative peut compenser l'écrasement de la première couche, appelé « patte d'éléphant »."

msgctxt "support_offset description"
msgid "Amount of offset applied to all support polygons in each layer. Positive values can smooth out the support areas and result in more sturdy support."
msgstr "Le décalage appliqué à tous les polygones pour chaque couche. Une valeur positive peut lisser les zones de support et rendre le support plus solide."

msgctxt "support_bottom_offset description"
msgid "Amount of offset applied to the floors of the support."
msgstr "Quantité de décalage appliqué aux bas du support."

msgctxt "support_roof_offset description"
msgid "Amount of offset applied to the roofs of the support."
msgstr "Quantité de décalage appliqué aux plafonds du support."

msgctxt "support_interface_offset description"
msgid "Amount of offset applied to the support interface polygons."
msgstr "Quantité de décalage appliquée aux polygones de l'interface de support."

msgctxt "wipe_retraction_amount description"
msgid "Amount to retract the filament so it does not ooze during the wipe sequence."
msgstr "La distance de rétraction du filament afin qu'il ne suinte pas pendant la séquence d'essuyage."

msgctxt "sub_div_rad_add description"
msgid "An addition to the radius from the center of each cube to check for the boundary of the model, as to decide whether this cube should be subdivided. Larger values lead to a thicker shell of small cubes near the boundary of the model."
msgstr "Une addition au rayon à partir du centre de chaque cube pour vérifier la bordure du modèle, afin de décider si ce cube doit être subdivisé. Des valeurs plus importantes entraînent une coque plus épaisse de petits cubes à proximité de la bordure du modèle."

msgctxt "anti_overhang_mesh label"
msgid "Anti Overhang Mesh"
msgstr "Maillage anti-surplomb"

msgctxt "material_anti_ooze_retracted_position label"
msgid "Anti-ooze Retracted Position"
msgstr "Position anti-suintage rétractée"

msgctxt "material_anti_ooze_retraction_speed label"
msgid "Anti-ooze Retraction Speed"
msgstr "Vitesse de rétraction de l'anti-suintage"

msgctxt "machine_use_extruder_offset_to_offset_coords description"
msgid "Apply the extruder offset to the coordinate system. Affects all extruders."
msgstr "Appliquez le décalage de l'extrudeuse au système de coordonnées. Affecte toutes les extrudeuses."

msgctxt "interlocking_enable description"
msgid "At the locations where models touch, generate an interlocking beam structure. This improves the adhesion between models, especially models printed in different materials."
msgstr "Aux endroits où les modèles 3D se touchent, générez une structure d'attaches de connexion. Cette fonctionnalité améliore l'adhérence entre les modèles 3D, en particulier ceux imprimés avec des matériaux différents."

msgctxt "travel_avoid_other_parts label"
msgid "Avoid Printed Parts When Traveling"
msgstr "Éviter les pièces imprimées lors du déplacement"

msgctxt "travel_avoid_supports label"
msgid "Avoid Supports When Traveling"
msgstr "Éviter les supports lors du déplacement"

msgctxt "z_seam_position option back"
msgid "Back"
msgstr "Arrière"

msgctxt "z_seam_position option backleft"
msgid "Back Left"
msgstr "Arrière gauche"

msgctxt "z_seam_position option backright"
msgid "Back Right"
msgstr "Arrière droit"

msgctxt "machine_gcode_flavor option BFB"
msgid "Bits from Bytes"
msgstr "Bits from Bytes"

msgctxt "magic_mesh_surface_mode option both"
msgid "Both"
msgstr "Les deux"

msgctxt "support_interface_priority option nothing"
msgid "Both overlap"
msgstr "Chevauchement"

msgctxt "bottom_layers label"
msgid "Bottom Layers"
msgstr "Couches inférieures"

msgctxt "top_bottom_pattern_0 label"
msgid "Bottom Pattern Initial Layer"
msgstr "Couche initiale du motif du dessous"

msgctxt "bottom_skin_expand_distance label"
msgid "Bottom Skin Expand Distance"
msgstr "Distance d'expansion de la couche extérieure inférieure"

msgctxt "bottom_skin_preshrink label"
msgid "Bottom Skin Removal Width"
msgstr "Largeur de retrait de la couche extérieure inférieure"

msgctxt "bottom_thickness label"
msgid "Bottom Thickness"
msgstr "Épaisseur du dessous"

msgctxt "support_tree_top_rate label"
msgid "Branch Density"
msgstr "Densité des branches"

msgctxt "support_tree_branch_diameter label"
msgid "Branch Diameter"
msgstr "Diamètre des branches"

msgctxt "support_tree_branch_diameter_angle label"
msgid "Branch Diameter Angle"
msgstr "Angle de diamètre des branches"

msgctxt "material_break_preparation_retracted_position label"
msgid "Break Preparation Retracted Position"
msgstr "Préparation de rupture Position rétractée"

msgctxt "material_break_preparation_speed label"
msgid "Break Preparation Retraction Speed"
msgstr "Vitesse de rétraction de préparation de rupture"

msgctxt "material_break_preparation_temperature label"
msgid "Break Preparation Temperature"
msgstr "Température de préparation de rupture"

msgctxt "material_break_retracted_position label"
msgid "Break Retracted Position"
msgstr "Position rétractée de rupture"

msgctxt "material_break_speed label"
msgid "Break Retraction Speed"
msgstr "Vitesse de rétraction de rupture"

msgctxt "material_break_temperature label"
msgid "Break Temperature"
msgstr "Température de rupture"

msgctxt "support_skip_some_zags label"
msgid "Break Up Support In Chunks"
msgstr "Démantèlement du support en morceaux"

msgctxt "bridge_fan_speed label"
msgid "Bridge Fan Speed"
msgstr "Vitesse du ventilateur du pont"

msgctxt "bridge_enable_more_layers label"
msgid "Bridge Has Multiple Layers"
msgstr "Le pont possède plusieurs couches"

msgctxt "bridge_skin_density_2 label"
msgid "Bridge Second Skin Density"
msgstr "Densité de la deuxième couche extérieure du pont"

msgctxt "bridge_fan_speed_2 label"
msgid "Bridge Second Skin Fan Speed"
msgstr "Vitesse du ventilateur de la deuxième couche extérieure du pont"

msgctxt "bridge_skin_material_flow_2 label"
msgid "Bridge Second Skin Flow"
msgstr "Débit de la deuxième couche extérieure du pont"

msgctxt "bridge_skin_speed_2 label"
msgid "Bridge Second Skin Speed"
msgstr "Vitesse de la deuxième couche extérieure du pont"

msgctxt "bridge_skin_density label"
msgid "Bridge Skin Density"
msgstr "Densité de la couche extérieure du pont"

msgctxt "bridge_skin_material_flow label"
msgid "Bridge Skin Flow"
msgstr "Débit de la couche extérieure du pont"

msgctxt "bridge_skin_speed label"
msgid "Bridge Skin Speed"
msgstr "Vitesse de la couche extérieure du pont"

msgctxt "bridge_skin_support_threshold label"
msgid "Bridge Skin Support Threshold"
msgstr "Limite de support de la couche extérieure du pont"

msgctxt "bridge_sparse_infill_max_density label"
msgid "Bridge Sparse Infill Max Density"
msgstr "Densité maximale du remplissage mince du pont"

msgctxt "bridge_skin_density_3 label"
msgid "Bridge Third Skin Density"
msgstr "Densité de la troisième couche extérieure du pont"

msgctxt "bridge_fan_speed_3 label"
msgid "Bridge Third Skin Fan Speed"
msgstr "Vitesse du ventilateur de la troisième couche extérieure du pont"

msgctxt "bridge_skin_material_flow_3 label"
msgid "Bridge Third Skin Flow"
msgstr "Débit de la troisième couche extérieure du pont"

msgctxt "bridge_skin_speed_3 label"
msgid "Bridge Third Skin Speed"
msgstr "Vitesse de la troisième couche extérieure du pont"

msgctxt "bridge_wall_coast label"
msgid "Bridge Wall Coasting"
msgstr "Roue libre pour paroi du pont"

msgctxt "bridge_wall_material_flow label"
msgid "Bridge Wall Flow"
msgstr "Débit de paroi du pont"

msgctxt "bridge_wall_speed label"
msgid "Bridge Wall Speed"
msgstr "Vitesse de paroi du pont"

msgctxt "adhesion_type option brim"
msgid "Brim"
msgstr "Bordure"

msgctxt "brim_gap label"
msgid "Brim Distance"
msgstr "Distance de la bordure"

msgctxt "brim_inside_margin label"
msgid "Brim Inside Avoid Margin"
msgstr "Marge d'évitement de la bordure intérieure"

msgctxt "brim_line_count label"
msgid "Brim Line Count"
msgstr "Nombre de lignes de la bordure"

msgctxt "brim_outside_only label"
msgid "Brim Only on Outside"
msgstr "Bordure uniquement sur l'extérieur"

msgctxt "brim_replaces_support label"
msgid "Brim Replaces Support"
msgstr "La bordure remplace le support"

msgctxt "brim_width label"
msgid "Brim Width"
msgstr "Largeur de la bordure"

msgctxt "platform_adhesion label"
msgid "Build Plate Adhesion"
msgstr "Adhérence du plateau"

msgctxt "adhesion_extruder_nr label"
msgid "Build Plate Adhesion Extruder"
msgstr "Extrudeuse d'adhérence du plateau"

msgctxt "adhesion_type label"
msgid "Build Plate Adhesion Type"
msgstr "Type d'adhérence du plateau"

msgctxt "machine_buildplate_type label"
msgid "Build Plate Material"
msgstr "Matériau du plateau"

msgctxt "machine_shape label"
msgid "Build Plate Shape"
msgstr "Forme du plateau"

msgctxt "material_bed_temperature label"
msgid "Build Plate Temperature"
msgstr "Température du plateau"

msgctxt "material_bed_temperature_layer_0 label"
msgid "Build Plate Temperature Initial Layer"
msgstr "Température du plateau couche initiale"

msgctxt "build_volume_temperature label"
msgid "Build Volume Temperature"
msgstr "Température du volume d'impression"

msgctxt "prime_tower_brim_enable description"
msgid "By enabling this setting, your prime-tower will get a brim, even if the model doesn't. If you want a sturdier base for a high tower, you can increase the base height."
msgstr "En activant ce paramètre, votre tour d'amorçage aura un bord même si le modèle n'en a pas. Si vous souhaitez une base plus solide pour une tour élevée, vous pouvez augmenter la hauteur de la base."

msgctxt "center_object label"
msgid "Center Object"
msgstr "Centrer l'objet"

msgctxt "conical_overhang_enabled description"
msgid "Change the geometry of the printed model such that minimal support is required. Steep overhangs will become shallow overhangs. Overhanging areas will drop down to become more vertical."
msgstr "Change la géométrie du modèle imprimé de manière à nécessiter un support minimal. Les porte-à-faux abrupts deviendront des porte-à-faux minces. Les zones en porte-à-faux descendront pour devenir plus verticales."

msgctxt "support_structure description"
msgid "Chooses between the techniques available to generate support. \"Normal\" support creates a support structure directly below the overhanging parts and drops those areas straight down. \"Tree\" support creates branches towards the overhanging areas that support the model on the tips of those branches, and allows the branches to crawl around the model to support it from the build plate as much as possible."
msgstr "Choisit entre les techniques disponibles pour générer un support. Le support « Normal » créer une structure de support directement sous les pièces en porte-à-faux et fait descendre ces zones directement vers le bas. Le support « Arborescent » crée des branches vers les zones en porte-à-faux qui supportent le modèle à l'extrémité de ces branches et permet aux branches de ramper autour du modèle afin de les supporter le plus possible sur le plateau de fabrication."

msgctxt "coasting_speed label"
msgid "Coasting Speed"
msgstr "Vitesse de roue libre"

msgctxt "coasting_volume label"
msgid "Coasting Volume"
msgstr "Volume en roue libre"

msgctxt "coasting_enable description"
msgid "Coasting replaces the last part of an extrusion path with a travel path. The oozed material is used to print the last piece of the extrusion path in order to reduce stringing."
msgstr "L'option « roue libre » remplace la dernière partie d'un mouvement d'extrusion par un mouvement de déplacement. Le matériau qui suinte de la buse est alors utilisé pour imprimer la dernière partie du tracé du mouvement d'extrusion, ce qui réduit le stringing."

msgctxt "retraction_combing label"
msgid "Combing Mode"
msgstr "Mode de détours"

msgctxt "retraction_combing description"
msgid "Combing keeps the nozzle within already printed areas when traveling. This results in slightly longer travel moves but reduces the need for retractions. If combing is off, the material will retract and the nozzle moves in a straight line to the next point. It is also possible to avoid combing over top/bottom skin areas or to only comb within the infill."
msgstr "Les détours maintiennent la buse dans les zones déjà imprimées lors des déplacements. Cela résulte en des déplacements légèrement plus longs mais réduit le recours aux rétractions. Si les détours sont désactivés, le matériau se rétractera et la buse se déplacera en ligne droite jusqu'au point suivant. Il est également possible d'éviter les détours sur les zones de la couche du dessus / dessous ou d'effectuer les détours uniquement dans le remplissage."

msgctxt "command_line_settings label"
msgid "Command Line Settings"
msgstr "Paramètres de ligne de commande"

msgctxt "infill_pattern option concentric"
msgid "Concentric"
msgstr "Concentrique"

msgctxt "ironing_pattern option concentric"
msgid "Concentric"
msgstr "Concentrique"

msgctxt "roofing_pattern option concentric"
msgid "Concentric"
msgstr "Concentrique"

msgctxt "support_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Concentrique"

msgctxt "support_interface_pattern option concentric"
msgid "Concentric"
msgstr "Concentrique"

msgctxt "support_pattern option concentric"
msgid "Concentric"
msgstr "Concentrique"

msgctxt "support_roof_pattern option concentric"
msgid "Concentric"
msgstr "Concentrique"

msgctxt "top_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Concentrique"

msgctxt "top_bottom_pattern_0 option concentric"
msgid "Concentric"
msgstr "Concentrique"

msgctxt "support_conical_angle label"
msgid "Conical Support Angle"
msgstr "Angle des supports coniques"

msgctxt "support_conical_min_width label"
msgid "Conical Support Minimum Width"
msgstr "Largeur minimale des supports coniques"

msgctxt "zig_zaggify_infill label"
msgid "Connect Infill Lines"
msgstr "Relier les lignes de remplissage"

msgctxt "connect_infill_polygons label"
msgid "Connect Infill Polygons"
msgstr "Relier les polygones de remplissage"

msgctxt "zig_zaggify_support label"
msgid "Connect Support Lines"
msgstr "Relier les lignes de support"

msgctxt "support_connect_zigzags label"
msgid "Connect Support ZigZags"
msgstr "Relier les zigzags de support"

msgctxt "connect_skin_polygons label"
msgid "Connect Top/Bottom Polygons"
msgstr "Relier les polygones supérieurs / inférieurs"

msgctxt "connect_infill_polygons description"
msgid "Connect infill paths where they run next to each other. For infill patterns which consist of several closed polygons, enabling this setting greatly reduces the travel time."
msgstr "Relier les voies de remplissage lorsqu'elles sont côte à côte. Pour les motifs de remplissage composés de plusieurs polygones fermés, ce paramètre permet de réduire considérablement le temps de parcours."

msgctxt "support_connect_zigzags description"
msgid "Connect the ZigZags. This will increase the strength of the zig zag support structure."
msgstr "Relie les zigzags. Cela augmente la solidité des supports en zigzag."

msgctxt "zig_zaggify_support description"
msgid "Connect the ends of the support lines together. Enabling this setting can make your support more sturdy and reduce underextrusion, but it will cost more material."
msgstr "Relie les extrémités des lignes de support. L'activation de ce paramètre peut rendre votre support plus robuste et réduire la sous-extrusion, mais cela demandera d'utiliser plus de matériau."

msgctxt "zig_zaggify_infill description"
msgid "Connect the ends where the infill pattern meets the inner wall using a line which follows the shape of the inner wall. Enabling this setting can make the infill adhere to the walls better and reduce the effects of infill on the quality of vertical surfaces. Disabling this setting reduces the amount of material used."
msgstr "Relie les extrémités où le motif de remplissage touche la paroi interne, à l'aide d'une ligne épousant la forme de la paroi interne. Activer ce paramètre peut faire mieux coller le remplissage aux parois, et réduit les effets du remplissage sur la qualité des surfaces verticales. Désactiver ce paramètre diminue la quantité de matière utilisée."

msgctxt "connect_skin_polygons description"
msgid "Connect top/bottom skin paths where they run next to each other. For the concentric pattern enabling this setting greatly reduces the travel time, but because the connections can happen midway over infill this feature can reduce the top surface quality."
msgstr "Relier les voies de couche extérieure supérieures / inférieures lorsqu'elles sont côte à côte. Pour le motif concentrique, ce paramètre réduit considérablement le temps de parcours, mais comme les liens peuvent se trouver à mi-chemin sur le remplissage, cette fonctionnalité peut réduire la qualité de la surface supérieure."

msgctxt "z_seam_corner description"
msgid "Control whether corners on the model outline influence the position of the seam. None means that corners have no influence on the seam position. Hide Seam makes the seam more likely to occur on an inside corner. Expose Seam makes the seam more likely to occur on an outside corner. Hide or Expose Seam makes the seam more likely to occur at an inside or outside corner. Smart Hiding allows both inside and outside corners, but chooses inside corners more frequently, if appropriate."
msgstr "Vérifie si les angles du contour du modèle influencent l'emplacement de la jointure. « Aucune » signifie que les angles n'ont aucune influence sur l'emplacement de la jointure. « Masquer la jointure » génère le positionnement de la jointure sur un angle intérieur. « Exposer la jointure » génère le positionnement de la jointure sur un angle extérieur. « Masquer ou exposer la jointure » génère le positionnement de la jointure sur un angle intérieur ou extérieur. « Jointure intelligente » autorise les angles intérieurs et extérieurs, mais choisit plus fréquemment les angles intérieurs, le cas échéant."

msgctxt "infill_multiplier description"
msgid "Convert each infill line to this many lines. The extra lines do not cross over each other, but avoid each other. This makes the infill stiffer, but increases print time and material usage."
msgstr "Convertir chaque ligne de remplissage en ce nombre de lignes. Les lignes supplémentaires ne se croisent pas entre elles, mais s'évitent mutuellement. Cela rend le remplissage plus rigide, mais augmente le temps d'impression et la quantité de matériau utilisé."

msgctxt "machine_nozzle_cool_down_speed label"
msgid "Cool Down Speed"
msgstr "Vitesse de refroidissement"

msgctxt "cooling description"
msgid "Cooling"
msgstr "Refroidissement"

msgctxt "cooling label"
msgid "Cooling"
msgstr "Refroidissement"

msgctxt "infill_pattern option cross"
msgid "Cross"
msgstr "Entrecroisé"

msgctxt "support_pattern option cross"
msgid "Cross"
msgstr "Entrecroisé"

msgctxt "infill_pattern option cross_3d"
msgid "Cross 3D"
msgstr "Entrecroisé 3D"

msgctxt "cross_infill_pocket_size label"
msgid "Cross 3D Pocket Size"
msgstr "Taille de poches entrecroisées 3D"

msgctxt "cross_support_density_image label"
msgid "Cross Fill Density Image for Support"
msgstr "Image de densité du remplissage croisé pour le support"

msgctxt "cross_infill_density_image label"
msgid "Cross Infill Density Image"
msgstr "Image de densité du remplissage croisé"

msgctxt "material_crystallinity label"
msgid "Crystalline Material"
msgstr "Matériau cristallin"

msgctxt "infill_pattern option cubic"
msgid "Cubic"
msgstr "Cubique"

msgctxt "infill_pattern option cubicsubdiv"
msgid "Cubic Subdivision"
msgstr "Subdivision cubique"

msgctxt "sub_div_rad_add label"
msgid "Cubic Subdivision Shell"
msgstr "Coque de la subdivision cubique"

msgctxt "cutting_mesh label"
msgid "Cutting Mesh"
msgstr "Maille de coupe"

msgctxt "material_flow_temp_graph description"
msgid "Data linking material flow (in mm3 per second) to temperature (degrees Celsius)."
msgstr "Données reliant le flux de matériau (en mm3 par seconde) à la température (degrés Celsius)."

msgctxt "machine_acceleration label"
msgid "Default Acceleration"
msgstr "Accélération par défaut"

msgctxt "default_material_bed_temperature label"
msgid "Default Build Plate Temperature"
msgstr "Température du plateau par défaut"

msgctxt "machine_max_jerk_e label"
msgid "Default Filament Jerk"
msgstr "Saccade par défaut du filament"

msgctxt "default_material_print_temperature label"
msgid "Default Printing Temperature"
msgstr "Température d’impression par défaut"

msgctxt "machine_max_jerk_xy label"
msgid "Default X-Y Jerk"
msgstr "Saccade X-Y par défaut"

msgctxt "machine_max_jerk_z label"
msgid "Default Z Jerk"
msgstr "Saccade Z par défaut"

msgctxt "machine_max_jerk_xy description"
msgid "Default jerk for movement in the horizontal plane."
msgstr "Saccade par défaut pour le mouvement sur le plan horizontal."

msgctxt "machine_max_jerk_z description"
msgid "Default jerk for the motor of the Z-direction."
msgstr "Saccade par défaut pour le moteur du sens Z."

msgctxt "machine_max_jerk_e description"
msgid "Default jerk for the motor of the filament."
msgstr "Saccade par défaut pour le moteur du filament."

msgctxt "bridge_settings_enabled description"
msgid "Detect bridges and modify print speed, flow and fan settings while bridges are printed."
msgstr "Détecter les ponts et modifier la vitesse d'impression, le débit et les paramètres du ventilateur pendant l'impression des ponts."

msgctxt "inset_direction description"
msgid "Determines the order in which walls are printed. Printing outer walls earlier helps with dimensional accuracy, as faults from inner walls cannot propagate to the outside. However printing them later allows them to stack better when overhangs are printed. When there is an uneven amount of total innner walls, the 'center last line' is always printed last."
msgstr "Détermine l'ordre dans lequel les parois sont imprimées. L'impression des parois extérieures plus tôt permet une précision dimensionnelle car les défauts des parois intérieures ne peuvent pas se propager à l'extérieur. Cependant, le fait de les imprimer plus tard leur permet de mieux s'empiler lorsque les saillies sont imprimées. Lorsqu'il y a une quantité totale inégale de parois intérieures, la « dernière ligne centrale » est toujours imprimée en dernier."

msgctxt "infill_mesh_order description"
msgid "Determines the priority of this mesh when considering multiple overlapping infill meshes. Areas where multiple infill meshes overlap will take on the settings of the mesh with the highest rank. An infill mesh with a higher rank will modify the infill of infill meshes with lower rank and normal meshes."
msgstr "Détermine la priorité de cette maille lorsque plusieurs chevauchements de mailles de remplissage sont pris en considération. Les zones comportant plusieurs chevauchements de mailles de remplissage prendront en compte les paramètres du maillage ayant l'ordre le plus élevé. Une maille de remplissage possédant un ordre plus élevé modifiera le remplissage des mailles de remplissage ayant un ordre plus bas et des mailles normales."

msgctxt "lightning_infill_support_angle description"
msgid "Determines when a lightning infill layer has to support anything above it. Measured in the angle given the thickness of a layer."
msgstr "Détermine quand une couche de remplissage éclair doit soutenir tout ce qui se trouve au-dessus. Mesuré dans l'angle au vu de l'épaisseur d'une couche."

msgctxt "lightning_infill_overhang_angle description"
msgid "Determines when a lightning infill layer has to support the model above it. Measured in the angle given the thickness."
msgstr "Détermine quand une couche de remplissage éclair doit soutenir le modèle au-dessus. Mesuré dans l'angle au vu de l'épaisseur."

msgctxt "material_diameter label"
msgid "Diameter"
msgstr "Diamètre"

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model label"
msgid "Diameter Increase To Model"
msgstr "Augmentation du diamètre des branches rattachées au modèle"

msgctxt "support_tree_bp_diameter description"
msgid "Diameter every branch tries to achieve when reaching the buildplate. Improves bed adhesion."
msgstr "Il s'agit du diamètre que chaque branche essaie d'atteindre au niveau du plateau. Ce paramètre améliore l'adhérence au plateau."

msgctxt "adhesion_type description"
msgid "Different options that help to improve both priming your extrusion and adhesion to the build plate. Brim adds a single layer flat area around the base of your model to prevent warping. Raft adds a thick grid with a roof below the model. Skirt is a line printed around the model, but not connected to the model."
msgstr "Différentes options qui permettent d'améliorer la préparation de votre extrusion et l'adhérence au plateau. La bordure ajoute une zone plate d'une seule couche autour de la base de votre modèle, afin de l'empêcher de se redresser. Le radeau ajoute une grille épaisse avec un toit sous le modèle. La jupe est une ligne imprimée autour du modèle mais qui n'est pas rattachée au modèle."

msgctxt "machine_disallowed_areas label"
msgid "Disallowed Areas"
msgstr "Zones interdites"

msgctxt "infill_line_distance description"
msgid "Distance between the printed infill lines. This setting is calculated by the infill density and the infill line width."
msgstr "Distance entre les lignes de remplissage imprimées. Ce paramètre est calculé par la densité du remplissage et la largeur de ligne de remplissage."

msgctxt "support_initial_layer_line_distance description"
msgid "Distance between the printed initial layer support structure lines. This setting is calculated by the support density."
msgstr "Distance entre les lignes de la structure de support de la couche initiale imprimée. Ce paramètre est calculé en fonction de la densité du support."

msgctxt "support_bottom_line_distance description"
msgid "Distance between the printed support floor lines. This setting is calculated by the Support Floor Density, but can be adjusted separately."
msgstr "Distance entre les lignes du bas de support imprimées. Ce paramètre est calculé par la densité du bas de support mais peut également être défini séparément."

msgctxt "support_roof_line_distance description"
msgid "Distance between the printed support roof lines. This setting is calculated by the Support Roof Density, but can be adjusted separately."
msgstr "Distance entre les lignes du plafond de support imprimées. Ce paramètre est calculé par la densité du plafond de support mais peut également être défini séparément."

msgctxt "support_line_distance description"
msgid "Distance between the printed support structure lines. This setting is calculated by the support density."
msgstr "Distance entre les lignes de support imprimées. Ce paramètre est calculé par la densité du support."

msgctxt "support_bottom_distance description"
msgid "Distance from the print to the bottom of the support. Note that this is rounded up to the next layer height."
msgstr "Distance de l'impression au bas du support. Notez que cela est arrondi à la hauteur de couche suivante."

msgctxt "support_top_distance description"
msgid "Distance from the top of the support to the print."
msgstr "Distance entre l’impression et le haut des supports."

msgctxt "support_z_distance description"
msgid "Distance from the top/bottom of the support structure to the print. This gap provides clearance to remove the supports after the model is printed. The topmost support layer below the model might be a fraction of regular layers."
msgstr "Distance entre le haut/bas de la structure de support et l'impression. Cet écart permet de retirer les supports après l'impression du modèle. La couche de support la plus haute sous le modèle pourrait être une fraction des couches régulières."

msgctxt "infill_wipe_dist description"
msgid "Distance of a travel move inserted after every infill line, to make the infill stick to the walls better. This option is similar to infill overlap, but without extrusion and only on one end of the infill line."
msgstr "Distance de déplacement à insérer après chaque ligne de remplissage, pour s'assurer que le remplissage collera mieux aux parois externes. Cette option est similaire au chevauchement du remplissage, mais sans extrusion et seulement à l'une des deux extrémités de la ligne de remplissage."

msgctxt "wall_0_wipe_dist description"
msgid "Distance of a travel move inserted after the outer wall, to hide the Z seam better."
msgstr "Distance d'un déplacement inséré après la paroi extérieure, pour mieux masquer la jointure en Z."

msgctxt "draft_shield_dist description"
msgid "Distance of the draft shield from the print, in the X/Y directions."
msgstr "Distance entre la pièce et le bouclier dans les directions X et Y."

msgctxt "ooze_shield_dist description"
msgid "Distance of the ooze shield from the print, in the X/Y directions."
msgstr "Distance entre le bouclier de suintage et l'impression dans les directions X/Y."

msgctxt "support_xy_distance_overhang description"
msgid "Distance of the support structure from the overhang in the X/Y directions."
msgstr "Distance entre la structure de support et le porte-à-faux dans les directions X/Y."

msgctxt "support_xy_distance description"
msgid "Distance of the support structure from the print in the X/Y directions."
msgstr "Distance entre le support et l'impression dans les directions X/Y."

msgctxt "meshfix_fluid_motion_shift_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "Les points de distance sont décalés pour lisser le chemin"

msgctxt "meshfix_fluid_motion_small_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "Les points de distance sont décalés pour lisser le chemin"

msgctxt "min_infill_area description"
msgid "Don't generate areas of infill smaller than this (use skin instead)."
msgstr "Ne pas générer de zones de remplissage plus petites que cela (utiliser plutôt une couche extérieure)"

msgctxt "draft_shield_height label"
msgid "Draft Shield Height"
msgstr "Hauteur du bouclier"

msgctxt "draft_shield_height_limitation label"
msgid "Draft Shield Limitation"
msgstr "Limite du bouclier"

msgctxt "draft_shield_dist label"
msgid "Draft Shield X/Y Distance"
msgstr "Distance X/Y du bouclier"

msgctxt "support_mesh_drop_down label"
msgid "Drop Down Support Mesh"
msgstr "Maillage de support descendant"

msgctxt "dual label"
msgid "Dual Extrusion"
msgstr "Double extrusion"

msgctxt "machine_shape option elliptic"
msgid "Elliptic"
msgstr "Elliptique"

msgctxt "acceleration_enabled label"
msgid "Enable Acceleration Control"
msgstr "Activer le contrôle d'accélération"

msgctxt "bridge_settings_enabled label"
msgid "Enable Bridge Settings"
msgstr "Activer les paramètres du pont"

msgctxt "coasting_enable label"
msgid "Enable Coasting"
msgstr "Activer la roue libre"

msgctxt "support_conical_enabled label"
msgid "Enable Conical Support"
msgstr "Activer les supports coniques"

msgctxt "draft_shield_enabled label"
msgid "Enable Draft Shield"
msgstr "Activer le bouclier"

msgctxt "meshfix_fluid_motion_enabled label"
msgid "Enable Fluid Motion"
msgstr "Activer le mouvement fluide"

msgctxt "ironing_enabled label"
msgid "Enable Ironing"
msgstr "Activer l'étirage"

msgctxt "jerk_enabled label"
msgid "Enable Jerk Control"
msgstr "Activer le contrôle de saccade"

msgctxt "machine_nozzle_temp_enabled label"
msgid "Enable Nozzle Temperature Control"
msgstr "Permettre le contrôle de la température de la buse"

msgctxt "ooze_shield_enabled label"
msgid "Enable Ooze Shield"
msgstr "Activer le bouclier de suintage"

msgctxt "prime_blob_enable label"
msgid "Enable Prime Blob"
msgstr "Activer la goutte de préparation"

msgctxt "prime_tower_enable label"
msgid "Enable Prime Tower"
msgstr "Activer la tour d'amorçage"

msgctxt "cool_fan_enabled label"
msgid "Enable Print Cooling"
msgstr "Activer le refroidissement de l'impression"

msgctxt "retraction_enable label"
msgid "Enable Retraction"
msgstr "Activer la rétraction"

msgctxt "support_brim_enable label"
msgid "Enable Support Brim"
msgstr "Activer la bordure du support"

msgctxt "support_bottom_enable label"
msgid "Enable Support Floor"
msgstr "Activer les bas de support"

msgctxt "support_interface_enable label"
msgid "Enable Support Interface"
msgstr "Activer l'interface de support"

msgctxt "support_roof_enable label"
msgid "Enable Support Roof"
msgstr "Activer les plafonds de support"

msgctxt "acceleration_travel_enabled label"
msgid "Enable Travel Acceleration"
msgstr "Activer l'accélération des déplacements"

msgctxt "jerk_travel_enabled label"
msgid "Enable Travel Jerk"
msgstr "Activer les saccades de déplacement"

msgctxt "ooze_shield_enabled description"
msgid "Enable exterior ooze shield. This will create a shell around the model which is likely to wipe a second nozzle if it's at the same height as the first nozzle."
msgstr "Activer le bouclier de suintage extérieur. Cela créera une coque autour du modèle qui est susceptible d'essuyer une deuxième buse si celle-ci est à la même hauteur que la première buse."

msgctxt "small_skin_on_surface description"
msgid "Enable small (up to 'Small Top/Bottom Width') regions on the topmost skinned layer (exposed to air) to be filled with walls instead of the default pattern."
msgstr "Permet aux petites zones (jusqu'à « Petite largeur Haut/Bas ») de la couche supérieure (exposée à l'air) d'être remplies avec des parois au lieu du motif par défaut."

msgctxt "jerk_enabled description"
msgid "Enables adjusting the jerk of print head when the velocity in the X or Y axis changes. Increasing the jerk can reduce printing time at the cost of print quality."
msgstr "Active le réglage de la saccade de la tête d'impression lorsque la vitesse sur l'axe X ou Y change. Augmenter les saccades peut réduire la durée d'impression au détriment de la qualité d'impression."

msgctxt "acceleration_enabled description"
msgid "Enables adjusting the print head acceleration. Increasing the accelerations can reduce printing time at the cost of print quality."
msgstr "Active le réglage de l'accélération de la tête d'impression. Augmenter les accélérations peut réduire la durée d'impression au détriment de la qualité d'impression."

msgctxt "cool_fan_enabled description"
msgid "Enables the print cooling fans while printing. The fans improve print quality on layers with short layer times and bridging / overhangs."
msgstr "Active les ventilateurs de refroidissement de l'impression pendant l'impression. Les ventilateurs améliorent la qualité de l'impression sur les couches présentant des durées de couche courtes et des ponts / porte-à-faux."

msgctxt "machine_end_gcode label"
msgid "End G-code"
msgstr "G-Code de fin"

msgctxt "material_end_of_filament_purge_length label"
msgid "End of Filament Purge Length"
msgstr "Longueur de purge de l'extrémité du filament"

msgctxt "material_end_of_filament_purge_speed label"
msgid "End of Filament Purge Speed"
msgstr "Vitesse de purge de l'extrémité du filament"

msgctxt "brim_replaces_support description"
msgid "Enforce brim to be printed around the model even if that space would otherwise be occupied by support. This replaces some regions of the first layer of support by brim regions."
msgstr "Appliquer la bordure à imprimer autour du modèle même si cet espace aurait autrement dû être occupé par le support, en remplaçant certaines régions de la première couche de support par des régions de la bordure."

msgctxt "support_type option everywhere"
msgid "Everywhere"
msgstr "Partout"

msgctxt "slicing_tolerance option exclusive"
msgid "Exclusive"
msgstr "Exclusif"

msgctxt "experimental label"
msgid "Experimental"
msgstr "Expérimental"

msgctxt "z_seam_corner option z_seam_corner_outer"
msgid "Expose Seam"
msgstr "Exposer jointure"

msgctxt "meshfix_extensive_stitching label"
msgid "Extensive Stitching"
msgstr "Raccommodage"

msgctxt "meshfix_extensive_stitching description"
msgid "Extensive stitching tries to stitch up open holes in the mesh by closing the hole with touching polygons. This option can introduce a lot of processing time."
msgstr "Le raccommodage consiste en la suppression des trous dans le maillage en tentant de fermer le trou avec des intersections entre polygones existants. Cette option peut induire beaucoup de temps de calcul."

msgctxt "infill_wall_line_count label"
msgid "Extra Infill Wall Count"
msgstr "Nombre de parois de remplissage supplémentaire"

msgctxt "skin_outline_count label"
msgid "Extra Skin Wall Count"
msgstr "Nombre supplémentaire de parois extérieures"

msgctxt "switch_extruder_extra_prime_amount description"
msgid "Extra material to prime after nozzle switching."
msgstr "Matériel supplémentaire à amorcer après le changement de buse."

msgctxt "extruder_prime_pos_x label"
msgid "Extruder Prime X Position"
msgstr "Extrudeuse Position d'amorçage X"

msgctxt "extruder_prime_pos_y label"
msgid "Extruder Prime Y Position"
msgstr "Extrudeuse Position d'amorçage Y"

msgctxt "extruder_prime_pos_z label"
msgid "Extruder Prime Z Position"
msgstr "Extrudeuse Position d'amorçage Z"

msgctxt "machine_extruders_share_heater label"
msgid "Extruders Share Heater"
msgstr "Les extrudeurs partagent le chauffage"

msgctxt "machine_extruders_share_nozzle label"
msgid "Extruders Share Nozzle"
msgstr "Les extrudeuses partagent la buse"

msgctxt "material_extrusion_cool_down_speed label"
msgid "Extrusion Cool Down Speed Modifier"
msgstr "Modificateur de vitesse de refroidissement de l'extrusion"

msgctxt "speed_equalize_flow_width_factor description"
msgid "Extrusion width based correction factor on the speed. At 0% the movement speed is kept constant at the Print Speed. At 100% the movement speed is adjusted so that the flow (in mm³/s) is kept constant, i.e. lines half the normal Line Width are printed twice as fast and lines twice as wide are printed half as fast. A value larger than 100% can help to compensate for the higher pressure required to extrude wide lines."
msgstr "Facteur de correction de la largeur d'extrusion en fonction de la vitesse. À 0 %, la vitesse de mouvement reste constante à la vitesse d'impression. À 100 %, la vitesse de mouvement est ajustée de sorte que le débit (en mm³/s) reste constant, c'est-à-dire que les lignes à la moitié de la largeur de ligne normale sont imprimées deux fois plus vite et que les lignes à la moitié de la largeur sont imprimées aussi vite. Une valeur supérieure à 100 % peut aider à compenser la pression plus élevée requise pour extruder les lignes larges."

msgctxt "cool_fan_speed label"
msgid "Fan Speed"
msgstr "Vitesse du ventilateur"

msgctxt "support_fan_enable label"
msgid "Fan Speed Override"
msgstr "Annulation de la vitesse du ventilateur"

msgctxt "small_feature_max_length description"
msgid "Feature outlines that are shorter than this length will be printed using Small Feature Speed."
msgstr "Les contours des structures dont le diamètre est inférieur à cette longueur seront imprimés en utilisant l'option Vitesse de petite structure."

msgctxt "experimental description"
msgid "Features that haven't completely been fleshed out yet."
msgstr "Des fonctionnalités qui n'ont pas encore été complètement développées."

msgctxt "machine_feeder_wheel_diameter label"
msgid "Feeder Wheel Diameter"
msgstr "Diamètre de roue du chargeur"

msgctxt "material_final_print_temperature label"
msgid "Final Printing Temperature"
msgstr "Température d’impression finale"

msgctxt "machine_firmware_retract label"
msgid "Firmware Retraction"
msgstr "Rétraction du firmware"

msgctxt "support_extruder_nr_layer_0 label"
msgid "First Layer Support Extruder"
msgstr "Extrudeuse de support de la première couche"

msgctxt "material_flow label"
msgid "Flow"
msgstr "Débit"

msgctxt "speed_equalize_flow_width_factor label"
msgid "Flow Equalization Ratio"
msgstr "Rapport d'égalisation des débits"

msgctxt "flow_rate_extrusion_offset_factor label"
msgid "Flow Rate Compensation Factor"
msgstr "Facteur de compensation du débit"

msgctxt "flow_rate_max_extrusion_offset label"
msgid "Flow Rate Compensation Max Extrusion Offset"
msgstr "Décalage d'extrusion max. pour compensation du débit"

msgctxt "material_flow_temp_graph label"
msgid "Flow Temperature Graph"
msgstr "Graphique de la température du flux"

msgctxt "material_flow_layer_0 description"
msgid "Flow compensation for the first layer: the amount of material extruded on the initial layer is multiplied by this value."
msgstr "Compensation du débit pour la couche initiale : la quantité de matériau extrudée sur la couche initiale est multipliée par cette valeur."

msgctxt "skin_material_flow_layer_0 description"
msgid "Flow compensation on bottom lines of the first layer"
msgstr "Compensation de débit sur les lignes inférieures de la première couche"

msgctxt "infill_material_flow description"
msgid "Flow compensation on infill lines."
msgstr "Compensation de débit sur les lignes de remplissage."

msgctxt "support_interface_material_flow description"
msgid "Flow compensation on lines of support roof or floor."
msgstr "Compensation de débit sur les lignes de plafond ou de bas de support."

msgctxt "roofing_material_flow description"
msgid "Flow compensation on lines of the areas at the top of the print."
msgstr "Compensation de débit sur les lignes des zones en haut de l'impression."

msgctxt "prime_tower_flow description"
msgid "Flow compensation on prime tower lines."
msgstr "Compensation de débit sur les lignes de la tour d'amorçage."

msgctxt "skirt_brim_material_flow description"
msgid "Flow compensation on skirt or brim lines."
msgstr "Compensation de débit sur les lignes de jupe ou bordure."

msgctxt "support_bottom_material_flow description"
msgid "Flow compensation on support floor lines."
msgstr "Compensation de débit sur les lignes de bas de support."

msgctxt "support_roof_material_flow description"
msgid "Flow compensation on support roof lines."
msgstr "Compensation de débit sur les lignes du plafond de support."

msgctxt "support_material_flow description"
msgid "Flow compensation on support structure lines."
msgstr "Compensation de débit sur les lignes de support."

msgctxt "wall_0_material_flow_layer_0 description"
msgid "Flow compensation on the outermost wall line of the first layer."
msgstr "Compensation de débit sur la ligne de paroi la plus externe de la première couche."

msgctxt "wall_0_material_flow description"
msgid "Flow compensation on the outermost wall line."
msgstr "Compensation de débit sur la ligne de la paroi la plus à l'extérieur."

msgctxt "wall_0_material_flow_roofing description"
msgid "Flow compensation on the top surface outermost wall line."
msgstr "Compensation de flux sur la ligne de paroi la plus externe de la surface supérieure."

msgctxt "wall_x_material_flow_roofing description"
msgid "Flow compensation on top surface wall lines for all wall lines except the outermost one."
msgstr "Compensation du flux sur les lignes de paroi de la surface supérieure pour toutes les lignes de paroi sauf la plus externe."

msgctxt "skin_material_flow description"
msgid "Flow compensation on top/bottom lines."
msgstr "Compensation de débit sur les lignes du dessus/dessous."

msgctxt "wall_x_material_flow_layer_0 description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one, but only for the first layer"
msgstr "Compensation de débit sur les lignes de paroi pour toutes les lignes de paroi, à l'exception de la ligne la plus plus externe, mais uniquement pour la première couche"

msgctxt "wall_x_material_flow description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one."
msgstr "Compensation de débit sur les lignes de la paroi pour toutes les lignes de paroi, à l'exception de la ligne la plus externe."

msgctxt "wall_material_flow description"
msgid "Flow compensation on wall lines."
msgstr "Compensation de débit sur les lignes de la paroi."

msgctxt "material_flow description"
msgid "Flow compensation: the amount of material extruded is multiplied by this value."
msgstr "Compensation du débit : la quantité de matériau extrudée est multipliée par cette valeur."

msgctxt "meshfix_fluid_motion_angle label"
msgid "Fluid Motion Angle"
msgstr "Angle de mouvement fluide"

msgctxt "meshfix_fluid_motion_shift_distance label"
msgid "Fluid Motion Shift Distance"
msgstr "Distance de décalage du mouvement fluide"

msgctxt "meshfix_fluid_motion_small_distance label"
msgid "Fluid Motion Small Distance"
msgstr "Faible distance de décalage du mouvement fluide"

msgctxt "material_flush_purge_length label"
msgid "Flush Purge Length"
msgstr "Longueur de la purge d'insertion"

msgctxt "material_flush_purge_speed label"
msgid "Flush Purge Speed"
msgstr "Vitesse de purge d'insertion"

msgctxt "min_wall_line_width description"
msgid "For thin structures around once or twice the nozzle size, the line widths need to be altered to adhere to the thickness of the model. This setting controls the minimum line width allowed for the walls. The minimum line widths inherently also determine the maximum line widths, since we transition from N to N+1 walls at some geometry thickness where the N walls are wide and the N+1 walls are narrow. The widest possible wall line is twice the Minimum Wall Line Width."
msgstr "Pour les structures fines dont la taille correspond à une ou deux fois celle de la buse, il faut modifier la largeur des lignes pour respecter l'épaisseur du modèle. Ce paramètre contrôle la largeur de ligne minimale autorisée pour les parois. Les largeurs de lignes minimales déterminent également les largeurs de lignes maximales, puisque nous passons de N à N+1 parois à une certaine épaisseur géométrique où les N parois sont larges et les N+1 parois sont étroites. La ligne de paroi la plus large possible est égale à deux fois la largeur minimale de la ligne de paroi."

msgctxt "z_seam_position option front"
msgid "Front"
msgstr "Avant"

msgctxt "z_seam_position option frontleft"
msgid "Front Left"
msgstr "Avant gauche"

msgctxt "z_seam_position option frontright"
msgid "Front Right"
msgstr "Avant droit"

msgctxt "draft_shield_height_limitation option full"
msgid "Full"
msgstr "Pleine hauteur"

msgctxt "magic_fuzzy_skin_enabled label"
msgid "Fuzzy Skin"
msgstr "Surfaces floues"

msgctxt "magic_fuzzy_skin_point_density label"
msgid "Fuzzy Skin Density"
msgstr "Densité de la couche floue"

msgctxt "magic_fuzzy_skin_outside_only label"
msgid "Fuzzy Skin Outside Only"
msgstr "Couche floue à l'extérieur uniquement"

msgctxt "magic_fuzzy_skin_point_dist label"
msgid "Fuzzy Skin Point Distance"
msgstr "Distance entre les points de la couche floue"

msgctxt "magic_fuzzy_skin_thickness label"
msgid "Fuzzy Skin Thickness"
msgstr "Épaisseur de la couche floue"

msgctxt "machine_gcode_flavor label"
msgid "G-code Flavor"
msgstr "Parfum G-Code"

msgctxt "machine_end_gcode description"
msgid ""
"G-code commands to be executed at the very end - separated by \n"
"."
msgstr ""
"Commandes G-Code à exécuter tout à la fin, séparées par \n"
"."

msgctxt "machine_start_gcode description"
msgid ""
"G-code commands to be executed at the very start - separated by \n"
"."
msgstr ""
"Commandes G-Code à exécuter au tout début, séparées par \n"
"."

msgctxt "material_guid description"
msgid "GUID of the material. This is set automatically."
msgstr "GUID du matériau. Cela est configuré automatiquement."

msgctxt "gantry_height label"
msgid "Gantry Height"
msgstr "Hauteur du portique"

msgctxt "interlocking_enable label"
msgid "Generate Interlocking Structure"
msgstr "Générer une structure de connexion"

msgctxt "support_enable label"
msgid "Generate Support"
msgstr "Générer les supports"

msgctxt "support_brim_enable description"
msgid "Generate a brim within the support infill regions of the first layer. This brim is printed underneath the support, not around it. Enabling this setting increases the adhesion of support to the build plate."
msgstr "Générer un bord à l'intérieur des zones de remplissage du support de la première couche. Cette bordure est imprimée sous le support et non autour de celui-ci, ce qui augmente l'adhérence du support au plateau."

msgctxt "support_interface_enable description"
msgid "Generate a dense interface between the model and the support. This will create a skin at the top of the support on which the model is printed and at the bottom of the support, where it rests on the model."
msgstr "Générer une interface dense entre le modèle et le support. Cela créera une couche sur le dessus du support sur lequel le modèle est imprimé et sur le dessous du support sur lequel le modèle repose."

msgctxt "support_bottom_enable description"
msgid "Generate a dense slab of material between the bottom of the support and the model. This will create a skin between the model and support."
msgstr "Générer une plaque dense de matériau entre le bas du support et le modèle. Cela créera une couche extérieure entre le modèle et le support."

msgctxt "support_roof_enable description"
msgid "Generate a dense slab of material between the top of support and the model. This will create a skin between the model and support."
msgstr "Générer une plaque dense de matériau entre le plafond du support et le modèle. Cela créera une couche extérieure entre le modèle et le support."

msgctxt "support_enable description"
msgid "Generate structures to support parts of the model which have overhangs. Without these structures, such parts would collapse during printing."
msgstr "Générer des structures pour soutenir les parties du modèle qui possèdent des porte-à-faux. Sans ces structures, ces parties s'effondreront durant l'impression."

msgctxt "machine_buildplate_type option glass"
msgid "Glass"
msgstr "Verre"

msgctxt "ironing_enabled description"
msgid "Go over the top surface one additional time, but this time extruding very little material. This is meant to melt the plastic on top further, creating a smoother surface. The pressure in the nozzle chamber is kept high so that the creases in the surface are filled with material."
msgstr "Allez au-dessus de la surface une fois supplémentaire, mais en extrudant très peu de matériau. Cela signifie de faire fondre le plastique en haut un peu plus, pour créer une surface lisse. La pression dans la chambre de la buse est maintenue élevée afin que les plis de la surface soient remplis de matériau."

msgctxt "gradual_infill_step_height label"
msgid "Gradual Infill Step Height"
msgstr "Hauteur de l'étape de remplissage progressif"

msgctxt "gradual_infill_steps label"
msgid "Gradual Infill Steps"
msgstr "Étapes de remplissage progressif"

msgctxt "gradual_support_infill_step_height label"
msgid "Gradual Support Infill Step Height"
msgstr "Hauteur d'étape de remplissage graduel du support"

msgctxt "gradual_support_infill_steps label"
msgid "Gradual Support Infill Steps"
msgstr "Étapes de remplissage graduel du support"

msgctxt "cool_min_temperature description"
msgid "Gradually reduce to this temperature when printing at reduced speeds because of minimum layer time."
msgstr "Réduisez progressivement à cette température lors de l'impression à des vitesses réduites en raison de la durée minimale d’une couche."

msgctxt "infill_pattern option grid"
msgid "Grid"
msgstr "Grille"

msgctxt "support_bottom_pattern option grid"
msgid "Grid"
msgstr "Grille"

msgctxt "support_interface_pattern option grid"
msgid "Grid"
msgstr "Grille"

msgctxt "support_pattern option grid"
msgid "Grid"
msgstr "Grille"

msgctxt "support_roof_pattern option grid"
msgid "Grid"
msgstr "Grille"

msgctxt "machine_gcode_flavor option Griffin"
msgid "Griffin"
msgstr "Griffin"

msgctxt "group_outer_walls label"
msgid "Group Outer Walls"
msgstr "Regrouper les parois extérieures"

msgctxt "infill_pattern option gyroid"
msgid "Gyroid"
msgstr "Gyroïde"

msgctxt "support_pattern option gyroid"
msgid "Gyroid"
msgstr "Gyroïde"

msgctxt "machine_heated_build_volume label"
msgid "Has Build Volume Temperature Stabilization"
msgstr "Est dotée de la stabilisation de la température du volume d'impression"

msgctxt "machine_heated_bed label"
msgid "Has Heated Build Plate"
msgstr "A un plateau chauffé"

msgctxt "machine_nozzle_heat_up_speed label"
msgid "Heat Up Speed"
msgstr "Vitesse de chauffage"

msgctxt "machine_heat_zone_length label"
msgid "Heat Zone Length"
msgstr "Longueur de la zone chauffée"

msgctxt "draft_shield_height description"
msgid "Height limitation of the draft shield. Above this height no draft shield will be printed."
msgstr "Hauteur limite du bouclier. Au-delà de cette hauteur, aucun bouclier ne sera imprimé."

msgctxt "z_seam_corner option z_seam_corner_inner"
msgid "Hide Seam"
msgstr "Masquer jointure"

msgctxt "z_seam_corner option z_seam_corner_any"
msgid "Hide or Expose Seam"
msgstr "Masquer ou exposer jointure"

msgctxt "hole_xy_offset label"
msgid "Hole Horizontal Expansion"
msgstr "Expansion horizontale des trous"

msgctxt "hole_xy_offset_max_diameter label"
msgid "Hole Horizontal Expansion Max Diameter"
msgstr "Diamètre maximal de l'expansion horizontale des trous"

msgctxt "small_hole_max_size description"
msgid "Holes and part outlines with a diameter smaller than this will be printed using Small Feature Speed."
msgstr "Les trous et les contours des pièces dont le diamètre est inférieur à celui-ci seront imprimés en utilisant l'option Vitesse de petite structure."

msgctxt "xy_offset label"
msgid "Horizontal Expansion"
msgstr "Expansion horizontale"

msgctxt "material_shrinkage_percentage_xy label"
msgid "Horizontal Scaling Factor Shrinkage Compensation"
msgstr "Compensation du rétrécissement du facteur d'échelle horizontale"

msgctxt "material_break_preparation_retracted_position description"
msgid "How far the filament can be stretched before it breaks, while heated."
msgstr "Jusqu'où le filament peut être étiré avant qu'il ne se casse, pendant qu'il est chauffé."

msgctxt "material_anti_ooze_retracted_position description"
msgid "How far the material needs to be retracted before it stops oozing."
msgstr "Jusqu'où le matériau doit être rétracté avant qu'il cesse de suinter."

msgctxt "flow_rate_extrusion_offset_factor description"
msgid "How far to move the filament in order to compensate for changes in flow rate, as a percentage of how far the filament would move in one second of extrusion."
msgstr "La distance de déplacement du filament pour compenser les variations du débit, en pourcentage de la distance de déplacement du filament en une seconde d'extrusion."

msgctxt "material_break_retracted_position description"
msgid "How far to retract the filament in order to break it cleanly."
msgstr "Jusqu'où rétracter le filament afin de le casser proprement."

msgctxt "material_break_preparation_speed description"
msgid "How fast the filament needs to be retracted just before breaking it off in a retraction."
msgstr "La vitesse à laquelle le filament doit être rétracté juste avant de le briser dans une rétraction."

msgctxt "material_anti_ooze_retraction_speed description"
msgid "How fast the material needs to be retracted during a filament switch to prevent oozing."
msgstr "À quelle vitesse le matériau doit-il être rétracté lors d'un changement de filament pour empêcher le suintage."

msgctxt "material_end_of_filament_purge_speed description"
msgid "How fast to prime the material after replacing an empty spool with a fresh spool of the same material."
msgstr "La vitesse d'amorçage du matériau après le remplacement d'une bobine vide par une nouvelle bobine du même matériau."

msgctxt "material_flush_purge_speed description"
msgid "How fast to prime the material after switching to a different material."
msgstr "La vitesse d'amorçage du matériau après le passage à un autre matériau."

msgctxt "material_maximum_park_duration description"
msgid "How long the material can be kept out of dry storage safely."
msgstr "La durée pendant laquelle le matériau peut être conservé à l'abri de la sécheresse."

msgctxt "machine_steps_per_mm_x description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the X direction."
msgstr "Nombre de pas du moteur pas à pas correspondant à un mouvement d'un millimètre dans la direction X."

msgctxt "machine_steps_per_mm_y description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Y direction."
msgstr "Nombre de pas du moteur pas à pas correspondant à un mouvement d'un millimètre dans la direction Y."

msgctxt "machine_steps_per_mm_z description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Z direction."
msgstr "Nombre de pas du moteur pas à pas correspondant à un mouvement d'un millimètre dans la direction Z."

msgctxt "machine_steps_per_mm_e description"
msgid "How many steps of the stepper motors will result in moving the feeder wheel by one millimeter around its circumference."
msgstr "Nombre de pas des moteurs pas à pas correspondant au déplacement de la roue du chargeur d'un millimètre sur sa circonférence."

msgctxt "material_end_of_filament_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when replacing an empty spool with a fresh spool of the same material."
msgstr "La quantité de matériau à utiliser pour purger le matériau précédent de la buse (en longueur de filament) lors du remplacement d'une bobine vide par une nouvelle bobine du même matériau."

msgctxt "material_flush_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when switching to a different material."
msgstr "La quantité de matériau à utiliser pour purger le matériau précédent de la buse (en longueur de filament) lors du passage à un autre matériau."

msgctxt "machine_extruders_shared_nozzle_initial_retraction description"
msgid "How much the filament of each extruder is assumed to have been retracted from the shared nozzle tip at the completion of the printer-start gcode script; the value should be equal to or greater than the length of the common part of the nozzle's ducts."
msgstr "La quantité de filament de chaque extrudeuse qui est supposée avoir été rétractée de l'extrémité de la buse partagée à la fin du script gcode de démarrage de l'imprimante ; la valeur doit être égale ou supérieure à la longueur de la partie commune des conduits de la buse."

msgctxt "support_interface_priority description"
msgid "How support interface and support will interact when they overlap. Currently only implemented for support roof."
msgstr "Ce paramètre détermine la façon dont l'interface de support et le support interagissent en cas de chevauchement. Il n'est actuellement disponible que pour le plafond de support."

msgctxt "support_tree_min_height_to_model description"
msgid "How tall a branch has to be if it is placed on the model. Prevents small blobs of support. This setting is ignored when a branch is supporting a support roof."
msgstr "Il s'agit de la hauteur minimale que doit atteindre une branche si elle est rattachée au modèle. Ce paramètre empêche la formation de petites gouttes de support. Il est ignoré lorsqu'une branche soutient un plafond de support."

msgctxt "bridge_skin_support_threshold description"
msgid "If a skin region is supported for less than this percentage of its area, print it using the bridge settings. Otherwise it is printed using the normal skin settings."
msgstr "Si une région de couche extérieure est supportée pour une valeur inférieure à ce pourcentage de sa surface, elle sera imprimée selon les paramètres du pont. Sinon, elle sera imprimée selon les paramètres normaux de la couche extérieure."

msgctxt "meshfix_fluid_motion_angle description"
msgid "If a toolpath-segment deviates more than this angle from the general motion it is smoothed."
msgstr "Si un segment du parcours d'outil s'écarte d'une valeur supérieure à cet angle par rapport au mouvement général, il est lissé."

msgctxt "bridge_enable_more_layers description"
msgid "If enabled, the second and third layers above the air are printed using the following settings. Otherwise, those layers are printed using the normal settings."
msgstr "Si cette option est activée, les deuxième et troisième couches au-dessus de la zone d'air seront imprimées selon les paramètres suivants. Sinon, ces couches seront imprimées selon les paramètres normaux."

msgctxt "wall_transition_filter_distance description"
msgid "If it would be transitioning back and forth between different numbers of walls in quick succession, don't transition at all. Remove transitions if they are closer together than this distance."
msgstr "S'il s'agit d'une transition d'avant en arrière entre différents nombres de parois en succession rapide, ne faites pas du tout la transition. Supprimez les transitions si elles sont plus proches les unes des autres que cette distance."

msgctxt "raft_margin description"
msgid "If the raft is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Si vous avez appliqué un radeau, alors il s’agit de l’espace de radeau supplémentaire autour du modèle qui dispose déjà d’un radeau. L’augmentation de cette marge va créer un radeau plus solide, mais requiert davantage de matériau et laisse moins de place pour votre impression."

msgctxt "meshfix_union_all description"
msgid "Ignore the internal geometry arising from overlapping volumes within a mesh and print the volumes as one. This may cause unintended internal cavities to disappear."
msgstr "Ignorer la géométrie interne pouvant découler de volumes se chevauchant à l'intérieur d'un maillage et imprimer les volumes comme un seul. Cela peut entraîner la disparition des cavités internes accidentelles."

msgctxt "material_bed_temp_prepend label"
msgid "Include Build Plate Temperature"
msgstr "Inclure la température du plateau"

msgctxt "material_print_temp_prepend label"
msgid "Include Material Temperatures"
msgstr "Inclure les températures du matériau"

msgctxt "slicing_tolerance option inclusive"
msgid "Inclusive"
msgstr "Inclusif"

msgctxt "infill description"
msgid "Infill"
msgstr "Remplissage"

msgctxt "infill label"
msgid "Infill"
msgstr "Remplissage"

msgctxt "acceleration_infill label"
msgid "Infill Acceleration"
msgstr "Accélération de remplissage"

msgctxt "infill_before_walls label"
msgid "Infill Before Walls"
msgstr "Imprimer le remplissage avant les parois"

msgctxt "infill_sparse_density label"
msgid "Infill Density"
msgstr "Densité du remplissage"

msgctxt "infill_extruder_nr label"
msgid "Infill Extruder"
msgstr "Extrudeuse de remplissage"

msgctxt "infill_material_flow label"
msgid "Infill Flow"
msgstr "Débit de remplissage"

msgctxt "jerk_infill label"
msgid "Infill Jerk"
msgstr "Saccade de remplissage"

msgctxt "infill_sparse_thickness label"
msgid "Infill Layer Thickness"
msgstr "Épaisseur de la couche de remplissage"

msgctxt "infill_angles label"
msgid "Infill Line Directions"
msgstr "Sens de ligne de remplissage"

msgctxt "infill_line_distance label"
msgid "Infill Line Distance"
msgstr "Distance d'écartement de ligne de remplissage"

msgctxt "infill_multiplier label"
msgid "Infill Line Multiplier"
msgstr "Multiplicateur de ligne de remplissage"

msgctxt "infill_line_width label"
msgid "Infill Line Width"
msgstr "Largeur de ligne de remplissage"

msgctxt "infill_mesh label"
msgid "Infill Mesh"
msgstr "Maille de remplissage"

msgctxt "infill_support_angle label"
msgid "Infill Overhang Angle"
msgstr "Angle de porte-à-faux de remplissage"

msgctxt "infill_overlap_mm label"
msgid "Infill Overlap"
msgstr "Chevauchement du remplissage"

msgctxt "infill_overlap label"
msgid "Infill Overlap Percentage"
msgstr "Pourcentage de chevauchement du remplissage"

msgctxt "infill_pattern label"
msgid "Infill Pattern"
msgstr "Motif de remplissage"

msgctxt "speed_infill label"
msgid "Infill Speed"
msgstr "Vitesse de remplissage"

msgctxt "infill_support_enabled label"
msgid "Infill Support"
msgstr "Support de remplissage"

msgctxt "infill_enable_travel_optimization label"
msgid "Infill Travel Optimization"
msgstr "Optimisation du déplacement de remplissage"

msgctxt "infill_wipe_dist label"
msgid "Infill Wipe Distance"
msgstr "Distance de remplissage"

msgctxt "infill_offset_x label"
msgid "Infill X Offset"
msgstr "Remplissage Décalage X"

msgctxt "infill_offset_y label"
msgid "Infill Y Offset"
msgstr "Remplissage Décalage Y"

msgctxt "initial_bottom_layers label"
msgid "Initial Bottom Layers"
msgstr "Couches inférieures initiales"

msgctxt "cool_fan_speed_0 label"
msgid "Initial Fan Speed"
msgstr "Vitesse des ventilateurs initiale"

msgctxt "acceleration_layer_0 label"
msgid "Initial Layer Acceleration"
msgstr "Accélération de la couche initiale"

msgctxt "skin_material_flow_layer_0 label"
msgid "Initial Layer Bottom Flow"
msgstr "Débit inférieur de la couche initiale"

msgctxt "support_tree_bp_diameter label"
msgid "Initial Layer Diameter"
msgstr "Diamètre de la couche initiale"

msgctxt "material_flow_layer_0 label"
msgid "Initial Layer Flow"
msgstr "Débit de la couche initiale"

msgctxt "layer_height_0 label"
msgid "Initial Layer Height"
msgstr "Hauteur de la couche initiale"

msgctxt "xy_offset_layer_0 label"
msgid "Initial Layer Horizontal Expansion"
msgstr "Expansion horizontale de la couche initiale"

msgctxt "wall_x_material_flow_layer_0 label"
msgid "Initial Layer Inner Wall Flow"
msgstr "Débit de la paroi intérieure de la couche initiale"

msgctxt "jerk_layer_0 label"
msgid "Initial Layer Jerk"
msgstr "Saccade de la couche initiale"

msgctxt "initial_layer_line_width_factor label"
msgid "Initial Layer Line Width"
msgstr "Largeur de ligne couche initiale"

msgctxt "wall_0_material_flow_layer_0 label"
msgid "Initial Layer Outer Wall Flow"
msgstr "Débit de la paroi extérieure de la couche initiale"

msgctxt "acceleration_print_layer_0 label"
msgid "Initial Layer Print Acceleration"
msgstr "Accélération de l'impression de la couche initiale"

msgctxt "jerk_print_layer_0 label"
msgid "Initial Layer Print Jerk"
msgstr "Saccade d’impression de la couche initiale"

msgctxt "speed_print_layer_0 label"
msgid "Initial Layer Print Speed"
msgstr "Vitesse d’impression de la couche initiale"

msgctxt "speed_layer_0 label"
msgid "Initial Layer Speed"
msgstr "Vitesse de la couche initiale"

msgctxt "support_initial_layer_line_distance label"
msgid "Initial Layer Support Line Distance"
msgstr "Distance d'écartement de ligne du support de la couche initiale"

msgctxt "acceleration_travel_layer_0 label"
msgid "Initial Layer Travel Acceleration"
msgstr "Accélération de déplacement de la couche initiale"

msgctxt "jerk_travel_layer_0 label"
msgid "Initial Layer Travel Jerk"
msgstr "Saccade de déplacement de la couche initiale"

msgctxt "speed_travel_layer_0 label"
msgid "Initial Layer Travel Speed"
msgstr "Vitesse de déplacement de la couche initiale"

msgctxt "layer_0_z_overlap label"
msgid "Initial Layer Z Overlap"
msgstr "Chevauchement Z de la couche initiale"

msgctxt "material_initial_print_temperature label"
msgid "Initial Printing Temperature"
msgstr "Température d’impression initiale"

msgctxt "acceleration_wall_x label"
msgid "Inner Wall Acceleration"
msgstr "Accélération de la paroi intérieure"

msgctxt "wall_x_extruder_nr label"
msgid "Inner Wall Extruder"
msgstr "Extrudeuse de paroi interne"

msgctxt "jerk_wall_x label"
msgid "Inner Wall Jerk"
msgstr "Saccade de paroi intérieure"

msgctxt "speed_wall_x label"
msgid "Inner Wall Speed"
msgstr "Vitesse d'impression de la paroi interne"

msgctxt "wall_x_material_flow label"
msgid "Inner Wall(s) Flow"
msgstr "Débit de paroi(s) interne(s)"

msgctxt "wall_line_width_x label"
msgid "Inner Wall(s) Line Width"
msgstr "Largeur de ligne de la (des) paroi(s) interne(s)"

msgctxt "wall_0_inset description"
msgid "Inset applied to the path of the outer wall. If the outer wall is smaller than the nozzle, and printed after the inner walls, use this offset to get the hole in the nozzle to overlap with the inner walls instead of the outside of the model."
msgstr "Insert appliqué sur le passage de la paroi externe. Si la paroi externe est plus petite que la buse et imprimée après les parois intérieures, utiliser ce décalage pour que le trou dans la buse chevauche les parois internes et non l'extérieur du modèle."

msgctxt "inset_direction option inside_out"
msgid "Inside To Outside"
msgstr "De l'intérieur vers l'extérieur"

msgctxt "support_interface_priority option interface_lines_overwrite_support_area"
msgid "Interface lines preferred"
msgstr "Priorité aux lignes d'interface"

msgctxt "support_interface_priority option interface_area_overwrite_support_area"
msgid "Interface preferred"
msgstr "Priorité à l'interface"

msgctxt "interlocking_beam_layer_count label"
msgid "Interlocking Beam Layer Count"
msgstr "Nombre de couches des attaches de connexion"

msgctxt "interlocking_beam_width label"
msgid "Interlocking Beam Width"
msgstr "Largeur des attaches de connexion"

msgctxt "interlocking_boundary_avoidance label"
msgid "Interlocking Boundary Avoidance"
msgstr "Distance limite de connexion"

msgctxt "interlocking_depth label"
msgid "Interlocking Depth"
msgstr "Profondeur de connexion"

msgctxt "interlocking_orientation label"
msgid "Interlocking Structure Orientation"
msgstr "Orientation de la structure de connexion"

msgctxt "ironing_only_highest_layer label"
msgid "Iron Only Highest Layer"
msgstr "N'étirer que la couche supérieure"

msgctxt "acceleration_ironing label"
msgid "Ironing Acceleration"
msgstr "Accélération d'étirage"

msgctxt "ironing_flow label"
msgid "Ironing Flow"
msgstr "Flux d'étirage"

msgctxt "ironing_inset label"
msgid "Ironing Inset"
msgstr "Insert d'étirage"

msgctxt "jerk_ironing label"
msgid "Ironing Jerk"
msgstr "Saccade d'étirage"

msgctxt "ironing_line_spacing label"
msgid "Ironing Line Spacing"
msgstr "Interligne de l'étirage"

msgctxt "ironing_pattern label"
msgid "Ironing Pattern"
msgstr "Motif d'étirage"

msgctxt "speed_ironing label"
msgid "Ironing Speed"
msgstr "Vitesse d'étirage"

msgctxt "machine_center_is_zero label"
msgid "Is Center Origin"
msgstr "Est l'origine du centre"

msgctxt "material_is_support_material label"
msgid "Is support material"
msgstr "Matériau de support"

msgctxt "material_crystallinity description"
msgid "Is this material the type that breaks off cleanly when heated (crystalline), or is it the type that produces long intertwined polymer chains (non-crystalline)?"
msgstr "Ce matériau se casse-t-il proprement lorsqu'il est chauffé (cristallin) ou est-ce le type qui produit de longues chaînes polymères entrelacées (non cristallines) ?"

msgctxt "material_is_support_material description"
msgid "Is this material typically used as a support material during printing."
msgstr "Ce paramètre permet-il d'indiquer si un matériau est généralement utilisé comme matériau de support pendant l'impression."

msgctxt "magic_fuzzy_skin_outside_only description"
msgid "Jitter only the parts' outlines and not the parts' holes."
msgstr "N'agitez que les contours des pièces et non les trous des pièces."

msgctxt "meshfix_keep_open_polygons label"
msgid "Keep Disconnected Faces"
msgstr "Conserver les faces disjointes"

msgctxt "layer_height label"
msgid "Layer Height"
msgstr "Hauteur de la couche"

msgctxt "layer_start_x label"
msgid "Layer Start X"
msgstr "X début couche"

msgctxt "layer_start_y label"
msgid "Layer Start Y"
msgstr "Y début couche"

msgctxt "raft_base_thickness description"
msgid "Layer thickness of the base raft layer. This should be a thick layer which sticks firmly to the printer build plate."
msgstr "Épaisseur de la couche de base du radeau. Cette couche doit être épaisse et adhérer fermement au plateau."

msgctxt "raft_interface_thickness description"
msgid "Layer thickness of the middle raft layer."
msgstr "Épaisseur de la couche intermédiaire du radeau."

msgctxt "raft_surface_thickness description"
msgid "Layer thickness of the top raft layers."
msgstr "Épaisseur des couches supérieures du radeau."

msgctxt "support_skip_zag_per_mm description"
msgid "Leave out a connection between support lines once every N millimeter to make the support structure easier to break away."
msgstr "Ignorer une connexion entre lignes du support tous les N millimètres, pour rendre la structure de support plus facile à casser."

msgctxt "z_seam_position option left"
msgid "Left"
msgstr "Gauche"

msgctxt "cool_lift_head label"
msgid "Lift Head"
msgstr "Relever la tête"

msgctxt "infill_pattern option lightning"
msgid "Lightning"
msgstr "Éclair"

msgctxt "lightning_infill_overhang_angle label"
msgid "Lightning Infill Overhang Angle"
msgstr "Angle de saillie du remplissage éclair"

msgctxt "lightning_infill_prune_angle label"
msgid "Lightning Infill Prune Angle"
msgstr "Angle d'élagage du remplissage éclair"

msgctxt "lightning_infill_straightening_angle label"
msgid "Lightning Infill Straightening Angle"
msgstr "Angle de redressement du remplissage éclair"

msgctxt "lightning_infill_support_angle label"
msgid "Lightning Infill Support Angle"
msgstr "Angle de support du remplissage éclair"

msgctxt "support_tree_limit_branch_reach label"
msgid "Limit Branch Reach"
msgstr "Limitation de la portée des branches"

msgctxt "support_tree_limit_branch_reach description"
msgid "Limit how far each branch should travel from the point it supports. This can make the support more sturdy, but will increase the amount of branches (and because of that material usage/print time)"
msgstr "Ce paramètre limite la distance parcourue par chaque branche à partir du point qu'elle soutient. Le support peut ainsi être plus solide, mais le nombre de branches augmentera (tout comme la quantité de matériau utilisée et le temps d'impression)"

msgctxt "cutting_mesh description"
msgid "Limit the volume of this mesh to within other meshes. You can use this to make certain areas of one mesh print with different settings and with a whole different extruder."
msgstr "Limiter le volume de ce maillage à celui des autres maillages. Cette option permet de faire en sorte que certaines zones d'un maillage s'impriment avec des paramètres différents et avec une extrudeuse entièrement différente."

msgctxt "draft_shield_height_limitation option limited"
msgid "Limited"
msgstr "Limitée"

msgctxt "line_width label"
msgid "Line Width"
msgstr "Largeur de ligne"

msgctxt "infill_pattern option lines"
msgid "Lines"
msgstr "Lignes"

msgctxt "roofing_pattern option lines"
msgid "Lines"
msgstr "Lignes"

msgctxt "support_bottom_pattern option lines"
msgid "Lines"
msgstr "Lignes"

msgctxt "support_interface_pattern option lines"
msgid "Lines"
msgstr "Lignes"

msgctxt "support_pattern option lines"
msgid "Lines"
msgstr "Lignes"

msgctxt "support_roof_pattern option lines"
msgid "Lines"
msgstr "Lignes"

msgctxt "top_bottom_pattern option lines"
msgid "Lines"
msgstr "Lignes"

msgctxt "top_bottom_pattern_0 option lines"
msgid "Lines"
msgstr "Lignes"

msgctxt "machine_gcode_flavor option MACH3"
msgid "Mach3"
msgstr "Mach3"

msgctxt "machine_settings label"
msgid "Machine"
msgstr "Machine"

msgctxt "machine_depth label"
msgid "Machine Depth"
msgstr "Profondeur de la machine"

msgctxt "machine_head_with_fans_polygon label"
msgid "Machine Head & Fan Polygon"
msgstr "Polygone de la tête de la machine et du ventilateur"

msgctxt "machine_height label"
msgid "Machine Height"
msgstr "Hauteur de la machine"

msgctxt "machine_name label"
msgid "Machine Type"
msgstr "Type de machine"

msgctxt "machine_width label"
msgid "Machine Width"
msgstr "Largeur de la machine"

msgctxt "machine_settings description"
msgid "Machine specific settings"
msgstr "Paramètres spécifiques de la machine"

msgctxt "conical_overhang_enabled label"
msgid "Make Overhang Printable"
msgstr "Rendre le porte-à-faux imprimable"

msgctxt "multiple_mesh_overlap description"
msgid "Make meshes which are touching each other overlap a bit. This makes them bond together better."
msgstr "Faire de sorte que les maillages qui se touchent se chevauchent légèrement. Cela permet aux maillages de mieux adhérer les uns aux autres."

msgctxt "support_conical_enabled description"
msgid "Make support areas smaller at the bottom than at the overhang."
msgstr "Rendre les aires de support plus petites en bas qu'au niveau du porte-à-faux à supporter."

msgctxt "support_mesh_drop_down description"
msgid "Make support everywhere below the support mesh, so that there's no overhang in the support mesh."
msgstr "Inclure du support à tout emplacement sous le maillage de support, de sorte à ce qu'il n'y ait pas de porte-à-faux dans le maillage de support."

msgctxt "extruder_prime_pos_abs description"
msgid "Make the extruder prime position absolute rather than relative to the last-known location of the head."
msgstr "Rendre la position d'amorçage de l'extrudeuse absolue plutôt que relative à la dernière position connue de la tête."

msgctxt "layer_0_z_overlap description"
msgid "Make the first and second layer of the model overlap in the Z direction to compensate for the filament lost in the airgap. All models above the first model layer will be shifted down by this amount."
msgstr "La première et la deuxième couche du modèle se chevauchent dans la direction Z pour compenser le filament perdu dans l'entrefer. Toutes les couches au-dessus de la première couche du modèle seront décalées de ce montant."

msgctxt "meshfix description"
msgid "Make the meshes more suited for 3D printing."
msgstr "Rendez les mailles plus adaptées à l'impression 3D."

msgctxt "machine_gcode_flavor option Makerbot"
msgid "Makerbot"
msgstr "Makerbot"

msgctxt "machine_gcode_flavor option RepRap (Marlin/Sprinter)"
msgid "Marlin"
msgstr "Marlin"

msgctxt "machine_gcode_flavor option RepRap (Volumetric)"
msgid "Marlin (Volumetric)"
msgstr "Marlin (Volumétrique)"

msgctxt "material description"
msgid "Material"
msgstr "Matériau"

msgctxt "material label"
msgid "Material"
msgstr "Matériau"

msgctxt "material_guid label"
msgid "Material GUID"
msgstr "GUID matériau"

msgctxt "max_extrusion_before_wipe label"
msgid "Material Volume Between Wipes"
msgstr "Volume de matériau entre les essuyages"

msgctxt "retraction_combing_max_distance label"
msgid "Max Comb Distance With No Retract"
msgstr "Distance de détour max. sans rétraction"

msgctxt "machine_max_acceleration_x label"
msgid "Maximum Acceleration X"
msgstr "Accélération maximale X"

msgctxt "machine_max_acceleration_y label"
msgid "Maximum Acceleration Y"
msgstr "Accélération maximale Y"

msgctxt "machine_max_acceleration_z label"
msgid "Maximum Acceleration Z"
msgstr "Accélération maximale Z"

msgctxt "support_tree_angle label"
msgid "Maximum Branch Angle"
msgstr "Angle maximal des branches"

msgctxt "meshfix_maximum_deviation label"
msgid "Maximum Deviation"
msgstr "Écart maximum"

msgctxt "meshfix_maximum_extrusion_area_deviation label"
msgid "Maximum Extrusion Area Deviation"
msgstr "Écart maximal de la surface d'extrusion"

msgctxt "cool_fan_speed_max label"
msgid "Maximum Fan Speed"
msgstr "Vitesse maximale du ventilateur"

msgctxt "machine_max_acceleration_e label"
msgid "Maximum Filament Acceleration"
msgstr "Accélération maximale du filament"

msgctxt "conical_overhang_angle label"
msgid "Maximum Model Angle"
msgstr "Angle maximal du modèle"

msgctxt "conical_overhang_hole_size label"
msgid "Maximum Overhang Hole Area"
msgstr "Surface maximale du trou en porte-à-faux"

msgctxt "material_maximum_park_duration label"
msgid "Maximum Park Duration"
msgstr "Durée maximum du stationnement"

msgctxt "meshfix_maximum_resolution label"
msgid "Maximum Resolution"
msgstr "Résolution maximum"

msgctxt "retraction_count_max label"
msgid "Maximum Retraction Count"
msgstr "Nombre maximal de rétractions"

msgctxt "max_skin_angle_for_expansion label"
msgid "Maximum Skin Angle for Expansion"
msgstr "Angle maximum de la couche extérieure pour l'expansion"

msgctxt "machine_max_feedrate_e label"
msgid "Maximum Speed E"
msgstr "Vitesse maximale E"

msgctxt "machine_max_feedrate_x label"
msgid "Maximum Speed X"
msgstr "Vitesse maximale X"

msgctxt "machine_max_feedrate_y label"
msgid "Maximum Speed Y"
msgstr "Vitesse maximale Y"

msgctxt "machine_max_feedrate_z label"
msgid "Maximum Speed Z"
msgstr "Vitesse maximale Z"

msgctxt "support_tower_maximum_supported_diameter label"
msgid "Maximum Tower-Supported Diameter"
msgstr "Diamètre maximal supporté par la tour"

msgctxt "meshfix_maximum_travel_resolution label"
msgid "Maximum Travel Resolution"
msgstr "Résolution de déplacement maximum"

msgctxt "machine_max_acceleration_x description"
msgid "Maximum acceleration for the motor of the X-direction"
msgstr "Accélération maximale pour le moteur du sens X"

msgctxt "machine_max_acceleration_y description"
msgid "Maximum acceleration for the motor of the Y-direction."
msgstr "Accélération maximale pour le moteur du sens Y."

msgctxt "machine_max_acceleration_z description"
msgid "Maximum acceleration for the motor of the Z-direction."
msgstr "Accélération maximale pour le moteur du sens Z."

msgctxt "machine_max_acceleration_e description"
msgid "Maximum acceleration for the motor of the filament."
msgstr "Accélération maximale pour le moteur du filament."

msgctxt "bridge_sparse_infill_max_density description"
msgid "Maximum density of infill considered to be sparse. Skin over sparse infill is considered to be unsupported and so may be treated as a bridge skin."
msgstr "Densité maximale du remplissage considéré comme étant mince. La couche sur le remplissage mince est considérée comme non soutenue et peut donc être traitée comme une couche du pont."

msgctxt "support_tower_maximum_supported_diameter description"
msgid "Maximum diameter in the X/Y directions of a small area which is to be supported by a specialized support tower."
msgstr "Le diamètre maximal sur les axes X/Y d’une petite zone qui doit être soutenue par une tour de soutien spéciale."

msgctxt "max_extrusion_before_wipe description"
msgid "Maximum material that can be extruded before another nozzle wipe is initiated. If this value is less than the volume of material required in a layer, the setting has no effect in this layer, i.e. it is limited to one wipe per layer."
msgstr "Le volume maximum de matériau qui peut être extrudé avant qu'un autre essuyage de buse ne soit lancé. Si cette valeur est inférieure au volume de matériau nécessaire dans une couche, le paramètre n'a aucun effet dans cette couche, c'est-à-dire qu'il est limité à un essuyage par couche."

msgctxt "multiple_mesh_overlap label"
msgid "Merged Meshes Overlap"
msgstr "Chevauchement des mailles fusionnées"

msgctxt "meshfix label"
msgid "Mesh Fixes"
msgstr "Corrections"

msgctxt "mesh_position_x label"
msgid "Mesh Position X"
msgstr "Position X de la maille"

msgctxt "mesh_position_y label"
msgid "Mesh Position Y"
msgstr "Position Y de la maille"

msgctxt "mesh_position_z label"
msgid "Mesh Position Z"
msgstr "Position Z de la maille"

msgctxt "infill_mesh_order label"
msgid "Mesh Processing Rank"
msgstr "Rang de traitement du maillage"

msgctxt "mesh_rotation_matrix label"
msgid "Mesh Rotation Matrix"
msgstr "Matrice de rotation de la maille"

msgctxt "slicing_tolerance option middle"
msgid "Middle"
msgstr "Milieu"

msgctxt "mold_width label"
msgid "Minimal Mold Width"
msgstr "Largeur minimale de moule"

msgctxt "machine_min_cool_heat_time_window label"
msgid "Minimal Time Standby Temperature"
msgstr "Durée minimale température de veille"

msgctxt "bridge_wall_min_length label"
msgid "Minimum Bridge Wall Length"
msgstr "Longueur minimale de la paroi du pont"

msgctxt "min_even_wall_line_width label"
msgid "Minimum Even Wall Line Width"
msgstr "Largeur minimale de la ligne de paroi uniforme"

msgctxt "retraction_extrusion_window label"
msgid "Minimum Extrusion Distance Window"
msgstr "Intervalle de distance minimale d'extrusion"

msgctxt "min_feature_size label"
msgid "Minimum Feature Size"
msgstr "Taille minimale des entités"

msgctxt "machine_minimum_feedrate label"
msgid "Minimum Feedrate"
msgstr "Taux d'alimentation minimal"

msgctxt "support_tree_min_height_to_model label"
msgid "Minimum Height To Model"
msgstr "Hauteur minimale par rapport au modèle"

msgctxt "min_infill_area label"
msgid "Minimum Infill Area"
msgstr "Zone de remplissage minimum"

msgctxt "cool_min_layer_time label"
msgid "Minimum Layer Time"
msgstr "Durée minimale d’une couche"

msgctxt "min_odd_wall_line_width label"
msgid "Minimum Odd Wall Line Width"
msgstr "Largeur minimale de la ligne de paroi impaire"

msgctxt "minimum_polygon_circumference label"
msgid "Minimum Polygon Circumference"
msgstr "Circonférence minimale du polygone"

msgctxt "min_skin_width_for_expansion label"
msgid "Minimum Skin Width for Expansion"
msgstr "Largeur minimum de la couche extérieure pour l'expansion"

msgctxt "cool_min_speed label"
msgid "Minimum Speed"
msgstr "Vitesse minimale"

msgctxt "minimum_support_area label"
msgid "Minimum Support Area"
msgstr "Surface minimale de support"

msgctxt "minimum_bottom_area label"
msgid "Minimum Support Floor Area"
msgstr "Surface minimale du bas de support"

msgctxt "minimum_interface_area label"
msgid "Minimum Support Interface Area"
msgstr "Surface minimale de l'interface de support"

msgctxt "minimum_roof_area label"
msgid "Minimum Support Roof Area"
msgstr "Surface minimale du plafond de support"

msgctxt "support_xy_distance_overhang label"
msgid "Minimum Support X/Y Distance"
msgstr "Distance X/Y minimale des supports"

msgctxt "min_bead_width label"
msgid "Minimum Thin Wall Line Width"
msgstr "Largeur minimale de la ligne de paroi fine"

msgctxt "coasting_min_volume label"
msgid "Minimum Volume Before Coasting"
msgstr "Volume minimal avant roue libre"

msgctxt "min_wall_line_width label"
msgid "Minimum Wall Line Width"
msgstr "Largeur minimale de la ligne de paroi"

msgctxt "minimum_interface_area description"
msgid "Minimum area size for support interface polygons. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Taille minimale de la surface des polygones d'interface de support. Les polygones dont la surface est inférieure à cette valeur ne seront pas imprimés comme support normal."

msgctxt "minimum_support_area description"
msgid "Minimum area size for support polygons. Polygons which have an area smaller than this value will not be generated."
msgstr "Taille minimale de la surface des polygones de support : les polygones dont la surface est inférieure à cette valeur ne seront pas générés."

msgctxt "minimum_bottom_area description"
msgid "Minimum area size for the floors of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Taille minimale de la surface des bas du support. Les polygones dont la surface est inférieure à cette valeur ne seront pas imprimés comme support normal."

msgctxt "minimum_roof_area description"
msgid "Minimum area size for the roofs of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Taille minimale de la surface des plafonds du support. Les polygones dont la surface est inférieure à cette valeur ne seront pas imprimés comme support normal."

msgctxt "min_feature_size description"
msgid "Minimum thickness of thin features. Model features that are thinner than this value will not be printed, while features thicker than the Minimum Feature Size will be widened to the Minimum Wall Line Width."
msgstr "Épaisseur minimale des entités fines. Les entités de modèle qui sont plus fines que cette valeur ne seront pas imprimées, tandis que les entités plus épaisses que la taille d'entité minimale seront élargies à la largeur minimale de la ligne de paroi."

msgctxt "support_conical_min_width description"
msgid "Minimum width to which the base of the conical support area is reduced. Small widths can lead to unstable support structures."
msgstr "Largeur minimale à laquelle la base du support conique est réduite. Des largeurs étroites peuvent entraîner des supports instables."

msgctxt "mold_enabled label"
msgid "Mold"
msgstr "Moule"

msgctxt "mold_angle label"
msgid "Mold Angle"
msgstr "Angle du moule"

msgctxt "mold_roof_height label"
msgid "Mold Roof Height"
msgstr "Hauteur du plafond de moule"

msgctxt "ironing_monotonic label"
msgid "Monotonic Ironing Order"
msgstr "Ordre d'étirage monotone"

msgctxt "roofing_monotonic label"
msgid "Monotonic Top Surface Order"
msgstr "Ordre monotone de la surface supérieure"

msgctxt "skin_monotonic label"
msgid "Monotonic Top/Bottom Order"
msgstr "Ordre monotone dessus / dessous"

msgctxt "skirt_line_count description"
msgid "Multiple skirt lines help to prime your extrusion better for small models. Setting this to 0 will disable the skirt."
msgstr "Une jupe à plusieurs lignes vous aide à mieux préparer votre extrusion pour les petits modèles. Définissez celle valeur sur 0 pour désactiver la jupe."

msgctxt "initial_layer_line_width_factor description"
msgid "Multiplier of the line width on the first layer. Increasing this could improve bed adhesion."
msgstr "Multiplicateur de la largeur de la ligne sur la première couche. Augmenter le multiplicateur peut améliorer l'adhésion au plateau."

msgctxt "material_no_load_move_factor label"
msgid "No Load Move Factor"
msgstr "Facteur de déplacement sans chargement"

msgctxt "skin_no_small_gaps_heuristic label"
msgid "No Skin in Z Gaps"
msgstr "Aucune couche dans les trous en Z"

msgctxt "blackmagic description"
msgid "Non-traditional ways to print your models."
msgstr "Des moyens non traditionnels d'imprimer vos modèles."

msgctxt "adhesion_type option none"
msgid "None"
msgstr "Aucun"

msgctxt "z_seam_corner option z_seam_corner_none"
msgid "None"
msgstr "Aucun"

msgctxt "magic_mesh_surface_mode option normal"
msgid "Normal"
msgstr "Normal"

msgctxt "support_structure option normal"
msgid "Normal"
msgstr "Normal"

msgctxt "meshfix_keep_open_polygons description"
msgid "Normally Cura tries to stitch up small holes in the mesh and remove parts of a layer with big holes. Enabling this option keeps those parts which cannot be stitched. This option should be used as a last resort option when everything else fails to produce proper g-code."
msgstr "Normalement, Cura essaye de raccommoder les petits trous dans le maillage et supprime les parties des couches contenant de gros trous. Activer cette option pousse Cura à garder les parties qui ne peuvent être raccommodées. Cette option doit être utilisée en dernier recours quand tout le reste échoue à produire un G-Code correct."

msgctxt "retraction_combing option noskin"
msgid "Not in Skin"
msgstr "Pas dans la couche extérieure"

msgctxt "retraction_combing option no_outer_surfaces"
msgid "Not on Outer Surface"
msgstr "Pas sur la surface extérieure"

msgctxt "machine_nozzle_expansion_angle label"
msgid "Nozzle Angle"
msgstr "Angle de la buse"

msgctxt "machine_nozzle_size label"
msgid "Nozzle Diameter"
msgstr "Diamètre de la buse"

msgctxt "nozzle_disallowed_areas label"
msgid "Nozzle Disallowed Areas"
msgstr "Zones interdites au bec d'impression"

msgctxt "machine_nozzle_id label"
msgid "Nozzle ID"
msgstr "ID buse"

msgctxt "machine_nozzle_head_distance label"
msgid "Nozzle Length"
msgstr "Longueur de la buse"

msgctxt "switch_extruder_extra_prime_amount label"
msgid "Nozzle Switch Extra Prime Amount"
msgstr "Montant de l'amorce supplémentaire lors d'un changement de buse"

msgctxt "switch_extruder_prime_speed label"
msgid "Nozzle Switch Prime Speed"
msgstr "Vitesse d'amorçage de changement de buse"

msgctxt "switch_extruder_retraction_speed label"
msgid "Nozzle Switch Retract Speed"
msgstr "Vitesse de rétraction de changement de buse"

msgctxt "switch_extruder_retraction_amount label"
msgid "Nozzle Switch Retraction Distance"
msgstr "Distance de rétraction de changement de buse"

msgctxt "switch_extruder_retraction_speeds label"
msgid "Nozzle Switch Retraction Speed"
msgstr "Vitesse de rétraction de changement de buse"

msgctxt "machine_extruder_count label"
msgid "Number of Extruders"
msgstr "Nombre d'extrudeuses"

msgctxt "extruders_enabled_count label"
msgid "Number of Extruders That Are Enabled"
msgstr "Nombre d'extrudeuses activées"

msgctxt "speed_slowdown_layers label"
msgid "Number of Slower Layers"
msgstr "Nombre de couches plus lentes"

msgctxt "extruders_enabled_count description"
msgid "Number of extruder trains that are enabled; automatically set in software"
msgstr "Nombre de trains d'extrusion activés ; automatiquement défini dans le logiciel"

msgctxt "machine_extruder_count description"
msgid "Number of extruder trains. An extruder train is the combination of a feeder, bowden tube, and nozzle."
msgstr "Nombre de trains d'extrudeuse. Un train d'extrudeuse est la combinaison d'un chargeur, d'un tube bowden et d'une buse."

msgctxt "wipe_repeat_count description"
msgid "Number of times to move the nozzle across the brush."
msgstr "Le nombre de déplacements de la buse à travers la brosse."

msgctxt "gradual_infill_steps description"
msgid "Number of times to reduce the infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Infill Density."
msgstr "Nombre de fois pour réduire la densité de remplissage de moitié en poursuivant sous les surfaces du dessus. Les zones qui sont plus proches des surfaces du dessus possèdent une densité plus élevée, jusqu'à la Densité du remplissage."

msgctxt "gradual_support_infill_steps description"
msgid "Number of times to reduce the support infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Support Infill Density."
msgstr "Nombre de fois pour réduire la densité de remplissage du support de moitié en poursuivant sous les surfaces du dessus. Les zones qui sont plus proches des surfaces du dessus possèdent une densité plus élevée, jusqu'à la Densité de remplissage du support."

msgctxt "infill_pattern option tetrahedral"
msgid "Octet"
msgstr "Octaédrique"

msgctxt "retraction_combing option off"
msgid "Off"
msgstr "Désactivé"

msgctxt "mesh_position_x description"
msgid "Offset applied to the object in the x direction."
msgstr "Offset appliqué à l'objet dans la direction X."

msgctxt "mesh_position_y description"
msgid "Offset applied to the object in the y direction."
msgstr "Offset appliqué à l'objet dans la direction Y."

msgctxt "mesh_position_z description"
msgid "Offset applied to the object in the z direction. With this you can perform what was used to be called 'Object Sink'."
msgstr "Décalage appliqué à l'objet dans le sens z. Cela vous permet d'exécuter ce que l'on appelait « Affaissement de l'objet »."

msgctxt "machine_use_extruder_offset_to_offset_coords label"
msgid "Offset with Extruder"
msgstr "Décalage avec extrudeuse"

msgctxt "support_tree_rest_preference option buildplate"
msgid "On buildplate when possible"
msgstr "Sur le plateau si possible"

msgctxt "support_tree_rest_preference option graceful"
msgid "On model if required"
msgstr "Sur le modèle si nécessaire"

msgctxt "print_sequence option one_at_a_time"
msgid "One at a Time"
msgstr "Un à la fois"

msgctxt "retraction_hop_only_when_collides description"
msgid "Only perform a Z Hop when moving over printed parts which cannot be avoided by horizontal motion by Avoid Printed Parts when Traveling."
msgstr "Appliquer un décalage en Z uniquement lors du mouvement au-dessus de pièces imprimées qui ne peuvent être évitées par le mouvement horizontal, via Éviter les pièces imprimées lors du déplacement."

msgctxt "ironing_only_highest_layer description"
msgid "Only perform ironing on the very last layer of the mesh. This saves time if the lower layers don't need a smooth surface finish."
msgstr "N'exécute un étirage que sur l'ultime couche du maillage. Ceci économise du temps si les couches inférieures ne nécessitent pas de fini lisse de surface."

msgctxt "brim_outside_only description"
msgid "Only print the brim on the outside of the model. This reduces the amount of brim you need to remove afterwards, while it doesn't reduce the bed adhesion that much."
msgstr "Imprimer uniquement la bordure sur l'extérieur du modèle. Cela réduit la quantité de bordure que vous devez retirer par la suite, sans toutefois véritablement réduire l'adhérence au plateau."

msgctxt "ooze_shield_angle label"
msgid "Ooze Shield Angle"
msgstr "Angle du bouclier de suintage"

msgctxt "ooze_shield_dist label"
msgid "Ooze Shield Distance"
msgstr "Distance du bouclier de suintage"

msgctxt "support_tree_branch_reach_limit label"
msgid "Optimal Branch Range"
msgstr "Portée optimale des branches"

msgctxt "optimize_wall_printing_order label"
msgid "Optimize Wall Printing Order"
msgstr "Optimiser l'ordre d'impression des parois"

msgctxt "optimize_wall_printing_order description"
msgid "Optimize the order in which walls are printed so as to reduce the number of retractions and the distance travelled. Most parts will benefit from this being enabled but some may actually take longer so please compare the print time estimates with and without optimization. First layer is not optimized when choosing brim as build plate adhesion type."
msgstr "Optimiser l'ordre dans lequel des parois sont imprimées de manière à réduire le nombre de retraits et les distances parcourues. La plupart des pièces bénéficieront de cette possibilité, mais certaines peuvent en fait prendre plus de temps à l'impression ; veuillez dès lors comparer les estimations de durée d'impression avec et sans optimisation. La première couche n'est pas optimisée lorsque le type d'adhérence au plateau est défini sur bordure."

msgctxt "machine_nozzle_tip_outer_diameter label"
msgid "Outer Nozzle Diameter"
msgstr "Diamètre extérieur de la buse"

msgctxt "acceleration_wall_0 label"
msgid "Outer Wall Acceleration"
msgstr "Accélération de la paroi externe"

msgctxt "wall_0_extruder_nr label"
msgid "Outer Wall Extruder"
msgstr "Extrudeuse de paroi externe"

msgctxt "wall_0_material_flow label"
msgid "Outer Wall Flow"
msgstr "Débit de paroi externe"

msgctxt "wall_0_inset label"
msgid "Outer Wall Inset"
msgstr "Insert de paroi externe"

msgctxt "jerk_wall_0 label"
msgid "Outer Wall Jerk"
msgstr "Saccade de paroi externe"

msgctxt "wall_line_width_0 label"
msgid "Outer Wall Line Width"
msgstr "Largeur de ligne de la paroi externe"

msgctxt "speed_wall_0 label"
msgid "Outer Wall Speed"
msgstr "Vitesse d'impression de la paroi externe"

msgctxt "wall_0_wipe_dist label"
msgid "Outer Wall Wipe Distance"
msgstr "Distance d'essuyage paroi extérieure"

msgctxt "group_outer_walls description"
msgid "Outer walls of different islands in the same layer are printed in sequence. When enabled the amount of flow changes is limited because walls are printed one type at a time, when disabled the number of travels between islands is reduced because walls in the same islands are grouped."
msgstr "Les parois extérieures de différentes îles de la même couche sont imprimées séquentiellement. Lorsque ce paramètre est activé, le nombre de changements de débit est limité car les parois sont imprimées une par une ; lorsqu'il est désactivé, le nombre de déplacements entre les îles est réduit car les parois des mêmes îles sont regroupées."

msgctxt "inset_direction option outside_in"
msgid "Outside To Inside"
msgstr "De l'extérieur vers l'intérieur"

msgctxt "wall_overhang_angle label"
msgid "Overhanging Wall Angle"
msgstr "Angle de parois en porte-à-faux"

msgctxt "wall_overhang_speed_factor label"
msgid "Overhanging Wall Speed"
msgstr "Vitesse de paroi en porte-à-faux"

msgctxt "wall_overhang_speed_factor description"
msgid "Overhanging walls will be printed at this percentage of their normal print speed."
msgstr "Les parois en porte-à-faux seront imprimées à ce pourcentage de leur vitesse d'impression normale."

msgctxt "wipe_pause description"
msgid "Pause after the unretract."
msgstr "Pause après l'irrétraction."

msgctxt "bridge_fan_speed description"
msgid "Percentage fan speed to use when printing bridge walls and skin."
msgstr "Vitesse du ventilateur en pourcentage à utiliser pour l'impression des parois et de la couche extérieure du pont."

msgctxt "bridge_fan_speed_2 description"
msgid "Percentage fan speed to use when printing the second bridge skin layer."
msgstr "Vitesse du ventilateur en pourcentage à utiliser pour l'impression de la deuxième couche extérieure du pont."

msgctxt "support_supported_skin_fan_speed description"
msgid "Percentage fan speed to use when printing the skin regions immediately above the support. Using a high fan speed can make the support easier to remove."
msgstr "Pourcentage de la vitesse du ventilateur à utiliser lors de l'impression des zones de couche extérieure situées immédiatement au-dessus du support. Une vitesse de ventilateur élevée facilite le retrait du support."

msgctxt "bridge_fan_speed_3 description"
msgid "Percentage fan speed to use when printing the third bridge skin layer."
msgstr "Vitesse du ventilateur en pourcentage à utiliser pour l'impression de la troisième couche extérieure du pont."

msgctxt "minimum_polygon_circumference description"
msgid "Polygons in sliced layers that have a circumference smaller than this amount will be filtered out. Lower values lead to higher resolution mesh at the cost of slicing time. It is meant mostly for high resolution SLA printers and very tiny 3D models with a lot of details."
msgstr "Les polygones en couches tranchées dont la circonférence est inférieure à cette valeur seront filtrés. Des valeurs élevées permettent d'obtenir un maillage de meilleure résolution mais augmentent le temps de découpe. Cette option est principalement destinée aux imprimantes SLA haute résolution et aux modèles 3D de très petite taille avec beaucoup de détails."

msgctxt "support_tree_angle_slow label"
msgid "Preferred Branch Angle"
msgstr "Angle des branches souhaité"

msgctxt "wall_transition_filter_deviation description"
msgid "Prevent transitioning back and forth between one extra wall and one less. This margin extends the range of line widths which follow to [Minimum Wall Line Width - Margin, 2 * Minimum Wall Line Width + Margin]. Increasing this margin reduces the number of transitions, which reduces the number of extrusion starts/stops and travel time. However, large line width variation can lead to under- or overextrusion problems."
msgstr "Empêchez la transition d'avant en arrière entre une paroi supplémentaire et une paroi en moins. Cette marge étend la gamme des largeurs de ligne qui suivent à [Largeur minimale de la ligne de paroi - marge, 2 * Largeur minimale de la ligne de paroi + marge]. L'augmentation de cette marge réduit le nombre de transitions, ce qui réduit le nombre de démarrages/arrêts d'extrusion et le temps de trajet. Cependant, une grande variation de la largeur de la ligne peut entraîner des problèmes de sous-extrusion ou de sur-extrusion."

msgctxt "acceleration_prime_tower label"
msgid "Prime Tower Acceleration"
msgstr "Accélération de la tour d'amorçage"

msgctxt "prime_tower_brim_enable label"
msgid "Prime Tower Base"
msgstr "Base de la tour d'amorçage"

msgctxt "prime_tower_base_height label"
msgid "Prime Tower Base Height"
msgstr "Hauteur de la base de la tour d'amorçage"

msgctxt "prime_tower_base_size label"
msgid "Prime Tower Base Size"
msgstr "Taille de la base de la tour d'amorçage"

msgctxt "prime_tower_base_curve_magnitude label"
msgid "Prime Tower Base Slope"
msgstr "Pente de la base de la tour d'amorçage"

msgctxt "prime_tower_flow label"
msgid "Prime Tower Flow"
msgstr "Débit de la tour d'amorçage"

msgctxt "jerk_prime_tower label"
msgid "Prime Tower Jerk"
msgstr "Saccade de la tour d'amorçage"

msgctxt "prime_tower_line_width label"
msgid "Prime Tower Line Width"
msgstr "Largeur de ligne de la tour d'amorçage"

msgctxt "prime_tower_min_volume label"
msgid "Prime Tower Minimum Volume"
msgstr "Volume minimum de la tour d'amorçage"

msgctxt "prime_tower_raft_base_line_spacing label"
msgid "Prime Tower Raft Line Spacing"
msgstr "Espacement des lignes de radeau de la tour d'amorçage"

msgctxt "prime_tower_size label"
msgid "Prime Tower Size"
msgstr "Taille de la tour d'amorçage"

msgctxt "speed_prime_tower label"
msgid "Prime Tower Speed"
msgstr "Vitesse de la tour d'amorçage"

msgctxt "prime_tower_position_x label"
msgid "Prime Tower X Position"
msgstr "Position X de la tour d'amorçage"

msgctxt "prime_tower_position_y label"
msgid "Prime Tower Y Position"
msgstr "Position Y de la tour d'amorçage"

msgctxt "acceleration_print label"
msgid "Print Acceleration"
msgstr "Accélération de l'impression"

msgctxt "jerk_print label"
msgid "Print Jerk"
msgstr "Imprimer en saccade"

msgctxt "print_sequence label"
msgid "Print Sequence"
msgstr "Séquence d'impression"

msgctxt "speed_print label"
msgid "Print Speed"
msgstr "Vitesse d’impression"

msgctxt "fill_outline_gaps label"
msgid "Print Thin Walls"
msgstr "Imprimer parois fines"

msgctxt "prime_tower_enable description"
msgid "Print a tower next to the print which serves to prime the material after each nozzle switch."
msgstr "Imprimer une tour à côté de l'impression qui sert à amorcer le matériau après chaque changement de buse."

msgctxt "infill_support_enabled description"
msgid "Print infill structures only where tops of the model should be supported. Enabling this reduces print time and material usage, but leads to ununiform object strength."
msgstr "Imprimer les structures de remplissage uniquement là où le haut du modèle doit être supporté, ce qui permet de réduire le temps d'impression et l'utilisation de matériau, mais conduit à une résistance uniforme de l'objet."

msgctxt "ironing_monotonic description"
msgid "Print ironing lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Imprimez les lignes d'étirage dans un ordre tel qu'elles se chevauchent toujours avec les lignes adjacentes dans une seule direction. Cela prend un peu plus de temps à imprimer, mais les surfaces planes ont l'air plus cohérentes."

msgctxt "mold_enabled description"
msgid "Print models as a mold, which can be cast in order to get a model which resembles the models on the build plate."
msgstr "Imprimer les modèles comme moule, qui peut être coulé afin d'obtenir un modèle ressemblant à ceux présents sur le plateau."

msgctxt "fill_outline_gaps description"
msgid "Print pieces of the model which are horizontally thinner than the nozzle size."
msgstr "Imprimer les parties du modèle qui sont horizontalement plus fines que la taille de la buse."

msgctxt "bridge_skin_speed_2 description"
msgid "Print speed to use when printing the second bridge skin layer."
msgstr "Vitesse d'impression à utiliser lors de l'impression de la deuxième couche extérieure du pont."

msgctxt "bridge_skin_speed_3 description"
msgid "Print speed to use when printing the third bridge skin layer."
msgstr "Vitesse d'impression à utiliser lors de l'impression de la troisième couche extérieure du pont."

msgctxt "infill_before_walls description"
msgid "Print the infill before printing the walls. Printing the walls first may lead to more accurate walls, but overhangs print worse. Printing the infill first leads to sturdier walls, but the infill pattern might sometimes show through the surface."
msgstr "Imprime le remplissage avant d'imprimer les parois. Imprimer les parois d'abord permet d'obtenir des parois plus précises, mais les porte-à-faux s'impriment plus mal. Imprimer le remplissage d'abord entraîne des parois plus résistantes, mais le motif de remplissage se verra parfois à travers la surface."

msgctxt "roofing_monotonic description"
msgid "Print top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Imprimez les lignes de la surface supérieure dans un ordre tel qu'elles se chevauchent toujours avec les lignes adjacentes dans une seule direction. Cela prend un peu plus de temps à imprimer, mais les surfaces planes ont l'air plus cohérentes."

msgctxt "skin_monotonic description"
msgid "Print top/bottom lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Imprimez les lignes supérieures et inférieures dans un ordre tel qu'elles se chevauchent toujours avec les lignes adjacentes dans une seule direction. Cela prend un peu plus de temps à imprimer, mais les surfaces planes ont l'air plus cohérentes."

msgctxt "material_print_temperature label"
msgid "Printing Temperature"
msgstr "Température d’impression"

msgctxt "material_print_temperature_layer_0 label"
msgid "Printing Temperature Initial Layer"
msgstr "Température d’impression couche initiale"

msgctxt "skirt_height description"
msgid "Printing the innermost skirt line with multiple layers makes it easy to remove the skirt."
msgstr "La jupe est plus facile à retirer lorsque sa ligne la plus intérieure est imprimée en plusieurs couches."

msgctxt "alternate_extra_perimeter description"
msgid "Prints an extra wall at every other layer. This way infill gets caught between these extra walls, resulting in stronger prints."
msgstr "Imprime une paroi supplémentaire une couche sur deux. Ainsi, le remplissage est pris entre ces parois supplémentaires pour créer des impressions plus solides."

msgctxt "resolution label"
msgid "Quality"
msgstr "Qualité"

msgctxt "infill_pattern option quarter_cubic"
msgid "Quarter Cubic"
msgstr "Quart cubique"

msgctxt "adhesion_type option raft"
msgid "Raft"
msgstr "Radeau"

msgctxt "raft_airgap label"
msgid "Raft Air Gap"
msgstr "Lame d'air du radeau"

msgctxt "raft_base_extruder_nr label"
msgid "Raft Base Extruder"
msgstr "Extrudeur de la base du raft"

msgctxt "raft_base_fan_speed label"
msgid "Raft Base Fan Speed"
msgstr "Vitesse du ventilateur pour la base du radeau"

msgctxt "raft_base_line_spacing label"
msgid "Raft Base Line Spacing"
msgstr "Espacement des lignes de base du radeau"

msgctxt "raft_base_line_width label"
msgid "Raft Base Line Width"
msgstr "Largeur de la ligne de base du radeau"

msgctxt "raft_base_acceleration label"
msgid "Raft Base Print Acceleration"
msgstr "Accélération de l'impression de la base du radeau"

msgctxt "raft_base_jerk label"
msgid "Raft Base Print Jerk"
msgstr "Saccade d’impression de la base du radeau"

msgctxt "raft_base_speed label"
msgid "Raft Base Print Speed"
msgstr "Vitesse d’impression de la base du radeau"

msgctxt "raft_base_thickness label"
msgid "Raft Base Thickness"
msgstr "Épaisseur de la base du radeau"

msgctxt "raft_base_wall_count label"
msgid "Raft Base Wall Count"
msgstr "Nombre de parois à la base du radeau"

msgctxt "raft_margin label"
msgid "Raft Extra Margin"
msgstr "Marge supplémentaire du radeau"

msgctxt "raft_fan_speed label"
msgid "Raft Fan Speed"
msgstr "Vitesse du ventilateur pendant le radeau"

msgctxt "raft_interface_extruder_nr label"
msgid "Raft Middle Extruder"
msgstr "Extrudeur du milieu du radeau"

msgctxt "raft_interface_fan_speed label"
msgid "Raft Middle Fan Speed"
msgstr "Vitesse du ventilateur pour le milieu du radeau"

msgctxt "raft_interface_layers label"
msgid "Raft Middle Layers"
msgstr "Couches du milieu du radeau"

msgctxt "raft_interface_line_width label"
msgid "Raft Middle Line Width"
msgstr "Largeur de la ligne intermédiaire du radeau"

msgctxt "raft_interface_acceleration label"
msgid "Raft Middle Print Acceleration"
msgstr "Accélération de l'impression du milieu du radeau"

msgctxt "raft_interface_jerk label"
msgid "Raft Middle Print Jerk"
msgstr "Saccade d’impression du milieu du radeau"

msgctxt "raft_interface_speed label"
msgid "Raft Middle Print Speed"
msgstr "Vitesse d’impression du milieu du radeau"

msgctxt "raft_interface_line_spacing label"
msgid "Raft Middle Spacing"
msgstr "Interligne intermédiaire du radeau"

msgctxt "raft_interface_thickness label"
msgid "Raft Middle Thickness"
msgstr "Épaisseur intermédiaire du radeau"

msgctxt "raft_acceleration label"
msgid "Raft Print Acceleration"
msgstr "Accélération de l'impression du radeau"

msgctxt "raft_jerk label"
msgid "Raft Print Jerk"
msgstr "Saccade d’impression du radeau"

msgctxt "raft_speed label"
msgid "Raft Print Speed"
msgstr "Vitesse d’impression du radeau"

msgctxt "raft_smoothing label"
msgid "Raft Smoothing"
msgstr "Lissage de radeau"

msgctxt "raft_surface_extruder_nr label"
msgid "Raft Top Extruder"
msgstr "Extrudeur du haut du radeau"

msgctxt "raft_surface_fan_speed label"
msgid "Raft Top Fan Speed"
msgstr "Vitesse du ventilateur pour le dessus du radeau"

msgctxt "raft_surface_thickness label"
msgid "Raft Top Layer Thickness"
msgstr "Épaisseur de la couche supérieure du radeau"

msgctxt "raft_surface_layers label"
msgid "Raft Top Layers"
msgstr "Couches supérieures du radeau"

msgctxt "raft_surface_line_width label"
msgid "Raft Top Line Width"
msgstr "Largeur de la ligne supérieure du radeau"

msgctxt "raft_surface_acceleration label"
msgid "Raft Top Print Acceleration"
msgstr "Accélération de l'impression du dessus du radeau"

msgctxt "raft_surface_jerk label"
msgid "Raft Top Print Jerk"
msgstr "Saccade d’impression du dessus du radeau"

msgctxt "raft_surface_speed label"
msgid "Raft Top Print Speed"
msgstr "Vitesse d’impression du dessus du radeau"

msgctxt "raft_surface_line_spacing label"
msgid "Raft Top Spacing"
msgstr "Interligne supérieur du radeau"

msgctxt "z_seam_type option random"
msgid "Random"
msgstr "Aléatoire"

msgctxt "infill_randomize_start_location label"
msgid "Randomize Infill Start"
msgstr "Randomiser le démarrage du remplissage"

msgctxt "infill_randomize_start_location description"
msgid "Randomize which infill line is printed first. This prevents one segment becoming the strongest, but it does so at the cost of an additional travel move."
msgstr "Randomisez la ligne de remplissage qui est imprimée en premier. Cela empêche un segment de devenir plus fort, mais cela se fait au prix d'un déplacement supplémentaire."

msgctxt "magic_fuzzy_skin_enabled description"
msgid "Randomly jitter while printing the outer wall, so that the surface has a rough and fuzzy look."
msgstr "Produit une agitation aléatoire lors de l'impression de la paroi extérieure, ce qui lui donne une apparence rugueuse et floue."

msgctxt "machine_shape option rectangular"
msgid "Rectangular"
msgstr "Rectangulaire"

msgctxt "cool_fan_speed_min label"
msgid "Regular Fan Speed"
msgstr "Vitesse régulière du ventilateur"

msgctxt "cool_fan_full_at_height label"
msgid "Regular Fan Speed at Height"
msgstr "Vitesse régulière du ventilateur à la hauteur"

msgctxt "cool_fan_full_layer label"
msgid "Regular Fan Speed at Layer"
msgstr "Vitesse régulière du ventilateur à la couche"

msgctxt "cool_min_layer_time_fan_speed_max label"
msgid "Regular/Maximum Fan Speed Threshold"
msgstr "Limite de vitesse régulière/maximale du ventilateur"

msgctxt "relative_extrusion label"
msgid "Relative Extrusion"
msgstr "Extrusion relative"

msgctxt "meshfix_union_all_remove_holes label"
msgid "Remove All Holes"
msgstr "Supprimer tous les trous"

msgctxt "remove_empty_first_layers label"
msgid "Remove Empty First Layers"
msgstr "Supprimer les premières couches vides"

msgctxt "carve_multiple_volumes label"
msgid "Remove Mesh Intersection"
msgstr "Supprimer l'intersection des mailles"

msgctxt "raft_remove_inside_corners label"
msgid "Remove Raft Inside Corners"
msgstr "Supprimer les coins intérieurs du radeau"

msgctxt "carve_multiple_volumes description"
msgid "Remove areas where multiple meshes are overlapping with each other. This may be used if merged dual material objects overlap with each other."
msgstr "Supprime les zones sur lesquelles plusieurs mailles se chevauchent. Cette option peut être utilisée si des objets à matériau double fusionné se chevauchent."

msgctxt "remove_empty_first_layers description"
msgid "Remove empty layers beneath the first printed layer if they are present. Disabling this setting can cause empty first layers if the Slicing Tolerance setting is set to Exclusive or Middle."
msgstr "Supprimer les couches vides sous la première couche imprimée si elles sont présentes. Le fait de désactiver ce paramètre peut entraîner l'apparition de premières couches vides si le paramètre Tolérance à la découpe est défini sur Exclusif ou Milieu."

msgctxt "raft_remove_inside_corners description"
msgid "Remove inside corners from the raft, causing the raft to become convex."
msgstr "Supprimez les coins intérieurs du radeau afin de le rendre convexe."

msgctxt "meshfix_union_all_remove_holes description"
msgid "Remove the holes in each layer and keep only the outside shape. This will ignore any invisible internal geometry. However, it also ignores layer holes which can be viewed from above or below."
msgstr "Supprime les trous dans chacune des couches et conserve uniquement la forme extérieure. Tous les détails internes invisibles seront ignorés. Il en va de même pour les trous qui pourraient être visibles depuis le dessus ou le dessous de la pièce."

msgctxt "machine_gcode_flavor option RepRap (RepRap)"
msgid "RepRap"
msgstr "RepRap"

msgctxt "machine_gcode_flavor option Repetier"
msgid "Repetier"
msgstr "Repetier"

msgctxt "skin_outline_count description"
msgid "Replaces the outermost part of the top/bottom pattern with a number of concentric lines. Using one or two lines improves roofs that start on infill material."
msgstr "Remplace la partie la plus externe du motif du dessus/dessous par un certain nombre de lignes concentriques. Le fait d'utiliser une ou deux lignes améliore les plafonds qui commencent sur du matériau de remplissage."

msgctxt "support_tree_rest_preference label"
msgid "Rest Preference"
msgstr "Préférence d'emplacement"

msgctxt "travel_retract_before_outer_wall label"
msgid "Retract Before Outer Wall"
msgstr "Rétracter avant la paroi externe"

msgctxt "retract_at_layer_change label"
msgid "Retract at Layer Change"
msgstr "Rétracter au changement de couche"

msgctxt "retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "Rétracte le filament quand la buse se déplace vers une zone non imprimée."

msgctxt "wipe_retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "Rétracte le filament quand la buse se déplace vers une zone non imprimée."

msgctxt "retract_at_layer_change description"
msgid "Retract the filament when the nozzle is moving to the next layer."
msgstr "Rétracter le filament quand le bec se déplace vers la prochaine couche."

msgctxt "retraction_amount label"
msgid "Retraction Distance"
msgstr "Distance de rétraction"

msgctxt "retraction_extra_prime_amount label"
msgid "Retraction Extra Prime Amount"
msgstr "Volume supplémentaire à l'amorçage"

msgctxt "retraction_min_travel label"
msgid "Retraction Minimum Travel"
msgstr "Déplacement minimal de rétraction"

msgctxt "retraction_prime_speed label"
msgid "Retraction Prime Speed"
msgstr "Vitesse de rétraction d'amorçage"

msgctxt "retraction_retract_speed label"
msgid "Retraction Retract Speed"
msgstr "Vitesse de rétraction"

msgctxt "retraction_speed label"
msgid "Retraction Speed"
msgstr "Vitesse de rétraction"

msgctxt "z_seam_position option right"
msgid "Right"
msgstr "Droite"

msgctxt "machine_scale_fan_speed_zero_to_one label"
msgid "Scale Fan Speed To 0-1"
msgstr "Mise à l'échelle de la vitesse du ventilateur à 0-1"

msgctxt "machine_scale_fan_speed_zero_to_one description"
msgid "Scale the fan speed to be between 0 and 1 instead of between 0 and 256."
msgstr "Mettez à l'échelle la vitesse du ventilateur de 0 à 1 au lieu de 0 à 256."

msgctxt "material_shrinkage_percentage label"
msgid "Scaling Factor Shrinkage Compensation"
msgstr "Mise à l'échelle du facteur de compensation de contraction"

msgctxt "support_meshes_present label"
msgid "Scene Has Support Meshes"
msgstr "La scène comporte un maillage de support"

msgctxt "z_seam_corner label"
msgid "Seam Corner Preference"
msgstr "Préférence de jointure d'angle"

msgctxt "draft_shield_height_limitation description"
msgid "Set the height of the draft shield. Choose to print the draft shield at the full height of the model or at a limited height."
msgstr "Définit la hauteur du bouclier. Choisissez d'imprimer le bouclier à la pleine hauteur du modèle ou à une hauteur limitée."

msgctxt "dual description"
msgid "Settings used for printing with multiple extruders."
msgstr "Paramètres utilisés pour imprimer avec plusieurs extrudeuses."

msgctxt "command_line_settings description"
msgid "Settings which are only used if CuraEngine isn't called from the Cura frontend."
msgstr "Paramètres qui sont utilisés uniquement si CuraEngine n'est pas invoqué depuis l'interface Cura."

msgctxt "machine_extruders_shared_nozzle_initial_retraction label"
msgid "Shared Nozzle Initial Retraction"
msgstr "Rétraction initiale de la buse partagée"

msgctxt "z_seam_type option sharpest_corner"
msgid "Sharpest Corner"
msgstr "Angle le plus aigu"

msgctxt "shell description"
msgid "Shell"
msgstr "Coque"

msgctxt "z_seam_type option shortest"
msgid "Shortest"
msgstr "Plus court"

msgctxt "machine_show_variants label"
msgid "Show Machine Variants"
msgstr "Afficher les variantes de la machine"

msgctxt "skin_edge_support_layers label"
msgid "Skin Edge Support Layers"
msgstr "Couches de soutien des bords de la couche extérieure"

msgctxt "skin_edge_support_thickness label"
msgid "Skin Edge Support Thickness"
msgstr "Épaisseur de soutien des bords de la couche"

msgctxt "expand_skins_expand_distance label"
msgid "Skin Expand Distance"
msgstr "Distance d'expansion de la couche extérieure"

msgctxt "skin_overlap_mm label"
msgid "Skin Overlap"
msgstr "Chevauchement de la couche extérieure"

msgctxt "skin_overlap label"
msgid "Skin Overlap Percentage"
msgstr "Pourcentage de chevauchement de la couche extérieure"

msgctxt "skin_preshrink label"
msgid "Skin Removal Width"
msgstr "Largeur de retrait de la couche extérieure"

msgctxt "min_skin_width_for_expansion description"
msgid "Skin areas narrower than this are not expanded. This avoids expanding the narrow skin areas that are created when the model surface has a slope close to the vertical."
msgstr "Les zones de couche extérieure plus étroites que cette valeur ne seront pas étendues. Cela permet d'éviter d'étendre les zones de couche extérieure étroites qui sont créées lorsque la surface du modèle possède une pente proche de la verticale."

msgctxt "support_zag_skip_count description"
msgid "Skip one in every N connection lines to make the support structure easier to break away."
msgstr "Ignorer une ligne de connexion sur N pour rendre la structure de support plus facile à casser."

msgctxt "support_skip_some_zags description"
msgid "Skip some support line connections to make the support structure easier to break away. This setting is applicable to the Zig Zag support infill pattern."
msgstr "Ignorer certaines connexions de ligne du support pour rendre la structure de support plus facile à casser. Ce paramètre s'applique au motif de remplissage du support en zigzag."

msgctxt "adhesion_type option skirt"
msgid "Skirt"
msgstr "Jupe"

msgctxt "skirt_gap label"
msgid "Skirt Distance"
msgstr "Distance de la jupe"

msgctxt "skirt_height label"
msgid "Skirt Height"
msgstr "Hauteur de la jupe"

msgctxt "skirt_line_count label"
msgid "Skirt Line Count"
msgstr "Nombre de lignes de la jupe"

msgctxt "acceleration_skirt_brim label"
msgid "Skirt/Brim Acceleration"
msgstr "Accélération de la jupe/bordure"

msgctxt "skirt_brim_extruder_nr label"
msgid "Skirt/Brim Extruder"
msgstr "Extrudeur de la jupe/bordure"

msgctxt "skirt_brim_material_flow label"
msgid "Skirt/Brim Flow"
msgstr "Débit de la jupe/bordure"

msgctxt "jerk_skirt_brim label"
msgid "Skirt/Brim Jerk"
msgstr "Saccade de la jupe/bordure"

msgctxt "skirt_brim_line_width label"
msgid "Skirt/Brim Line Width"
msgstr "Largeur des lignes de jupe/bordure"

msgctxt "skirt_brim_minimal_length label"
msgid "Skirt/Brim Minimum Length"
msgstr "Longueur minimale de la jupe/bordure"

msgctxt "skirt_brim_speed label"
msgid "Skirt/Brim Speed"
msgstr "Vitesse d'impression de la jupe/bordure"

msgctxt "slicing_tolerance label"
msgid "Slicing Tolerance"
msgstr "Tolérance à la découpe"

msgctxt "small_feature_speed_factor_0 label"
msgid "Small Feature Initial Layer Speed"
msgstr "Vitesse de la couche initiale de petite structure"

msgctxt "small_feature_max_length label"
msgid "Small Feature Max Length"
msgstr "Longueur max de petite structure"

msgctxt "small_feature_speed_factor label"
msgid "Small Feature Speed"
msgstr "Vitesse de petite structure"

msgctxt "small_hole_max_size label"
msgid "Small Hole Max Size"
msgstr "Taille maximale des petits trous"

msgctxt "cool_min_temperature label"
msgid "Small Layer Printing Temperature"
msgstr "Température d'impression en cas de petite couche"

msgctxt "small_skin_on_surface label"
msgid "Small Top/Bottom On Surface"
msgstr "Petit Haut/Bas sur la surface"

msgctxt "small_skin_width label"
msgid "Small Top/Bottom Width"
msgstr "Petite largeur du dessus/dessous"

msgctxt "small_feature_speed_factor_0 description"
msgid "Small features on the first layer will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "Les petites structures sur la première couche seront imprimées à ce pourcentage de la vitesse d'impression normale. Une impression plus lente peut aider à l'adhésion et à la précision."

msgctxt "small_feature_speed_factor description"
msgid "Small features will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "Les petites structures seront imprimées à ce pourcentage de la vitesse d'impression normale. Une impression plus lente peut aider à l'adhésion et à la précision."

msgctxt "small_skin_width description"
msgid "Small top/bottom regions are filled with walls instead of the default top/bottom pattern. This helps to avoids jerky motions. Off for the topmost (air-exposed) layer by default (see 'Small Top/Bottom On Surface')."
msgstr "Les petites zones haut/bas sont remplies avec des parois au lieu du motif haut/bas par défaut. Cela permet d'éviter les mouvements saccadés. Par défaut, l'option est désactivée pour la couche supérieure (exposée à l'air) (voir « Petit Haut/Bas sur la surface »)."

msgctxt "brim_smart_ordering label"
msgid "Smart Brim"
msgstr "Bordure intelligente"

msgctxt "z_seam_corner option z_seam_corner_weighted"
msgid "Smart Hiding"
msgstr "Masquage intelligent"

msgctxt "smooth_spiralized_contours label"
msgid "Smooth Spiralized Contours"
msgstr "Lisser les contours spiralisés"

msgctxt "smooth_spiralized_contours description"
msgid "Smooth the spiralized contours to reduce the visibility of the Z seam (the Z seam should be barely visible on the print but will still be visible in the layer view). Note that smoothing will tend to blur fine surface details."
msgstr "Lisser les contours spiralisés pour réduire la visibilité de la jointure en Z (la jointure en Z doit être à peine visible sur l'impression mais sera toujours visible dans la vue en couches). Veuillez remarquer que le lissage aura tendance à estomper les détails très fins de la surface."

msgctxt "retraction_extra_prime_amount description"
msgid "Some material can ooze away during a travel move, which can be compensated for here."
msgstr "Du matériau peut suinter pendant un déplacement, ce qui peut être compensé ici."

msgctxt "wipe_retraction_extra_prime_amount description"
msgid "Some material can ooze away during a wipe travel moves, which can be compensated for here."
msgstr "Du matériau peut suinter pendant un déplacement d'essuyage, ce qui peut être compensé ici."

msgctxt "blackmagic label"
msgid "Special Modes"
msgstr "Modes spéciaux"

msgctxt "speed description"
msgid "Speed"
msgstr "Vitesse"

msgctxt "speed label"
msgid "Speed"
msgstr "Vitesse"

msgctxt "wipe_hop_speed description"
msgid "Speed to move the z-axis during the hop."
msgstr "Vitesse de déplacement de l'axe Z pendant le décalage."

msgctxt "magic_spiralize label"
msgid "Spiralize Outer Contour"
msgstr "Spiraliser le contour extérieur"

msgctxt "magic_spiralize description"
msgid "Spiralize smooths out the Z move of the outer edge. This will create a steady Z increase over the whole print. This feature turns a solid model into a single walled print with a solid bottom. This feature should only be enabled when each layer only contains a single part."
msgstr "Cette fonction ajuste le déplacement en Z sur le bord extérieur. Cela va créer une augmentation stable de Z sur toute l’impression. Cette fonction transforme un modèle solide en une impression à paroi unique avec une base solide. Cette fonctionnalité doit être activée seulement lorsque chaque couche contient uniquement une seule partie."

msgctxt "material_standby_temperature label"
msgid "Standby Temperature"
msgstr "Température de veille"

msgctxt "machine_start_gcode label"
msgid "Start G-code"
msgstr "G-Code de démarrage"

msgctxt "z_seam_type description"
msgid "Starting point of each path in a layer. When paths in consecutive layers start at the same point a vertical seam may show on the print. When aligning these near a user specified location, the seam is easiest to remove. When placed randomly the inaccuracies at the paths' start will be less noticeable. When taking the shortest path the print will be quicker."
msgstr "Point de départ de chaque voie dans une couche. Quand les voies dans les couches consécutives démarrent au même endroit, une jointure verticale peut apparaître sur l'impression. En alignant les points de départ près d'un emplacement défini par l'utilisateur, la jointure sera plus facile à faire disparaître. Lorsqu'elles sont disposées de manière aléatoire, les imprécisions de départ des voies seront moins visibles. En choisissant la voie la plus courte, l'impression se fera plus rapidement."

msgctxt "machine_steps_per_mm_e label"
msgid "Steps per Millimeter (E)"
msgstr "Pas par millimètre (E)"

msgctxt "machine_steps_per_mm_x label"
msgid "Steps per Millimeter (X)"
msgstr "Pas par millimètre (X)"

msgctxt "machine_steps_per_mm_y label"
msgid "Steps per Millimeter (Y)"
msgstr "Pas par millimètre (Y)"

msgctxt "machine_steps_per_mm_z label"
msgid "Steps per Millimeter (Z)"
msgstr "Pas par millimètre (Z)"

msgctxt "support description"
msgid "Support"
msgstr "Supports"

msgctxt "support label"
msgid "Support"
msgstr "Supports"

msgctxt "acceleration_support label"
msgid "Support Acceleration"
msgstr "Accélération du support"

msgctxt "support_bottom_distance label"
msgid "Support Bottom Distance"
msgstr "Distance inférieure des supports"

msgctxt "support_bottom_wall_count label"
msgid "Support Bottom Wall Line Count"
msgstr "Nombre de lignes de parois inférieures du support"

msgctxt "support_brim_line_count label"
msgid "Support Brim Line Count"
msgstr "Nombre de lignes de la bordure du support"

msgctxt "support_brim_width label"
msgid "Support Brim Width"
msgstr "Largeur de la bordure du support"

msgctxt "support_zag_skip_count label"
msgid "Support Chunk Line Count"
msgstr "Comptage des lignes de morceaux du support"

msgctxt "support_skip_zag_per_mm label"
msgid "Support Chunk Size"
msgstr "Taille de morceaux du support"

msgctxt "support_infill_rate label"
msgid "Support Density"
msgstr "Densité du support"

msgctxt "support_xy_overrides_z label"
msgid "Support Distance Priority"
msgstr "Priorité de distance des supports"

msgctxt "support_extruder_nr label"
msgid "Support Extruder"
msgstr "Extrudeuse de support"

msgctxt "acceleration_support_bottom label"
msgid "Support Floor Acceleration"
msgstr "Accélération des bas de support"

msgctxt "support_bottom_density label"
msgid "Support Floor Density"
msgstr "Densité du bas de support"

msgctxt "support_bottom_extruder_nr label"
msgid "Support Floor Extruder"
msgstr "Extrudeuse des bas de support"

msgctxt "support_bottom_material_flow label"
msgid "Support Floor Flow"
msgstr "Débit du bas de support"

msgctxt "support_bottom_offset label"
msgid "Support Floor Horizontal Expansion"
msgstr "Expansion horizontale du bas de support"

msgctxt "jerk_support_bottom label"
msgid "Support Floor Jerk"
msgstr "Saccade des bas de support"

msgctxt "support_bottom_angles label"
msgid "Support Floor Line Directions"
msgstr "Direction de la ligne de bas de support"

msgctxt "support_bottom_line_distance label"
msgid "Support Floor Line Distance"
msgstr "Distance d'écartement de ligne de bas de support"

msgctxt "support_bottom_line_width label"
msgid "Support Floor Line Width"
msgstr "Largeur de ligne de bas de support"

msgctxt "support_bottom_pattern label"
msgid "Support Floor Pattern"
msgstr "Motif du bas de support"

msgctxt "speed_support_bottom label"
msgid "Support Floor Speed"
msgstr "Vitesse d'impression des bas de support"

msgctxt "support_bottom_height label"
msgid "Support Floor Thickness"
msgstr "Épaisseur du bas de support"

msgctxt "support_material_flow label"
msgid "Support Flow"
msgstr "Débit du support"

msgctxt "support_offset label"
msgid "Support Horizontal Expansion"
msgstr "Expansion horizontale des supports"

msgctxt "acceleration_support_infill label"
msgid "Support Infill Acceleration"
msgstr "Accélération de remplissage du support"

msgctxt "support_infill_extruder_nr label"
msgid "Support Infill Extruder"
msgstr "Extrudeuse de remplissage du support"

msgctxt "jerk_support_infill label"
msgid "Support Infill Jerk"
msgstr "Saccade de remplissage du support"

msgctxt "support_infill_sparse_thickness label"
msgid "Support Infill Layer Thickness"
msgstr "Épaisseur de la couche de remplissage de support"

msgctxt "support_infill_angles label"
msgid "Support Infill Line Directions"
msgstr "Direction de ligne de remplissage du support"

msgctxt "speed_support_infill label"
msgid "Support Infill Speed"
msgstr "Vitesse d'impression du remplissage de support"

msgctxt "acceleration_support_interface label"
msgid "Support Interface Acceleration"
msgstr "Accélération de l'interface du support"

msgctxt "support_interface_density label"
msgid "Support Interface Density"
msgstr "Densité de l'interface de support"

msgctxt "support_interface_extruder_nr label"
msgid "Support Interface Extruder"
msgstr "Extrudeuse de l'interface du support"

msgctxt "support_interface_material_flow label"
msgid "Support Interface Flow"
msgstr "Débit de l'interface de support"

msgctxt "support_interface_offset label"
msgid "Support Interface Horizontal Expansion"
msgstr "Expansion horizontale de l'interface de support"

msgctxt "jerk_support_interface label"
msgid "Support Interface Jerk"
msgstr "Saccade de l'interface de support"

msgctxt "support_interface_angles label"
msgid "Support Interface Line Directions"
msgstr "Direction de ligne d'interface du support"

msgctxt "support_interface_line_width label"
msgid "Support Interface Line Width"
msgstr "Largeur de ligne d'interface de support"

msgctxt "support_interface_pattern label"
msgid "Support Interface Pattern"
msgstr "Motif de l'interface de support"

msgctxt "support_interface_priority label"
msgid "Support Interface Priority"
msgstr "Priorité de l'interface de support"

msgctxt "support_interface_skip_height label"
msgid "Support Interface Resolution"
msgstr "Résolution de l'interface du support"

msgctxt "speed_support_interface label"
msgid "Support Interface Speed"
msgstr "Vitesse d'impression de l'interface de support"

msgctxt "support_interface_height label"
msgid "Support Interface Thickness"
msgstr "Épaisseur de l'interface de support"

msgctxt "support_interface_wall_count label"
msgid "Support Interface Wall Line Count"
msgstr "Nombre de lignes de parois de l'interface du support"

msgctxt "jerk_support label"
msgid "Support Jerk"
msgstr "Saccade des supports"

msgctxt "support_join_distance label"
msgid "Support Join Distance"
msgstr "Distance de jointement des supports"

msgctxt "support_line_distance label"
msgid "Support Line Distance"
msgstr "Distance d'écartement de ligne du support"

msgctxt "support_line_width label"
msgid "Support Line Width"
msgstr "Largeur de ligne de support"

msgctxt "support_mesh label"
msgid "Support Mesh"
msgstr "Maillage de support"

msgctxt "support_angle label"
msgid "Support Overhang Angle"
msgstr "Angle de porte-à-faux de support"

msgctxt "support_pattern label"
msgid "Support Pattern"
msgstr "Motif du support"

msgctxt "support_type label"
msgid "Support Placement"
msgstr "Positionnement des supports"

msgctxt "acceleration_support_roof label"
msgid "Support Roof Acceleration"
msgstr "Accélération des plafonds de support"

msgctxt "support_roof_density label"
msgid "Support Roof Density"
msgstr "Densité du plafond de support"

msgctxt "support_roof_extruder_nr label"
msgid "Support Roof Extruder"
msgstr "Extrudeuse des plafonds de support"

msgctxt "support_roof_material_flow label"
msgid "Support Roof Flow"
msgstr "Débit du plafond de support"

msgctxt "support_roof_offset label"
msgid "Support Roof Horizontal Expansion"
msgstr "Expansion horizontale du plafond de support"

msgctxt "jerk_support_roof label"
msgid "Support Roof Jerk"
msgstr "Saccade des plafonds de support"

msgctxt "support_roof_angles label"
msgid "Support Roof Line Directions"
msgstr "Direction de la ligne de plafond de support"

msgctxt "support_roof_line_distance label"
msgid "Support Roof Line Distance"
msgstr "Distance d'écartement de ligne du plafond de support"

msgctxt "support_roof_line_width label"
msgid "Support Roof Line Width"
msgstr "Largeur de ligne de plafond de support"

msgctxt "support_roof_pattern label"
msgid "Support Roof Pattern"
msgstr "Motif du plafond de support"

msgctxt "speed_support_roof label"
msgid "Support Roof Speed"
msgstr "Vitesse d'impression des plafonds de support"

msgctxt "support_roof_height label"
msgid "Support Roof Thickness"
msgstr "Épaisseur du plafond de support"

msgctxt "support_roof_wall_count label"
msgid "Support Roof Wall Line Count"
msgstr "Nombre de lignes de parois du toit du support"

msgctxt "speed_support label"
msgid "Support Speed"
msgstr "Vitesse d'impression des supports"

msgctxt "support_bottom_stair_step_height label"
msgid "Support Stair Step Height"
msgstr "Hauteur de la marche de support"

msgctxt "support_bottom_stair_step_width label"
msgid "Support Stair Step Maximum Width"
msgstr "Largeur maximale de la marche de support"

msgctxt "support_bottom_stair_step_min_slope label"
msgid "Support Stair Step Minimum Slope Angle"
msgstr "Angle de pente minimum de la marche de support"

msgctxt "support_structure label"
msgid "Support Structure"
msgstr "Structure du support"

msgctxt "support_top_distance label"
msgid "Support Top Distance"
msgstr "Distance supérieure des supports"

msgctxt "support_wall_count label"
msgid "Support Wall Line Count"
msgstr "Nombre de lignes de la paroi du support"

msgctxt "support_xy_distance label"
msgid "Support X/Y Distance"
msgstr "Distance X/Y des supports"

msgctxt "support_z_distance label"
msgid "Support Z Distance"
msgstr "Distance Z des supports"

msgctxt "support_interface_priority option support_lines_overwrite_interface_area"
msgid "Support lines preferred"
msgstr "Priorité aux lignes de support"

msgctxt "support_interface_priority option support_area_overwrite_interface_area"
msgid "Support preferred"
msgstr "Priorité au support"

msgctxt "support_supported_skin_fan_speed label"
msgid "Supported Skin Fan Speed"
msgstr "Vitesse du ventilateur de couche extérieure supportée"

msgctxt "magic_mesh_surface_mode option surface"
msgid "Surface"
msgstr "Surface"

msgctxt "material_surface_energy label"
msgid "Surface Energy"
msgstr "Énergie de la surface"

msgctxt "magic_mesh_surface_mode label"
msgid "Surface Mode"
msgstr "Mode de surface"

msgctxt "material_adhesion_tendency description"
msgid "Surface adhesion tendency."
msgstr "Tendance à l'adhérence de la surface."

msgctxt "material_surface_energy description"
msgid "Surface energy."
msgstr "Énergie de la surface."

msgctxt "brim_smart_ordering description"
msgid "Swap print order of the innermost and second innermost brim lines. This improves brim removal."
msgstr "Ce paramètre inverse l'ordre d'impression de la ligne de bordure la plus intérieure et de la deuxième ligne de bordure la plus intérieure. La bordure est ainsi plus facile à retirer."

msgctxt "alternate_carve_order description"
msgid "Switch to which mesh intersecting volumes will belong with every layer, so that the overlapping meshes become interwoven. Turning this setting off will cause one of the meshes to obtain all of the volume in the overlap, while it is removed from the other meshes."
msgstr "Passe aux volumes d'intersection de maille qui appartiennent à chaque couche, de manière à ce que les mailles qui se chevauchent soient entrelacées. Si vous désactivez ce paramètre, l'une des mailles obtiendra tout le volume dans le chevauchement tandis qu'il est retiré des autres mailles."

msgctxt "adaptive_layer_height_threshold description"
msgid "Target horizontal distance between two adjacent layers. Reducing this setting causes thinner layers to be used to bring the edges of the layers closer together."
msgstr "Distance horizontale cible entre deux couches adjacentes. La réduction de ce paramètre entraîne l'utilisation de couches plus fines pour rapprocher les bords des couches."

msgctxt "layer_start_x description"
msgid "The X coordinate of the position near where to find the part to start printing each layer."
msgstr "Coordonnée X de la position près de laquelle trouver la partie pour démarrer l'impression de chaque couche."

msgctxt "z_seam_x description"
msgid "The X coordinate of the position near where to start printing each part in a layer."
msgstr "Coordonnée X de la position près de laquelle démarrer l'impression de chaque partie dans une couche."

msgctxt "extruder_prime_pos_x description"
msgid "The X coordinate of the position where the nozzle primes at the start of printing."
msgstr "Les coordonnées X de la position à laquelle la buse s'amorce au début de l'impression."

msgctxt "layer_start_y description"
msgid "The Y coordinate of the position near where to find the part to start printing each layer."
msgstr "Coordonnée Y de la position près de laquelle trouver la partie pour démarrer l'impression de chaque couche."

msgctxt "z_seam_y description"
msgid "The Y coordinate of the position near where to start printing each part in a layer."
msgstr "Coordonnée Y de la position près de laquelle démarrer l'impression de chaque partie dans une couche."

msgctxt "extruder_prime_pos_y description"
msgid "The Y coordinate of the position where the nozzle primes at the start of printing."
msgstr "Les coordonnées Y de la position à laquelle la buse s'amorce au début de l'impression."

msgctxt "extruder_prime_pos_z description"
msgid "The Z coordinate of the position where the nozzle primes at the start of printing."
msgstr "Les coordonnées Z de la position à laquelle la buse s'amorce au début de l'impression."

msgctxt "acceleration_print_layer_0 description"
msgid "The acceleration during the printing of the initial layer."
msgstr "L'accélération durant l'impression de la couche initiale."

msgctxt "acceleration_layer_0 description"
msgid "The acceleration for the initial layer."
msgstr "L'accélération pour la couche initiale."

msgctxt "acceleration_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "L'accélération pour les déplacements dans la couche initiale."

msgctxt "jerk_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "L'accélération pour les déplacements dans la couche initiale."

msgctxt "acceleration_wall_x description"
msgid "The acceleration with which all inner walls are printed."
msgstr "L'accélération selon laquelle toutes les parois intérieures sont imprimées."

msgctxt "acceleration_infill description"
msgid "The acceleration with which infill is printed."
msgstr "L'accélération selon laquelle le remplissage est imprimé."

msgctxt "acceleration_ironing description"
msgid "The acceleration with which ironing is performed."
msgstr "L'accélération selon laquelle l'étirage est effectué."

msgctxt "acceleration_print description"
msgid "The acceleration with which printing happens."
msgstr "L'accélération selon laquelle l'impression s'effectue."

msgctxt "raft_base_acceleration description"
msgid "The acceleration with which the base raft layer is printed."
msgstr "L'accélération selon laquelle la couche de base du radeau est imprimée."

msgctxt "acceleration_support_bottom description"
msgid "The acceleration with which the floors of support are printed. Printing them at lower acceleration can improve adhesion of support on top of your model."
msgstr "L'accélération selon laquelle les bas de support sont imprimés. Les imprimer avec une accélération plus faible renforce l'adhésion du support au-dessus du modèle."

msgctxt "acceleration_support_infill description"
msgid "The acceleration with which the infill of support is printed."
msgstr "L'accélération selon laquelle le remplissage de support est imprimé."

msgctxt "raft_interface_acceleration description"
msgid "The acceleration with which the middle raft layer is printed."
msgstr "L'accélération selon laquelle la couche du milieu du radeau est imprimée."

msgctxt "acceleration_wall_0 description"
msgid "The acceleration with which the outermost walls are printed."
msgstr "L'accélération selon laquelle les parois externes sont imprimées."

msgctxt "acceleration_prime_tower description"
msgid "The acceleration with which the prime tower is printed."
msgstr "L'accélération selon laquelle la tour d'amorçage est imprimée."

msgctxt "raft_acceleration description"
msgid "The acceleration with which the raft is printed."
msgstr "L'accélération selon laquelle le radeau est imprimé."

msgctxt "acceleration_support_interface description"
msgid "The acceleration with which the roofs and floors of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "L'accélération selon laquelle les plafonds et bas de support sont imprimés. Les imprimer avec une accélération plus faible améliore la qualité des porte-à-faux."

msgctxt "acceleration_support_roof description"
msgid "The acceleration with which the roofs of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "L'accélération selon laquelle les plafonds de support sont imprimés. Les imprimer avec une accélération plus faible améliore la qualité des porte-à-faux."

msgctxt "acceleration_skirt_brim description"
msgid "The acceleration with which the skirt and brim are printed. Normally this is done with the initial layer acceleration, but sometimes you might want to print the skirt or brim at a different acceleration."
msgstr "L'accélération selon laquelle la jupe et la bordure sont imprimées. Normalement, cette accélération est celle de la couche initiale, mais il est parfois nécessaire d’imprimer la jupe ou la bordure à une accélération différente."

msgctxt "acceleration_support description"
msgid "The acceleration with which the support structure is printed."
msgstr "L'accélération selon laquelle la structure de support est imprimée."

msgctxt "raft_surface_acceleration description"
msgid "The acceleration with which the top raft layers are printed."
msgstr "L'accélération selon laquelle les couches du dessus du radeau sont imprimées."

msgctxt "acceleration_wall_x_roofing description"
msgid "The acceleration with which the top surface inner walls are printed."
msgstr "L'accélération avec laquelle les parois internes de la surface supérieure sont imprimées."

msgctxt "acceleration_wall_0_roofing description"
msgid "The acceleration with which the top surface outermost walls are printed."
msgstr "L'accélération avec laquelle la paroi externe de la surface supérieure est imprimée."

msgctxt "acceleration_wall description"
msgid "The acceleration with which the walls are printed."
msgstr "L'accélération selon laquelle les parois sont imprimées."

msgctxt "acceleration_roofing description"
msgid "The acceleration with which top surface skin layers are printed."
msgstr "La vitesse à laquelle les couches extérieures de surface supérieure sont imprimées."

msgctxt "acceleration_topbottom description"
msgid "The acceleration with which top/bottom layers are printed."
msgstr "L'accélération selon laquelle les couches du dessus/dessous sont imprimées."

msgctxt "acceleration_travel description"
msgid "The acceleration with which travel moves are made."
msgstr "L'accélération selon laquelle les déplacements s'effectuent."

msgctxt "ironing_flow description"
msgid "The amount of material, relative to a normal skin line, to extrude during ironing. Keeping the nozzle filled helps filling some of the crevices of the top surface, but too much results in overextrusion and blips on the side of the surface."
msgstr "La quantité de matériau, relative à une ligne de couche extérieure normale, à extruder pendant l'étirage. Le fait de garder la buse pleine aide à remplir certaines des crevasses de la surface supérieure ; mais si la quantité est trop importante, cela entraînera une surextrusion et l'apparition de coupures sur le côté de la surface."

msgctxt "infill_overlap description"
msgid "The amount of overlap between the infill and the walls as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Le degré de chevauchement entre le remplissage et les parois exprimé en pourcentage de la largeur de ligne de remplissage. Un chevauchement faible permet aux parois de se connecter fermement au remplissage."

msgctxt "infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Le degré de chevauchement entre le remplissage et les parois. Un léger chevauchement permet de lier fermement les parois au remplissage."

msgctxt "switch_extruder_retraction_amount description"
msgid "The amount of retraction when switching extruders. Set to 0 for no retraction at all. This should generally be the same as the length of the heat zone."
msgstr "Degré de rétraction lors de la commutation d'extrudeuses. Une valeur de 0 signifie qu'il n'y aura aucune rétraction. En général, cette valeur doit être équivalente à la longueur de la zone de chauffe."

msgctxt "machine_nozzle_expansion_angle description"
msgid "The angle between the horizontal plane and the conical part right above the tip of the nozzle."
msgstr "L'angle entre le plan horizontal et la partie conique juste au-dessus de la pointe de la buse."

msgctxt "support_tower_roof_angle description"
msgid "The angle of a rooftop of a tower. A higher value results in pointed tower roofs, a lower value results in flattened tower roofs."
msgstr "L'angle du toit d'une tour. Une valeur plus élevée entraîne des toits de tour pointus, tandis qu'une valeur plus basse résulte en des toits plats."

msgctxt "mold_angle description"
msgid "The angle of overhang of the outer walls created for the mold. 0° will make the outer shell of the mold vertical, while 90° will make the outside of the model follow the contour of the model."
msgstr "L'angle de porte-à-faux des parois externes créées pour le moule. La valeur 0° rendra la coque externe du moule verticale, alors que 90° fera que l'extérieur du modèle suive les contours du modèle."

msgctxt "support_tree_branch_diameter_angle description"
msgid "The angle of the branches' diameter as they gradually become thicker towards the bottom. An angle of 0 will cause the branches to have uniform thickness over their length. A bit of an angle can increase stability of the tree support."
msgstr "Angle du diamètre des branches au fur et à mesure qu'elles s'épaississent lorsqu'elles sont proches du fond. Avec un angle de 0°, les branches auront une épaisseur uniforme sur toute leur longueur. Donner un peu d'angle permet d'augmenter la stabilité du support arborescent."

msgctxt "support_conical_angle description"
msgid "The angle of the tilt of conical support. With 0 degrees being vertical, and 90 degrees being horizontal. Smaller angles cause the support to be more sturdy, but consist of more material. Negative angles cause the base of the support to be wider than the top."
msgstr "Angle d'inclinaison des supports coniques. Un angle de 0 degré est vertical tandis qu'un angle de 90 degrés est horizontal. Les petits angles rendent le support plus solide mais utilisent plus de matière. Les angles négatifs rendent la base du support plus large que le sommet."

msgctxt "magic_fuzzy_skin_point_density description"
msgid "The average density of points introduced on each polygon in a layer. Note that the original points of the polygon are discarded, so a low density results in a reduction of the resolution."
msgstr "Densité moyenne de points ajoutée à chaque polygone sur une couche. Notez que les points originaux du polygone ne seront plus pris en compte, une faible densité résultant alors en une diminution de la résolution."

msgctxt "magic_fuzzy_skin_point_dist description"
msgid "The average distance between the random points introduced on each line segment. Note that the original points of the polygon are discarded, so a high smoothness results in a reduction of the resolution. This value must be higher than half the Fuzzy Skin Thickness."
msgstr "Distance moyenne entre les points ajoutés aléatoirement sur chaque segment de ligne. Il faut noter que les points originaux du polygone ne sont plus pris en compte donc un fort lissage conduira à une diminution de la résolution. Cette valeur doit être supérieure à la moitié de l'épaisseur de la couche floue."

msgctxt "machine_acceleration description"
msgid "The default acceleration of print head movement."
msgstr "L'accélération par défaut du mouvement de la tête d'impression."

msgctxt "default_material_print_temperature description"
msgid "The default temperature used for printing. This should be the \"base\" temperature of a material. All other print temperatures should use offsets based on this value"
msgstr "La température par défaut utilisée pour l'impression. Il doit s'agir de la température de « base » d'un matériau. Toutes les autres températures d'impression doivent utiliser des décalages basés sur cette valeur"

msgctxt "default_material_bed_temperature description"
msgid "The default temperature used for the heated build plate. This should be the \"base\" temperature of a build plate. All other print temperatures should use offsets based on this value"
msgstr "Température par défaut utilisée pour le plateau chauffant. Il doit s'agir de la température de « base » d'un plateau. Toutes les autres températures d'impression sont définies en fonction de cette valeur"

msgctxt "bridge_skin_density description"
msgid "The density of the bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "Densité de la couche extérieure du pont. Des valeurs inférieures à 100 augmenteront les écarts entre les lignes de la couche extérieure."

msgctxt "support_bottom_density description"
msgid "The density of the floors of the support structure. A higher value results in better adhesion of the support on top of the model."
msgstr "La densité des bas de la structure de support. Une valeur plus élevée résulte en une meilleure adhésion du support au-dessus du modèle."

msgctxt "support_roof_density description"
msgid "The density of the roofs of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "La densité des plafonds de la structure de support. Une valeur plus élevée résulte en de meilleurs porte-à-faux, mais les supports sont plus difficiles à enlever."

msgctxt "bridge_skin_density_2 description"
msgid "The density of the second bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "Densité de la deuxième couche extérieure du pont. Des valeurs inférieures à 100 augmenteront les écarts entre les lignes de la couche extérieure."

msgctxt "bridge_skin_density_3 description"
msgid "The density of the third bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "Densité de la troisième couche extérieure du pont. Des valeurs inférieures à 100 augmenteront les écarts entre les lignes de la couche extérieure."

msgctxt "machine_depth description"
msgid "The depth (Y-direction) of the printable area."
msgstr "La profondeur (sens Y) de la zone imprimable."

msgctxt "support_tower_diameter description"
msgid "The diameter of a special tower."
msgstr "Le diamètre d’une tour spéciale."

msgctxt "support_tree_branch_diameter description"
msgid "The diameter of the thinnest branches of tree support. Thicker branches are more sturdy. Branches towards the base will be thicker than this."
msgstr "Diamètre des branches les plus minces du support arborescent. Plus les branches sont épaisses, plus elles sont robustes ; les branches proches de la base seront plus épaisses que cette valeur."

msgctxt "support_tree_tip_diameter description"
msgid "The diameter of the top of the tip of the branches of tree support."
msgstr "Il s'agit du diamètre des extrémités des branches du support arborescent."

msgctxt "machine_feeder_wheel_diameter description"
msgid "The diameter of the wheel that drives the material in the feeder."
msgstr "Diamètre de la roue qui entraîne le matériau dans le chargeur."

msgctxt "support_tree_max_diameter description"
msgid "The diameter of the widest branches of tree support. A thicker trunk is more sturdy; a thinner trunk takes up less space on the build plate."
msgstr "Le diamètre des branches les plus larges du support arborescent. Un tronc plus épais est plus robuste ; un tronc plus fin prend moins de place sur le plateau de fabrication."

msgctxt "adaptive_layer_height_variation_step description"
msgid "The difference in height of the next layer height compared to the previous one."
msgstr "Différence de hauteur de la couche suivante par rapport à la précédente."

msgctxt "ironing_line_spacing description"
msgid "The distance between the lines of ironing."
msgstr "La distance entre les lignes d'étirage."

msgctxt "travel_avoid_distance description"
msgid "The distance between the nozzle and already printed parts when avoiding during travel moves."
msgstr "La distance entre la buse et les pièces déjà imprimées lors du contournement pendant les déplacements."

msgctxt "raft_base_line_spacing description"
msgid "The distance between the raft lines for the base raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "La distance entre les lignes du radeau pour la couche de base de celui-ci. Un interligne large facilite le retrait du radeau du plateau."

msgctxt "raft_interface_line_spacing description"
msgid "The distance between the raft lines for the middle raft layer. The spacing of the middle should be quite wide, while being dense enough to support the top raft layers."
msgstr "La distance entre les lignes du radeau pour la couche intermédiaire de celui-ci. L'espace intermédiaire doit être assez large et suffisamment dense pour supporter les couches supérieures du radeau."

msgctxt "raft_surface_line_spacing description"
msgid "The distance between the raft lines for the top raft layers. The spacing should be equal to the line width, so that the surface is solid."
msgstr "La distance entre les lignes du radeau pour les couches supérieures de celui-ci. Cet espace doit être égal à la largeur de ligne afin de créer une surface solide."

msgctxt "prime_tower_raft_base_line_spacing description"
msgid "The distance between the raft lines for the unique prime tower raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "La distance entre les lignes de radeau pour la couche de radeau unique de la tour d'amorçage. Un espacement large permet un retrait facile du radeau du plateau"

msgctxt "interlocking_depth description"
msgid "The distance from the boundary between models to generate interlocking structure, measured in cells. Too few cells will result in poor adhesion."
msgstr "Limite de distance entre les modèles 3D à partir de laquelle générer une structure de connexion, mesurée en cellules. Un nombre de cellules trop bas entraînera une mauvaise adhérence."

msgctxt "brim_width description"
msgid "The distance from the model to the outermost brim line. A larger brim enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "La distance entre le modèle et la ligne de bordure la plus à l'extérieur. Une bordure plus large renforce l'adhérence au plateau mais réduit également la zone d'impression réelle."

msgctxt "interlocking_boundary_avoidance description"
msgid "The distance from the outside of a model where interlocking structures will not be generated, measured in cells."
msgstr "Distance depuis l'extérieur d'un modèle 3D à partir de laquelle les structures de connexion ne seront pas générées, mesurée en cellules."

msgctxt "machine_heat_zone_length description"
msgid "The distance from the tip of the nozzle in which heat from the nozzle is transferred to the filament."
msgstr "Distance depuis la pointe du bec d'impression sur laquelle la chaleur du bec d'impression est transférée au filament."

msgctxt "bottom_skin_expand_distance description"
msgid "The distance the bottom skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the skin adhere better to the walls on the layer below. Lower values save amount of material used."
msgstr "La distance à laquelle les couches extérieures inférieures s'étendent à l'intérieur du remplissage. Des valeurs élevées lient mieux la couche extérieure au motif de remplissage et font mieux adhérer à cette couche les parois de la couche inférieure. Des valeurs faibles économisent la quantité de matériau utilisé."

msgctxt "expand_skins_expand_distance description"
msgid "The distance the skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on neighboring layers adhere better to the skin. Lower values save amount of material used."
msgstr "La distance à laquelle les couches extérieures s'étendent à l'intérieur du remplissage. Des valeurs élevées lient mieux la couche extérieure au motif de remplissage et font mieux adhérer à cette couche les parois des couches voisines. Des valeurs faibles économisent la quantité de matériau utilisé."

msgctxt "top_skin_expand_distance description"
msgid "The distance the top skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on the layer above adhere better to the skin. Lower values save amount of material used."
msgstr "La distance à laquelle les couches extérieures supérieures s'étendent à l'intérieur du remplissage. Des valeurs élevées lient mieux la couche extérieure au motif de remplissage et font mieux adhérer à cette couche les parois de la couche supérieure. Des valeurs faibles économisent la quantité de matériau utilisé."

msgctxt "wipe_move_distance description"
msgid "The distance to move the head back and forth across the brush."
msgstr "La distance de déplacement de la tête d'avant en arrière à travers la brosse."

msgctxt "lightning_infill_prune_angle description"
msgid "The endpoints of infill lines are shortened to save on material. This setting is the angle of overhang of the endpoints of these lines."
msgstr "Les extrémités des lignes de remplissage sont raccourcies pour économiser du matériau. Ce paramètre est l'angle de saillie des extrémités de ces lignes."

msgctxt "material_extrusion_cool_down_speed description"
msgid "The extra speed by which the nozzle cools while extruding. The same value is used to signify the heat up speed lost when heating up while extruding."
msgstr "La vitesse supplémentaire à laquelle la buse refroidit pendant l'extrusion. La même valeur est utilisée pour indiquer la perte de vitesse de chauffage pendant l'extrusion."

msgctxt "support_extruder_nr_layer_0 description"
msgid "The extruder train to use for printing the first layer of support infill. This is used in multi-extrusion."
msgstr "Le train d'extrudeuse à utiliser pour l'impression de la première couche de remplissage du support. Cela est utilisé en multi-extrusion."

msgctxt "raft_base_extruder_nr description"
msgid "The extruder train to use for printing the first layer of the raft. This is used in multi-extrusion."
msgstr "Le train d'extrudeur à utiliser pour l'impression de la première couche du radeau. Cela est utilisé en multi-extrusion."

msgctxt "support_bottom_extruder_nr description"
msgid "The extruder train to use for printing the floors of the support. This is used in multi-extrusion."
msgstr "Le train d'extrudeuse à utiliser pour l'impression des bas du support. Cela est utilisé en multi-extrusion."

msgctxt "support_infill_extruder_nr description"
msgid "The extruder train to use for printing the infill of the support. This is used in multi-extrusion."
msgstr "Le train d'extrudeuse à utiliser pour l'impression du remplissage du support. Cela est utilisé en multi-extrusion."

msgctxt "raft_interface_extruder_nr description"
msgid "The extruder train to use for printing the middle layer of the raft. This is used in multi-extrusion."
msgstr "Le train d'extrudeur à utiliser pour imprimer la couche intermédiaire du radeau. Cela est utilisé en multi-extrusion."

msgctxt "support_interface_extruder_nr description"
msgid "The extruder train to use for printing the roofs and floors of the support. This is used in multi-extrusion."
msgstr "Le train d'extrudeuse à utiliser pour l'impression des plafonds et bas du support. Cela est utilisé en multi-extrusion."

msgctxt "support_roof_extruder_nr description"
msgid "The extruder train to use for printing the roofs of the support. This is used in multi-extrusion."
msgstr "Le train d'extrudeuse à utiliser pour l'impression des plafonds du support. Cela est utilisé en multi-extrusion."

msgctxt "skirt_brim_extruder_nr description"
msgid "The extruder train to use for printing the skirt or brim. This is used in multi-extrusion."
msgstr "Le train d'extrudeur à utiliser pour l'impression de la jupe ou de la bordure. Cela est utilisé en multi-extrusion."

msgctxt "adhesion_extruder_nr description"
msgid "The extruder train to use for printing the skirt/brim/raft. This is used in multi-extrusion."
msgstr "Le train d'extrudeuse à utiliser pour l'impression de la jupe/la bordure/du radeau. Cela est utilisé en multi-extrusion."

msgctxt "support_extruder_nr description"
msgid "The extruder train to use for printing the support. This is used in multi-extrusion."
msgstr "Le train d'extrudeuse à utiliser pour l'impression du support. Cela est utilisé en multi-extrusion."

msgctxt "raft_surface_extruder_nr description"
msgid "The extruder train to use for printing the top layer(s) of the raft. This is used in multi-extrusion."
msgstr "Le train d'extrudeur à utiliser pour imprimer la ou les couches du haut du radeau. Cela est utilisé en multi-extrusion."

msgctxt "infill_extruder_nr description"
msgid "The extruder train used for printing infill. This is used in multi-extrusion."
msgstr "Le train d'extrudeuse utilisé pour l'impression du remplissage. Cela est utilisé en multi-extrusion."

msgctxt "wall_x_extruder_nr description"
msgid "The extruder train used for printing the inner walls. This is used in multi-extrusion."
msgstr "Le train d'extrudeuse utilisé pour l'impression des parois internes. Cela est utilisé en multi-extrusion."

msgctxt "wall_0_extruder_nr description"
msgid "The extruder train used for printing the outer wall. This is used in multi-extrusion."
msgstr "Le train d'extrudeuse utilisé pour l'impression des parois externes. Cela est utilisé en multi-extrusion."

msgctxt "top_bottom_extruder_nr description"
msgid "The extruder train used for printing the top and bottom skin. This is used in multi-extrusion."
msgstr "Le train d'extrudeuse utilisé pour l'impression de la couche extérieure du haut et du bas. Cela est utilisé en multi-extrusion."

msgctxt "roofing_extruder_nr description"
msgid "The extruder train used for printing the top most skin. This is used in multi-extrusion."
msgstr "Le train d'extrudeuse utilisé pour l'impression de la couche extérieure supérieure. Cela est utilisé en multi-extrusion."

msgctxt "wall_extruder_nr description"
msgid "The extruder train used for printing the walls. This is used in multi-extrusion."
msgstr "Le train d'extrudeuse utilisé pour l'impression des parois. Cela est utilisé en multi-extrusion."

msgctxt "raft_base_fan_speed description"
msgid "The fan speed for the base raft layer."
msgstr "La vitesse du ventilateur pour la couche de base du radeau."

msgctxt "raft_interface_fan_speed description"
msgid "The fan speed for the middle raft layer."
msgstr "La vitesse du ventilateur pour la couche du milieu du radeau."

msgctxt "raft_fan_speed description"
msgid "The fan speed for the raft."
msgstr "La vitesse du ventilateur pour le radeau."

msgctxt "raft_surface_fan_speed description"
msgid "The fan speed for the top raft layers."
msgstr "La vitesse du ventilateur pour les couches du dessus du radeau."

msgctxt "cross_infill_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the infill of the print."
msgstr "Emplacement du fichier d'une image dont les valeurs de luminosité déterminent la densité minimale à l'emplacement correspondant dans le remplissage de l'impression."

msgctxt "cross_support_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the support."
msgstr "Emplacement du fichier d'une image dont les valeurs de luminosité déterminent la densité minimale à l'emplacement correspondant dans le support."

msgctxt "speed_slowdown_layers description"
msgid "The first few layers are printed slower than the rest of the model, to get better adhesion to the build plate and improve the overall success rate of prints. The speed is gradually increased over these layers."
msgstr "Les premières couches sont imprimées plus lentement que le reste du modèle afin d’obtenir une meilleure adhérence au plateau et d’améliorer le taux de réussite global des impressions. La vitesse augmente graduellement à chacune de ces couches."

msgctxt "raft_airgap description"
msgid "The gap between the final raft layer and the first layer of the model. Only the first layer is raised by this amount to lower the bonding between the raft layer and the model. Makes it easier to peel off the raft."
msgstr "L’espace entre la dernière couche du radeau et la première couche du modèle. Seule la première couche est surélevée de cette quantité d’espace pour réduire l’adhérence entre la couche du radeau et le modèle. Cela facilite le décollage du radeau."

msgctxt "machine_height description"
msgid "The height (Z-direction) of the printable area."
msgstr "La hauteur (sens Z) de la zone imprimable."

msgctxt "mold_roof_height description"
msgid "The height above horizontal parts in your model which to print mold."
msgstr "La hauteur au-dessus des parties horizontales dans votre modèle pour laquelle imprimer le moule."

msgctxt "cool_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "Hauteur à laquelle les ventilateurs tournent à la vitesse régulière. Pour les couches situées en-dessous, la vitesse des ventilateurs augmente progressivement de la vitesse des ventilateurs initiale jusqu'à la vitesse régulière."

msgctxt "gantry_height description"
msgid "The height difference between the tip of the nozzle and the gantry system (X and Y axes)."
msgstr "La différence de hauteur entre la pointe de la buse et le système de portique (axes X et Y)."

msgctxt "machine_nozzle_head_distance description"
msgid "The height difference between the tip of the nozzle and the lowest part of the print head."
msgstr "La différence de hauteur entre la pointe de la buse et la partie la plus basse de la tête d'impression."

msgctxt "retraction_hop_after_extruder_switch_height description"
msgid "The height difference when performing a Z Hop after extruder switch."
msgstr "La différence de hauteur lors de la réalisation d'un décalage en Z après changement d'extrudeuse."

msgctxt "retraction_hop description"
msgid "The height difference when performing a Z Hop."
msgstr "La différence de hauteur lors de la réalisation d'un décalage en Z."

msgctxt "wipe_hop_amount description"
msgid "The height difference when performing a Z Hop."
msgstr "La différence de hauteur lors de la réalisation d'un décalage en Z."

msgctxt "layer_height description"
msgid "The height of each layer in mm. Higher values produce faster prints in lower resolution, lower values produce slower prints in higher resolution."
msgstr "La hauteur de chaque couche en mm. Des valeurs plus élevées créent des impressions plus rapides dans une résolution moindre, tandis que des valeurs plus basses entraînent des impressions plus lentes dans une résolution plus élevée."

msgctxt "gradual_infill_step_height description"
msgid "The height of infill of a given density before switching to half the density."
msgstr "La hauteur de remplissage d'une densité donnée avant de passer à la moitié de la densité."

msgctxt "gradual_support_infill_step_height description"
msgid "The height of support infill of a given density before switching to half the density."
msgstr "La hauteur de remplissage de support d'une densité donnée avant de passer à la moitié de la densité."

msgctxt "interlocking_beam_layer_count description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "La hauteur des attaches de la structure de connexion, mesurée en nombre de couches. Des couches moins nombreuses seront plus solides, mais davantage sujettes à des imperfections."

msgctxt "interlocking_orientation description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "La hauteur des attaches de la structure de connexion, mesurée en nombre de couches. Des couches moins nombreuses seront plus solides, mais davantage sujettes à des imperfections."

msgctxt "layer_height_0 description"
msgid "The height of the initial layer in mm. A thicker initial layer makes adhesion to the build plate easier."
msgstr "La hauteur de la couche initiale en mm. Une couche initiale plus épaisse adhère plus facilement au plateau."

msgctxt "prime_tower_base_height description"
msgid "The height of the prime tower base. Increasing this value will result in a more sturdy prime tower because the base will be wider. If this setting is too low, the prime tower will not have a sturdy base."
msgstr "Augmenter cette valeur rendra la tour d'amorçage plus robuste car la base sera plus large. Si ce paramètre est trop bas, la tour d'amorçage n'aura pas une base solide."

msgctxt "support_bottom_stair_step_height description"
msgid "The height of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures. Set to zero to turn off the stair-like behaviour."
msgstr "La hauteur de la marche du support en forme d'escalier reposant sur le modèle. Une valeur faible rend le support plus difficile à enlever, mais des valeurs trop élevées peuvent entraîner des supports instables. Définir la valeur sur zéro pour désactiver le comportement en forme d'escalier."

msgctxt "brim_gap description"
msgid "The horizontal distance between the first brim line and the outline of the first layer of the print. A small gap can make the brim easier to remove while still providing the thermal benefits."
msgstr "La distance horizontale entre la première ligne de bordure et le contour de la première couche de l'impression. Un petit trou peut faciliter l'enlèvement de la bordure tout en offrant des avantages thermiques."

msgctxt "skirt_gap description"
msgid ""
"The horizontal distance between the skirt and the first layer of the print.\n"
"This is the minimum distance. Multiple skirt lines will extend outwards from this distance."
msgstr ""
"La distance horizontale entre la jupe et la première couche de l’impression.\n"
"Il s’agit de la distance minimale séparant la jupe de l’objet. Si la jupe a d’autres lignes, celles-ci s’étendront vers l’extérieur."

msgctxt "lightning_infill_straightening_angle description"
msgid "The infill lines are straightened out to save on printing time. This is the maximum angle of overhang allowed across the length of the infill line."
msgstr "Les lignes de remplissage sont redressées pour gagner du temps d'impression. Il s'agit de l'angle maximal de saillie autorisé sur la longueur de la ligne de remplissage."

msgctxt "infill_offset_x description"
msgid "The infill pattern is moved this distance along the X axis."
msgstr "Le motif de remplissage est décalé de cette distance sur l'axe X."

msgctxt "infill_offset_y description"
msgid "The infill pattern is moved this distance along the Y axis."
msgstr "Le motif de remplissage est décalé de cette distance sur l'axe Y."

msgctxt "machine_nozzle_size description"
msgid "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size."
msgstr "Le diamètre intérieur de la buse. Modifiez ce paramètre si vous utilisez une taille de buse non standard."

msgctxt "raft_base_jerk description"
msgid "The jerk with which the base raft layer is printed."
msgstr "La saccade selon laquelle la couche de base du radeau est imprimée."

msgctxt "raft_interface_jerk description"
msgid "The jerk with which the middle raft layer is printed."
msgstr "La saccade selon laquelle la couche du milieu du radeau est imprimée."

msgctxt "raft_jerk description"
msgid "The jerk with which the raft is printed."
msgstr "La saccade selon laquelle le radeau est imprimé."

msgctxt "raft_surface_jerk description"
msgid "The jerk with which the top raft layers are printed."
msgstr "La saccade selon laquelle les couches du dessus du radeau sont imprimées."

msgctxt "bottom_skin_preshrink description"
msgid "The largest width of bottom skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing bottom skin at slanted surfaces in the model."
msgstr "La plus grande largeur des zones de la couche extérieure inférieure à faire disparaître. Toute zone de la couche extérieure plus étroite que cette valeur disparaîtra. Ceci peut aider à limiter le temps et la quantité de matière utilisés pour imprimer la couche extérieure inférieure sur les surfaces obliques du modèle."

msgctxt "skin_preshrink description"
msgid "The largest width of skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top/bottom skin at slanted surfaces in the model."
msgstr "La plus grande largeur des zones de la couche extérieure à faire disparaître. Toute zone de la couche extérieure plus étroite que cette valeur disparaîtra. Ceci peut aider à limiter le temps et la quantité de matière utilisés pour imprimer la couche extérieure supérieure/inférieure sur les surfaces obliques du modèle."

msgctxt "top_skin_preshrink description"
msgid "The largest width of top skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top skin at slanted surfaces in the model."
msgstr "La plus grande largeur des zones de la couche extérieure supérieure à faire disparaître. Toute zone de la couche extérieure plus étroite que cette valeur disparaîtra. Ceci peut aider à limiter le temps et la quantité de matière utilisés pour imprimer la couche extérieure supérieure sur les surfaces obliques du modèle."

msgctxt "cool_fan_full_layer description"
msgid "The layer at which the fans spin on regular fan speed. If regular fan speed at height is set, this value is calculated and rounded to a whole number."
msgstr "La couche à laquelle les ventilateurs tournent à la vitesse régulière. Si la vitesse régulière du ventilateur à la hauteur est définie, cette valeur est calculée et arrondie à un nombre entier."

msgctxt "cool_min_layer_time_fan_speed_max description"
msgid "The layer time which sets the threshold between regular fan speed and maximum fan speed. Layers that print slower than this time use regular fan speed. For faster layers the fan speed gradually increases towards the maximum fan speed."
msgstr "La durée de couche qui définit la limite entre la vitesse régulière et la vitesse maximale du ventilateur. Les couches qui s'impriment moins vite que cette durée utilisent la vitesse régulière du ventilateur. Pour les couches plus rapides, la vitesse du ventilateur augmente progressivement jusqu'à atteindre la vitesse maximale."

msgctxt "retraction_amount description"
msgid "The length of material retracted during a retraction move."
msgstr "La longueur de matériau rétracté pendant une rétraction."

msgctxt "prime_tower_base_curve_magnitude description"
msgid "The magnitude factor used for the slope of the prime tower base. If you increase this value, the base will become slimmer. If you decrease it, the base will become thicker."
msgstr "Le facteur de magnitude utilisé pour la pente de la base de la tour d'amorçage. Si vous augmentez cette valeur, la base deviendra plus mince. Si vous la diminuez, la base deviendra plus épaisse."

msgctxt "machine_buildplate_type description"
msgid "The material of the build plate installed on the printer."
msgstr "Matériau du plateau installé sur l'imprimante."

msgctxt "adaptive_layer_height_variation description"
msgid "The maximum allowed height different from the base layer height."
msgstr "Hauteur maximale autorisée par rapport à la couche de base."

msgctxt "ooze_shield_angle description"
msgid "The maximum angle a part in the ooze shield will have. With 0 degrees being vertical, and 90 degrees being horizontal. A smaller angle leads to less failed ooze shields, but more material."
msgstr "L'angle maximal qu'une partie du bouclier de suintage peut adopter. Zéro degré est vertical et 90 degrés est horizontal. Un angle plus petit entraîne moins d'échecs au niveau des boucliers de suintage, mais utilise plus de matériaux."

msgctxt "conical_overhang_angle description"
msgid "The maximum angle of overhangs after the they have been made printable. At a value of 0° all overhangs are replaced by a piece of model connected to the build plate, 90° will not change the model in any way."
msgstr "L'angle maximal des porte-à-faux après qu'ils aient été rendus imprimables. À une valeur de 0°, tous les porte-à-faux sont remplacés par une pièce de modèle rattachée au plateau, tandis que 90° ne changera en rien le modèle."

msgctxt "support_tree_angle description"
msgid "The maximum angle of the branches while they grow around the model. Use a lower angle to make them more vertical and more stable. Use a higher angle to be able to have more reach."
msgstr "Il s'agit de l'angle maximal des branches imprimées autour du modèle. Si vous utilisez un angle faible, les branches seront plus verticales et plus stables. Si vous utilisez un angle élevé, vous obtiendrez une plus grande portée."

msgctxt "conical_overhang_hole_size description"
msgid "The maximum area of a hole in the base of the model before it's removed by Make Overhang Printable.  Holes smaller than this will be retained.  A value of 0 mm² will fill all holes in the models base."
msgstr "Zone maximale d'un trou dans la base du modèle avant d'être retirée par l'outil Rendre le porte-à-faux imprimable.  Les trous plus petits seront conservés.  Une valeur de 0 mm² remplira tous les trous dans la base des modèles."

msgctxt "meshfix_maximum_deviation description"
msgid "The maximum deviation allowed when reducing the resolution for the Maximum Resolution setting. If you increase this, the print will be less accurate, but the g-code will be smaller. Maximum Deviation is a limit for Maximum Resolution, so if the two conflict the Maximum Deviation will always be held true."
msgstr "L'écart maximum autorisé lors de la réduction de la résolution pour le paramètre Résolution maximum. Si vous augmentez cette valeur, l'impression sera moins précise, mais le G-Code sera plus petit. L'écart maximum est une limite pour la résolution maximum. Donc si les deux entrent en conflit, l'Écart maximum restera valable."

msgctxt "support_join_distance description"
msgid "The maximum distance between support structures in the X/Y directions. When separate structures are closer together than this value, the structures merge into one."
msgstr "La distance maximale entre les supports dans les directions X/Y. Lorsque des modèle séparés sont plus rapprochés que cette valeur, ils fusionnent."

msgctxt "flow_rate_max_extrusion_offset description"
msgid "The maximum distance in mm to move the filament to compensate for changes in flow rate."
msgstr "La distance maximale en mm pour déplacer le filament afin de compenser les variations du débit."

msgctxt "meshfix_maximum_extrusion_area_deviation description"
msgid "The maximum extrusion area deviation allowed when removing intermediate points from a straight line. An intermediate point may serve as width-changing point in a long straight line. Therefore, if it is removed, it will cause the line to have a uniform width and, as a result, lose (or gain) a bit of extrusion area. If you increase this you may notice slight under- (or over-) extrusion in between straight parallel walls, as more intermediate width-changing points will be allowed to be removed. Your print will be less accurate, but the g-code will be smaller."
msgstr "L'écart maximal de la surface d'extrusion autorisé lors de la suppression des points intermédiaires d'une ligne droite. Un point intermédiaire peut servir de point de changement de largeur dans une longue ligne droite. Par conséquent, s'il est supprimé, la ligne aura une largeur uniforme et, par conséquent, cela engendrera la perte (ou le gain) d'un peu de surface d'extrusion. Si vous augmentez cette valeur, vous pourrez constater une légère sous-extrusion (ou sur-extrusion) entre les parois parallèles droites car davantage de points intermédiaires de changement de largeur pourront être supprimés. Votre impression sera moins précise, mais le G-code sera plus petit."

msgctxt "jerk_print_layer_0 description"
msgid "The maximum instantaneous velocity change during the printing of the initial layer."
msgstr "Le changement instantané maximal de vitesse durant l'impression de la couche initiale."

msgctxt "jerk_print description"
msgid "The maximum instantaneous velocity change of the print head."
msgstr "Le changement instantané maximal de vitesse de la tête d'impression."

msgctxt "jerk_ironing description"
msgid "The maximum instantaneous velocity change while performing ironing."
msgstr "Le changement instantané maximal de vitesse lors de l'étirage."

msgctxt "jerk_wall_x description"
msgid "The maximum instantaneous velocity change with which all inner walls are printed."
msgstr "Le changement instantané maximal de vitesse selon lequel les parois intérieures sont imprimées."

msgctxt "jerk_infill description"
msgid "The maximum instantaneous velocity change with which infill is printed."
msgstr "Le changement instantané maximal de vitesse selon lequel le remplissage est imprimé."

msgctxt "jerk_support_bottom description"
msgid "The maximum instantaneous velocity change with which the floors of support are printed."
msgstr "Le changement instantané maximal de vitesse selon lequel les bas de support sont imprimés."

msgctxt "jerk_support_infill description"
msgid "The maximum instantaneous velocity change with which the infill of support is printed."
msgstr "Le changement instantané maximal de vitesse selon lequel le remplissage de support est imprimé."

msgctxt "jerk_wall_0 description"
msgid "The maximum instantaneous velocity change with which the outermost walls are printed."
msgstr "Le changement instantané maximal de vitesse selon lequel les parois externes sont imprimées."

msgctxt "jerk_prime_tower description"
msgid "The maximum instantaneous velocity change with which the prime tower is printed."
msgstr "Le changement instantané maximal de vitesse selon lequel la tour d'amorçage est imprimée."

msgctxt "jerk_support_interface description"
msgid "The maximum instantaneous velocity change with which the roofs and floors of support are printed."
msgstr "Le changement instantané maximal de vitesse selon lequel les plafonds et bas sont imprimés."

msgctxt "jerk_support_roof description"
msgid "The maximum instantaneous velocity change with which the roofs of support are printed."
msgstr "Le changement instantané maximal de vitesse selon lequel les plafonds de support sont imprimés."

msgctxt "jerk_skirt_brim description"
msgid "The maximum instantaneous velocity change with which the skirt and brim are printed."
msgstr "Le changement instantané maximal de vitesse selon lequel la jupe et la bordure sont imprimées."

msgctxt "jerk_support description"
msgid "The maximum instantaneous velocity change with which the support structure is printed."
msgstr "Le changement instantané maximal de vitesse selon lequel la structure de support est imprimée."

msgctxt "jerk_wall_x_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface inner walls are printed."
msgstr "Le changement instantané maximal de vitesse selon lequel les parois intérieures de la surface supérieure sont imprimées."

msgctxt "jerk_wall_0_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface outermost walls are printed."
msgstr "Le changement instantané maximal de vitesse selon lequel la paroi extérieure de la surface supérieure est imprimée."

msgctxt "jerk_wall description"
msgid "The maximum instantaneous velocity change with which the walls are printed."
msgstr "Le changement instantané maximal de vitesse selon lequel les parois sont imprimées."

msgctxt "jerk_roofing description"
msgid "The maximum instantaneous velocity change with which top surface skin layers are printed."
msgstr "Le changement instantané maximal de vitesse selon lequel les couches extérieures de surface supérieure sont imprimées."

msgctxt "jerk_topbottom description"
msgid "The maximum instantaneous velocity change with which top/bottom layers are printed."
msgstr "Le changement instantané maximal de vitesse selon lequel les couches du dessus/dessous sont imprimées."

msgctxt "jerk_travel description"
msgid "The maximum instantaneous velocity change with which travel moves are made."
msgstr "Le changement instantané maximal de vitesse selon lequel les déplacements s'effectuent."

msgctxt "machine_max_feedrate_x description"
msgid "The maximum speed for the motor of the X-direction."
msgstr "La vitesse maximale pour le moteur du sens X."

msgctxt "machine_max_feedrate_y description"
msgid "The maximum speed for the motor of the Y-direction."
msgstr "La vitesse maximale pour le moteur du sens Y."

msgctxt "machine_max_feedrate_z description"
msgid "The maximum speed for the motor of the Z-direction."
msgstr "La vitesse maximale pour le moteur du sens Z."

msgctxt "machine_max_feedrate_e description"
msgid "The maximum speed of the filament."
msgstr "La vitesse maximale du filament."

msgctxt "support_bottom_stair_step_width description"
msgid "The maximum width of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures."
msgstr "La largeur maximale de la marche du support en forme d'escalier reposant sur le modèle. Une valeur faible rend le support plus difficile à enlever, mais des valeurs trop élevées peuvent entraîner des supports instables."

msgctxt "mold_width description"
msgid "The minimal distance between the outside of the mold and the outside of the model."
msgstr "La distance minimale entre l'extérieur du moule et l'extérieur du modèle."

msgctxt "machine_minimum_feedrate description"
msgid "The minimal movement speed of the print head."
msgstr "La vitesse minimale de mouvement de la tête d'impression."

msgctxt "material_initial_print_temperature description"
msgid "The minimal temperature while heating up to the Printing Temperature at which printing can already start."
msgstr "La température minimale pendant le chauffage jusqu'à la température d'impression à laquelle l'impression peut démarrer."

msgctxt "machine_min_cool_heat_time_window description"
msgid "The minimal time an extruder has to be inactive before the nozzle is cooled. Only when an extruder is not used for longer than this time will it be allowed to cool down to the standby temperature."
msgstr "La durée minimale pendant laquelle une extrudeuse doit être inactive avant que la buse ait refroidi. Ce n'est que si une extrudeuse n'est pas utilisée pendant une durée supérieure à celle-ci qu'elle pourra refroidir jusqu'à la température de veille."

msgctxt "infill_support_angle description"
msgid "The minimum angle of internal overhangs for which infill is added. At a value of 0° objects are totally filled with infill, 90° will not provide any infill."
msgstr "Angle minimal des porte-à-faux internes pour lesquels le remplissage est ajouté. À une valeur de 0°, les objets sont totalement remplis, 90° ne fournira aucun remplissage."

msgctxt "support_angle description"
msgid "The minimum angle of overhangs for which support is added. At a value of 0° all overhangs are supported, 90° will not provide any support."
msgstr "L'angle minimal des porte-à-faux pour lesquels un support est ajouté. À une valeur de 0 °, tous les porte-à-faux sont soutenus, tandis qu'à 90 °, aucun support ne sera créé."

msgctxt "retraction_min_travel description"
msgid "The minimum distance of travel needed for a retraction to happen at all. This helps to get fewer retractions in a small area."
msgstr "La distance minimale de déplacement nécessaire pour qu’une rétraction ait lieu. Cela permet d’éviter qu’un grand nombre de rétractions ne se produisent sur une petite portion."

msgctxt "skirt_brim_minimal_length description"
msgid "The minimum length of the skirt or brim. If this length is not reached by all skirt or brim lines together, more skirt or brim lines will be added until the minimum length is reached. Note: If the line count is set to 0 this is ignored."
msgstr "La longueur minimale de la jupe ou bordure. Si cette longueur n’est pas atteinte par toutes les lignes de jupe ou de bordure ensemble, d’autres lignes de jupe ou de bordure seront ajoutées afin d’atteindre la longueur minimale. Veuillez noter que si le nombre de lignes est défini sur 0, cette option est ignorée."

msgctxt "min_odd_wall_line_width description"
msgid "The minimum line width for middle line gap filler polyline walls. This setting determines at which model thickness we switch from printing two wall lines, to printing two outer walls and a single central wall in the middle. A higher Minimum Odd Wall Line Width leads to a higher maximum even wall line width. The maximum odd wall line width is calculated as 2 * Minimum Even Wall Line Width."
msgstr "Largeur de ligne minimale pour les parois de polyligne de remplissage de l'espace de ligne médiane. Ce paramètre détermine à partir de quelle épaisseur de modèle 3D nous passons de l'impression de deux lignes de parois à l'impression de deux parois extérieures et d'une seule paroi centrale au milieu. Une largeur de ligne de paroi impaire minimale plus élevée conduit à une largeur de ligne de paroi paire plus élevée. La largeur maximale de la ligne de paroi impaire représente 2 fois la largeur minimale de la ligne de paroi paire."

msgctxt "min_even_wall_line_width description"
msgid "The minimum line width for normal polygonal walls. This setting determines at which model thickness we switch from printing a single thin wall line, to printing two wall lines. A higher Minimum Even Wall Line Width leads to a higher maximum odd wall line width. The maximum even wall line width is calculated as Outer Wall Line Width + 0.5 * Minimum Odd Wall Line Width."
msgstr "Largeur de ligne minimale pour les murs polygonaux normaux. Ce paramètre détermine à quelle épaisseur de modèle nous passons de l'impression d'une seule ligne de paroi fine à l'impression de deux lignes de paroi. Une largeur minimale de ligne de paroi paire plus élevée entraîne une largeur maximale de ligne de paroi impaire plus élevée. La largeur maximale de la ligne de paroi paire est calculée comme suit : largeur de la ligne de paroi extérieure + 0,5 * largeur minimale de la ligne de paroi impaire."

msgctxt "cool_min_speed description"
msgid "The minimum print speed, despite slowing down due to the minimum layer time. When the printer would slow down too much, the pressure in the nozzle would be too low and result in bad print quality."
msgstr "La vitesse minimale d'impression, malgré le ralentissement dû à la durée minimale d'une couche. Si l'imprimante devait trop ralentir, la pression au niveau de la buse serait trop faible, ce qui résulterait en une mauvaise qualité d'impression."

msgctxt "meshfix_maximum_resolution description"
msgid "The minimum size of a line segment after slicing. If you increase this, the mesh will have a lower resolution. This may allow the printer to keep up with the speed it has to process g-code and will increase slice speed by removing details of the mesh that it can't process anyway."
msgstr "Taille minimum d'un segment de ligne après découpage. Si vous augmentez cette valeur, la maille aura une résolution plus faible. Cela peut permettre à l'imprimante de suivre la vitesse à laquelle elle doit traiter le G-Code et augmentera la vitesse de découpe en enlevant des détails de la maille que l'imprimante ne peut pas traiter de toute manière."

msgctxt "meshfix_maximum_travel_resolution description"
msgid "The minimum size of a travel line segment after slicing. If you increase this, the travel moves will have less smooth corners. This may allow the printer to keep up with the speed it has to process g-code, but it may cause model avoidance to become less accurate."
msgstr "Taille minimale d'un segment de ligne de déplacement après la découpe. Si vous augmentez cette valeur, les mouvements de déplacement auront des coins moins lisses. Cela peut permettre à l'imprimante de suivre la vitesse à laquelle elle doit traiter le G-Code, mais cela peut réduire la précision de l'évitement du modèle."

msgctxt "support_bottom_stair_step_min_slope description"
msgid "The minimum slope of the area for stair-stepping to take effect. Low values should make support easier to remove on shallower slopes, but really low values may result in some very counter-intuitive results on other parts of the model."
msgstr "La pente minimum de la zone pour un effet de marche de support. Des valeurs basses devraient faciliter l'enlèvement du support sur les pentes peu superficielles ; des valeurs très basses peuvent donner des résultats vraiment contre-intuitifs sur d'autres pièces du modèle."

msgctxt "cool_min_layer_time description"
msgid "The minimum time spent in a layer. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "Temps minimum passé sur une couche. Cela force l'imprimante à ralentir afin de passer au minimum la durée définie ici sur une couche. Cela permet au matériau imprimé de refroidir correctement avant l'impression de la couche suivante. Les couches peuvent néanmoins prendre moins de temps que le temps de couche minimum si « Lift Head  » (Relever Tête) est désactivé et si la vitesse minimum serait autrement non respectée."

msgctxt "prime_tower_min_volume description"
msgid "The minimum volume for each layer of the prime tower in order to purge enough material."
msgstr "Le volume minimum pour chaque touche de la tour d'amorçage afin de purger suffisamment de matériau."

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model description"
msgid "The most the diameter of a branch that has to connect to the model may increase by merging with branches that could reach the buildplate. Increasing this reduces print time, but increases the area of support that rests on model"
msgstr "Le diamètre maximal d'une branche rattachée au modèle peut augmenter lorsqu'elle fusionne avec des branches pouvant atteindre le plateau. Le fait d'augmenter ce diamètre réduit le temps d'impression, mais agrandit la surface du support sur laquelle repose le modèle."

msgctxt "machine_name description"
msgid "The name of your 3D printer model."
msgstr "Le nom du modèle de votre imprimante 3D."

msgctxt "machine_nozzle_id description"
msgid "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\"."
msgstr "ID buse pour un train d'extrudeuse, comme « AA 0.4 » et « BB 0.8 »."

msgctxt "travel_avoid_other_parts description"
msgid "The nozzle avoids already printed parts when traveling. This option is only available when combing is enabled."
msgstr "La buse contourne les pièces déjà imprimées lorsqu'elle se déplace. Cette option est disponible uniquement lorsque les détours sont activés."

msgctxt "travel_avoid_supports description"
msgid "The nozzle avoids already printed supports when traveling. This option is only available when combing is enabled."
msgstr "La buse contourne les supports déjà imprimés lorsqu'elle se déplace. Cette option est disponible uniquement lorsque les détours sont activés."

msgctxt "bottom_layers description"
msgid "The number of bottom layers. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "Le nombre de couches inférieures. Lorsqu'elle est calculée par l'épaisseur du dessous, cette valeur est arrondie à un nombre entier."

msgctxt "raft_base_wall_count description"
msgid "The number of contours to print around the linear pattern in the base layer of the raft."
msgstr "Le nombre de contours à imprimer autour du motif linéaire dans la couche de base du radeau."

msgctxt "skin_edge_support_layers description"
msgid "The number of infill layers that supports skin edges."
msgstr "Le nombre de couches de remplissage qui soutient les bords de la couche."

msgctxt "initial_bottom_layers description"
msgid "The number of initial bottom layers, from the build-plate upwards. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "Le nombre de couches inférieures initiales à partir du haut du plateau. Lorsqu'elle est calculée par l'épaisseur du dessous, cette valeur est arrondie à un nombre entier."

msgctxt "raft_interface_layers description"
msgid "The number of layers between the base and the surface of the raft. These comprise the main thickness of the raft. Increasing this creates a thicker, sturdier raft."
msgstr "Nombre de couches entre la base et la surface du radeau. Elles comprennent l'épaisseur principale du radeau. En l'augmentant, on obtient un radeau plus épais et plus solide."

msgctxt "brim_line_count description"
msgid "The number of lines used for a brim. More brim lines enhance adhesion to the build plate, but also reduces the effective print area."
msgstr "Le nombre de lignes utilisées pour une bordure. Un plus grand nombre de lignes de bordure renforce l'adhérence au plateau mais réduit également la zone d'impression réelle."

msgctxt "support_brim_line_count description"
msgid "The number of lines used for the support brim. More brim lines enhance adhesion to the build plate, at the cost of some extra material."
msgstr "Nombre de lignes utilisées pour la bordure du support. L'augmentation du nombre de lignes de bordure améliore l'adhérence au plateau, mais demande un peu de matériau supplémentaire."

msgctxt "raft_surface_layers description"
msgid "The number of top layers on top of the 2nd raft layer. These are fully filled layers that the model sits on. 2 layers result in a smoother top surface than 1."
msgstr "Nombre de couches de surface au-dessus de la deuxième couche du radeau. Il s’agit des couches entièrement remplies sur lesquelles le modèle est posé. En général, deux couches offrent une surface plus lisse qu'une seule."

msgctxt "top_layers description"
msgid "The number of top layers. When calculated by the top thickness, this value is rounded to a whole number."
msgstr "Le nombre de couches supérieures. Lorsqu'elle est calculée par l'épaisseur du dessus, cette valeur est arrondie à un nombre entier."

msgctxt "roofing_layer_count description"
msgid "The number of top most skin layers. Usually only one top most layer is sufficient to generate higher quality top surfaces."
msgstr "Nombre de couches extérieures supérieures. En général, une seule couche supérieure est suffisante pour générer des surfaces supérieures de qualité."

msgctxt "support_wall_count description"
msgid "The number of walls with which to surround support infill. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Nombre de parois qui entourent le remplissage de support. L'ajout d'une paroi peut rendre l'impression de support plus fiable et mieux supporter les porte-à-faux, mais augmente le temps d'impression et la quantité de matériau nécessaire."

msgctxt "support_bottom_wall_count description"
msgid "The number of walls with which to surround support interface floor. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Le nombre de parois avec lesquelles entourer la surface inférieure de l'interface du support. L'ajout d'une paroi rend l'impression du support plus fiable et permet de mieux soutenir les saillies, mais augmente le temps d'impression et la quantité de matériau utilisé."

msgctxt "support_roof_wall_count description"
msgid "The number of walls with which to surround support interface roof. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Nombre de parois avec lesquelles entourer le toit de l'interface du support. L'ajout d'une paroi rend l'impression du support plus fiable et permet de mieux soutenir les saillies, mais augmente le temps d'impression et la quantité de matériau utilisé."

msgctxt "support_interface_wall_count description"
msgid "The number of walls with which to surround support interface. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Nombre de parois avec lesquelles entourer la surface de support. L'ajout d'une paroi rend l'impression du support plus fiable et permet de mieux soutenir les saillies, mais augmente le temps d'impression et la quantité de matériau utilisé."

msgctxt "wall_distribution_count description"
msgid "The number of walls, counted from the center, over which the variation needs to be spread. Lower values mean that the outer walls don't change in width."
msgstr "Le nombre de parois, comptées à partir du centre, sur lesquelles la variation doit être répartie. Les valeurs inférieures signifient que les parois extérieures ne changent pas en termes de largeur."

msgctxt "wall_line_count description"
msgid "The number of walls. When calculated by the wall thickness, this value is rounded to a whole number."
msgstr "Le nombre de parois. Lorsqu'elle est calculée par l'épaisseur de la paroi, cette valeur est arrondie à un nombre entier."

msgctxt "machine_nozzle_tip_outer_diameter description"
msgid "The outer diameter of the tip of the nozzle."
msgstr "Le diamètre extérieur de la pointe de la buse."

msgctxt "infill_pattern description"
msgid "The pattern of the infill material of the print. The line and zig zag infill swap direction on alternate layers, reducing material cost. The grid, triangle, tri-hexagon, cubic, octet, quarter cubic, cross and concentric patterns are fully printed every layer. Gyroid, cubic, quarter cubic and octet infill change with every layer to provide a more equal distribution of strength over each direction. Lightning infill tries to minimize the infill, by only supporting the ceiling of the object."
msgstr "Le motif du matériau de remplissage de l'impression. La ligne et le remplissage en zigzag changent de sens à chaque alternance de couche, réduisant ainsi les coûts matériels. Les motifs en grille, en triangle, tri-hexagonaux, cubiques, octaédriques, quart cubiques, entrecroisés et concentriques sont entièrement imprimés sur chaque couche. Les remplissages gyroïdes, cubiques, quart cubiques et octaédriques changent à chaque couche afin d'offrir une répartition plus égale de la solidité dans chaque direction. Le remplissage éclair tente de minimiser le remplissage, en ne supportant que le plafond de l'objet."

msgctxt "support_pattern description"
msgid "The pattern of the support structures of the print. The different options available result in sturdy or easy to remove support."
msgstr "Le motif des supports de l'impression. Les différentes options disponibles résultent en des supports difficiles ou faciles à retirer."

msgctxt "roofing_pattern description"
msgid "The pattern of the top most layers."
msgstr "Le motif des couches supérieures."

msgctxt "top_bottom_pattern description"
msgid "The pattern of the top/bottom layers."
msgstr "Le motif des couches du dessus/dessous."

msgctxt "top_bottom_pattern_0 description"
msgid "The pattern on the bottom of the print on the first layer."
msgstr "Motif au bas de l'impression sur la première couche."

msgctxt "ironing_pattern description"
msgid "The pattern to use for ironing top surfaces."
msgstr "Le motif à utiliser pour étirer les surfaces supérieures."

msgctxt "support_bottom_pattern description"
msgid "The pattern with which the floors of the support are printed."
msgstr "Le motif d'impression pour les bas de support."

msgctxt "support_interface_pattern description"
msgid "The pattern with which the interface of the support with the model is printed."
msgstr "Le motif selon lequel l'interface du support avec le modèle est imprimée."

msgctxt "support_roof_pattern description"
msgid "The pattern with which the roofs of the support are printed."
msgstr "Le motif d'impression pour les plafonds de support."

msgctxt "z_seam_position description"
msgid "The position near where to start printing each part in a layer."
msgstr "La position près de laquelle démarre l'impression de chaque partie dans une couche."

msgctxt "support_tree_angle_slow description"
msgid "The preferred angle of the branches, when they do not have to avoid the model. Use a lower angle to make them more vertical and more stable. Use a higher angle for branches to merge faster."
msgstr "Ce paramètre détermine l'angle souhaité pour les branches, lorsqu'elles n'ont pas à contourner le modèle. Si vous utilisez un angle faible, les branches seront plus verticales et plus stables. Si vous utilisez un angle élevé, les branches fusionneront plus rapidement."

msgctxt "support_tree_rest_preference description"
msgid "The preferred placement of the support structures. If structures can't be placed at the preferred location, they will be place elsewhere, even if that means placing them on the model."
msgstr "Il s'agit de l'emplacement souhaité pour les structures de support. Si les structures ne peuvent pas être placées à l'endroit souhaité, elles seront placées ailleurs, quitte à ce que ce soit sur le modèle."

msgctxt "jerk_layer_0 description"
msgid "The print maximum instantaneous velocity change for the initial layer."
msgstr "Le changement instantané maximal de vitesse pour la couche initiale."

msgctxt "machine_shape description"
msgid "The shape of the build plate without taking unprintable areas into account."
msgstr "La forme du plateau sans prendre les zones non imprimables en compte."

msgctxt "machine_head_with_fans_polygon description"
msgid "The shape of the print head. These are coordinates relative to the position of the print head, which is usually the position of its first extruder. The dimensions left and in front of the print head must be negative coordinates."
msgstr "La forme de la tête d'impression. Ce sont des coordonnées par rapport à la position de la tête d'impression, qui est généralement la position de son premier extrudeur. Les dimensions à gauche et devant la tête d'impression doivent être des coordonnées négatives."

msgctxt "cross_infill_pocket_size description"
msgid "The size of pockets at four-way crossings in the cross 3D pattern at heights where the pattern is touching itself."
msgstr "La taille de poches aux croisements à quatre branches dans le motif entrecroisé 3D, à des hauteurs où le motif se touche lui-même."

msgctxt "coasting_min_volume description"
msgid "The smallest volume an extrusion path should have before allowing coasting. For smaller extrusion paths, less pressure has been built up in the bowden tube and so the coasted volume is scaled linearly. This value should always be larger than the Coasting Volume."
msgstr "Le plus petit volume qu'un mouvement d'extrusion doit entraîner avant d'autoriser la roue libre. Pour les petits mouvements d'extrusion, une pression moindre s'est formée dans le tube bowden, de sorte que le volume déposable en roue libre est alors réduit linéairement. Cette valeur doit toujours être supérieure au volume en roue libre."

msgctxt "machine_nozzle_cool_down_speed description"
msgid "The speed (°C/s) by which the nozzle cools down averaged over the window of normal printing temperatures and the standby temperature."
msgstr "La vitesse (°C/s) à laquelle la buse refroidit, sur une moyenne de la plage de températures d'impression normales et la température en veille."

msgctxt "machine_nozzle_heat_up_speed description"
msgid "The speed (°C/s) by which the nozzle heats up averaged over the window of normal printing temperatures and the standby temperature."
msgstr "La vitesse (°C/s) à laquelle la buse chauffe, sur une moyenne de la plage de températures d'impression normales et la température en veille."

msgctxt "speed_wall_x description"
msgid "The speed at which all inner walls are printed. Printing the inner wall faster than the outer wall will reduce printing time. It works well to set this in between the outer wall speed and the infill speed."
msgstr "La vitesse à laquelle toutes les parois internes seront imprimées. L’impression de la paroi interne à une vitesse supérieure réduira le temps d'impression global. Il est bon de définir cette vitesse entre celle de l'impression de la paroi externe et du remplissage."

msgctxt "bridge_skin_speed description"
msgid "The speed at which bridge skin regions are printed."
msgstr "Vitesse à laquelle les régions de la couche extérieure du pont sont imprimées."

msgctxt "speed_infill description"
msgid "The speed at which infill is printed."
msgstr "La vitesse à laquelle le remplissage est imprimé."

msgctxt "speed_print description"
msgid "The speed at which printing happens."
msgstr "La vitesse à laquelle l'impression s'effectue."

msgctxt "raft_base_speed description"
msgid "The speed at which the base raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "La vitesse à laquelle la couche de base du radeau est imprimée. Cette couche doit être imprimée suffisamment lentement du fait que la quantité de matériau sortant de la buse est assez importante."

msgctxt "bridge_wall_speed description"
msgid "The speed at which the bridge walls are printed."
msgstr "Vitesse à laquelle les parois de pont sont imprimées."

msgctxt "cool_fan_speed_0 description"
msgid "The speed at which the fans spin at the start of the print. In subsequent layers the fan speed is gradually increased up to the layer corresponding to Regular Fan Speed at Height."
msgstr "Vitesse à laquelle les ventilateurs tournent au début de l'impression. Pour les couches suivantes, la vitesse des ventilateurs augmente progressivement jusqu'à la couche qui correspond à la vitesse régulière des ventilateurs en hauteur."

msgctxt "cool_fan_speed_min description"
msgid "The speed at which the fans spin before hitting the threshold. When a layer prints faster than the threshold, the fan speed gradually inclines towards the maximum fan speed."
msgstr "La vitesse à laquelle les ventilateurs tournent avant d'atteindre la limite. Lorsqu'une couche s'imprime plus rapidement que la limite, la vitesse du ventilateur augmente progressivement jusqu'à atteindre la vitesse maximale."

msgctxt "cool_fan_speed_max description"
msgid "The speed at which the fans spin on the minimum layer time. The fan speed gradually increases between the regular fan speed and maximum fan speed when the threshold is hit."
msgstr "La vitesse à laquelle les ventilateurs tournent sur la durée minimale d'une couche. La vitesse du ventilateur augmente progressivement entre la vitesse régulière du ventilateur et la vitesse maximale lorsque la limite est atteinte."

msgctxt "retraction_prime_speed description"
msgid "The speed at which the filament is primed during a retraction move."
msgstr "La vitesse à laquelle le filament est préparé pendant une rétraction."

msgctxt "wipe_retraction_prime_speed description"
msgid "The speed at which the filament is primed during a wipe retraction move."
msgstr "La vitesse à laquelle le filament est préparé pendant un déplacement de rétraction d'essuyage."

msgctxt "switch_extruder_prime_speed description"
msgid "The speed at which the filament is pushed back after a nozzle switch retraction."
msgstr "La vitesse à laquelle le filament est poussé vers l'arrière après une rétraction de changement de buse."

msgctxt "retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a retraction move."
msgstr "La vitesse à laquelle le filament est rétracté et préparé pendant une rétraction."

msgctxt "wipe_retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a wipe retraction move."
msgstr "La vitesse à laquelle le filament est rétracté et préparé pendant un déplacement de rétraction d'essuyage."

msgctxt "switch_extruder_retraction_speed description"
msgid "The speed at which the filament is retracted during a nozzle switch retract."
msgstr "La vitesse à laquelle le filament est rétracté pendant une rétraction de changement de buse."

msgctxt "retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a retraction move."
msgstr "La vitesse à laquelle le filament est rétracté pendant une rétraction."

msgctxt "wipe_retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a wipe retraction move."
msgstr "La vitesse à laquelle le filament est rétracté pendant un déplacement de rétraction d'essuyage."

msgctxt "switch_extruder_retraction_speeds description"
msgid "The speed at which the filament is retracted. A higher retraction speed works better, but a very high retraction speed can lead to filament grinding."
msgstr "La vitesse à laquelle le filament est rétracté. Une vitesse de rétraction plus élevée fonctionne mieux, mais une vitesse de rétraction très élevée peut causer l'écrasement du filament."

msgctxt "speed_support_bottom description"
msgid "The speed at which the floor of support is printed. Printing it at lower speed can improve adhesion of support on top of your model."
msgstr "La vitesse à laquelle le bas de support est imprimé. L'impression à une vitesse plus faible permet de renforcer l'adhésion du support au-dessus de votre modèle."

msgctxt "speed_support_infill description"
msgid "The speed at which the infill of support is printed. Printing the infill at lower speeds improves stability."
msgstr "La vitesse à laquelle le remplissage de support est imprimé. L'impression du remplissage à une vitesse plus faible permet de renforcer la stabilité."

msgctxt "raft_interface_speed description"
msgid "The speed at which the middle raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "La vitesse à laquelle la couche du milieu du radeau est imprimée. Cette couche doit être imprimée suffisamment lentement du fait que la quantité de matériau sortant de la buse est assez importante."

msgctxt "speed_wall_0 description"
msgid "The speed at which the outermost walls are printed. Printing the outer wall at a lower speed improves the final skin quality. However, having a large difference between the inner wall speed and the outer wall speed will affect quality in a negative way."
msgstr "La vitesse à laquelle les parois externes sont imprimées. L’impression de la paroi externe à une vitesse inférieure améliore la qualité finale de la coque. Néanmoins, si la différence entre la vitesse de la paroi interne et la vitesse de la paroi externe est importante, la qualité finale sera réduite."

msgctxt "speed_prime_tower description"
msgid "The speed at which the prime tower is printed. Printing the prime tower slower can make it more stable when the adhesion between the different filaments is suboptimal."
msgstr "La vitesse à laquelle la tour d'amorçage est imprimée. L'impression plus lente de la tour d'amorçage peut la rendre plus stable lorsque l'adhérence entre les différents filaments est sous-optimale."

msgctxt "cool_fan_speed description"
msgid "The speed at which the print cooling fans spin."
msgstr "La vitesse à laquelle les ventilateurs de refroidissement de l'impression tournent."

msgctxt "raft_speed description"
msgid "The speed at which the raft is printed."
msgstr "La vitesse à laquelle le radeau est imprimé."

msgctxt "speed_support_interface description"
msgid "The speed at which the roofs and floors of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "La vitesse à laquelle les plafonds et bas de support sont imprimés. Les imprimer à de plus faibles vitesses améliore la qualité des porte-à-faux."

msgctxt "speed_support_roof description"
msgid "The speed at which the roofs of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "La vitesse à laquelle les plafonds de support sont imprimés. Les imprimer à de plus faibles vitesses améliore la qualité des porte-à-faux."

msgctxt "skirt_brim_speed description"
msgid "The speed at which the skirt and brim are printed. Normally this is done at the initial layer speed, but sometimes you might want to print the skirt or brim at a different speed."
msgstr "La vitesse à laquelle la jupe et la bordure sont imprimées. Normalement, cette vitesse est celle de la couche initiale, mais il est parfois nécessaire d’imprimer la jupe ou la bordure à une vitesse différente."

msgctxt "speed_support description"
msgid "The speed at which the support structure is printed. Printing support at higher speeds can greatly reduce printing time. The surface quality of the support structure is not important since it is removed after printing."
msgstr "La vitesse à laquelle les supports sont imprimés. Imprimer les supports à une vitesse supérieure peut fortement accélérer l’impression. Par ailleurs, la qualité de la structure des supports n’a généralement pas beaucoup d’importance du fait qu'elle est retirée après l'impression."

msgctxt "raft_surface_speed description"
msgid "The speed at which the top raft layers are printed. These should be printed a bit slower, so that the nozzle can slowly smooth out adjacent surface lines."
msgstr "Vitesse à laquelle les couches du dessus du radeau sont imprimées. Elles doivent être imprimées légèrement plus lentement afin que la buse puisse lentement lisser les lignes de surface adjacentes."

msgctxt "speed_wall_x_roofing description"
msgid "The speed at which the top surface inner walls are printed."
msgstr "La vitesse à laquelle les parois internes de la surface supérieure sont imprimées."

msgctxt "speed_wall_0_roofing description"
msgid "The speed at which the top surface outermost wall is printed."
msgstr "La vitesse à laquelle la paroi externe de la surface supérieure est imprimée."

msgctxt "speed_z_hop description"
msgid "The speed at which the vertical Z movement is made for Z Hops. This is typically lower than the print speed since the build plate or machine's gantry is harder to move."
msgstr "La vitesse à laquelle le mouvement vertical en Z est effectué pour des décalages en Z. Cette vitesse est généralement inférieure à la vitesse d'impression car le plateau ou le portique de la machine est plus difficile à déplacer."

msgctxt "speed_wall description"
msgid "The speed at which the walls are printed."
msgstr "La vitesse à laquelle les parois sont imprimées."

msgctxt "speed_ironing description"
msgid "The speed at which to pass over the top surface."
msgstr "La vitesse à laquelle passer sur la surface supérieure."

msgctxt "material_break_speed description"
msgid "The speed at which to retract the filament in order to break it cleanly."
msgstr "La vitesse à laquelle rétracter le filament afin de le rompre proprement."

msgctxt "speed_roofing description"
msgid "The speed at which top surface skin layers are printed."
msgstr "La vitesse à laquelle les couches extérieures de la surface supérieure sont imprimées."

msgctxt "speed_topbottom description"
msgid "The speed at which top/bottom layers are printed."
msgstr "La vitesse à laquelle les couches du dessus/dessous sont imprimées."

msgctxt "speed_travel description"
msgid "The speed at which travel moves are made."
msgstr "La vitesse à laquelle les déplacements s'effectuent."

msgctxt "coasting_speed description"
msgid "The speed by which to move during coasting, relative to the speed of the extrusion path. A value slightly under 100% is advised, since during the coasting move the pressure in the bowden tube drops."
msgstr "Vitesse de déplacement pendant une roue libre, par rapport à la vitesse de déplacement pendant l'extrusion. Une valeur légèrement inférieure à 100 % est conseillée car, lors du mouvement en roue libre, la pression dans le tube bowden chute."

msgctxt "speed_layer_0 description"
msgid "The speed for the initial layer. A lower value is advised to improve adhesion to the build plate. Does not affect the build plate adhesion structures themselves, like brim and raft."
msgstr "La vitesse de la couche initiale. Une valeur plus faible est recommandée pour améliorer l'adhérence au plateau de fabrication. N'affecte pas les structures d'adhérence au plateau, comme la bordure et le radeau."

msgctxt "speed_print_layer_0 description"
msgid "The speed of printing for the initial layer. A lower value is advised to improve adhesion to the build plate."
msgstr "La vitesse d'impression de la couche initiale. Une valeur plus faible est recommandée pour améliorer l'adhérence au plateau."

msgctxt "speed_travel_layer_0 description"
msgid "The speed of travel moves in the initial layer. A lower value is advised to prevent pulling previously printed parts away from the build plate. The value of this setting can automatically be calculated from the ratio between the Travel Speed and the Print Speed."
msgstr "Vitesse des mouvements de déplacement dans la couche initiale. Une valeur plus faible est recommandée pour éviter que les pièces déjà imprimées ne s'écartent du plateau. La valeur de ce paramètre peut être calculée automatiquement à partir du ratio entre la vitesse des mouvements et la vitesse d'impression."

msgctxt "material_break_temperature description"
msgid "The temperature at which the filament is broken for a clean break."
msgstr "La température à laquelle le filament est cassé pour une rupture propre."

msgctxt "build_volume_temperature description"
msgid "The temperature of the environment to print in. If this is 0, the build volume temperature will not be adjusted."
msgstr "La température de l'environnement d'impression. Si cette valeur est 0, la température du volume d'impression ne sera pas ajustée."

msgctxt "material_standby_temperature description"
msgid "The temperature of the nozzle when another nozzle is currently used for printing."
msgstr "La température de la buse lorsqu'une autre buse est actuellement utilisée pour l'impression."

msgctxt "material_final_print_temperature description"
msgid "The temperature to which to already start cooling down just before the end of printing."
msgstr "La température à laquelle le refroidissement commence juste avant la fin de l'impression."

msgctxt "material_print_temperature_layer_0 description"
msgid "The temperature used for printing the first layer."
msgstr "La température utilisée pour l'impression de la première couche."

msgctxt "material_print_temperature description"
msgid "The temperature used for printing."
msgstr "Température utilisée pour l'impression."

msgctxt "material_bed_temperature_layer_0 description"
msgid "The temperature used for the heated build plate at the first layer. If this is 0, the build plate is left unheated during the first layer."
msgstr "Température utilisée pour le plateau de fabrication chauffé à la première couche. Si elle est définie sur 0, le plateau de fabrication ne sera pas chauffé lors de la première couche."

msgctxt "material_bed_temperature description"
msgid "The temperature used for the heated build plate. If this is 0, the build plate is left unheated."
msgstr "Température utilisée pour le plateau de fabrication chauffé. Si elle est définie sur 0, le plateau de fabrication ne sera pas chauffé."

msgctxt "material_break_preparation_temperature description"
msgid "The temperature used to purge material, should be roughly equal to the highest possible printing temperature."
msgstr "La température utilisée pour purger le matériau devrait être à peu près égale à la température d'impression la plus élevée possible."

msgctxt "bottom_thickness description"
msgid "The thickness of the bottom layers in the print. This value divided by the layer height defines the number of bottom layers."
msgstr "L’épaisseur des couches du dessous dans l'impression. Cette valeur divisée par la hauteur de la couche définit le nombre de couches du dessous."

msgctxt "skin_edge_support_thickness description"
msgid "The thickness of the extra infill that supports skin edges."
msgstr "L'épaisseur du remplissage supplémentaire qui soutient les bords de la couche."

msgctxt "support_interface_height description"
msgid "The thickness of the interface of the support where it touches with the model on the bottom or the top."
msgstr "L'épaisseur de l'interface du support à l'endroit auquel il touche le modèle, sur le dessous ou le dessus."

msgctxt "support_bottom_height description"
msgid "The thickness of the support floors. This controls the number of dense layers that are printed on top of places of a model on which support rests."
msgstr "L'épaisseur des bas de support. Cela contrôle le nombre de couches denses imprimées sur le dessus des endroits d'un modèle sur lequel le support repose."

msgctxt "support_roof_height description"
msgid "The thickness of the support roofs. This controls the amount of dense layers at the top of the support on which the model rests."
msgstr "L'épaisseur des plafonds de support. Cela contrôle la quantité de couches denses sur le dessus du support sur lequel le modèle repose."

msgctxt "top_thickness description"
msgid "The thickness of the top layers in the print. This value divided by the layer height defines the number of top layers."
msgstr "L’épaisseur des couches du dessus dans l'impression. Cette valeur divisée par la hauteur de la couche définit le nombre de couches du dessus."

msgctxt "top_bottom_thickness description"
msgid "The thickness of the top/bottom layers in the print. This value divided by the layer height defines the number of top/bottom layers."
msgstr "L’épaisseur des couches du dessus/dessous dans l'impression. Cette valeur divisée par la hauteur de la couche définit le nombre de couches du dessus/dessous."

msgctxt "wall_thickness description"
msgid "The thickness of the walls in the horizontal direction. This value divided by the wall line width defines the number of walls."
msgstr "Épaisseur des parois en sens horizontal. Cette valeur divisée par la largeur de la ligne de la paroi définit le nombre de parois."

msgctxt "infill_sparse_thickness description"
msgid "The thickness per layer of infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "L'épaisseur par couche de matériau de remplissage. Cette valeur doit toujours être un multiple de la hauteur de la couche et arrondie."

msgctxt "support_infill_sparse_thickness description"
msgid "The thickness per layer of support infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "L'épaisseur par couche de matériau de remplissage de support. Cette valeur doit toujours être un multiple de la hauteur de la couche et arrondie."

msgctxt "machine_gcode_flavor description"
msgid "The type of g-code to be generated."
msgstr "Type de G-Code à générer."

msgctxt "coasting_volume description"
msgid "The volume otherwise oozed. This value should generally be close to the nozzle diameter cubed."
msgstr "Volume de matière qui devrait suinter de la buse. Cette valeur doit généralement rester proche du diamètre de la buse au cube."

msgctxt "machine_width description"
msgid "The width (X-direction) of the printable area."
msgstr "La largeur (sens X) de la zone imprimable."

msgctxt "support_brim_width description"
msgid "The width of the brim to print underneath the support. A larger brim enhances adhesion to the build plate, at the cost of some extra material."
msgstr "Largeur de la bordure à imprimer sous le support. Une plus grande bordure améliore l'adhérence au plateau, mais demande un peu de matériau supplémentaire."

msgctxt "interlocking_beam_width description"
msgid "The width of the interlocking structure beams."
msgstr "La largeur des attaches de la structure de connexion."

msgctxt "prime_tower_base_size description"
msgid "The width of the prime tower brim/base. A larger base enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "La largeur du bord/de la base de la tour d'amorçage. Une base plus large améliore l'adhésion au plateau, mais réduit également la zone d'impression effective."

msgctxt "prime_tower_size description"
msgid "The width of the prime tower."
msgstr "La largeur de la tour d'amorçage."

msgctxt "magic_fuzzy_skin_thickness description"
msgid "The width within which to jitter. It's advised to keep this below the outer wall width, since the inner walls are unaltered."
msgstr "Largeur autorisée pour l'agitation aléatoire. Il est conseillé de garder cette valeur inférieure à l'épaisseur de la paroi extérieure, ainsi, les parois intérieures ne seront pas altérées."

msgctxt "retraction_extrusion_window description"
msgid "The window in which the maximum retraction count is enforced. This value should be approximately the same as the retraction distance, so that effectively the number of times a retraction passes the same patch of material is limited."
msgstr "L'intervalle dans lequel le nombre maximal de rétractions est incrémenté. Cette valeur doit être du même ordre de grandeur que la distance de rétraction, limitant ainsi le nombre de mouvements de rétraction sur une même portion de matériau."

msgctxt "prime_tower_position_x description"
msgid "The x coordinate of the position of the prime tower."
msgstr "Les coordonnées X de la position de la tour d'amorçage."

msgctxt "prime_tower_position_y description"
msgid "The y coordinate of the position of the prime tower."
msgstr "Les coordonnées Y de la position de la tour d'amorçage."

msgctxt "support_meshes_present description"
msgid "There are support meshes present in the scene. This setting is controlled by Cura."
msgstr "Un maillage de support est présent sur la scène. Ce paramètre est contrôlé par Cura."

msgctxt "bridge_wall_coast description"
msgid "This controls the distance the extruder should coast immediately before a bridge wall begins. Coasting before the bridge starts can reduce the pressure in the nozzle and may produce a flatter bridge."
msgstr "Ce paramètre contrôle la distance que l'extrudeuse doit parcourir en roue libre immédiatement avant le début d'une paroi de pont. L'utilisation de la roue libre avant le début du pont permet de réduire la pression à l'intérieur de la buse et d'obtenir un pont plus plat."

msgctxt "raft_smoothing description"
msgid "This setting controls how much inner corners in the raft outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Ce paramètre définit combien d'angles intérieurs sont arrondis dans le contour de radeau. Les angles internes sont arrondis en un demi-cercle avec un rayon égal à la valeur indiquée ici. Ce paramètre élimine également les cavités dans le contour de radeau qui sont d'une taille inférieure à ce cercle."

msgctxt "retraction_count_max description"
msgid "This setting limits the number of retractions occurring within the minimum extrusion distance window. Further retractions within this window will be ignored. This avoids retracting repeatedly on the same piece of filament, as that can flatten the filament and cause grinding issues."
msgstr "Ce paramètre limite le nombre de rétractions dans l'intervalle de distance minimal d'extrusion. Les rétractions qui dépassent cette valeur seront ignorées. Cela évite les rétractions répétitives sur le même morceau de filament, car cela risque de l’aplatir et de générer des problèmes d’écrasement."

msgctxt "draft_shield_enabled description"
msgid "This will create a wall around the model, which traps (hot) air and shields against exterior airflow. Especially useful for materials which warp easily."
msgstr "Cela créera une paroi autour du modèle qui retient l'air (chaud) et protège contre les courants d'air. Particulièrement utile pour les matériaux qui se soulèvent facilement."

msgctxt "support_tree_tip_diameter label"
msgid "Tip Diameter"
msgstr "Diamètre des extrémités"

msgctxt "material_shrinkage_percentage_xy description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the XY-direction (horizontally)."
msgstr "Pour compenser le rétrécissement du matériau lors du refroidissement, le modèle sera mis à l'échelle avec ce facteur dans la direction XY (horizontalement)."

msgctxt "material_shrinkage_percentage_z description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the Z-direction (vertically)."
msgstr "Pour compenser le rétrécissement du matériau lors du refroidissement, le modèle sera mis à l'échelle avec ce facteur dans la direction Z (verticalement)."

msgctxt "material_shrinkage_percentage description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor."
msgstr "Pour compenser la contraction du matériau lors de son refroidissement, le modèle est mis à l'échelle avec ce facteur."

msgctxt "top_layers label"
msgid "Top Layers"
msgstr "Couches supérieures"

msgctxt "top_skin_expand_distance label"
msgid "Top Skin Expand Distance"
msgstr "Distance d'expansion de la couche extérieure supérieure"

msgctxt "top_skin_preshrink label"
msgid "Top Skin Removal Width"
msgstr "Largeur de retrait de la couche extérieure supérieure"

msgctxt "acceleration_wall_x_roofing label"
msgid "Top Surface Inner Wall Acceleration"
msgstr "Accélération des parois internes de la surface supérieure"

msgctxt "jerk_wall_x_roofing label"
msgid "Top Surface Inner Wall Jerk"
msgstr "Saccade des parois internes de la surface supérieure"

msgctxt "speed_wall_x_roofing label"
msgid "Top Surface Inner Wall Speed"
msgstr "Vitesse d'impression des parois internes de la surface supérieure"

msgctxt "wall_x_material_flow_roofing label"
msgid "Top Surface Inner Wall(s) Flow"
msgstr "Débit des parois internes de la surface supérieure"

msgctxt "acceleration_wall_0_roofing label"
msgid "Top Surface Outer Wall Acceleration"
msgstr "Accélération de la paroi externe de la surface supérieure"

msgctxt "wall_0_material_flow_roofing label"
msgid "Top Surface Outer Wall Flow"
msgstr "Débit de la paroi externe de la surface supérieure"

msgctxt "jerk_wall_0_roofing label"
msgid "Top Surface Outer Wall Jerk"
msgstr "Saccade de la paroi externe de la surface supérieure"

msgctxt "speed_wall_0_roofing label"
msgid "Top Surface Outer Wall Speed"
msgstr "Vitesse d'impression de la paroi externe de la surface supérieure"

msgctxt "acceleration_roofing label"
msgid "Top Surface Skin Acceleration"
msgstr "Accélération de couche extérieure de surface supérieure"

msgctxt "roofing_extruder_nr label"
msgid "Top Surface Skin Extruder"
msgstr "Extrudeuse de couche extérieure de la surface supérieure"

msgctxt "roofing_material_flow label"
msgid "Top Surface Skin Flow"
msgstr "Débit de la surface du dessus"

msgctxt "jerk_roofing label"
msgid "Top Surface Skin Jerk"
msgstr "Saccade de couches extérieures de la surface supérieure"

msgctxt "roofing_layer_count label"
msgid "Top Surface Skin Layers"
msgstr "Couches extérieures de la surface supérieure"

msgctxt "roofing_angles label"
msgid "Top Surface Skin Line Directions"
msgstr "Sens de lignes de couche extérieure de surface supérieure"

msgctxt "roofing_line_width label"
msgid "Top Surface Skin Line Width"
msgstr "Largeur de ligne de couche extérieure de la surface supérieure"

msgctxt "roofing_pattern label"
msgid "Top Surface Skin Pattern"
msgstr "Motif de couche extérieure de surface supérieure"

msgctxt "speed_roofing label"
msgid "Top Surface Skin Speed"
msgstr "Vitesse de la couche extérieure de la surface supérieure"

msgctxt "top_thickness label"
msgid "Top Thickness"
msgstr "Épaisseur du dessus"

msgctxt "max_skin_angle_for_expansion description"
msgid "Top and/or bottom surfaces of your object with an angle larger than this setting, won't have their top/bottom skin expanded. This avoids expanding the narrow skin areas that are created when the model surface has a near vertical slope. An angle of 0° is horizontal and will cause no skin to be expanded, while an angle of 90° is vertical and will cause all skin to be expanded."
msgstr "Les couches extérieures supérieures / inférieures des surfaces supérieures et / ou inférieures de votre objet possédant un angle supérieur à ce paramètre ne seront pas étendues. Cela permet d'éviter d'étendre les zones de couche extérieure étroites qui sont créées lorsque la surface du modèle possède une pente proche de la verticale. Un angle de 0° est horizontal et évitera l'extension des couches ; un angle de 90° est vertical et entraînera l'extension de toutes les couches."

msgctxt "top_bottom description"
msgid "Top/Bottom"
msgstr "Haut / bas"

msgctxt "top_bottom label"
msgid "Top/Bottom"
msgstr "Haut / bas"

msgctxt "acceleration_topbottom label"
msgid "Top/Bottom Acceleration"
msgstr "Accélération du dessus/dessous"

msgctxt "top_bottom_extruder_nr label"
msgid "Top/Bottom Extruder"
msgstr "Extrudeuse du dessus/dessous"

msgctxt "skin_material_flow label"
msgid "Top/Bottom Flow"
msgstr "Débit du dessus/dessous"

msgctxt "jerk_topbottom label"
msgid "Top/Bottom Jerk"
msgstr "Saccade du dessus/dessous"

msgctxt "skin_angles label"
msgid "Top/Bottom Line Directions"
msgstr "Sens de la ligne du dessus / dessous"

msgctxt "skin_line_width label"
msgid "Top/Bottom Line Width"
msgstr "Largeur de la ligne du dessus/dessous"

msgctxt "top_bottom_pattern label"
msgid "Top/Bottom Pattern"
msgstr "Motif du dessus/dessous"

msgctxt "speed_topbottom label"
msgid "Top/Bottom Speed"
msgstr "Vitesse d'impression du dessus/dessous"

msgctxt "top_bottom_thickness label"
msgid "Top/Bottom Thickness"
msgstr "Épaisseur du dessus/dessous"

msgctxt "support_type option buildplate"
msgid "Touching Buildplate"
msgstr "En contact avec le plateau"

msgctxt "support_tower_diameter label"
msgid "Tower Diameter"
msgstr "Diamètre de la tour"

msgctxt "support_tower_roof_angle label"
msgid "Tower Roof Angle"
msgstr "Angle du toit de la tour"

msgctxt "mesh_rotation_matrix description"
msgid "Transformation matrix to be applied to the model when loading it from file."
msgstr "Matrice de transformation à appliquer au modèle lors de son chargement depuis le fichier."

msgctxt "travel label"
msgid "Travel"
msgstr "Déplacement"

msgctxt "acceleration_travel label"
msgid "Travel Acceleration"
msgstr "Accélération de déplacement"

msgctxt "travel_avoid_distance label"
msgid "Travel Avoid Distance"
msgstr "Distance d'évitement du déplacement"

msgctxt "jerk_travel label"
msgid "Travel Jerk"
msgstr "Saccade de déplacement"

msgctxt "speed_travel label"
msgid "Travel Speed"
msgstr "Vitesse de déplacement"

msgctxt "magic_mesh_surface_mode description"
msgid "Treat the model as a surface only, a volume, or volumes with loose surfaces. The normal print mode only prints enclosed volumes. \"Surface\" prints a single wall tracing the mesh surface with no infill and no top/bottom skin. \"Both\" prints enclosed volumes like normal and any remaining polygons as surfaces."
msgstr "Traite le modèle comme surface seule, un volume ou des volumes avec des surfaces seules. Le mode d'impression normal imprime uniquement des volumes fermés. « Surface » imprime une paroi seule autour de la surface de la maille, sans remplissage ni couche du dessus/dessous. « Les deux » imprime des volumes fermés comme en mode normal et les polygones restants comme surfaces."

msgctxt "support_structure option tree"
msgid "Tree"
msgstr "Arborescence"

msgctxt "infill_pattern option trihexagon"
msgid "Tri-Hexagon"
msgstr "Trihexagonal"

msgctxt "infill_pattern option triangles"
msgid "Triangles"
msgstr "Triangles"

msgctxt "support_bottom_pattern option triangles"
msgid "Triangles"
msgstr "Triangles"

msgctxt "support_interface_pattern option triangles"
msgid "Triangles"
msgstr "Triangles"

msgctxt "support_pattern option triangles"
msgid "Triangles"
msgstr "Triangles"

msgctxt "support_roof_pattern option triangles"
msgid "Triangles"
msgstr "Triangles"

msgctxt "support_tree_max_diameter label"
msgid "Trunk Diameter"
msgstr "Diamètre du tronc"

msgctxt "machine_gcode_flavor option UltiGCode"
msgid "Ultimaker 2"
msgstr "Ultimaker 2"

msgctxt "meshfix_union_all label"
msgid "Union Overlapping Volumes"
msgstr "Joindre les volumes se chevauchant"

msgctxt "bridge_wall_min_length description"
msgid "Unsupported walls shorter than this will be printed using the normal wall settings. Longer unsupported walls will be printed using the bridge wall settings."
msgstr "Les parois non supportées dont la longueur est inférieure à cette valeur seront imprimées selon les paramètres de parois normaux, tandis que celles dont la longueur est supérieure à cette valeur seront imprimées selon les paramètres de parois du pont."

msgctxt "adaptive_layer_height_enabled label"
msgid "Use Adaptive Layers"
msgstr "Utiliser des couches adaptatives"

msgctxt "support_use_towers label"
msgid "Use Towers"
msgstr "Utilisation de tours"

msgctxt "acceleration_travel_enabled description"
msgid "Use a separate acceleration rate for travel moves. If disabled, travel moves will use the acceleration value of the printed line at their destination."
msgstr "Utilisez un taux d'accélération distinct pour les déplacements. Si cette option est désactivée, les déplacements utiliseront la même accélération que celle de la ligne imprimée à l'emplacement cible."

msgctxt "jerk_travel_enabled description"
msgid "Use a separate jerk rate for travel moves. If disabled, travel moves will use the jerk value of the printed line at their destination."
msgstr "Utilisez un taux de saccades différent pour les déplacements. Si cette option est désactivée, les déplacements utiliseront les mêmes saccades que celles de la ligne imprimée à l'emplacement cible."

msgctxt "relative_extrusion description"
msgid "Use relative extrusion rather than absolute extrusion. Using relative E-steps makes for easier post-processing of the g-code. However, it's not supported by all printers and it may produce very slight deviations in the amount of deposited material compared to absolute E-steps. Irrespective of this setting, the extrusion mode will always be set to absolute before any g-code script is output."
msgstr "Utiliser l'extrusion relative au lieu de l'extrusion absolue. L'utilisation de pas E relatifs facilite le post-traitement du G-Code. Toutefois, cela n'est pas pris en charge par toutes les imprimantes et peut occasionner de très légers écarts dans la quantité de matériau déposé, par rapport à l'utilisation des pas E absolus. Indépendamment de ce paramètre, le mode d'extrusion sera défini par défaut comme absolu avant qu'un quelconque script de G-Code soit produit."

msgctxt "support_use_towers description"
msgid "Use specialized towers to support tiny overhang areas. These towers have a larger diameter than the region they support. Near the overhang the towers' diameter decreases, forming a roof."
msgstr "Utilise des tours spéciales pour soutenir de petites zones en porte-à-faux. Le diamètre de ces tours est plus large que la zone qu’elles soutiennent. Près du porte-à-faux, le diamètre des tours diminue pour former un toit."

msgctxt "infill_mesh description"
msgid "Use this mesh to modify the infill of other meshes with which it overlaps. Replaces infill regions of other meshes with regions for this mesh. It's suggested to only print one Wall and no Top/Bottom Skin for this mesh."
msgstr "Utiliser cette maille pour modifier le remplissage d'autres mailles qu'elle chevauche. Remplace les régions de remplissage d'autres mailles par des régions de cette maille. Il est conseillé d'imprimer uniquement une Paroi et pas de Couche du dessus/dessous pour cette maille."

msgctxt "support_mesh description"
msgid "Use this mesh to specify support areas. This can be used to generate support structure."
msgstr "Utiliser ce maillage pour spécifier des zones de support. Cela peut être utilisé pour générer une structure de support."

msgctxt "anti_overhang_mesh description"
msgid "Use this mesh to specify where no part of the model should be detected as overhang. This can be used to remove unwanted support structure."
msgstr "Utiliser cette maille pour préciser à quel endroit aucune partie du modèle doit être détectée comme porte-à-faux. Cette option peut être utilisée pour supprimer la structure de support non souhaitée."

msgctxt "z_seam_type option back"
msgid "User Specified"
msgstr "Utilisateur spécifié"

msgctxt "material_shrinkage_percentage_z label"
msgid "Vertical Scaling Factor Shrinkage Compensation"
msgstr "Compensation du rétrécissement du facteur d'échelle verticale"

msgctxt "slicing_tolerance description"
msgid "Vertical tolerance in the sliced layers. The contours of a layer are normally generated by taking cross sections through the middle of each layer's thickness (Middle). Alternatively each layer can have the areas which fall inside of the volume throughout the entire thickness of the layer (Exclusive) or a layer has the areas which fall inside anywhere within the layer (Inclusive). Inclusive retains the most details, Exclusive makes for the best fit and Middle stays closest to the original surface."
msgstr "Tolérance verticale dans les couches découpées. Les contours d'une couche sont normalement générés en faisant passer les sections entrecroisées au milieu de chaque épaisseur de couche (Milieu). Alternativement, chaque couche peut posséder des zones situées à l'intérieur du volume à travers toute l'épaisseur de la couche (Exclusif) ou une couche peut avoir des zones situées à l'intérieur à tout endroit dans la couche (Inclusif). L'option Inclusif permet de conserver le plus de détails ; l'option Exclusif permet d'obtenir une adaptation optimale ; l'option Milieu permet de rester proche de la surface d'origine."

msgctxt "material_bed_temp_wait label"
msgid "Wait for Build Plate Heatup"
msgstr "Attendre le chauffage du plateau"

msgctxt "material_print_temp_wait label"
msgid "Wait for Nozzle Heatup"
msgstr "Attendre le chauffage de la buse"

msgctxt "acceleration_wall label"
msgid "Wall Acceleration"
msgstr "Accélération de la paroi"

msgctxt "wall_distribution_count label"
msgid "Wall Distribution Count"
msgstr "Nombre de distributions des parois"

msgctxt "wall_extruder_nr label"
msgid "Wall Extruder"
msgstr "Extrudeuse de paroi"

msgctxt "wall_material_flow label"
msgid "Wall Flow"
msgstr "Débit de paroi"

msgctxt "jerk_wall label"
msgid "Wall Jerk"
msgstr "Saccade de paroi"

msgctxt "wall_line_count label"
msgid "Wall Line Count"
msgstr "Nombre de lignes de la paroi"

msgctxt "wall_line_width label"
msgid "Wall Line Width"
msgstr "Largeur de ligne de la paroi"

msgctxt "inset_direction label"
msgid "Wall Ordering"
msgstr "Ordre des parois"

msgctxt "speed_wall label"
msgid "Wall Speed"
msgstr "Vitesse d'impression de la paroi"

msgctxt "wall_thickness label"
msgid "Wall Thickness"
msgstr "Épaisseur de la paroi"

msgctxt "wall_transition_length label"
msgid "Wall Transition Length"
msgstr "Longueur de transition de la paroi"

msgctxt "wall_transition_filter_distance label"
msgid "Wall Transitioning Filter Distance"
msgstr "Distance du filtre de transition des parois"

msgctxt "wall_transition_filter_deviation label"
msgid "Wall Transitioning Filter Margin"
msgstr "Marge du filtre de transition des parois"

msgctxt "wall_transition_angle label"
msgid "Wall Transitioning Threshold Angle"
msgstr "Angle du seuil de transition de la paroi"

msgctxt "shell label"
msgid "Walls"
msgstr "Parois"

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either."
msgstr "Les parois ayant un angle supérieur à cette valeur seront imprimées en utilisant les paramètres de parois en porte-à-faux. Si la valeur est 90, aucune paroi ne sera considérée comme étant en porte-à-faux. La saillie soutenue par le support ne sera pas non plus considérée comme étant en porte-à-faux."

msgctxt "support_interface_skip_height description"
msgid "When checking where there's model above and below the support, take steps of the given height. Lower values will slice slower, while higher values may cause normal support to be printed in some places where there should have been support interface."
msgstr "Lors de la vérification de l'emplacement d'un modèle au-dessus et en-dessous du support, effectuer des étapes de la hauteur définie. Des valeurs plus faibles découperont plus lentement, tandis que des valeurs plus élevées peuvent causer l'impression d'un support normal à des endroits où il devrait y avoir une interface de support."

msgctxt "meshfix_fluid_motion_enabled description"
msgid "When enabled tool paths are corrected for printers with smooth motion planners. Small movements that deviate from the general tool path direction are smoothed to improve fluid motions."
msgstr "Lorsqu'ils sont activés, les parcours d'outils sont corrigés pour les imprimantes dotées de planificateurs de mouvements fluides. Les petits mouvements qui s'écartent de la direction générale de la trajectoire de l'outil sont lissés pour optimiser la fluidité des mouvements."

msgctxt "infill_enable_travel_optimization description"
msgid "When enabled, the order in which the infill lines are printed is optimized to reduce the distance travelled. The reduction in travel time achieved very much depends on the model being sliced, infill pattern, density, etc. Note that, for some models that have many small areas of infill, the time to slice the model may be greatly increased."
msgstr "Lorsque cette option est activée, l'ordre dans lequel les lignes de remplissage sont imprimées est optimisé pour réduire la distance parcourue. La réduction du temps de parcours dépend en grande partie du modèle à découper, du type de remplissage, de la densité, etc. Remarque : pour certains modèles possédant beaucoup de petites zones de remplissage, le temps de découpe du modèle peut en être considérablement augmenté."

msgctxt "support_fan_enable description"
msgid "When enabled, the print cooling fan speed is altered for the skin regions immediately above the support."
msgstr "Lorsque cette fonction est activée, la vitesse du ventilateur de refroidissement de l'impression est modifiée pour les régions de la couche extérieure situées immédiatement au-dessus du support."

msgctxt "z_seam_relative description"
msgid "When enabled, the z seam coordinates are relative to each part's centre. When disabled, the coordinates define an absolute position on the build plate."
msgstr "Si cette option est activée, les coordonnées de la jointure z sont relatives au centre de chaque partie. Si elle est désactivée, les coordonnées définissent une position absolue sur le plateau."

msgctxt "retraction_combing_max_distance description"
msgid "When greater than zero, combing travel moves that are longer than this distance will use retraction. If set to zero, there is no maximum and combing moves will not use retraction."
msgstr "Lorsque cette distance est supérieure à zéro, les déplacements de détour qui sont plus longs que cette distance utiliseront la rétraction. Si elle est définie sur zéro, il n'y a pas de maximum et les mouvements de détour n'utiliseront pas la rétraction."

msgctxt "hole_xy_offset_max_diameter description"
msgid "When greater than zero, the Hole Horizontal Expansion is gradually applied on small holes (small holes are expanded more). When set to zero the Hole Horizontal Expansion will be applied to all holes. Holes larger than the Hole Horizontal Expansion Max Diameter are not expanded."
msgstr "Lorsque le diamètre est supérieur à zéro, l'expansion horizontale des trous est progressivement appliquée aux petits trous (ces derniers sont élargis). Lorsque le diamètre est défini sur zéro, l'expansion horizontale des trous est appliquée à tous les trous. Les trous dont le diamètre est supérieur au diamètre maximal défini pour l'expansion horizontale des trous ne sont pas élargis."

msgctxt "hole_xy_offset description"
msgid "When greater than zero, the Hole Horizontal Expansion is the amount of offset applied to all holes in each layer. Positive values increase the size of the holes, negative values reduce the size of the holes. When this setting is enabled it can be further tuned with Hole Horizontal Expansion Max Diameter."
msgstr "Lorsqu'elle est supérieure à zéro, l'expansion horizontale du trou correspond à la quantité de décalage appliquée à la totalité des trous de chaque couche. Les valeurs positives augmentent la taille des trous, les valeurs négatives réduisent la taille des trous. Lorsque ce paramètre est activé, il peut être ajusté davantage avec le diamètre maximum d'expansion horizontale du trou."

msgctxt "bridge_skin_material_flow description"
msgid "When printing bridge skin regions, the amount of material extruded is multiplied by this value."
msgstr "Lors de l'impression des régions de la couche extérieure du pont, la quantité de matériau extrudé est multipliée par cette valeur."

msgctxt "bridge_wall_material_flow description"
msgid "When printing bridge walls, the amount of material extruded is multiplied by this value."
msgstr "Lors de l'impression des parois de pont, la quantité de matériau extrudé est multipliée par cette valeur."

msgctxt "bridge_skin_material_flow_2 description"
msgid "When printing the second bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "Lors de l'impression de la deuxième couche extérieure du pont, la quantité de matériau extrudé est multipliée par cette valeur."

msgctxt "bridge_skin_material_flow_3 description"
msgid "When printing the third bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "Lors de l'impression de la troisième couche extérieure du pont, la quantité de matériau extrudé est multipliée par cette valeur."

msgctxt "cool_lift_head description"
msgid "When the minimum speed is hit because of minimum layer time, lift the head away from the print and wait the extra time until the minimum layer time is reached."
msgstr "Lorsque la vitesse minimale est atteinte à cause de la durée minimale d'une couche, relève la tête de l'impression et attend que la durée supplémentaire jusqu'à la durée minimale d'une couche soit atteinte."

msgctxt "skin_no_small_gaps_heuristic description"
msgid "When the model has small vertical gaps of only a few layers, there should normally be skin around those layers in the narrow space. Enable this setting to not generate skin if the vertical gap is very small. This improves printing time and slicing time, but technically leaves infill exposed to the air."
msgstr "Lorsque le modèle comporte de petits trous verticaux de quelques couches seulement, il doit normalement y avoir une couche autour de celles-ci dans l'espace étroit. Activez ce paramètre pour ne pas générer de couche si le trou vertical est très petit. Cela améliore le temps d'impression et le temps de découpage, mais laisse techniquement le remplissage exposé à l'air."

msgctxt "wall_transition_angle description"
msgid "When to create transitions between even and odd numbers of walls. A wedge shape with an angle greater than this setting will not have transitions and no walls will be printed in the center to fill the remaining space. Reducing this setting reduces the number and length of these center walls, but may leave gaps or overextrude."
msgstr "Quand créer des transitions entre un nombre uniforme et impair de parois. Une forme de coin dont l'angle est supérieur à ce paramètre n'aura pas de transitions et aucune paroi ne sera imprimée au centre pour remplir l'espace restant. En réduisant ce paramètre, on réduit le nombre et la longueur de ces parois centrales, mais on risque de laisser des trous ou sur-extruder."

msgctxt "wall_transition_length description"
msgid "When transitioning between different numbers of walls as the part becomes thinner, a certain amount of space is allotted to split or join the wall lines."
msgstr "Lorsque l'on passe d'un nombre de parois à un autre, au fur et à mesure que la pièce s'amincit, un certain espace est alloué pour diviser ou joindre les lignes de parois."

msgctxt "wipe_hop_enable description"
msgid "When wiping, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "Lors de l'essuyage, le plateau de fabrication est abaissé pour créer un espace entre la buse et l'impression. Cela évite que la buse ne touche l'impression pendant les déplacements, réduisant ainsi le risque de heurter l'impression à partir du plateau de fabrication."

msgctxt "retraction_hop_enabled description"
msgid "Whenever a retraction is done, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "À chaque rétraction, le plateau est abaissé pour créer un espace entre la buse et l'impression. Cela évite que la buse ne touche l'impression pendant les déplacements, réduisant ainsi le risque de heurter l'impression à partir du plateau."

msgctxt "support_xy_overrides_z description"
msgid "Whether the Support X/Y Distance overrides the Support Z Distance or vice versa. When X/Y overrides Z the X/Y distance can push away the support from the model, influencing the actual Z distance to the overhang. We can disable this by not applying the X/Y distance around overhangs."
msgstr "Si la Distance X/Y des supports annule la Distance Z des supports ou inversement. Lorsque X/Y annule Z, la distance X/Y peut écarter le support du modèle, influençant ainsi la distance Z réelle par rapport au porte-à-faux. Nous pouvons désactiver cela en n'appliquant pas la distance X/Y autour des porte-à-faux."

msgctxt "machine_center_is_zero description"
msgid "Whether the X/Y coordinates of the zero position of the printer is at the center of the printable area."
msgstr "Si les coordonnées X/Y de la position zéro de l'imprimante se situent au centre de la zone imprimable."

msgctxt "machine_endstop_positive_direction_x description"
msgid "Whether the endstop of the X axis is in the positive direction (high X coordinate) or negative (low X coordinate)."
msgstr "Détermine si la butée de l'axe X est en sens positif (haute coordonnée X) ou négatif (basse coordonnée X)."

msgctxt "machine_endstop_positive_direction_y description"
msgid "Whether the endstop of the Y axis is in the positive direction (high Y coordinate) or negative (low Y coordinate)."
msgstr "Détermine si la butée de l'axe Y est en sens positif (haute coordonnée Y) ou négatif (basse coordonnée Y)."

msgctxt "machine_endstop_positive_direction_z description"
msgid "Whether the endstop of the Z axis is in the positive direction (high Z coordinate) or negative (low Z coordinate)."
msgstr "Détermine si la butée de l'axe Z est en sens positif (haute coordonnée Z) ou négatif (basse coordonnée Z)."

msgctxt "machine_extruders_share_heater description"
msgid "Whether the extruders share a single heater rather than each extruder having its own heater."
msgstr "Si les extrudeurs partagent un seul chauffage au lieu que chaque extrudeur ait son propre chauffage."

msgctxt "machine_extruders_share_nozzle description"
msgid "Whether the extruders share a single nozzle rather than each extruder having its own nozzle. When set to true, it is expected that the printer-start gcode script properly sets up all extruders in an initial retraction state that is known and mutually compatible (either zero or one filament not retracted); in that case the initial retraction status is described, per extruder, by the 'machine_extruders_shared_nozzle_initial_retraction' parameter."
msgstr "Lorsque les extrudeuses partagent une seule buse au lieu que chaque extrudeuse ait sa propre buse. Lorsqu'il est défini à true, le script gcode de démarrage de l'imprimante doit configurer correctement toutes les extrudeuses dans un état de rétraction initial connu et mutuellement compatible (zéro ou un filament non rétracté) ; dans ce cas, l'état de rétraction initial est décrit, par extrudeuse, par le paramètre 'machine_extruders_shared_nozzle_initial_retraction'."

msgctxt "machine_heated_bed description"
msgid "Whether the machine has a heated build plate present."
msgstr "Si la machine a un plateau chauffé présent."

msgctxt "machine_heated_build_volume description"
msgid "Whether the machine is able to stabilize the build volume temperature."
msgstr "Si la machine est capable de stabiliser la température du volume d'impression."

msgctxt "center_object description"
msgid "Whether to center the object on the middle of the build platform (0,0), instead of using the coordinate system in which the object was saved."
msgstr "S'il faut centrer l'objet au milieu du plateau d'impression (0,0) au lieu d'utiliser le système de coordonnées dans lequel l'objet a été enregistré."

msgctxt "machine_nozzle_temp_enabled description"
msgid "Whether to control temperature from Cura. Turn this off to control nozzle temperature from outside of Cura."
msgstr "Contrôler ou non la température depuis Cura. Désactivez cette option pour contrôler la température de la buse depuis une source autre que Cura."

msgctxt "material_bed_temp_prepend description"
msgid "Whether to include build plate temperature commands at the start of the gcode. When the start_gcode already contains build plate temperature commands Cura frontend will automatically disable this setting."
msgstr "Inclure ou non les commandes de température du plateau au début du gcode. Si le gcode_démarrage contient déjà les commandes de température du plateau, l'interface Cura désactive automatiquement ce paramètre."

msgctxt "material_print_temp_prepend description"
msgid "Whether to include nozzle temperature commands at the start of the gcode. When the start_gcode already contains nozzle temperature commands Cura frontend will automatically disable this setting."
msgstr "Inclure ou non les commandes de température de la buse au début du gcode. Si le gcode_démarrage contient déjà les commandes de température de la buse, l'interface Cura désactive automatiquement ce paramètre."

msgctxt "clean_between_layers description"
msgid "Whether to include nozzle wipe G-Code between layers (maximum 1 per layer). Enabling this setting could influence behavior of retract at layer change. Please use Wipe Retraction settings to control retraction at layers where the wipe script will be working."
msgstr "Inclure ou non le G-Code d'essuyage de la buse entre les couches (maximum 1 par couche). L'activation de ce paramètre peut influencer le comportement de la rétraction lors du changement de couche. Veuillez utiliser les paramètres de rétraction d'essuyage pour contrôler la rétraction aux couches où le script d'essuyage sera exécuté."

msgctxt "material_bed_temp_wait description"
msgid "Whether to insert a command to wait until the build plate temperature is reached at the start."
msgstr "Insérer ou non une commande pour attendre que la température du plateau soit atteinte au démarrage."

msgctxt "prime_blob_enable description"
msgid "Whether to prime the filament with a blob before printing. Turning this setting on will ensure that the extruder will have material ready at the nozzle before printing. Printing Brim or Skirt can act like priming too, in which case turning this setting off saves some time."
msgstr "Préparer les filaments avec une goutte avant l'impression. Ce paramètre permet d'assurer que l'extrudeuse disposera de matériau prêt au niveau de la buse avant l'impression. La jupe/bordure d'impression peut également servir de préparation, auquel cas le fait de laisser ce paramètre désactivé permet de gagner un peu de temps."

msgctxt "print_sequence description"
msgid "Whether to print all models one layer at a time or to wait for one model to finish, before moving on to the next. One at a time mode is possible if a) only one extruder is enabled and b) all models are separated in such a way that the whole print head can move in between and all models are lower than the distance between the nozzle and the X/Y axes."
msgstr "Imprime tous les modèles en même temps, couche par couche, ou attend la fin d'un modèle pour en commencer un autre. Le mode « Un modèle à la fois » est disponible seulement si a) un seul extrudeur est activé et si b) tous les modèles sont suffisamment éloignés pour que la tête puisse passer entre eux et qu'ils sont tous inférieurs à la distance entre la buse et les axes X/Y."

msgctxt "machine_show_variants description"
msgid "Whether to show the different variants of this machine, which are described in separate json files."
msgstr "Afficher ou non les différentes variantes de cette machine qui sont décrites dans des fichiers json séparés."

msgctxt "machine_firmware_retract description"
msgid "Whether to use firmware retract commands (G10/G11) instead of using the E property in G1 commands to retract the material."
msgstr "S'il faut utiliser les commandes de rétraction du firmware (G10 / G11) au lieu d'utiliser la propriété E dans les commandes G1 pour rétracter le matériau."

msgctxt "material_print_temp_wait description"
msgid "Whether to wait until the nozzle temperature is reached at the start."
msgstr "Attendre ou non que la température de la buse soit atteinte au démarrage."

msgctxt "infill_line_width description"
msgid "Width of a single infill line."
msgstr "Largeur d'une seule ligne de remplissage."

msgctxt "support_interface_line_width description"
msgid "Width of a single line of support roof or floor."
msgstr "Largeur d'une seule ligne de plafond ou de bas de support."

msgctxt "roofing_line_width description"
msgid "Width of a single line of the areas at the top of the print."
msgstr "Largeur d'une seule ligne de la zone en haut de l'impression."

msgctxt "line_width description"
msgid "Width of a single line. Generally, the width of each line should correspond to the width of the nozzle. However, slightly reducing this value could produce better prints."
msgstr "Largeur d'une ligne. Généralement, la largeur de chaque ligne doit correspondre à la largeur de la buse. Toutefois, le fait de diminuer légèrement cette valeur peut fournir de meilleures impressions."

msgctxt "prime_tower_line_width description"
msgid "Width of a single prime tower line."
msgstr "Largeur d'une seule ligne de tour d'amorçage."

msgctxt "skirt_brim_line_width description"
msgid "Width of a single skirt or brim line."
msgstr "Largeur d'une seule ligne de jupe ou de bordure."

msgctxt "support_bottom_line_width description"
msgid "Width of a single support floor line."
msgstr "Largeur d'une seule ligne de bas de support."

msgctxt "support_roof_line_width description"
msgid "Width of a single support roof line."
msgstr "Largeur d'une seule ligne de plafond de support."

msgctxt "support_line_width description"
msgid "Width of a single support structure line."
msgstr "Largeur d'une seule ligne de support."

msgctxt "skin_line_width description"
msgid "Width of a single top/bottom line."
msgstr "Largeur d'une seule ligne du dessus/dessous."

msgctxt "wall_line_width_x description"
msgid "Width of a single wall line for all wall lines except the outermost one."
msgstr "Largeur d'une seule ligne de la paroi pour toutes les lignes de paroi, à l’exception de la ligne la plus externe."

msgctxt "wall_line_width description"
msgid "Width of a single wall line."
msgstr "Largeur d'une seule ligne de la paroi."

msgctxt "raft_base_line_width description"
msgid "Width of the lines in the base raft layer. These should be thick lines to assist in build plate adhesion."
msgstr "Largeur des lignes de la couche de base du radeau. Elles doivent être épaisses pour permettre l’adhérence au plateau."

msgctxt "raft_interface_line_width description"
msgid "Width of the lines in the middle raft layer. Making the second layer extrude more causes the lines to stick to the build plate."
msgstr "Largeur des lignes de la couche intermédiaire du radeau. Une plus grande extrusion de la deuxième couche renforce l'adhérence des lignes au plateau."

msgctxt "raft_surface_line_width description"
msgid "Width of the lines in the top surface of the raft. These can be thin lines so that the top of the raft becomes smooth."
msgstr "Largeur des lignes de la surface supérieure du radeau. Elles doivent être fines pour rendre le dessus du radeau lisse."

msgctxt "wall_line_width_0 description"
msgid "Width of the outermost wall line. By lowering this value, higher levels of detail can be printed."
msgstr "Largeur de la ligne la plus à l'extérieur de la paroi. Le fait de réduire cette valeur permet d'imprimer des niveaux plus élevés de détails."

msgctxt "min_bead_width description"
msgid "Width of the wall that will replace thin features (according to the Minimum Feature Size) of the model. If the Minimum Wall Line Width is thinner than the thickness of the feature, the wall will become as thick as the feature itself."
msgstr "La largeur de la paroi qui remplacera les entités fines (selon la taille minimale des entités) du modèle. Si la largeur minimale de la ligne de paroi est plus fine que l'épaisseur de l'entité, la paroi deviendra aussi épaisse que l'entité elle-même."

msgctxt "wipe_brush_pos_x label"
msgid "Wipe Brush X Position"
msgstr "Position X de la brosse d'essuyage"

msgctxt "wipe_hop_speed label"
msgid "Wipe Hop Speed"
msgstr "Vitesse du décalage d'essuyage"

msgctxt "prime_tower_wipe_enabled label"
msgid "Wipe Inactive Nozzle on Prime Tower"
msgstr "Essuyer le bec d'impression inactif sur la tour d'amorçage"

msgctxt "wipe_move_distance label"
msgid "Wipe Move Distance"
msgstr "Distance de déplacement d'essuyage"

msgctxt "clean_between_layers label"
msgid "Wipe Nozzle Between Layers"
msgstr "Essuyer la buse entre les couches"

msgctxt "wipe_pause label"
msgid "Wipe Pause"
msgstr "Pause d'essuyage"

msgctxt "wipe_repeat_count label"
msgid "Wipe Repeat Count"
msgstr "Nombre de répétitions d'essuyage"

msgctxt "wipe_retraction_amount label"
msgid "Wipe Retraction Distance"
msgstr "Distance de rétraction d'essuyage"

msgctxt "wipe_retraction_enable label"
msgid "Wipe Retraction Enable"
msgstr "Activation de la rétraction d'essuyage"

msgctxt "wipe_retraction_extra_prime_amount label"
msgid "Wipe Retraction Extra Prime Amount"
msgstr "Degré supplémentaire de rétraction d'essuyage d'amorçage"

msgctxt "wipe_retraction_prime_speed label"
msgid "Wipe Retraction Prime Speed"
msgstr "Vitesse primaire de rétraction d'essuyage"

msgctxt "wipe_retraction_retract_speed label"
msgid "Wipe Retraction Retract Speed"
msgstr "Vitesse de rétraction d'essuyage"

msgctxt "wipe_retraction_speed label"
msgid "Wipe Retraction Speed"
msgstr "Vitesse de rétraction d'essuyage"

msgctxt "wipe_hop_enable label"
msgid "Wipe Z Hop"
msgstr "Décalage en Z de l'essuyage"

msgctxt "wipe_hop_amount label"
msgid "Wipe Z Hop Height"
msgstr "Hauteur du décalage en Z d'essuyage"

msgctxt "retraction_combing option infill"
msgid "Within Infill"
msgstr "À l'intérieur du remplissage"

msgctxt "machine_always_write_active_tool description"
msgid "Write active tool after sending temp commands to inactive tool. Required for Dual Extruder printing with Smoothie or other firmware with modal tool commands."
msgstr "Écrivez l'outil actif après avoir envoyé des commandes temporaires à l'outil inactif. Requis pour l'impression à double extrusion avec Smoothie ou un autre micrologiciel avec des commandes d'outils modaux."

msgctxt "machine_endstop_positive_direction_x label"
msgid "X Endstop in Positive Direction"
msgstr "Butée X en sens positif"

msgctxt "wipe_brush_pos_x description"
msgid "X location where wipe script will start."
msgstr "Emplacement X où le script d'essuyage démarrera."

msgctxt "support_xy_overrides_z option xy_overrides_z"
msgid "X/Y overrides Z"
msgstr "X/Y annule Z"

msgctxt "machine_endstop_positive_direction_y label"
msgid "Y Endstop in Positive Direction"
msgstr "Butée Y en sens positif"

msgctxt "machine_endstop_positive_direction_z label"
msgid "Z Endstop in Positive Direction"
msgstr "Butée Z en sens positif"

msgctxt "retraction_hop_after_extruder_switch label"
msgid "Z Hop After Extruder Switch"
msgstr "Décalage en Z après changement d'extrudeuse"

msgctxt "retraction_hop_after_extruder_switch_height label"
msgid "Z Hop After Extruder Switch Height"
msgstr "Décalage en Z après changement de hauteur d'extrudeuse"

msgctxt "retraction_hop label"
msgid "Z Hop Height"
msgstr "Hauteur du décalage en Z"

msgctxt "retraction_hop_only_when_collides label"
msgid "Z Hop Only Over Printed Parts"
msgstr "Décalage en Z uniquement sur les pièces imprimées"

msgctxt "speed_z_hop label"
msgid "Z Hop Speed"
msgstr "Vitesse du décalage en Z"

msgctxt "retraction_hop_enabled label"
msgid "Z Hop When Retracted"
msgstr "Décalage en Z lors d’une rétraction"

msgctxt "z_seam_type label"
msgid "Z Seam Alignment"
msgstr "Alignement de la jointure en Z"

msgctxt "z_seam_position label"
msgid "Z Seam Position"
msgstr "Position de la jointure en Z"

msgctxt "z_seam_relative label"
msgid "Z Seam Relative"
msgstr "Relatif à la jointure en Z"

msgctxt "z_seam_x label"
msgid "Z Seam X"
msgstr "X Jointure en Z"

msgctxt "z_seam_y label"
msgid "Z Seam Y"
msgstr "Y Jointure en Z"

msgctxt "support_xy_overrides_z option z_overrides_xy"
msgid "Z overrides X/Y"
msgstr "Z annule X/Y"

msgctxt "infill_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "ironing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "roofing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "support_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "support_interface_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "support_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "support_roof_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "top_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "top_bottom_pattern_0 option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "travel description"
msgid "travel"
msgstr "déplacement"

msgctxt "gradual_flow_discretisation_step_size description"
msgid "Duration of each step in the gradual flow change"
msgstr "Durée de chaque étape du changement progressif de débit"

msgctxt "gradual_flow_enabled description"
msgid "Enable gradual flow changes. When enabled, the flow is gradually increased/decreased to the target flow. This is useful for printers with a bowden tube where the flow is not immediately changed when the extruder motor starts/stops."
msgstr "Permettre des changements de débit progressifs. Lorsque cette option est activée, le débit est progressivement augmenté/diminué jusqu'au débit cible. Cette option est utile pour les imprimantes équipées d'un tube Bowden avec lesquelles le débit n'est pas immédiatement modifié lorsque le moteur de l'extrudeuse démarre/s'arrête."

msgctxt "reset_flow_duration description"
msgid "For any travel move longer than this value, the material flow is reset to the paths target flow"
msgstr "Pour tout déplacement plus long que cette valeur, le débit de matière est réinitialisé au débit cible du parcours"

msgctxt "gradual_flow_discretisation_step_size label"
msgid "Gradual flow discretisation step size"
msgstr "Taille du pas de discrétisation du débit progressif"

msgctxt "gradual_flow_enabled label"
msgid "Gradual flow enabled"
msgstr "Débit progressif activé"

msgctxt "max_flow_acceleration label"
msgid "Gradual flow max acceleration"
msgstr "Accélération maximale du débit progressif"

msgctxt "layer_0_max_flow_acceleration label"
msgid "Initial layer max flow acceleration"
msgstr "Accélération maximale du débit de la couche initiale"

msgctxt "max_flow_acceleration description"
msgid "Maximum acceleration for gradual flow changes"
msgstr "Accélération maximale des changements de débit progressifs"

msgctxt "layer_0_max_flow_acceleration description"
msgid "Minimum speed for gradual flow changes for the first layer"
msgstr "Vitesse minimale des changements de débit progressifs pour la première couche"

msgctxt "reset_flow_duration label"
msgid "Reset flow duration"
msgstr "Réinitialiser la durée du débit"
