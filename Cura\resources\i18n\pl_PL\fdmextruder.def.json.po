# Cura JSON setting files
# Copyright (C) 2022 Ultimaker B.V.
# This file is distributed under the same license as the Cura package.
#
msgid ""
msgstr ""
"Project-Id-Version: Cura 5.1\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2023-06-08 16:32+0000\n"
"PO-Revision-Date: 2019-03-13 14:00+0200\n"
"Last-Translator: <PERSON>z 'Virgin71' <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: reprapy.pl\n"
"Language: pl_PL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Generator: Poedit 2.1.1\n"

msgctxt "platform_adhesion description"
msgid "Adhesion"
msgstr "Przyczepność"

msgctxt "material_diameter description"
msgid "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament."
msgstr "Dostosuj średnicę użytego filamentu. Dopasuj tę wartość do średnicy używanego filamentu."

msgctxt "platform_adhesion label"
msgid "Build Plate Adhesion"
msgstr "Przyczepność do stołu"

msgctxt "material_diameter label"
msgid "Diameter"
msgstr "Średnica"

msgctxt "machine_extruder_end_code description"
msgid "End g-code to execute when switching away from this extruder."
msgstr "Końcowy G-code do wykonania przy przełączeniu na ten ekstruder."

msgctxt "extruder_nr label"
msgid "Extruder"
msgstr "Ekstruder"

msgctxt "machine_extruder_end_code label"
msgid "Extruder End G-Code"
msgstr "Końcowy G-code Ekstrudera"

msgctxt "machine_extruder_end_pos_abs label"
msgid "Extruder End Position Absolute"
msgstr "Bezwzgl. Końcowa Pozycja Ekstrudera"

msgctxt "machine_extruder_end_pos_x label"
msgid "Extruder End Position X"
msgstr "Końcowa Pozycja X Ekstrudera"

msgctxt "machine_extruder_end_pos_y label"
msgid "Extruder End Position Y"
msgstr "Końcowa Pozycja Y Ekstrudera"

msgctxt "extruder_prime_pos_x label"
msgid "Extruder Prime X Position"
msgstr "Pozycja X Czyszczenia Dyszy"

msgctxt "extruder_prime_pos_y label"
msgid "Extruder Prime Y Position"
msgstr "Pozycja Y Czyszczenia Dyszy"

msgctxt "extruder_prime_pos_z label"
msgid "Extruder Prime Z Position"
msgstr "Pozycja Z Czyszczenia Dyszy"

msgctxt "machine_extruder_cooling_fan_number label"
msgid "Extruder Print Cooling Fan"
msgstr "Wentylator ekstrudera"

msgctxt "machine_extruder_start_code label"
msgid "Extruder Start G-Code"
msgstr "Początkowy G-code Ekstrudera"

msgctxt "machine_extruder_start_pos_abs label"
msgid "Extruder Start Position Absolute"
msgstr "Bezwzględna Pozycja Początkowa Ekstrudera"

msgctxt "machine_extruder_start_pos_x label"
msgid "Extruder Start Position X"
msgstr "Początkowa Pozycja X Ekstrudera"

msgctxt "machine_extruder_start_pos_y label"
msgid "Extruder Start Position Y"
msgstr "Początkowa Pozycja Y Ekstrudera"

msgctxt "machine_settings label"
msgid "Machine"
msgstr "Maszyna"

msgctxt "machine_settings description"
msgid "Machine specific settings"
msgstr "Specyficzne ustawienia maszyny"

msgctxt "machine_extruder_end_pos_abs description"
msgid "Make the extruder ending position absolute rather than relative to the last-known location of the head."
msgstr "Zmień pozycję końcową ekstrudera na bezwzględną, zamiast względem ostatniej pozycji głowicy."

msgctxt "machine_extruder_start_pos_abs description"
msgid "Make the extruder starting position absolute rather than relative to the last-known location of the head."
msgstr "Zmień pozycję początkową ekstrudera na bezwzględną, zamiast względem ostatniej pozycji głowicy."

msgctxt "material description"
msgid "Material"
msgstr "Materiał"

msgctxt "material label"
msgid "Material"
msgstr "Materiał"

msgctxt "machine_nozzle_size label"
msgid "Nozzle Diameter"
msgstr "Średnica Dyszy"

msgctxt "machine_nozzle_id label"
msgid "Nozzle ID"
msgstr "ID Dyszy"

msgctxt "machine_nozzle_offset_x label"
msgid "Nozzle X Offset"
msgstr "Przesunięcie X Dyszy"

msgctxt "machine_nozzle_offset_y label"
msgid "Nozzle Y Offset"
msgstr "Przesunięcie Y Dyszy"

msgctxt "machine_extruder_start_code description"
msgid "Start g-code to execute when switching to this extruder."
msgstr "Początkowy G-code do wykonania przy przełączeniu na ten ekstruder."

msgctxt "extruder_prime_pos_x description"
msgid "The X coordinate of the position where the nozzle primes at the start of printing."
msgstr "Współrzędna X, w której dysza jest czyszczona na początku wydruku."

msgctxt "extruder_prime_pos_y description"
msgid "The Y coordinate of the position where the nozzle primes at the start of printing."
msgstr "Współrzędna Y, w której dysza jest czyszczona na początku wydruku."

msgctxt "extruder_prime_pos_z description"
msgid "The Z coordinate of the position where the nozzle primes at the start of printing."
msgstr "Współrzędna Z, w której dysza jest czyszczona na początku wydruku."

msgctxt "extruder_nr description"
msgid "The extruder train used for printing. This is used in multi-extrusion."
msgstr "Ekstruder używany do drukowania. Ta opcja używana jest podczas multi-ekstruzji."

msgctxt "machine_nozzle_size description"
msgid "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size."
msgstr "Wewnętrzna średnica dyszy. Zmień to ustawienie kiedy używasz niestandardowego rozmiaru dyszy."

msgctxt "machine_nozzle_id description"
msgid "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\"."
msgstr "ID dyszy dla wózka ekstrudera np. \"AA 0.4\" i \"BB 0.8\"."

msgctxt "machine_extruder_cooling_fan_number description"
msgid "The number of the print cooling fan associated with this extruder. Only change this from the default value of 0 when you have a different print cooling fan for each extruder."
msgstr "Numer wentylatora przypisanego do ekstrudera. Zmień z domyślnej wartości 0, tylko w przypadku, kiedy posiadasz oddzielny wentylator dla każdego ekstrudera."

msgctxt "machine_extruder_end_pos_x description"
msgid "The x-coordinate of the ending position when turning the extruder off."
msgstr "Współrzędna X końcowej pozycji ekstrudera podczas jego wyłączania."

msgctxt "machine_nozzle_offset_x description"
msgid "The x-coordinate of the offset of the nozzle."
msgstr "Współrzędna X przesunięcia dyszy."

msgctxt "machine_extruder_start_pos_x description"
msgid "The x-coordinate of the starting position when turning the extruder on."
msgstr "Współrzędna X początkowej pozycji ekstrudera podczas jego włączania."

msgctxt "machine_extruder_end_pos_y description"
msgid "The y-coordinate of the ending position when turning the extruder off."
msgstr "Współrzędna Y końcowej pozycji ekstrudera podczas jego wyłączania."

msgctxt "machine_nozzle_offset_y description"
msgid "The y-coordinate of the offset of the nozzle."
msgstr "Współrzędna Y przesunięcia dyszy."

msgctxt "machine_extruder_start_pos_y description"
msgid "The y-coordinate of the starting position when turning the extruder on."
msgstr "Współrzędna Y początkowej pozycji ekstrudera podczas jego włączania."

#~ msgctxt "machine_extruder_end_code description"
#~ msgid "End g-code to execute whenever turning the extruder off."
#~ msgstr "Końcowy G-code, który jest wywoływany, kiedy ekstruder jest wyłączany."

#~ msgctxt "machine_extruder_start_code description"
#~ msgid "Start g-code to execute whenever turning the extruder on."
#~ msgstr "Początkowy G-code wywoływany kiedy ekstruder uruchamia się."
