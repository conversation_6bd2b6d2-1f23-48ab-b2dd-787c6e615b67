#!/usr/bin/env python3
"""
Cura Development Environment Requirements Checker
检查Cura开发环境的系统要求
"""

import sys
import subprocess
import platform
from packaging import version

def check_python_version():
    """检查Python版本"""
    print("=== Python版本检查 ===")
    current_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    required_version = "3.12.0"
    
    print(f"当前Python版本: {current_version}")
    print(f"要求Python版本: {required_version}+")
    
    if version.parse(current_version) >= version.parse(required_version):
        print("✅ Python版本符合要求")
        return True
    else:
        print("❌ Python版本不符合要求")
        print(f"请安装Python {required_version}或更高版本")
        
        system = platform.system()
        if system == "Windows":
            print("Windows安装指南: https://python.org")
        elif system == "Darwin":
            print("macOS安装指南: brew install python@3.12")
        elif system == "Linux":
            print("Linux安装指南: 使用包管理器安装python3.12")
        
        return False

def check_conan_version():
    """检查Conan版本"""
    print("\n=== Conan版本检查 ===")
    try:
        result = subprocess.run(['conan', '--version'], 
                              capture_output=True, text=True, check=True)
        output = result.stdout.strip()
        print(f"当前Conan版本: {output}")
        
        # 提取版本号
        if "Conan version" in output:
            conan_version = output.split("Conan version ")[1].strip()
            required_version = "2.7.0"
            
            print(f"要求Conan版本: {required_version}+")
            
            if version.parse(conan_version) >= version.parse(required_version):
                print("✅ Conan版本符合要求")
                return True
            else:
                print("❌ Conan版本不符合要求")
                print(f"请安装Conan {required_version}或更高版本")
                print("安装命令: pip install 'conan>=2.7.0'")
                return False
        else:
            print("❌ 无法解析Conan版本")
            return False
            
    except subprocess.CalledProcessError:
        print("❌ Conan未安装或无法运行")
        print("安装命令: pip install 'conan>=2.7.0'")
        return False
    except FileNotFoundError:
        print("❌ Conan未找到")
        print("安装命令: pip install 'conan>=2.7.0'")
        return False

def check_system_requirements():
    """检查系统要求"""
    print("\n=== 系统要求检查 ===")
    system = platform.system()
    print(f"操作系统: {system} {platform.release()}")
    
    if system == "Windows":
        print("Windows要求:")
        print("- Windows 10或更高版本")
        print("- Visual Studio with MSVC 2022或更高版本")
        print("- PowerShell 5.0或更高版本")
    elif system == "Darwin":
        print("macOS要求:")
        print("- macOS 11或更高版本")
        print("- Xcode 12或更高版本")
        print("- apple-clang-12.0或更高版本")
    elif system == "Linux":
        print("Linux要求:")
        print("- Ubuntu/Debian/Arch/Manjaro (glibc 2.28或更高版本)")
        print("- gcc-13或更高版本")
    
    print("✅ 请手动验证系统要求")
    return True

def check_git():
    """检查Git"""
    print("\n=== Git检查 ===")
    try:
        result = subprocess.run(['git', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"Git版本: {result.stdout.strip()}")
        print("✅ Git已安装")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Git未安装")
        print("请安装Git: https://git-scm.com/")
        return False

def main():
    """主函数"""
    print("Cura开发环境要求检查")
    print("=" * 50)
    
    checks = [
        check_python_version(),
        check_conan_version(),
        check_git(),
        check_system_requirements()
    ]
    
    print("\n" + "=" * 50)
    print("检查结果汇总:")
    
    passed = sum(checks)
    total = len(checks)
    
    if passed == total:
        print("✅ 所有要求都已满足，可以运行设置脚本")
        print("\n下一步:")
        system = platform.system()
        if system == "Windows":
            print("运行: .\\setup_cura_dev_env.ps1")
        else:
            print("运行: ./setup_cura_dev_env.sh")
    else:
        print(f"❌ {total - passed} 项要求未满足，请先解决上述问题")
        print("解决问题后重新运行此脚本")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n检查过程中出现错误: {e}")
        sys.exit(1)
