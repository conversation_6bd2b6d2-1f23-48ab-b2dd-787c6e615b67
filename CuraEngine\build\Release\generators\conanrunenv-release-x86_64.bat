@echo off
chcp 65001 > nul
setlocal
echo @echo off > "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
echo echo Restoring environment >> "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
for %%v in (PATH GRPC_DEFAULT_SSL_ROOTS_FILE_PATH OPENSSL_MODULES) do (
    set foundenvvar=
    for /f "delims== tokens=1,2" %%a in ('set') do (
        if /I "%%a" == "%%v" (
            echo set "%%a=%%b">> "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
            set foundenvvar=1
        )
    )
    if not defined foundenvvar (
        echo set %%v=>> "%~dp0/deactivate_conanrunenv-release-x86_64.bat"
    )
)
endlocal


set "PATH=C:\Users\<USER>\.conan2\p\b\arcus03cb8757a37eb\p\bin;C:\Users\<USER>\.conan2\p\b\clippf6b8d4243e255\p\bin;%PATH%"
set "GRPC_DEFAULT_SSL_ROOTS_FILE_PATH=C:\Users\<USER>\.conan2\p\b\grpcc342d5e50a306\p\res\grpc\roots.pem"
set "OPENSSL_MODULES=C:\Users\<USER>\.conan2\p\opensa8084e5231736\p\lib\ossl-modules"