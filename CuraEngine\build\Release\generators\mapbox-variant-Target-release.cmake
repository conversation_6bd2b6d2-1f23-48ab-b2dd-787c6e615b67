# Avoid multiple calls to find_package to append duplicated properties to the targets
include_guard()########### VARIABLES #######################################################################
#############################################################################################
set(mapbox-variant_FRAMEWORKS_FOUND_RELEASE "") # Will be filled later
conan_find_apple_frameworks(mapbox-variant_FRAMEWORKS_FOUND_RELEASE "${mapbox-variant_FRAMEWORKS_RELEASE}" "${mapbox-variant_FRAMEWORK_DIRS_RELEASE}")

set(mapbox-variant_LIBRARIES_TARGETS "") # Will be filled later


######## Create an interface target to contain all the dependencies (frameworks, system and conan deps)
if(NOT TARGET mapbox-variant_DEPS_TARGET)
    add_library(mapbox-variant_DEPS_TARGET INTERFACE IMPORTED)
endif()

set_property(TARGET mapbox-variant_DEPS_TARGET
             APPEND PROPERTY INTERFACE_LINK_LIBRARIES
             $<$<CONFIG:Release>:${mapbox-variant_FRAMEWORKS_FOUND_RELEASE}>
             $<$<CONFIG:Release>:${mapbox-variant_SYSTEM_LIBS_RELEASE}>
             $<$<CONFIG:Release>:>)

####### Find the libraries declared in cpp_info.libs, create an IMPORTED target for each one and link the
####### mapbox-variant_DEPS_TARGET to all of them
conan_package_library_targets("${mapbox-variant_LIBS_RELEASE}"    # libraries
                              "${mapbox-variant_LIB_DIRS_RELEASE}" # package_libdir
                              "${mapbox-variant_BIN_DIRS_RELEASE}" # package_bindir
                              "${mapbox-variant_LIBRARY_TYPE_RELEASE}"
                              "${mapbox-variant_IS_HOST_WINDOWS_RELEASE}"
                              mapbox-variant_DEPS_TARGET
                              mapbox-variant_LIBRARIES_TARGETS  # out_libraries_targets
                              "_RELEASE"
                              "mapbox-variant"    # package_name
                              "${mapbox-variant_NO_SONAME_MODE_RELEASE}")  # soname

# FIXME: What is the result of this for multi-config? All configs adding themselves to path?
set(CMAKE_MODULE_PATH ${mapbox-variant_BUILD_DIRS_RELEASE} ${CMAKE_MODULE_PATH})

########## GLOBAL TARGET PROPERTIES Release ########################################
    set_property(TARGET mapbox-variant::mapbox-variant
                 APPEND PROPERTY INTERFACE_LINK_LIBRARIES
                 $<$<CONFIG:Release>:${mapbox-variant_OBJECTS_RELEASE}>
                 $<$<CONFIG:Release>:${mapbox-variant_LIBRARIES_TARGETS}>
                 )

    if("${mapbox-variant_LIBS_RELEASE}" STREQUAL "")
        # If the package is not declaring any "cpp_info.libs" the package deps, system libs,
        # frameworks etc are not linked to the imported targets and we need to do it to the
        # global target
        set_property(TARGET mapbox-variant::mapbox-variant
                     APPEND PROPERTY INTERFACE_LINK_LIBRARIES
                     mapbox-variant_DEPS_TARGET)
    endif()

    set_property(TARGET mapbox-variant::mapbox-variant
                 APPEND PROPERTY INTERFACE_LINK_OPTIONS
                 $<$<CONFIG:Release>:${mapbox-variant_LINKER_FLAGS_RELEASE}>)
    set_property(TARGET mapbox-variant::mapbox-variant
                 APPEND PROPERTY INTERFACE_INCLUDE_DIRECTORIES
                 $<$<CONFIG:Release>:${mapbox-variant_INCLUDE_DIRS_RELEASE}>)
    # Necessary to find LINK shared libraries in Linux
    set_property(TARGET mapbox-variant::mapbox-variant
                 APPEND PROPERTY INTERFACE_LINK_DIRECTORIES
                 $<$<CONFIG:Release>:${mapbox-variant_LIB_DIRS_RELEASE}>)
    set_property(TARGET mapbox-variant::mapbox-variant
                 APPEND PROPERTY INTERFACE_COMPILE_DEFINITIONS
                 $<$<CONFIG:Release>:${mapbox-variant_COMPILE_DEFINITIONS_RELEASE}>)
    set_property(TARGET mapbox-variant::mapbox-variant
                 APPEND PROPERTY INTERFACE_COMPILE_OPTIONS
                 $<$<CONFIG:Release>:${mapbox-variant_COMPILE_OPTIONS_RELEASE}>)

########## For the modules (FindXXX)
set(mapbox-variant_LIBRARIES_RELEASE mapbox-variant::mapbox-variant)
