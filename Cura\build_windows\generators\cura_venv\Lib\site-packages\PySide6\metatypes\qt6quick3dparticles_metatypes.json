[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "Particle3D"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Particle3D is abstract"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticle", "enums": [{"isClass": false, "isFlag": false, "name": "FadeType", "values": ["FadeNone", "FadeOpacity", "FadeScale"]}, {"isClass": false, "isFlag": false, "name": "AlignMode", "values": ["AlignNone", "AlignTowardsTarget", "AlignTowardsStartVelocity"]}, {"isClass": false, "isFlag": false, "name": "SortMode", "values": ["SortNone", "SortNewest", "SortOldest", "SortDistance"]}], "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "maxAmount", "notify": "maxAmountChanged", "read": "maxAmount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setMaxAmount"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "reset": "resetColor", "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "colorVariation", "notify": "colorVariationChanged", "read": "colorVariation", "required": false, "scriptable": true, "stored": true, "type": "QVector4D", "user": false, "write": "setColorVariation"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "unifiedColorVariation", "notify": "unifiedColorVariationChanged", "read": "unifiedColorVariation", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setUnifiedColorVariation"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "fadeInEffect", "notify": "fadeInEffectChanged", "read": "fadeInEffect", "required": false, "scriptable": true, "stored": true, "type": "FadeType", "user": false, "write": "setFadeInEffect"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "fadeOutEffect", "notify": "fadeOutEffectChanged", "read": "fadeOutEffect", "required": false, "scriptable": true, "stored": true, "type": "FadeType", "user": false, "write": "setFadeOutEffect"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "fadeInDuration", "notify": "fadeInDurationChanged", "read": "fadeInDuration", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFadeInDuration"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "fadeOutDuration", "notify": "fadeOutDurationChanged", "read": "fadeOutDuration", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFadeOutDuration"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "alignMode", "notify": "alignModeChanged", "read": "alignMode", "required": false, "scriptable": true, "stored": true, "type": "AlignMode", "user": false, "write": "setAlignMode"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "alignTargetPosition", "notify": "alignTargetPositionChanged", "read": "alignTargetPosition", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setAlignTargetPosition"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "hasTransparency", "notify": "hasTransparencyChanged", "read": "hasTransparency", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHasTransparency"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "sortMode", "notify": "sortModeChanged", "read": "sortMode", "required": false, "scriptable": true, "stored": true, "type": "SortMode", "user": false, "write": "setSortMode"}], "qualifiedClassName": "QQuick3DParticle", "signals": [{"access": "public", "index": 0, "name": "systemChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "maxAmountChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "colorChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "colorVariationChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "unifiedColorVariationChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "fadeInEffectChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "fadeOutEffectChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "fadeInDurationChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "fadeOutDurationChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "alignModeChanged", "returnType": "void"}, {"access": "public", "index": 10, "name": "alignTargetPositionChanged", "returnType": "void"}, {"access": "public", "index": 11, "name": "hasTransparencyChanged", "returnType": "void"}, {"access": "public", "index": 12, "name": "sortModeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "system", "type": "QQuick3DParticleSystem*"}], "index": 13, "name": "setSystem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "maxAmount", "type": "int"}], "index": 14, "name": "setMaxAmount", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 15, "name": "setColor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "colorVariation", "type": "QVector4D"}], "index": 16, "name": "setColorVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "unified", "type": "bool"}], "index": 17, "name": "setUnifiedColorVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fadeInEffect", "type": "QQuick3DParticle::FadeType"}], "index": 18, "name": "setFadeInEffect", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fadeOutEffect", "type": "QQuick3DParticle::FadeType"}], "index": 19, "name": "setFadeOutEffect", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fadeInDuration", "type": "int"}], "index": 20, "name": "setFadeInDuration", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fadeOutDuration", "type": "int"}], "index": 21, "name": "setFadeOutDuration", "returnType": "void"}, {"access": "public", "arguments": [{"name": "alignMode", "type": "QQuick3DParticle::AlignMode"}], "index": 22, "name": "setAlignMode", "returnType": "void"}, {"access": "public", "arguments": [{"name": "alignPosition", "type": "QVector3D"}], "index": 23, "name": "setAlignTargetPosition", "returnType": "void"}, {"access": "public", "arguments": [{"name": "transparency", "type": "bool"}], "index": 24, "name": "setHasTransparency", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sortMode", "type": "QQuick3DParticle::SortMode"}], "index": 25, "name": "setSortMode", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DObject"}]}], "inputFile": "qquick3dparticle_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleAbstractShape", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 29, "object": true, "qualifiedClassName": "QQuick3DParticleAbstractShape", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qquick3dparticleabstractshape_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Affector3D"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Affector3D is abstract"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleAffector", "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "system", "notify": "systemChanged", "read": "system", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DParticleSystem*", "user": false, "write": "setSystem"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "particles", "read": "particles", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QQuick3DParticle>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "enabled", "notify": "enabledChanged", "read": "enabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setEnabled"}], "qualifiedClassName": "QQuick3DParticleAffector", "signals": [{"access": "public", "index": 0, "name": "update", "returnType": "void"}, {"access": "public", "index": 1, "name": "systemChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "enabledChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "system", "type": "QQuick3DParticleSystem*"}], "index": 3, "name": "setSystem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 4, "name": "setEnabled", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DNode"}]}], "inputFile": "qquick3dparticleaffector_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Attractor3D"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleAttractor", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "positionVariation", "notify": "positionVariationChanged", "read": "positionVariation", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setPositionVariation"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "shape", "notify": "shapeChanged", "read": "shape", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DParticleAbstractShape*", "user": false, "write": "setShape"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "duration", "notify": "durationChanged", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setDuration"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "durationVariation", "notify": "durationVariationChanged", "read": "durationVariation", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setDurationVariation"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "hideAtEnd", "notify": "hideAtEndChanged", "read": "hideAtEnd", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHideAtEnd"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "useCachedPositions", "notify": "useCachedPositionsChanged", "read": "useCachedPositions", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setUseCachedPositions"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "positionsAmount", "notify": "positionsAmountChanged", "read": "positionsAmount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setPositionsAmount"}], "qualifiedClassName": "QQuick3DParticleAttractor", "signals": [{"access": "public", "index": 0, "name": "positionVariationChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "shapeChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "durationChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "durationVariationChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "hideAtEndChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "useCachedPositionsChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "positionsAmountChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "positionVariation", "type": "QVector3D"}], "index": 7, "name": "setPositionVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shape", "type": "QQuick3DParticleAbstractShape*"}], "index": 8, "name": "setShape", "returnType": "void"}, {"access": "public", "arguments": [{"name": "duration", "type": "int"}], "index": 9, "name": "setDuration", "returnType": "void"}, {"access": "public", "arguments": [{"name": "durationVariation", "type": "int"}], "index": 10, "name": "setDurationVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hideAtEnd", "type": "bool"}], "index": 11, "name": "setHideAtEnd", "returnType": "void"}, {"access": "public", "arguments": [{"name": "useCachedPositions", "type": "bool"}], "index": 12, "name": "setUseCachedPositions", "returnType": "void"}, {"access": "public", "arguments": [{"name": "positionsAmount", "type": "int"}], "index": 13, "name": "setPositionsAmount", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticleAffector"}]}], "inputFile": "qquick3dparticleattractor_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ParticleCustomShape3D"}, {"name": "QML.AddedInVersion", "value": "1539"}], "className": "QQuick3DParticleCustomShape", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "randomizeData", "notify": "randomizeDataChanged", "read": "randomizeData", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setRandomizeData"}], "qualifiedClassName": "QQuick3DParticleCustomShape", "signals": [{"access": "public", "index": 0, "name": "sourceChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "randomizeDataChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "source", "type": "QUrl"}], "index": 2, "name": "setSource", "returnType": "void"}, {"access": "public", "arguments": [{"name": "random", "type": "bool"}], "index": 3, "name": "setRandomizeData", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticleAbstractShape"}]}], "inputFile": "qquick3dparticlecustomshape_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleDirection", "lineNumber": 29, "object": true, "qualifiedClassName": "QQuick3DParticleDirection", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquick3dparticledirection_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "DynamicBurst3D"}, {"name": "QML.AddedInVersion", "value": "1539"}], "className": "QQuick3DParticleDynamicBurst", "enums": [{"isClass": false, "isFlag": false, "name": "TriggerMode", "values": ["TriggerTime", "TriggerStart", "TriggerEnd"]}], "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "enabled", "notify": "enabledChanged", "read": "enabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setEnabled"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "amountVariation", "notify": "amountVariationChanged", "read": "amountVariation", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setAmountVariation"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "triggerMode", "notify": "triggerModeChanged", "read": "triggerMode", "required": false, "scriptable": true, "stored": true, "type": "TriggerMode", "user": false, "write": "setTriggerMode"}], "qualifiedClassName": "QQuick3DParticleDynamicBurst", "signals": [{"access": "public", "index": 0, "name": "enabledChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "amountVariationChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "triggerModeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 3, "name": "setEnabled", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "int"}], "index": 4, "name": "setAmountVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "TriggerMode"}], "index": 5, "name": "setTriggerMode", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticleEmitBurst"}]}], "inputFile": "qquick3dparticledynamicburst_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "EmitBurst3D"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleEmitBurst", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "time", "notify": "timeChanged", "read": "time", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTime"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "amount", "notify": "amountChanged", "read": "amount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setAmount"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "duration", "notify": "durationChanged", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setDuration"}], "qualifiedClassName": "QQuick3DParticleEmitBurst", "signals": [{"access": "public", "index": 0, "name": "timeChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "amountChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "durationChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "time", "type": "int"}], "index": 3, "name": "setTime", "returnType": "void"}, {"access": "public", "arguments": [{"name": "amount", "type": "int"}], "index": 4, "name": "setAmount", "returnType": "void"}, {"access": "public", "arguments": [{"name": "duration", "type": "int"}], "index": 5, "name": "setDuration", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qquick3dparticleemitburst_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ParticleEmitter3D"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleEmitter", "lineNumber": 28, "methods": [{"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 34, "name": "burst", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}, {"name": "duration", "type": "int"}], "index": 35, "name": "burst", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}, {"name": "duration", "type": "int"}, {"name": "position", "type": "QVector3D"}], "index": 36, "name": "burst", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "system", "notify": "systemChanged", "read": "system", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DParticleSystem*", "user": false, "write": "setSystem"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "emitBursts", "read": "emitBursts", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QQuick3DParticleEmitBurst>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "velocity", "notify": "velocityChanged", "read": "velocity", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DParticleDirection*", "user": false, "write": "setVelocity"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "particle", "notify": "particleChanged", "read": "particle", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DParticle*", "user": false, "write": "setParticle"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "enabled", "notify": "enabledChanged", "read": "enabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setEnabled"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "shape", "notify": "shapeChanged", "read": "shape", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DParticleAbstractShape*", "user": false, "write": "setShape"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "emitRate", "notify": "emitRateChanged", "read": "emitRate", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setEmitRate"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "lifeSpan", "notify": "lifeSpanChanged", "read": "lifeSpan", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLifeSpan"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "lifeSpanVariation", "notify": "lifeSpanVariationChanged", "read": "lifeSpanVariation", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLifeSpanVariation"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "particleScale", "notify": "particleScaleChanged", "read": "particleScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setParticleScale"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "particleEndScale", "notify": "particleEndScaleChanged", "read": "particleEndScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setParticleEndScale"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "particleScaleVariation", "notify": "particleScaleVariationChanged", "read": "particleScaleVariation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setParticleScaleVariation"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "particleEndScaleVariation", "notify": "particleEndScaleVariationChanged", "read": "particleEndScaleVariation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setParticleEndScaleVariation"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "particleRotation", "notify": "particleRotationChanged", "read": "particleRotation", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setParticleRotation"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "particleRotationVariation", "notify": "particleRotationVariationChanged", "read": "particleRotationVariation", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setParticleRotationVariation"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "particleRotationVelocity", "notify": "particleRotationVelocityChanged", "read": "particleRotationVelocity", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setParticleRotationVelocity"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "particleRotationVelocityVariation", "notify": "particleRotationVariationVelocityChanged", "read": "particleRotationVelocityVariation", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setParticleRotationVelocityVariation"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "depthBias", "notify": "depthBiasChanged", "read": "depthBias", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setDepthBias"}], "qualifiedClassName": "QQuick3DParticleEmitter", "signals": [{"access": "public", "index": 0, "name": "velocityChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "systemChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "emitRateChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "particleScaleChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "particleEndScaleChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "particleScaleVariationChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "particleEndScaleVariationChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "lifeSpanChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "lifeSpanVariationChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "particleChanged", "returnType": "void"}, {"access": "public", "index": 10, "name": "shapeChanged", "returnType": "void"}, {"access": "public", "index": 11, "name": "particleRotationChanged", "returnType": "void"}, {"access": "public", "index": 12, "name": "particleRotationVariationChanged", "returnType": "void"}, {"access": "public", "index": 13, "name": "particleRotationVelocityChanged", "returnType": "void"}, {"access": "public", "index": 14, "name": "particleRotationVariationVelocityChanged", "returnType": "void"}, {"access": "public", "index": 15, "name": "enabledChanged", "returnType": "void"}, {"access": "public", "index": 16, "name": "depthBiasChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 17, "name": "setEnabled", "returnType": "void"}, {"access": "public", "arguments": [{"name": "velocity", "type": "QQuick3DParticleDirection*"}], "index": 18, "name": "setVelocity", "returnType": "void"}, {"access": "public", "arguments": [{"name": "system", "type": "QQuick3DParticleSystem*"}], "index": 19, "name": "setSystem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "emitRate", "type": "float"}], "index": 20, "name": "setEmitRate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "particleScale", "type": "float"}], "index": 21, "name": "setParticleScale", "returnType": "void"}, {"access": "public", "arguments": [{"name": "particleEndScale", "type": "float"}], "index": 22, "name": "setParticleEndScale", "returnType": "void"}, {"access": "public", "arguments": [{"name": "particleScaleVariation", "type": "float"}], "index": 23, "name": "setParticleScaleVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "particleEndScaleVariation", "type": "float"}], "index": 24, "name": "setParticleEndScaleVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "lifeSpan", "type": "int"}], "index": 25, "name": "setLifeSpan", "returnType": "void"}, {"access": "public", "arguments": [{"name": "lifeSpanVariation", "type": "int"}], "index": 26, "name": "setLifeSpanVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "particle", "type": "QQuick3DParticle*"}], "index": 27, "name": "setParticle", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shape", "type": "QQuick3DParticleAbstractShape*"}], "index": 28, "name": "setShape", "returnType": "void"}, {"access": "public", "arguments": [{"name": "particleRotation", "type": "QVector3D"}], "index": 29, "name": "setParticleRotation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "particleRotationVariation", "type": "QVector3D"}], "index": 30, "name": "setParticleRotationVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "particleRotationVelocity", "type": "QVector3D"}], "index": 31, "name": "setParticleRotationVelocity", "returnType": "void"}, {"access": "public", "arguments": [{"name": "particleRotationVelocityVariation", "type": "QVector3D"}], "index": 32, "name": "setParticleRotationVelocityVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bias", "type": "float"}], "index": 33, "name": "setDepthBias", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DNode"}]}], "inputFile": "qquick3dparticleemitter_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Gravity3D"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleGravity", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "magnitude", "notify": "magnitudeChanged", "read": "magnitude", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMagnitude"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "direction", "notify": "directionChanged", "read": "direction", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setDirection"}], "qualifiedClassName": "QQuick3DParticleGravity", "signals": [{"access": "public", "index": 0, "name": "magnitudeChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "directionChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "direction", "type": "QVector3D"}], "index": 2, "name": "setDirection", "returnType": "void"}, {"access": "public", "arguments": [{"name": "magnitude", "type": "float"}], "index": 3, "name": "setMagnitude", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticleAffector"}]}], "inputFile": "qquick3dparticlegravity_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "LineParticle3D"}, {"name": "QML.AddedInVersion", "value": "1540"}], "className": "QQuick3DParticleLineParticle", "enums": [{"isClass": false, "isFlag": false, "name": "TexcoordMode", "values": ["Absolute", "Relative", "Fill"]}], "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "segmentCount", "notify": "segmentCountChanged", "read": "segmentCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSegmentCount"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "alphaFade", "notify": "alphaFadeChanged", "read": "alphaFade", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setAlphaFade"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "scaleMultiplier", "notify": "scaleMultiplierChanged", "read": "scaleMultiplier", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setScaleMultiplier"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "texcoordMultiplier", "notify": "texcoordMultiplierChanged", "read": "texcoordMultiplier", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTexcoordMultiplier"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "length", "notify": "lengthChanged", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "lengthVariation", "notify": "lengthVariationChanged", "read": "lengthVariation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setLengthVariation"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "lengthDeltaMin", "notify": "lengthDeltaMinChanged", "read": "lengthDeltaMin", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setLengthDeltaMin"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "eolFadeOutDuration", "notify": "eolFadeOutDurationChanged", "read": "eolFadeOutDuration", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setEolFadeOutDuration"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "texcoordMode", "notify": "texcoordModeChanged", "read": "texcoordMode", "required": false, "scriptable": true, "stored": true, "type": "TexcoordMode", "user": false, "write": "setTexcoordMode"}], "qualifiedClassName": "QQuick3DParticleLineParticle", "signals": [{"access": "public", "index": 0, "name": "segmentCountChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "alphaFadeChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "scaleMultiplierChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "texcoordMultiplierChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "lengthChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "lengthVariationChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "lengthDeltaMinChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "eolFadeOutDurationChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "texcoordModeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 9, "name": "setSegmentCount", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fade", "type": "float"}], "index": 10, "name": "setAlphaFade", "returnType": "void"}, {"access": "public", "arguments": [{"name": "multiplier", "type": "float"}], "index": 11, "name": "setScaleMultiplier", "returnType": "void"}, {"access": "public", "arguments": [{"name": "multiplier", "type": "float"}], "index": 12, "name": "setTexcoordMultiplier", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "index": 13, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "length", "type": "float"}], "index": 14, "name": "setLengthVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "float"}], "index": 15, "name": "setLengthDeltaMin", "returnType": "void"}, {"access": "public", "arguments": [{"name": "duration", "type": "int"}], "index": 16, "name": "setEolFadeOutDuration", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "QQuick3DParticleLineParticle::TexcoordMode"}], "index": 17, "name": "setTexcoordMode", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticleSpriteParticle"}]}], "inputFile": "qquick3dparticlelineparticle_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ModelBlendParticle3D"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleModelBlendParticle", "enums": [{"isClass": false, "isFlag": false, "name": "ModelBlendMode", "values": ["Explode", "Construct", "Transfer"]}, {"isClass": false, "isFlag": false, "name": "ModelBlendEmitMode", "values": ["Sequential", "Random", "Activation"]}], "lineNumber": 31, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "delegate", "notify": "delegate<PERSON><PERSON><PERSON>", "read": "delegate", "required": false, "scriptable": true, "stored": true, "type": "QQmlComponent*", "user": false, "write": "setDelegate"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "endNode", "notify": "endNodeChanged", "read": "endNode", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DNode*", "user": false, "write": "setEndNode"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "modelBlendMode", "notify": "modelBlendModeChanged", "read": "modelBlendMode", "required": false, "scriptable": true, "stored": true, "type": "ModelBlendMode", "user": false, "write": "setModelBlendMode"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "endTime", "notify": "endTimeChanged", "read": "endTime", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setEndTime"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "activationNode", "notify": "activationNodeChanged", "read": "activationNode", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DNode*", "user": false, "write": "setActivationNode"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "emitMode", "notify": "emitModeChanged", "read": "emitMode", "required": false, "scriptable": true, "stored": true, "type": "ModelBlendEmitMode", "user": false, "write": "setEmitMode"}], "qualifiedClassName": "QQuick3DParticleModelBlendParticle", "signals": [{"access": "public", "index": 0, "name": "delegate<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 1, "name": "blendFactorChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "endNodeChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "modelBlendModeChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "endTimeChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "activationNodeChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "emitModeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "setDelegate", "type": "QQmlComponent*"}], "index": 7, "name": "setDelegate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "endNode", "type": "QQuick3DNode*"}], "index": 8, "name": "setEndNode", "returnType": "void"}, {"access": "public", "arguments": [{"name": "endTime", "type": "int"}], "index": 9, "name": "setEndTime", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "ModelBlendMode"}], "index": 10, "name": "setModelBlendMode", "returnType": "void"}, {"access": "public", "arguments": [{"name": "activationNode", "type": "QQuick3DNode*"}], "index": 11, "name": "setActivationNode", "returnType": "void"}, {"access": "public", "arguments": [{"name": "emitMode", "type": "ModelBlendEmitMode"}], "index": 12, "name": "setEmitMode", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticle"}]}], "inputFile": "qquick3dparticlemodelblendparticle_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ModelParticle3D"}, {"name": "DefaultProperty", "value": "delegate"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleModelParticle", "lineNumber": 36, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "delegate", "notify": "delegate<PERSON><PERSON><PERSON>", "read": "delegate", "required": false, "scriptable": true, "stored": true, "type": "QQmlComponent*", "user": false, "write": "setDelegate"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "instanceTable", "notify": "instanceTableChanged", "read": "instanceTable", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DInstancing*", "user": false}], "qualifiedClassName": "QQuick3DParticleModelParticle", "signals": [{"access": "public", "index": 0, "name": "delegate<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 1, "name": "instanceTableChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "delegate", "type": "QQmlComponent*"}], "index": 2, "name": "setDelegate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticle"}]}], "inputFile": "qquick3dparticlemodelparticle_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ParticleModelShape3D"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleModelShape", "lineNumber": 26, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "fill", "notify": "fillChanged", "read": "fill", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFill"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "delegate", "notify": "delegate<PERSON><PERSON><PERSON>", "read": "delegate", "required": false, "scriptable": true, "stored": true, "type": "QQmlComponent*", "user": false, "write": "setDelegate"}], "qualifiedClassName": "QQuick3DParticleModelShape", "signals": [{"access": "public", "index": 0, "name": "fillChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "delegate<PERSON><PERSON><PERSON>", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "fill", "type": "bool"}], "index": 2, "name": "setFill", "returnType": "void"}, {"access": "public", "arguments": [{"name": "delegate", "type": "QQmlComponent*"}], "index": 3, "name": "setDelegate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "particleIndex", "type": "int"}], "index": 4, "name": "getPosition", "returnType": "QVector3D"}], "superClasses": [{"access": "public", "name": "QQuick3DParticleAbstractShape"}]}], "inputFile": "qquick3dparticlemodelshape_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "PointRotator3D"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticlePointRotator", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "magnitude", "notify": "magnitudeChanged", "read": "magnitude", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMagnitude"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "direction", "notify": "directionChanged", "read": "direction", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setDirection"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "pivotPoint", "notify": "pivotPointChanged", "read": "pivotPoint", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setPivotPoint"}], "qualifiedClassName": "QQuick3DParticlePointRotator", "signals": [{"access": "public", "index": 0, "name": "magnitudeChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "directionChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "pivotPointChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "magnitude", "type": "float"}], "index": 3, "name": "setMagnitude", "returnType": "void"}, {"access": "public", "arguments": [{"name": "direction", "type": "QVector3D"}], "index": 4, "name": "setDirection", "returnType": "void"}, {"access": "public", "arguments": [{"name": "point", "type": "QVector3D"}], "index": 5, "name": "setPivotPoint", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticleAffector"}]}], "inputFile": "qquick3dparticlepointrotator_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Repeller3D"}, {"name": "QML.AddedInVersion", "value": "1540"}], "className": "QQuick3DParticleRepeller", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "outerRadius", "notify": "outerRadiusChanged", "read": "outerRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setOuterRadius"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "strength", "notify": "strengthChanged", "read": "strength", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setStrength"}], "qualifiedClassName": "QQuick3DParticleRepeller", "signals": [{"access": "public", "index": 0, "name": "radiusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "outerRadiusChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "strengthChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 3, "name": "setRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "float"}], "index": 4, "name": "setOuterRadius", "returnType": "void"}, {"access": "public", "arguments": [{"name": "strength", "type": "float"}], "index": 5, "name": "setStrength", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticleAffector"}]}], "inputFile": "qquick3dparticlerepeller_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ScaleAffector3D"}, {"name": "QML.AddedInVersion", "value": "1540"}], "className": "QQuick3DParticleScaleAffector", "enums": [{"isClass": false, "isFlag": false, "name": "ScalingType", "values": ["Linear", "SewSaw", "SineWave", "AbsSineWave", "Step", "SmoothStep"]}], "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "minSize", "notify": "minSizeChanged", "read": "minSize", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMinSize"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "maxSize", "notify": "maxSizeChanged", "read": "maxSize", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMaxSize"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "duration", "notify": "durationChanged", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setDuration"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "type", "notify": "typeChanged", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "ScalingType", "user": false, "write": "setType"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "easingCurve", "notify": "easingCurveChanged", "read": "easingCurve", "required": false, "scriptable": true, "stored": true, "type": "QEasingCurve", "user": false, "write": "setEasingCurve"}], "qualifiedClassName": "QQuick3DParticleScaleAffector", "signals": [{"access": "public", "index": 0, "name": "minSizeChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "maxSizeChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "durationChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "typeChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "easingCurveChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "size", "type": "float"}], "index": 5, "name": "setMinSize", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "float"}], "index": 6, "name": "setMaxSize", "returnType": "void"}, {"access": "public", "arguments": [{"name": "duration", "type": "int"}], "index": 7, "name": "setDuration", "returnType": "void"}, {"access": "public", "arguments": [{"name": "type", "type": "ScalingType"}], "index": 8, "name": "setType", "returnType": "void"}, {"access": "public", "arguments": [{"name": "curve", "type": "QEasingCurve"}], "index": 9, "name": "setEasingCurve", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticleAffector"}]}], "inputFile": "qquick3dparticlescaleaffector_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ParticleShape3D"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleShape", "enums": [{"isClass": false, "isFlag": false, "name": "ShapeType", "values": ["C<PERSON>", "Sphere", "<PERSON><PERSON><PERSON>"]}], "lineNumber": 26, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "fill", "notify": "fillChanged", "read": "fill", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFill"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "type", "notify": "typeChanged", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "ShapeType", "user": false, "write": "setType"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "extents", "notify": "extentsChanged", "read": "extents", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setExtents"}], "qualifiedClassName": "QQuick3DParticleShape", "signals": [{"access": "public", "index": 0, "name": "fillChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "typeChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "extentsChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "fill", "type": "bool"}], "index": 3, "name": "setFill", "returnType": "void"}, {"access": "public", "arguments": [{"name": "type", "type": "QQuick3DParticleShape::ShapeType"}], "index": 4, "name": "setType", "returnType": "void"}, {"access": "public", "arguments": [{"name": "extends", "type": "QVector3D"}], "index": 5, "name": "setExtents", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticleAbstractShape"}]}], "inputFile": "qquick3dparticleshape_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "SpriteParticle3D"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleSpriteParticle", "enums": [{"isClass": false, "isFlag": false, "name": "BlendMode", "values": ["SourceOver", "Screen", "Multiply"]}], "lineNumber": 31, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "blendMode", "notify": "blendModeChanged", "read": "blendMode", "required": false, "scriptable": true, "stored": true, "type": "BlendMode", "user": false, "write": "setBlendMode"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "sprite", "notify": "spriteChanged", "read": "sprite", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DTexture*", "user": false, "write": "setSprite"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "spriteSequence", "notify": "spriteSequenceChanged", "read": "spriteSequence", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DParticleSpriteSequence*", "user": false, "write": "setSpriteSequence"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "billboard", "notify": "billboardChanged", "read": "billboard", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBillboard"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "particleScale", "notify": "particleScaleChanged", "read": "particleScale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setParticleScale"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "colorTable", "notify": "colorTableChanged", "read": "colorTable", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DTexture*", "user": false, "write": "setColorTable"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "lights", "notify": "lightsChanged", "read": "lights", "required": false, "revision": 1539, "scriptable": true, "stored": true, "type": "QQmlListProperty<QQuick3DAbstractLight>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "offsetX", "notify": "offsetXChanged", "read": "offsetX", "required": false, "revision": 1539, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setOffsetX"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "offsetY", "notify": "offsetYChanged", "read": "offsetY", "required": false, "revision": 1539, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setOffsetY"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "castsReflections", "notify": "castsReflectionsChanged", "read": "castsReflections", "required": false, "revision": 1540, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setCastsReflections"}], "qualifiedClassName": "QQuick3DParticleSpriteParticle", "signals": [{"access": "public", "index": 0, "name": "blendModeChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "spriteChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "spriteSequenceChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "billboardChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "particleScaleChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "colorTableChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "lightsChanged", "returnType": "void", "revision": 1539}, {"access": "public", "index": 7, "name": "offsetXChanged", "returnType": "void", "revision": 1539}, {"access": "public", "index": 8, "name": "offsetYChanged", "returnType": "void", "revision": 1539}, {"access": "public", "index": 9, "name": "castsReflectionsChanged", "returnType": "void", "revision": 1540}], "slots": [{"access": "public", "arguments": [{"name": "blendMode", "type": "QQuick3DParticleSpriteParticle::BlendMode"}], "index": 10, "name": "setBlendMode", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sprite", "type": "QQuick3DTexture*"}], "index": 11, "name": "setSprite", "returnType": "void"}, {"access": "public", "arguments": [{"name": "spriteSequence", "type": "QQuick3DParticleSpriteSequence*"}], "index": 12, "name": "setSpriteSequence", "returnType": "void"}, {"access": "public", "arguments": [{"name": "billboard", "type": "bool"}], "index": 13, "name": "setBillboard", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scale", "type": "float"}], "index": 14, "name": "setParticleScale", "returnType": "void"}, {"access": "public", "arguments": [{"name": "colorTable", "type": "QQuick3DTexture*"}], "index": 15, "name": "setColorTable", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "index": 16, "name": "setOffsetX", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "index": 17, "name": "setOffsetY", "returnType": "void"}, {"access": "public", "arguments": [{"name": "castsReflections", "type": "bool"}], "index": 18, "name": "setCastsReflections", "returnType": "void", "revision": 1540}, {"access": "private", "arguments": [{"name": "object", "type": "QObject*"}], "index": 19, "name": "onLightDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticle"}]}], "inputFile": "qquick3dparticlespriteparticle_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "SpriteSequence3D"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleSpriteSequence", "enums": [{"isClass": false, "isFlag": false, "name": "AnimationDirection", "values": ["Normal", "Reverse", "Alternate", "Alternate<PERSON><PERSON><PERSON>", "SingleFrame"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "frameCount", "notify": "frameCountChanged", "read": "frameCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFrameCount"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "frameIndex", "notify": "frameIndexChanged", "read": "frameIndex", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFrameIndex"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "interpolate", "notify": "interpolate<PERSON><PERSON>ed", "read": "interpolate", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setInterpolate"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "duration", "notify": "durationChanged", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setDuration"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "durationVariation", "notify": "durationVariationChanged", "read": "durationVariation", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setDurationVariation"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "randomStart", "notify": "randomStartChanged", "read": "randomStart", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setRandomStart"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "animationDirection", "notify": "animationDirectionChanged", "read": "animationDirection", "required": false, "scriptable": true, "stored": true, "type": "AnimationDirection", "user": false, "write": "setAnimationDirection"}], "qualifiedClassName": "QQuick3DParticleSpriteSequence", "signals": [{"access": "public", "index": 0, "name": "frameCountChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "frameIndexChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "interpolate<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "index": 3, "name": "durationChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "durationVariationChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "randomStartChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "animationDirectionChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "frameCount", "type": "int"}], "index": 7, "name": "setFrameCount", "returnType": "void"}, {"access": "public", "arguments": [{"name": "frameIndex", "type": "int"}], "index": 8, "name": "setFrameIndex", "returnType": "void"}, {"access": "public", "arguments": [{"name": "interpolate", "type": "bool"}], "index": 9, "name": "setInterpolate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "duration", "type": "int"}], "index": 10, "name": "setDuration", "returnType": "void"}, {"access": "public", "arguments": [{"name": "durationVariation", "type": "int"}], "index": 11, "name": "setDurationVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "randomStart", "type": "bool"}], "index": 12, "name": "setRandomStart", "returnType": "void"}, {"access": "public", "arguments": [{"name": "animationDirection", "type": "QQuick3DParticleSpriteSequence::AnimationDirection"}], "index": 13, "name": "setAnimationDirection", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qquick3dparticlespritesequence_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ParticleSystem3D"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleSystem", "lineNumber": 51, "methods": [{"access": "public", "index": 16, "name": "reset", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "running", "notify": "running<PERSON><PERSON>ed", "read": "isRunning", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setRunning"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "paused", "notify": "paused<PERSON><PERSON>ed", "read": "isPaused", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPaused"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "startTime", "notify": "startTimeChanged", "read": "startTime", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setStartTime"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "time", "notify": "timeChanged", "read": "time", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTime"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "useRandomSeed", "notify": "useRandomSeedChanged", "read": "useRandomSeed", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setUseRandomSeed"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "seed", "notify": "seedChanged", "read": "seed", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSeed"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "logging", "notify": "loggingChanged", "read": "logging", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLogging"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "loggingData", "notify": "loggingDataChanged", "read": "loggingData", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DParticleSystemLogging*", "user": false}], "qualifiedClassName": "QQuick3DParticleSystem", "signals": [{"access": "public", "index": 0, "name": "running<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "index": 1, "name": "paused<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "index": 2, "name": "timeChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "startTimeChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "useRandomSeedChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "seedChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "loggingChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "loggingDataChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "running", "type": "bool"}], "index": 8, "name": "setRunning", "returnType": "void"}, {"access": "public", "arguments": [{"name": "paused", "type": "bool"}], "index": 9, "name": "setPaused", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startTime", "type": "int"}], "index": 10, "name": "setStartTime", "returnType": "void"}, {"access": "public", "arguments": [{"name": "time", "type": "int"}], "index": 11, "name": "setTime", "returnType": "void"}, {"access": "public", "arguments": [{"name": "randomize", "type": "bool"}], "index": 12, "name": "setUseRandomSeed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "seed", "type": "int"}], "index": 13, "name": "setSeed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "logging", "type": "bool"}], "index": 14, "name": "setLogging", "returnType": "void"}, {"access": "public", "arguments": [{"name": "time", "type": "int"}], "index": 15, "name": "setEditorTime", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DNode"}]}, {"className": "QQuick3DParticleSystemAnimation", "lineNumber": 187, "object": true, "qualifiedClassName": "QQuick3DParticleSystemAnimation", "superClasses": [{"access": "public", "name": "QAbstractAnimation"}]}, {"className": "QQuick3DParticleSystemUpdate", "lineNumber": 213, "object": true, "qualifiedClassName": "QQuick3DParticleSystemUpdate", "superClasses": [{"access": "public", "name": "QAbstractAnimation"}]}], "inputFile": "qquick3dparticlesystem_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleSystemLogging", "lineNumber": 26, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "loggingInterval", "notify": "loggingIntervalChanged", "read": "loggingInterval", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLoggingInterval"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "updates", "notify": "updatesChanged", "read": "updates", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "particlesMax", "notify": "particlesMaxChanged", "read": "particlesMax", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "particlesUsed", "notify": "particlesUsedChanged", "read": "particlesUsed", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "time", "notify": "timeChanged", "read": "time", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "timeAverage", "notify": "timeAverageChanged", "read": "timeAverage", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "timeDeviation", "notify": "timeDeviationChanged", "read": "timeDeviation", "required": false, "revision": 1539, "scriptable": true, "stored": true, "type": "float", "user": false}], "qualifiedClassName": "QQuick3DParticleSystemLogging", "signals": [{"access": "public", "index": 0, "name": "loggingIntervalChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "updatesChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "particlesMaxChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "particlesUsedChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "timeChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "timeAverageChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "timeDeviationChanged", "returnType": "void", "revision": 1539}], "slots": [{"access": "public", "arguments": [{"name": "interval", "type": "int"}], "index": 7, "name": "setLoggingInterval", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qquick3dparticlesystemlogging_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "TargetDirection3D"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleTargetDirection", "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "positionVariation", "notify": "positionVariationChanged", "read": "positionVariation", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setPositionVariation"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "normalized", "notify": "normalizedChanged", "read": "normalized", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setNormalized"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "magnitude", "notify": "magnitudeChanged", "read": "magnitude", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMagnitude"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "magnitudeVariation", "notify": "magnitudeChangedVariation", "read": "magnitudeVariation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMagnitudeVariation"}], "qualifiedClassName": "QQuick3DParticleTargetDirection", "signals": [{"access": "public", "index": 0, "name": "positionChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "positionVariationChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "normalizedChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "magnitudeChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "magnitudeChangedVariation", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "positionVariation", "type": "QVector3D"}], "index": 5, "name": "setPositionVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normalized", "type": "bool"}], "index": 6, "name": "setNormalized", "returnType": "void"}, {"access": "public", "arguments": [{"name": "magnitude", "type": "float"}], "index": 7, "name": "setMagnitude", "returnType": "void"}, {"access": "public", "arguments": [{"name": "magnitudeVariation", "type": "float"}], "index": 8, "name": "setMagnitudeVariation", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticleDirection"}]}], "inputFile": "qquick3dparticletargetdirection_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "TrailEmitter3D"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleTrailEmitter", "lineNumber": 26, "methods": [{"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 2, "name": "burst", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "follow", "notify": "follow<PERSON><PERSON>ed", "read": "follow", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DParticle*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "QQuick3DParticleTrailEmitter", "signals": [{"access": "public", "index": 0, "name": "follow<PERSON><PERSON>ed", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "follow", "type": "QQuick3DParticle*"}], "index": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticleEmitter"}]}], "inputFile": "qquick3dparticletrailemitter_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "VectorDirection3D"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleVectorDirection", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "direction", "notify": "directionChanged", "read": "direction", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setDirection"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "directionVariation", "notify": "directionVariationChanged", "read": "directionVariation", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setDirectionVariation"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "normalized", "notify": "normalizedChanged", "read": "normalized", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setNormalized"}], "qualifiedClassName": "QQuick3DParticleVectorDirection", "signals": [{"access": "public", "index": 0, "name": "directionChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "directionVariationChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "normalizedChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "direction", "type": "QVector3D"}], "index": 3, "name": "setDirection", "returnType": "void"}, {"access": "public", "arguments": [{"name": "directionVariation", "type": "QVector3D"}], "index": 4, "name": "setDirectionVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "normalized", "type": "bool"}], "index": 5, "name": "setNormalized", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticleDirection"}]}], "inputFile": "qquick3dparticlevectordirection_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Wander3D"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DParticleWander", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "globalAmount", "notify": "globalAmountChanged", "read": "globalAmount", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setGlobalAmount"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "globalPace", "notify": "globalPaceChanged", "read": "globalPace", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setGlobalPace"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "globalPaceStart", "notify": "globalPaceStartChanged", "read": "globalPaceStart", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setGlobalPaceStart"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "uniqueAmount", "notify": "uniqueAmountChanged", "read": "uniqueAmount", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setUniqueAmount"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "uniquePace", "notify": "uniquePaceChanged", "read": "uniquePace", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setUniquePace"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "uniqueAmountVariation", "notify": "uniqueAmountVariationChanged", "read": "uniqueAmountVariation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setUniqueAmountVariation"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "uniquePaceVariation", "notify": "uniquePaceVariationChanged", "read": "uniquePaceVariation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setUniquePaceVariation"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "fadeInDuration", "notify": "fadeInDurationChanged", "read": "fadeInDuration", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFadeInDuration"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "fadeOutDuration", "notify": "fadeOutDurationChanged", "read": "fadeOutDuration", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setFadeOutDuration"}], "qualifiedClassName": "QQuick3DParticleWander", "signals": [{"access": "public", "index": 0, "name": "globalAmountChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "globalPaceChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "globalPaceStartChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "uniqueAmountChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "uniquePaceChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "uniqueAmountVariationChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "uniquePaceVariationChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "fadeInDurationChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "fadeOutDurationChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "globalAmount", "type": "QVector3D"}], "index": 9, "name": "setGlobalAmount", "returnType": "void"}, {"access": "public", "arguments": [{"name": "globalPace", "type": "QVector3D"}], "index": 10, "name": "setGlobalPace", "returnType": "void"}, {"access": "public", "arguments": [{"name": "globalPaceStart", "type": "QVector3D"}], "index": 11, "name": "setGlobalPaceStart", "returnType": "void"}, {"access": "public", "arguments": [{"name": "uniqueAmount", "type": "QVector3D"}], "index": 12, "name": "setUniqueAmount", "returnType": "void"}, {"access": "public", "arguments": [{"name": "uniquePace", "type": "QVector3D"}], "index": 13, "name": "setUniquePace", "returnType": "void"}, {"access": "public", "arguments": [{"name": "uniqueAmountVariation", "type": "float"}], "index": 14, "name": "setUniqueAmountVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "uniquePaceVariation", "type": "float"}], "index": 15, "name": "setUniquePaceVariation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fadeInDuration", "type": "int"}], "index": 16, "name": "setFadeInDuration", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fadeOutDuration", "type": "int"}], "index": 17, "name": "setFadeOutDuration", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DParticleAffector"}]}], "inputFile": "qquick3dparticlewander_p.h", "outputRevision": 69}]