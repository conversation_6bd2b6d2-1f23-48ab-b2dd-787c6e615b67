import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "thaiinputmethod_p.h"
        name: "QtVirtualKeyboard::ThaiInputMethod"
        accessSemantics: "reference"
        prototype: "QVirtualKeyboardAbstractInputMethod"
        exports: [
            "QtQuick.VirtualKeyboard.Plugins.Thai/ThaiInputMethod 2.0",
            "QtQuick.VirtualKeyboard.Plugins.Thai/ThaiInputMethod 6.0",
            "QtQuick.VirtualKeyboard.Plugins.Thai/ThaiInputMethod 6.1"
        ]
        exportMetaObjectRevisions: [512, 1536, 1537]
    }
}
