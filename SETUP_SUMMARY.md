# Cura & Uranium 并行开发环境设置完成总结

## 已完成的配置

### ✅ 1. 项目结构优化
- **CuraEngine排除**: 已在 `Cura/conandata.yml` 中注释掉CuraEngine依赖（第5-6行）
- **系统后缀支持**: 修改了 `Cura/conanfile.py` 和 `Uranium/conanfile.py`
  - 构建文件夹: `build_windows`, `build_macos`, `build_linux`
  - 虚拟环境: `venv_windows`, `venv_macos`, `venv_linux`

### ✅ 2. 自动化脚本
- **Windows脚本**: `setup_cura_dev_env.ps1`
- **macOS/Linux脚本**: `setup_cura_dev_env.sh`
- **要求检查脚本**: `check_requirements.py`

### ✅ 3. 文档完善
- **主要指南**: `README_DEV_SETUP.md`
- **依赖安装**: `INSTALL_DEPENDENCIES.md`
- **设置总结**: `SETUP_SUMMARY.md`

## 脚本功能

### 自动化设置脚本特性
1. **版本检查**: 严格按照官方要求检查Python 3.12+和Conan 2.7.0+
2. **虚拟环境**: 创建带系统后缀的Conan虚拟环境
3. **Conan配置**: 自动配置Conan profile和远程仓库
4. **Uranium可编辑模式**: 设置Uranium为editable模式实现并行开发
5. **PyCharm集成**: 生成PyCharm运行配置
6. **跨平台兼容**: 支持Windows、macOS、Linux

### 要求检查脚本功能
- Python版本验证（要求3.12+）
- Conan版本验证（要求2.7.0+）
- Git安装检查
- 系统要求提示

## 项目结构（设置完成后）

```
CuraProject/
├── Cura/
│   ├── build_windows/              # Windows构建文件
│   │   └── generators/
│   │       ├── cura_venv_windows/  # Windows Cura虚拟环境
│   │       └── virtual_python_env.ps1
│   ├── build_macos/                # macOS构建文件
│   │   └── generators/
│   │       ├── cura_venv_macos/    # macOS Cura虚拟环境
│   │       └── virtual_python_env.sh
│   └── conanfile.py                # 已修改：系统后缀+排除CuraEngine
├── Uranium/
│   ├── build_windows/              # Windows构建文件
│   ├── build_macos/                # macOS构建文件
│   └── conanfile.py                # 已修改：系统后缀
├── CuraEngine/                     # 独立开发，不参与构建
├── cura_conan_venv_windows/        # Windows Conan环境
├── cura_conan_venv_macos/          # macOS Conan环境
├── setup_cura_dev_env.ps1          # Windows设置脚本
├── setup_cura_dev_env.sh           # macOS/Linux设置脚本
├── check_requirements.py           # 要求检查脚本
├── README_DEV_SETUP.md             # 主要使用指南
├── INSTALL_DEPENDENCIES.md         # 依赖安装指南
└── SETUP_SUMMARY.md                # 本文件
```

## 使用流程

### 第一次设置
1. **检查要求**: `python check_requirements.py`
2. **安装依赖**: 参考 `INSTALL_DEPENDENCIES.md`
3. **运行设置**: 
   - Windows: `.\setup_cura_dev_env.ps1`
   - macOS: `./setup_cura_dev_env.sh`
4. **配置PyCharm**: 按照README指南设置

### 日常开发
1. **激活环境**:
   - Windows: `.\Cura\build_windows\generators\virtual_python_env.ps1`
   - macOS: `source Cura/build_macos/generators/virtual_python_env.sh`
2. **运行Cura**: `python cura_app.py`
3. **并行开发**: 直接修改Uranium代码，变更会立即反映在Cura中

## 关键特性

### 🔄 跨平台兼容
- 虚拟环境和构建文件夹使用系统后缀
- 避免macOS和Windows之间的文件冲突
- 支持同一源码在不同系统上开发

### 🚫 CuraEngine排除
- 已从Cura依赖中移除CuraEngine
- 可以独立开发CuraEngine
- 减少构建时间和复杂性

### 🔧 Uranium并行开发
- Uranium设置为editable模式
- 修改Uranium代码立即生效
- 支持同时调试Cura和Uranium

### 🎯 PyCharm集成
- 自动生成运行配置
- 支持多仓库项目
- 预配置调试环境

## 验证设置

### 测试Uranium并行开发
1. 删除 `Uranium/UM/Application.py`
2. 尝试启动Cura - 应该失败
3. 恢复文件 - Cura应该正常启动

### 测试跨平台兼容
- 在Windows上运行设置脚本
- 在macOS上运行设置脚本
- 确认生成不同的系统后缀文件夹

## 下一步

1. **安装Python 3.12+**: 按照官方要求安装正确版本
2. **安装Conan 2.7.0+**: 升级到要求的Conan版本
3. **运行设置脚本**: 执行自动化环境设置
4. **配置PyCharm**: 设置IDE开发环境
5. **开始开发**: 享受并行开发体验

## 技术细节

### 修改的文件
1. `Cura/conanfile.py`:
   - 第81-85行: 添加系统后缀到虚拟环境路径
   - 第558-563行: 添加系统后缀到构建文件夹
   - 第600-605行: 添加CuraEngine排除逻辑

2. `Cura/conandata.yml`:
   - 第5-6行: 注释掉CuraEngine依赖
   - 第93-98行: 注释掉CuraEngine二进制配置

3. `Uranium/conanfile.py`:
   - 第40-51行: 添加系统后缀到虚拟环境路径
   - 第120-125行: 添加系统后缀到构建文件夹

### 严格遵循官方要求
- Python版本: 严格要求3.12+（不允许降级）
- Conan版本: 严格要求2.7.0+（不允许降级）
- 依赖版本: 完全按照conandata.yml中的版本
- 构建流程: 遵循官方wiki指引

所有配置都已完成，现在您需要安装正确版本的Python和Conan，然后运行设置脚本即可开始开发。
