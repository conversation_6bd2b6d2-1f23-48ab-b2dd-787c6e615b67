########## MACROS ###########################################################################
#############################################################################################

# Requires CMake > 3.15
if(${CMAKE_VERSION} VERSION_LESS "3.15")
    message(FATAL_ERROR "The 'CMakeDeps' generator only works with CMake >= 3.15")
endif()

if(mapbox-variant_FIND_QUIETLY)
    set(mapbox-variant_MESSAGE_MODE VERBOSE)
else()
    set(mapbox-variant_MESSAGE_MODE STATUS)
endif()

include(${CMAKE_CURRENT_LIST_DIR}/cmakedeps_macros.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/mapbox-variantTargets.cmake)
include(CMakeFindDependencyMacro)

check_build_type_defined()

foreach(_DEPENDENCY ${mapbox-variant_FIND_DEPENDENCY_NAMES} )
    # Check that we have not already called a find_package with the transitive dependency
    if(NOT ${_DEPENDENCY}_FOUND)
        find_dependency(${_DEPENDENCY} REQUIRED ${${_DEPENDENCY}_FIND_MODE})
    endif()
endforeach()

set(mapbox-variant_VERSION_STRING "1.2.0")
set(mapbox-variant_INCLUDE_DIRS ${mapbox-variant_INCLUDE_DIRS_RELEASE} )
set(mapbox-variant_INCLUDE_DIR ${mapbox-variant_INCLUDE_DIRS_RELEASE} )
set(mapbox-variant_LIBRARIES ${mapbox-variant_LIBRARIES_RELEASE} )
set(mapbox-variant_DEFINITIONS ${mapbox-variant_DEFINITIONS_RELEASE} )


# Only the last installed configuration BUILD_MODULES are included to avoid the collision
foreach(_BUILD_MODULE ${mapbox-variant_BUILD_MODULES_PATHS_RELEASE} )
    message(${mapbox-variant_MESSAGE_MODE} "Conan: Including build module from '${_BUILD_MODULE}'")
    include(${_BUILD_MODULE})
endforeach()


