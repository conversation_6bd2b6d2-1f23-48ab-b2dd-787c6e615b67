[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "Accelerometer"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlAccelerometer", "enums": [{"isClass": false, "isFlag": false, "name": "AccelerationMode", "values": ["Combined", "Gravity", "User"]}], "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "accelerationMode", "notify": "accelerationModeChanged", "read": "accelerationMode", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "AccelerationMode", "user": false, "write": "setAccelerationMode"}], "qualifiedClassName": "QmlAccelerometer", "signals": [{"access": "public", "arguments": [{"name": "accelerationMode", "type": "AccelerationMode"}], "index": 0, "name": "accelerationModeChanged", "returnType": "void", "revision": 65281}], "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "AccelerometerReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create AccelerometerReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlAccelerometerReading", "lineNumber": 56, "object": true, "properties": [{"bindable": "bindableX", "constant": false, "designable": true, "final": false, "index": 0, "name": "x", "notify": "xChanged", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableY", "constant": false, "designable": true, "final": false, "index": 1, "name": "y", "notify": "y<PERSON><PERSON><PERSON>", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableZ", "constant": false, "designable": true, "final": false, "index": 2, "name": "z", "notify": "z<PERSON>hanged", "read": "z", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlAccelerometerReading", "signals": [{"access": "public", "index": 0, "name": "xChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "y<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 2, "name": "z<PERSON>hanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlaccelerometer_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "AmbientLightSensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlAmbientLightSensor", "lineNumber": 25, "object": true, "qualifiedClassName": "QmlAmbientLightSensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "AmbientLightReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create AmbientLightReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlAmbientLightSensorReading", "lineNumber": 42, "object": true, "properties": [{"bindable": "bindableLightLevel", "constant": false, "designable": true, "final": false, "index": 0, "name": "lightLevel", "notify": "lightLevelChanged", "read": "lightLevel", "required": false, "scriptable": true, "stored": true, "type": "QAmbientLightReading::LightLevel", "user": false}], "qualifiedClassName": "QmlAmbientLightSensorReading", "signals": [{"access": "public", "index": 0, "name": "lightLevelChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlambientlightsensor_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "AmbientTemperatureSensor"}, {"name": "QML.AddedInVersion", "value": "1281"}], "className": "QmlAmbientTemperatureSensor", "lineNumber": 23, "object": true, "qualifiedClassName": "QmlAmbientTemperatureSensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "AmbientTemperatureReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create AmbientTemperatureReading"}, {"name": "QML.AddedInVersion", "value": "1281"}], "className": "QmlAmbientTemperatureReading", "lineNumber": 40, "object": true, "properties": [{"bindable": "bindableTemperature", "constant": false, "designable": true, "final": false, "index": 0, "name": "temperature", "notify": "temperatureChanged", "read": "temperature", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlAmbientTemperatureReading", "signals": [{"access": "public", "index": 0, "name": "temperatureChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlambienttemperaturesensor_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "<PERSON>mp<PERSON>"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlCompass", "lineNumber": 24, "object": true, "qualifiedClassName": "QmlCompass", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "CompassReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create CompassReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlCompassReading", "lineNumber": 40, "object": true, "properties": [{"bindable": "bindableAzimuth", "constant": false, "designable": true, "final": false, "index": 0, "name": "azimuth", "notify": "azimuthChanged", "read": "azimuth", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableCalibrationLevel", "constant": false, "designable": true, "final": false, "index": 1, "name": "calibrationLevel", "notify": "calibrationLevelChanged", "read": "calibrationLevel", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlCompassReading", "signals": [{"access": "public", "index": 0, "name": "azimuthChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "calibrationLevelChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlcompass_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Gyroscope"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlGyroscope", "lineNumber": 24, "object": true, "qualifiedClassName": "QmlGyroscope", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "GyroscopeReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create GyroscopeReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlGyroscopeReading", "lineNumber": 40, "object": true, "properties": [{"bindable": "bindableX", "constant": false, "designable": true, "final": false, "index": 0, "name": "x", "notify": "xChanged", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableY", "constant": false, "designable": true, "final": false, "index": 1, "name": "y", "notify": "y<PERSON><PERSON><PERSON>", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableZ", "constant": false, "designable": true, "final": false, "index": 2, "name": "z", "notify": "z<PERSON>hanged", "read": "z", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlGyroscopeReading", "signals": [{"access": "public", "index": 0, "name": "xChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "y<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 2, "name": "z<PERSON>hanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlgyroscope_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "HumiditySensor"}, {"name": "QML.AddedInVersion", "value": "1289"}], "className": "QmlHumiditySensor", "lineNumber": 24, "object": true, "qualifiedClassName": "QmlHumiditySensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "HumidityReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create HumidityReading"}, {"name": "QML.AddedInVersion", "value": "1289"}], "className": "QmlHumidityReading", "lineNumber": 42, "object": true, "properties": [{"bindable": "bindableRelativeHumidity", "constant": false, "designable": true, "final": false, "index": 0, "name": "relativeHumidity", "notify": "relativeHumidityChanged", "read": "relativeHumidity", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableAbsoluteHumidity", "constant": false, "designable": true, "final": false, "index": 1, "name": "absoluteHumidity", "notify": "absoluteHumidityChanged", "read": "absoluteHumidity", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlHumidityReading", "signals": [{"access": "public", "index": 0, "name": "relativeHumidityChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "absoluteHumidityChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlhumiditysensor_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "IRProximitySensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlIRProximitySensor", "lineNumber": 24, "object": true, "qualifiedClassName": "QmlIRProximitySensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "IRProximityReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create IRProximityReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlIRProximitySensorReading", "lineNumber": 40, "object": true, "properties": [{"bindable": "bindableReflectance", "constant": false, "designable": true, "final": false, "index": 0, "name": "reflectance", "notify": "reflectanceChanged", "read": "reflectance", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlIRProximitySensorReading", "signals": [{"access": "public", "index": 0, "name": "reflectanceChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlirproximitysensor_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "LidSensor"}, {"name": "QML.AddedInVersion", "value": "1289"}], "className": "QmlLidSensor", "lineNumber": 23, "object": true, "qualifiedClassName": "QmlLidSensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "LidReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create LidReading"}, {"name": "QML.AddedInVersion", "value": "1289"}], "className": "QmlLidReading", "lineNumber": 40, "object": true, "properties": [{"bindable": "bindableBackLidClosed", "constant": false, "designable": true, "final": false, "index": 0, "name": "backLidClosed", "notify": "backLid<PERSON><PERSON>ed", "read": "backLidClosed", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"bindable": "bindableFrontLidClosed", "constant": false, "designable": true, "final": false, "index": 1, "name": "frontLidClosed", "notify": "frontLidChanged", "read": "frontLidClosed", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QmlLidReading", "signals": [{"access": "public", "arguments": [{"name": "closed", "type": "bool"}], "index": 0, "name": "backLid<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "closed", "type": "bool"}], "index": 1, "name": "frontLidChanged", "returnType": "bool"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmllidsensor_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "LightSensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlLightSensor", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "fieldOfView", "notify": "fieldOfViewChanged", "read": "fieldOfView", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlLightSensor", "signals": [{"access": "public", "arguments": [{"name": "fieldOfView", "type": "qreal"}], "index": 0, "name": "fieldOfViewChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "LightReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create LightReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlLightSensorReading", "lineNumber": 45, "object": true, "properties": [{"bindable": "bindableIlluminance", "constant": false, "designable": true, "final": false, "index": 0, "name": "illuminance", "notify": "illuminanceChanged", "read": "illuminance", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlLightSensorReading", "signals": [{"access": "public", "index": 0, "name": "illuminanceChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmllightsensor_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Magnetometer"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlMagnetometer", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "returnGeoValues", "notify": "returnGeoValuesChanged", "read": "returnGeoValues", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReturnGeoValues"}], "qualifiedClassName": "QmlMagnetometer", "signals": [{"access": "public", "arguments": [{"name": "returnGeoValues", "type": "bool"}], "index": 0, "name": "returnGeoValuesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "MagnetometerReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create MagnetometerReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlMagnetometerReading", "lineNumber": 47, "object": true, "properties": [{"bindable": "bindableX", "constant": false, "designable": true, "final": false, "index": 0, "name": "x", "notify": "xChanged", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableY", "constant": false, "designable": true, "final": false, "index": 1, "name": "y", "notify": "y<PERSON><PERSON><PERSON>", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableZ", "constant": false, "designable": true, "final": false, "index": 2, "name": "z", "notify": "z<PERSON>hanged", "read": "z", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableCalibrationLevel", "constant": false, "designable": true, "final": false, "index": 3, "name": "calibrationLevel", "notify": "calibrationLevelChanged", "read": "calibrationLevel", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlMagnetometerReading", "signals": [{"access": "public", "index": 0, "name": "xChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "y<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 2, "name": "z<PERSON>hanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "calibrationLevelChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlmagnetometer_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "OrientationSensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlOrientationSensor", "lineNumber": 25, "object": true, "qualifiedClassName": "QmlOrientationSensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "OrientationReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create OrientationReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlOrientationSensorReading", "lineNumber": 41, "object": true, "properties": [{"bindable": "bindableOrientation", "constant": false, "designable": true, "final": false, "index": 0, "name": "orientation", "notify": "orientationChanged", "read": "orientation", "required": false, "scriptable": true, "stored": true, "type": "QOrientationReading::Orientation", "user": false}], "qualifiedClassName": "QmlOrientationSensorReading", "signals": [{"access": "public", "index": 0, "name": "orientationChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlorientationsensor_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "PressureSensor"}, {"name": "QML.AddedInVersion", "value": "1281"}], "className": "QmlPressureSensor", "lineNumber": 23, "object": true, "qualifiedClassName": "QmlPressureSensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "PressureReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create PressureReading"}, {"name": "QML.AddedInVersion", "value": "1281"}], "className": "QmlPressureReading", "lineNumber": 40, "object": true, "properties": [{"bindable": "bindablePressure", "constant": false, "designable": true, "final": false, "index": 0, "name": "pressure", "notify": "pressureChanged", "read": "pressure", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableTemperature", "constant": false, "designable": true, "final": false, "index": 1, "name": "temperature", "notify": "temperatureChanged", "read": "temperature", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlPressureReading", "signals": [{"access": "public", "index": 0, "name": "pressureChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "temperatureChanged", "returnType": "void", "revision": 65281}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlpressuresensor_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ProximitySensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlProximitySensor", "lineNumber": 26, "object": true, "qualifiedClassName": "QmlProximitySensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "ProximityReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create ProximityReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlProximitySensorReading", "lineNumber": 42, "object": true, "properties": [{"bindable": "bindableNear", "constant": false, "designable": true, "final": false, "index": 0, "name": "near", "notify": "nearChanged", "read": "near", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QmlProximitySensorReading", "signals": [{"access": "public", "index": 0, "name": "nearChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlproximitysensor_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "RotationSensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlRotationSensor", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "hasZ", "notify": "hasZChanged", "read": "hasZ", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QmlRotationSensor", "signals": [{"access": "public", "arguments": [{"name": "hasZ", "type": "bool"}], "index": 0, "name": "hasZChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "RotationReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create RotationReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlRotationSensorReading", "lineNumber": 45, "object": true, "properties": [{"bindable": "bindableX", "constant": false, "designable": true, "final": false, "index": 0, "name": "x", "notify": "xChanged", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableY", "constant": false, "designable": true, "final": false, "index": 1, "name": "y", "notify": "y<PERSON><PERSON><PERSON>", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableZ", "constant": false, "designable": true, "final": false, "index": 2, "name": "z", "notify": "z<PERSON>hanged", "read": "z", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlRotationSensorReading", "signals": [{"access": "public", "index": 0, "name": "xChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "y<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 2, "name": "z<PERSON>hanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmlrotationsensor_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Sensor"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create Sensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlSensor", "enums": [{"isClass": false, "isFlag": false, "name": "Feature", "type": "int", "values": ["Buffering", "AlwaysOn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FieldOfView", "AccelerationMode", "SkipDuplicates", "AxesOrientation", "PressureSensorTemperature"]}, {"isClass": false, "isFlag": false, "name": "AxesOrientationMode", "values": ["FixedOrientation", "AutomaticOrientation", "UserOrientation"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 37, "methods": [{"access": "public", "arguments": [{"name": "feature", "type": "Feature"}], "index": 22, "isConst": true, "name": "isFeatureSupported", "returnType": "bool", "revision": 1543}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "identifier", "notify": "identifierChanged", "read": "identifier", "required": false, "scriptable": true, "stored": true, "type": "QByteArray", "user": false, "write": "setIdentifier"}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "type", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "QByteArray", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "connectedToBackend", "notify": "connectedToBackendChanged", "read": "isConnectedToBackend", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "availableDataRates", "notify": "availableDataRatesChanged", "read": "availableDataRates", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QmlSensorRange>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "dataRate", "notify": "dataRateChanged", "read": "dataRate", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setDataRate"}, {"bindable": "bindableReading", "constant": false, "designable": true, "final": false, "index": 5, "name": "reading", "notify": "readingChanged", "read": "reading", "required": false, "scriptable": true, "stored": true, "type": "QmlSensorReading*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "busy", "notify": "busyChanged", "read": "isBusy", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "active", "notify": "activeChanged", "read": "isActive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setActive"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "outputRanges", "notify": "outputRangesChanged", "read": "outputRanges", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QmlSensorOutputRange>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "outputRange", "notify": "outputRangeChanged", "read": "outputRange", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setOutputRange"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "description", "notify": "descriptionChanged", "read": "description", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "alwaysOn", "notify": "alwaysOnChanged", "read": "isAlwaysOn", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAlwaysOn"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "skipDuplicates", "notify": "skipDuplicatesChanged", "read": "skipDuplicates", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSkipDuplicates"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "axesOrientationMode", "notify": "axesOrientationModeChanged", "read": "axesOrientationMode", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "AxesOrientationMode", "user": false, "write": "setAxesOrientationMode"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "currentOrientation", "notify": "currentOrientationChanged", "read": "currentOrientation", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "userOrientation", "notify": "userOrientationChanged", "read": "userOrientation", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setUserOrientation"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "maxBufferSize", "notify": "maxBufferSizeChanged", "read": "maxBufferSize", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "efficientBufferSize", "notify": "efficientBufferSizeChanged", "read": "efficientBufferSize", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "bufferSize", "notify": "bufferSizeChanged", "read": "bufferSize", "required": false, "revision": 65281, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBufferSize"}], "qualifiedClassName": "QmlSensor", "signals": [{"access": "public", "index": 0, "name": "identifierChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "connectedToBackendChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "availableDataRatesChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "dataRateChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "readingChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "activeChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "outputRangesChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "outputRangeChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "descriptionChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "errorChanged", "returnType": "void"}, {"access": "public", "index": 10, "name": "alwaysOnChanged", "returnType": "void"}, {"access": "public", "index": 11, "name": "busyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "skipDuplicates", "type": "bool"}], "index": 12, "name": "skipDuplicatesChanged", "returnType": "void", "revision": 65281}, {"access": "public", "arguments": [{"name": "axesOrientationMode", "type": "AxesOrientationMode"}], "index": 13, "name": "axesOrientationModeChanged", "returnType": "void", "revision": 65281}, {"access": "public", "arguments": [{"name": "currentOrientation", "type": "int"}], "index": 14, "name": "currentOrientationChanged", "returnType": "void", "revision": 65281}, {"access": "public", "arguments": [{"name": "userOrientation", "type": "int"}], "index": 15, "name": "userOrientationChanged", "returnType": "void", "revision": 65281}, {"access": "public", "arguments": [{"name": "maxBufferSize", "type": "int"}], "index": 16, "name": "maxBufferSizeChanged", "returnType": "void", "revision": 65281}, {"access": "public", "arguments": [{"name": "efficientBufferSize", "type": "int"}], "index": 17, "name": "efficientBufferSizeChanged", "returnType": "void", "revision": 65281}, {"access": "public", "arguments": [{"name": "bufferSize", "type": "int"}], "index": 18, "name": "bufferSizeChanged", "returnType": "void", "revision": 65281}], "slots": [{"access": "public", "index": 19, "name": "start", "returnType": "bool"}, {"access": "public", "index": 20, "name": "stop", "returnType": "void"}, {"access": "private", "index": 21, "name": "updateReading", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}, {"classInfos": [{"name": "QML.Element", "value": "SensorReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create SensorReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlSensorReading", "lineNumber": 183, "object": true, "properties": [{"bindable": "bindableTimestamp", "constant": false, "designable": true, "final": false, "index": 0, "name": "timestamp", "notify": "timestampChanged", "read": "timestamp", "required": false, "scriptable": true, "stored": true, "type": "quint64", "user": false}], "qualifiedClassName": "QmlSensorReading", "signals": [{"access": "public", "index": 0, "name": "timestampChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmlsensor_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "QmlSensors"}, {"name": "QML.Singleton", "value": "true"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlSensorGlobal", "lineNumber": 27, "methods": [{"access": "public", "index": 1, "isConst": true, "name": "sensorTypes", "returnType": "QStringList"}, {"access": "public", "arguments": [{"name": "type", "type": "QString"}], "index": 2, "isConst": true, "name": "sensorsForType", "returnType": "QStringList"}, {"access": "public", "arguments": [{"name": "type", "type": "QString"}], "index": 3, "isConst": true, "name": "defaultSensorForType", "returnType": "QString"}], "object": true, "qualifiedClassName": "QmlSensorGlobal", "signals": [{"access": "public", "index": 0, "name": "availableSensorsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmlsensorglobal_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Range"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create Range"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlSensorRange", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "minimum", "read": "minimum", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "maximum", "read": "maximum", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "QmlSensorRange", "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "OutputRange"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create OutputRange"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlSensorOutputRange", "lineNumber": 48, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "minimum", "read": "minimum", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "maximum", "read": "maximum", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "accuracy", "read": "accuracy", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlSensorOutputRange", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmlsensorrange_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "TapSensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlTapSensor", "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "returnDoubleTapEvents", "notify": "returnDoubleTapEventsChanged", "read": "returnDoubleTapEvents", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReturnDoubleTapEvents"}], "qualifiedClassName": "QmlTapSensor", "signals": [{"access": "public", "arguments": [{"name": "returnDoubleTapEvents", "type": "bool"}], "index": 0, "name": "returnDoubleTapEventsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "TapReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create TapReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlTapSensorReading", "lineNumber": 48, "object": true, "properties": [{"bindable": "bindableTapDirection", "constant": false, "designable": true, "final": false, "index": 0, "name": "tapDirection", "notify": "tapDirectionChanged", "read": "tapDirection", "required": false, "scriptable": true, "stored": true, "type": "QTapReading::TapDirection", "user": false}, {"bindable": "bindableDoubleTap", "constant": false, "designable": true, "final": false, "index": 1, "name": "doubleTap", "notify": "isDoubleTapChanged", "read": "isDoubleTap", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QmlTapSensorReading", "signals": [{"access": "public", "index": 0, "name": "tapDirectionChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "isDoubleTapChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmltapsensor_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "TiltSensor"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlTiltSensor", "lineNumber": 25, "methods": [{"access": "public", "index": 0, "name": "calibrate", "returnType": "void"}], "object": true, "qualifiedClassName": "QmlTiltSensor", "superClasses": [{"access": "public", "name": "QmlSensor"}]}, {"classInfos": [{"name": "QML.Element", "value": "TiltReading"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Cannot create TiltReading"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QmlTiltSensorReading", "lineNumber": 43, "object": true, "properties": [{"bindable": "bindableYRotation", "constant": false, "designable": true, "final": false, "index": 0, "name": "yRotation", "notify": "yRotationChanged", "read": "yRotation", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"bindable": "bindableXRotation", "constant": false, "designable": true, "final": false, "index": 1, "name": "xRotation", "notify": "xRotationChanged", "read": "xRotation", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QmlTiltSensorReading", "signals": [{"access": "public", "index": 0, "name": "yRotationChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "xRotationChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QmlSensorReading"}]}], "inputFile": "qmltiltsensor_p.h", "outputRevision": 69}]