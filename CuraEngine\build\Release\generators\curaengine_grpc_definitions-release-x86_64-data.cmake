########### AGGREGATED COMPONENTS AND DEPENDENCIES FOR THE MULTI CONFIG #####################
#############################################################################################

set(curaengine_grpc_definitions_COMPONENT_NAMES "")
if(DEFINED curaengine_grpc_definitions_FIND_DEPENDENCY_NAMES)
  list(APPEND curaengine_grpc_definitions_FIND_DEPENDENCY_NAMES asio-grpc protobuf)
  list(REMOVE_DUPLICATES curaengine_grpc_definitions_FIND_DEPENDENCY_NAMES)
else()
  set(curaengine_grpc_definitions_FIND_DEPENDENCY_NAMES asio-grpc protobuf)
endif()
set(asio-grpc_FIND_MODE "NO_MODULE")
set(protobuf_FIND_MODE "NO_MODULE")

########### VARIABLES #######################################################################
#############################################################################################
set(curaengine_grpc_definitions_PACKAGE_FOLDER_RELEASE "C:/Users/<USER>/.conan2/p/b/curae291e1cb3d4032/p")
set(curaengine_grpc_definitions_BUILD_MODULES_PATHS_RELEASE )


set(curaengine_grpc_definitions_INCLUDE_DIRS_RELEASE "${curaengine_grpc_definitions_PACKAGE_FOLDER_RELEASE}/include")
set(curaengine_grpc_definitions_RES_DIRS_RELEASE )
set(curaengine_grpc_definitions_DEFINITIONS_RELEASE )
set(curaengine_grpc_definitions_SHARED_LINK_FLAGS_RELEASE )
set(curaengine_grpc_definitions_EXE_LINK_FLAGS_RELEASE )
set(curaengine_grpc_definitions_OBJECTS_RELEASE )
set(curaengine_grpc_definitions_COMPILE_DEFINITIONS_RELEASE )
set(curaengine_grpc_definitions_COMPILE_OPTIONS_C_RELEASE )
set(curaengine_grpc_definitions_COMPILE_OPTIONS_CXX_RELEASE )
set(curaengine_grpc_definitions_LIB_DIRS_RELEASE "${curaengine_grpc_definitions_PACKAGE_FOLDER_RELEASE}/lib")
set(curaengine_grpc_definitions_BIN_DIRS_RELEASE )
set(curaengine_grpc_definitions_LIBRARY_TYPE_RELEASE STATIC)
set(curaengine_grpc_definitions_IS_HOST_WINDOWS_RELEASE 1)
set(curaengine_grpc_definitions_LIBS_RELEASE curaengine_grpc_definitions)
set(curaengine_grpc_definitions_SYSTEM_LIBS_RELEASE )
set(curaengine_grpc_definitions_FRAMEWORK_DIRS_RELEASE )
set(curaengine_grpc_definitions_FRAMEWORKS_RELEASE )
set(curaengine_grpc_definitions_BUILD_DIRS_RELEASE )
set(curaengine_grpc_definitions_NO_SONAME_MODE_RELEASE FALSE)


# COMPOUND VARIABLES
set(curaengine_grpc_definitions_COMPILE_OPTIONS_RELEASE
    "$<$<COMPILE_LANGUAGE:CXX>:${curaengine_grpc_definitions_COMPILE_OPTIONS_CXX_RELEASE}>"
    "$<$<COMPILE_LANGUAGE:C>:${curaengine_grpc_definitions_COMPILE_OPTIONS_C_RELEASE}>")
set(curaengine_grpc_definitions_LINKER_FLAGS_RELEASE
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,SHARED_LIBRARY>:${curaengine_grpc_definitions_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,MODULE_LIBRARY>:${curaengine_grpc_definitions_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,EXECUTABLE>:${curaengine_grpc_definitions_EXE_LINK_FLAGS_RELEASE}>")


set(curaengine_grpc_definitions_COMPONENTS_RELEASE )