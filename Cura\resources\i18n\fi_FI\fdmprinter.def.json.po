# Cura JSON setting files
# Copyright (C) 2022 Ultimaker B.V.
# This file is distributed under the same license as the Cura package.
#
msgid ""
msgstr ""
"Project-Id-Version: Cura 5.1\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2023-11-24 12:51+0000\n"
"PO-Revision-Date: 2022-07-15 11:17+0200\n"
"Last-Translator: Bothof <<EMAIL>>\n"
"Language-Team: Finnish\n"
"Language: fi_FI\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 3.1.1\n"

msgctxt "ironing_inset description"
msgid "A distance to keep from the edges of the model. Ironing all the way to the edge of the mesh may result in a jagged edge on your print."
msgstr "Etäisyys mallin reunoihin. Silitys verkon reunoihin saakka voi johtaa rosoiseen reunaan tulosteessa."

msgctxt "material_no_load_move_factor description"
msgid "A factor indicating how much the filament gets compressed between the feeder and the nozzle chamber, used to determine how far to move the material for a filament switch."
msgstr ""

msgctxt "roofing_angles description"
msgid "A list of integer line directions to use when the top surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Luettelo käytettävistä linjojen kokonaislukusuunnista, kun yläpinnan pintakalvokerroksilla käytetään linja- tai siksak-kuviota. Tämän luettelon elementtejä käytetään järjestyksessä kerrosten edetessä, ja kun luettelon loppu saavutetaan, aloitetaan taas alusta. Luettelon kohteet on erotettu pilkuilla, ja koko luettelo on hakasulkeiden sisällä. Oletusarvo on tyhjä luettelo, jolloin käytetään perinteisiä oletuskulmia (45 ja 135 astetta)."

msgctxt "skin_angles description"
msgid "A list of integer line directions to use when the top/bottom layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Luettelo käytettävistä linjojen kokonaislukusuunnista, kun ylimmällä/alimmalla kerroksella käytetään linja- tai siksak-kuviota. Tämän luettelon elementtejä käytetään järjestyksessä kerrosten edetessä, ja kun luettelon loppu saavutetaan, aloitetaan taas alusta. Luettelon kohteet on erotettu pilkuilla, ja koko luettelo on hakasulkeiden sisällä. Oletusarvo on tyhjä luettelo, jolloin käytetään perinteisiä oletuskulmia (45 ja 135 astetta)."

msgctxt "support_infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angle 0 degrees."
msgstr ""

msgctxt "support_bottom_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr ""

msgctxt "support_interface_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr ""

msgctxt "support_roof_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr ""

msgctxt "infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees for the lines and zig zag patterns and 45 degrees for all other patterns)."
msgstr "Luettelo käytettävistä linjojen kokonaislukusuunnista. Tämän luettelon elementtejä käytetään järjestyksessä kerrosten edetessä, ja kun luettelon loppu saavutetaan, aloitetaan taas alusta. Luettelon kohteet on erotettu pilkuilla, ja koko luettelo on hakasulkeiden sisällä. Oletusarvo on tyhjä luettelo, jolloin käytetään perinteisiä oletuskulmia (45 ja 135 astetta linja- ja siksak-kuvioille ja 45 astetta muille kuvioille)."

msgctxt "nozzle_disallowed_areas description"
msgid "A list of polygons with areas the nozzle is not allowed to enter."
msgstr "Monikulmioluettelo, jossa on alueet, joihin suutin ei saa siirtyä."

msgctxt "machine_disallowed_areas description"
msgid "A list of polygons with areas the print head is not allowed to enter."
msgstr "Monikulmioluettelo, jossa on alueet, joihin tulostuspää ei saa siirtyä."

msgctxt "brim_inside_margin description"
msgid "A part fully enclosed inside another part can generate an outer brim that touches the inside of the other part. This removes all brim within this distance from internal holes."
msgstr ""

msgctxt "support_tree_branch_reach_limit description"
msgid "A recomendation to how far branches can move from the points they support. Branches can violate this value to reach their destination (buildplate or a flat part of the model). Lowering this value will make the support more sturdy, but increase the amount of branches (and because of that material usage/print time) "
msgstr ""

msgctxt "extruder_prime_pos_abs label"
msgid "Absolute Extruder Prime Position"
msgstr "Absoluuttinen suulakkeen esitäytön sijainti"

msgctxt "adaptive_layer_height_variation label"
msgid "Adaptive Layers Maximum Variation"
msgstr ""

msgctxt "adaptive_layer_height_threshold label"
msgid "Adaptive Layers Topography Size"
msgstr ""

msgctxt "adaptive_layer_height_variation_step label"
msgid "Adaptive Layers Variation Step Size"
msgstr ""

msgctxt "adaptive_layer_height_enabled description"
msgid "Adaptive layers computes the layer heights depending on the shape of the model."
msgstr ""

msgctxt "infill_wall_line_count description"
msgid ""
"Add extra walls around the infill area. Such walls can make top/bottom skin lines sag down less which means you need less top/bottom skin layers for the same quality at the cost of some extra material.\n"
"This feature can combine with the Connect Infill Polygons to connect all the infill into a single extrusion path without the need for travels or retractions if configured right."
msgstr ""

msgctxt "platform_adhesion description"
msgid "Adhesion"
msgstr "Tarttuvuus"

msgctxt "material_adhesion_tendency label"
msgid "Adhesion Tendency"
msgstr ""

msgctxt "skin_overlap description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines, as a percentage of the line widths of the skin lines and the innermost wall. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any percentage over 50% may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr ""

msgctxt "skin_overlap_mm description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any value over half the width of the wall may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr ""

msgctxt "infill_sparse_density description"
msgid "Adjusts the density of infill of the print."
msgstr "Säätää tulostuksen täytön tiheyttä."

msgctxt "support_interface_density description"
msgid "Adjusts the density of the roofs and floors of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Säätää tukirakenteen kattojen ja lattioiden tiheyttä. Korkeammat arvot tuottavat parempia ulokkeita, mutta tuet on vaikeampi poistaa."

msgctxt "support_tree_top_rate description"
msgid "Adjusts the density of the support structure used to generate the tips of the branches. A higher value results in better overhangs, but the supports are harder to remove. Use Support Roof for very high values or ensure support density is similarly high at the top."
msgstr ""

msgctxt "support_infill_rate description"
msgid "Adjusts the density of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Säätää tukirakenteen tiheyttä. Korkeammat arvot tuottavat parempia ulokkeita, mutta tuet on vaikeampi poistaa."

msgctxt "material_diameter description"
msgid "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament."
msgstr "Säätää käytetyn tulostuslangan halkaisijaa. Määritä tämä arvo vastaamaan käytetyn tulostuslangan halkaisijaa."

msgctxt "support_type description"
msgid "Adjusts the placement of the support structures. The placement can be set to touching build plate or everywhere. When set to everywhere the support structures will also be printed on the model."
msgstr "Säädä tukirakenteiden sijoittelua. Sijoituspaikka voidaan asettaa alustaa koskettavaksi tai kaikkialle. Kaikkialla-asetuksella tukirakenteet tulostetaan myös malliin."

msgctxt "prime_tower_wipe_enabled description"
msgid "After printing the prime tower with one nozzle, wipe the oozed material from the other nozzle off on the prime tower."
msgstr "Kun esitäyttötorni on tulostettu yhdellä suuttimella, pyyhi toisesta suuttimesta tihkunut materiaali pois esitäyttötornissa."

msgctxt "retraction_hop_after_extruder_switch description"
msgid "After the machine switched from one extruder to the other, the build plate is lowered to create clearance between the nozzle and the print. This prevents the nozzle from leaving oozed material on the outside of a print."
msgstr "Alustaa lasketaan koneen vaihdettua yhdestä suulakkeesta toiseen, jotta suuttimen ja tulosteen väliin jää tilaa. Tämä estää suutinta jättämästä tihkunutta ainetta tulosteen ulkopuolelle."

msgctxt "retraction_combing option all"
msgid "All"
msgstr "Kaikki"

msgctxt "print_sequence option all_at_once"
msgid "All at Once"
msgstr "Kaikki kerralla"

msgctxt "resolution description"
msgid "All settings that influence the resolution of the print. These settings have a large impact on the quality (and print time)"
msgstr "Kaikki tulostuksen resoluutioon vaikuttavat asetukset. Näillä asetuksilla on suuri vaikutus laatuun (ja tulostusaikaan)."

msgctxt "alternate_extra_perimeter label"
msgid "Alternate Extra Wall"
msgstr "Vuoroittainen lisäseinämä"

msgctxt "alternate_carve_order label"
msgid "Alternate Mesh Removal"
msgstr "Vuoroittainen verkon poisto"

msgctxt "material_alternate_walls label"
msgid "Alternate Wall Directions"
msgstr ""

msgctxt "material_alternate_walls description"
msgid "Alternate wall directions every other layer and inset. Useful for materials that can build up stress, like for metal printing."
msgstr ""

msgctxt "machine_buildplate_type option aluminum"
msgid "Aluminum"
msgstr ""

msgctxt "machine_always_write_active_tool label"
msgid "Always Write Active Tool"
msgstr ""

msgctxt "travel_retract_before_outer_wall description"
msgid "Always retract when moving to start an outer wall."
msgstr "Vedä aina takaisin, kun siirrytään ulkoseinämän aloittamista varten."

msgctxt "xy_offset description"
msgid "Amount of offset applied to all polygons in each layer. Positive values can compensate for too big holes; negative values can compensate for too small holes."
msgstr "Kaikkia monikulmioita kussakin kerroksessa koskeva siirtymien määrä. Positiivisilla arvoilla kompensoidaan liian suuria aukkoja ja negatiivisilla arvoilla kompensoidaan liian pieniä aukkoja."

msgctxt "xy_offset_layer_0 description"
msgid "Amount of offset applied to all polygons in the first layer. A negative value can compensate for squishing of the first layer known as \"elephant's foot\"."
msgstr "Kaikkia monikulmioita ensimmäisessä kerroksessa koskeva siirtymien määrä. Negatiivisella arvolla kompensoidaan ensimmäisen kerroksen litistymistä, joka tunnetaan \"elefantin jalkana\"."

msgctxt "support_offset description"
msgid "Amount of offset applied to all support polygons in each layer. Positive values can smooth out the support areas and result in more sturdy support."
msgstr "Kaikkia tukimonikulmioita kussakin kerroksessa koskeva siirtymien määrä. Positiivisilla arvoilla tasoitetaan tukialueita ja saadaan aikaan vankempi tuki."

msgctxt "support_bottom_offset description"
msgid "Amount of offset applied to the floors of the support."
msgstr ""

msgctxt "support_roof_offset description"
msgid "Amount of offset applied to the roofs of the support."
msgstr ""

msgctxt "support_interface_offset description"
msgid "Amount of offset applied to the support interface polygons."
msgstr ""

msgctxt "wipe_retraction_amount description"
msgid "Amount to retract the filament so it does not ooze during the wipe sequence."
msgstr ""

msgctxt "sub_div_rad_add description"
msgid "An addition to the radius from the center of each cube to check for the boundary of the model, as to decide whether this cube should be subdivided. Larger values lead to a thicker shell of small cubes near the boundary of the model."
msgstr "Lisäys säteeseen kunkin kuution keskipisteestä mallin rajojen tarkistamiseksi. Näin määritetään, tuleeko kuutioon tehdä alajako. Suuremmat arvot tuottavat paksumman kuoren pienempiin kuutioihin mallin rajojen lähellä."

msgctxt "anti_overhang_mesh label"
msgid "Anti Overhang Mesh"
msgstr "Verkko ulokkeiden estoon"

msgctxt "material_anti_ooze_retracted_position label"
msgid "Anti-ooze Retracted Position"
msgstr ""

msgctxt "material_anti_ooze_retraction_speed label"
msgid "Anti-ooze Retraction Speed"
msgstr ""

msgctxt "machine_use_extruder_offset_to_offset_coords description"
msgid "Apply the extruder offset to the coordinate system. Affects all extruders."
msgstr ""

msgctxt "interlocking_enable description"
msgid "At the locations where models touch, generate an interlocking beam structure. This improves the adhesion between models, especially models printed in different materials."
msgstr ""

msgctxt "travel_avoid_other_parts label"
msgid "Avoid Printed Parts When Traveling"
msgstr "Vältä tulostettuja osia siirtoliikkeen yhteydessä"

msgctxt "travel_avoid_supports label"
msgid "Avoid Supports When Traveling"
msgstr ""

msgctxt "z_seam_position option back"
msgid "Back"
msgstr ""

msgctxt "z_seam_position option backleft"
msgid "Back Left"
msgstr ""

msgctxt "z_seam_position option backright"
msgid "Back Right"
msgstr ""

msgctxt "machine_gcode_flavor option BFB"
msgid "Bits from Bytes"
msgstr "Bits from Bytes"

msgctxt "magic_mesh_surface_mode option both"
msgid "Both"
msgstr "Molemmat"

msgctxt "support_interface_priority option nothing"
msgid "Both overlap"
msgstr ""

msgctxt "bottom_layers label"
msgid "Bottom Layers"
msgstr "Alakerrokset"

msgctxt "top_bottom_pattern_0 label"
msgid "Bottom Pattern Initial Layer"
msgstr "Alaosan kuvio, alkukerros"

msgctxt "bottom_skin_expand_distance label"
msgid "Bottom Skin Expand Distance"
msgstr "Alapintakalvon laajennuksen etäisyys"

msgctxt "bottom_skin_preshrink label"
msgid "Bottom Skin Removal Width"
msgstr "Alapintakalvon poistoleveys"

msgctxt "bottom_thickness label"
msgid "Bottom Thickness"
msgstr "Alaosan paksuus"

msgctxt "support_tree_top_rate label"
msgid "Branch Density"
msgstr ""

msgctxt "support_tree_branch_diameter label"
msgid "Branch Diameter"
msgstr ""

msgctxt "support_tree_branch_diameter_angle label"
msgid "Branch Diameter Angle"
msgstr ""

msgctxt "material_break_preparation_retracted_position label"
msgid "Break Preparation Retracted Position"
msgstr ""

msgctxt "material_break_preparation_speed label"
msgid "Break Preparation Retraction Speed"
msgstr ""

msgctxt "material_break_preparation_temperature label"
msgid "Break Preparation Temperature"
msgstr ""

msgctxt "material_break_retracted_position label"
msgid "Break Retracted Position"
msgstr ""

msgctxt "material_break_speed label"
msgid "Break Retraction Speed"
msgstr ""

msgctxt "material_break_temperature label"
msgid "Break Temperature"
msgstr ""

msgctxt "support_skip_some_zags label"
msgid "Break Up Support In Chunks"
msgstr "Riko tuki lohkoihin"

msgctxt "bridge_fan_speed label"
msgid "Bridge Fan Speed"
msgstr ""

msgctxt "bridge_enable_more_layers label"
msgid "Bridge Has Multiple Layers"
msgstr ""

msgctxt "bridge_skin_density_2 label"
msgid "Bridge Second Skin Density"
msgstr ""

msgctxt "bridge_fan_speed_2 label"
msgid "Bridge Second Skin Fan Speed"
msgstr ""

msgctxt "bridge_skin_material_flow_2 label"
msgid "Bridge Second Skin Flow"
msgstr ""

msgctxt "bridge_skin_speed_2 label"
msgid "Bridge Second Skin Speed"
msgstr ""

msgctxt "bridge_skin_density label"
msgid "Bridge Skin Density"
msgstr ""

msgctxt "bridge_skin_material_flow label"
msgid "Bridge Skin Flow"
msgstr ""

msgctxt "bridge_skin_speed label"
msgid "Bridge Skin Speed"
msgstr ""

msgctxt "bridge_skin_support_threshold label"
msgid "Bridge Skin Support Threshold"
msgstr ""

msgctxt "bridge_sparse_infill_max_density label"
msgid "Bridge Sparse Infill Max Density"
msgstr ""

msgctxt "bridge_skin_density_3 label"
msgid "Bridge Third Skin Density"
msgstr ""

msgctxt "bridge_fan_speed_3 label"
msgid "Bridge Third Skin Fan Speed"
msgstr ""

msgctxt "bridge_skin_material_flow_3 label"
msgid "Bridge Third Skin Flow"
msgstr ""

msgctxt "bridge_skin_speed_3 label"
msgid "Bridge Third Skin Speed"
msgstr ""

msgctxt "bridge_wall_coast label"
msgid "Bridge Wall Coasting"
msgstr ""

msgctxt "bridge_wall_material_flow label"
msgid "Bridge Wall Flow"
msgstr ""

msgctxt "bridge_wall_speed label"
msgid "Bridge Wall Speed"
msgstr ""

msgctxt "adhesion_type option brim"
msgid "Brim"
msgstr "Reunus"

msgctxt "brim_gap label"
msgid "Brim Distance"
msgstr ""

msgctxt "brim_inside_margin label"
msgid "Brim Inside Avoid Margin"
msgstr ""

msgctxt "brim_line_count label"
msgid "Brim Line Count"
msgstr "Reunuksen linjaluku"

msgctxt "brim_outside_only label"
msgid "Brim Only on Outside"
msgstr "Reunus vain ulkopuolella"

msgctxt "brim_replaces_support label"
msgid "Brim Replaces Support"
msgstr ""

msgctxt "brim_width label"
msgid "Brim Width"
msgstr "Reunuksen leveys"

msgctxt "platform_adhesion label"
msgid "Build Plate Adhesion"
msgstr "Alustan tarttuvuus"

msgctxt "adhesion_extruder_nr label"
msgid "Build Plate Adhesion Extruder"
msgstr "Alustan tarttuvuuden suulake"

msgctxt "adhesion_type label"
msgid "Build Plate Adhesion Type"
msgstr "Alustan tarttuvuustyyppi"

msgctxt "machine_buildplate_type label"
msgid "Build Plate Material"
msgstr ""

msgctxt "machine_shape label"
msgid "Build Plate Shape"
msgstr "Alustan muoto"

msgctxt "material_bed_temperature label"
msgid "Build Plate Temperature"
msgstr "Alustan lämpötila"

msgctxt "material_bed_temperature_layer_0 label"
msgid "Build Plate Temperature Initial Layer"
msgstr "Alustan lämpötila (alkukerros)"

msgctxt "build_volume_temperature label"
msgid "Build Volume Temperature"
msgstr ""

msgctxt "prime_tower_brim_enable description"
msgid "By enabling this setting, your prime-tower will get a brim, even if the model doesn't. If you want a sturdier base for a high tower, you can increase the base height."
msgstr ""

msgctxt "center_object label"
msgid "Center Object"
msgstr ""

msgctxt "conical_overhang_enabled description"
msgid "Change the geometry of the printed model such that minimal support is required. Steep overhangs will become shallow overhangs. Overhanging areas will drop down to become more vertical."
msgstr "Muuttaa tulostettavan mallin geometriaa niin, että tarvitaan mahdollisimman vähän tukea. Jyrkistä ulokkeista tulee matalia ulokkeita. Ulokkeiset alueet putoavat alas, ja niistä tulee pystysuorempia."

msgctxt "support_structure description"
msgid "Chooses between the techniques available to generate support. \"Normal\" support creates a support structure directly below the overhanging parts and drops those areas straight down. \"Tree\" support creates branches towards the overhanging areas that support the model on the tips of those branches, and allows the branches to crawl around the model to support it from the build plate as much as possible."
msgstr ""

msgctxt "coasting_speed label"
msgid "Coasting Speed"
msgstr "Vapaaliukunopeus"

msgctxt "coasting_volume label"
msgid "Coasting Volume"
msgstr "Vapaaliu'un ainemäärä"

msgctxt "coasting_enable description"
msgid "Coasting replaces the last part of an extrusion path with a travel path. The oozed material is used to print the last piece of the extrusion path in order to reduce stringing."
msgstr "Vapaaliu'ulla siirtoreitti korvaa pursotusreitin viimeisen osan. Tihkuvalla aineella tulostetaan pursotusreitin viimeinen osuus rihmoittumisen vähentämiseksi."

msgctxt "retraction_combing label"
msgid "Combing Mode"
msgstr "Pyyhkäisytila"

msgctxt "retraction_combing description"
msgid "Combing keeps the nozzle within already printed areas when traveling. This results in slightly longer travel moves but reduces the need for retractions. If combing is off, the material will retract and the nozzle moves in a straight line to the next point. It is also possible to avoid combing over top/bottom skin areas or to only comb within the infill."
msgstr ""

msgctxt "command_line_settings label"
msgid "Command Line Settings"
msgstr "Komentorivin asetukset"

msgctxt "infill_pattern option concentric"
msgid "Concentric"
msgstr "Samankeskinen"

msgctxt "ironing_pattern option concentric"
msgid "Concentric"
msgstr "Samankeskinen"

msgctxt "roofing_pattern option concentric"
msgid "Concentric"
msgstr "Samankeskinen"

msgctxt "support_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Samankeskinen"

msgctxt "support_interface_pattern option concentric"
msgid "Concentric"
msgstr "Samankeskinen"

msgctxt "support_pattern option concentric"
msgid "Concentric"
msgstr "Samankeskinen"

msgctxt "support_roof_pattern option concentric"
msgid "Concentric"
msgstr "Samankeskinen"

msgctxt "top_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Samankeskinen"

msgctxt "top_bottom_pattern_0 option concentric"
msgid "Concentric"
msgstr "Samankeskinen"

msgctxt "support_conical_angle label"
msgid "Conical Support Angle"
msgstr "Kartiomaisen tuen kulma"

msgctxt "support_conical_min_width label"
msgid "Conical Support Minimum Width"
msgstr "Kartioimaisen tuen minimileveys"

msgctxt "zig_zaggify_infill label"
msgid "Connect Infill Lines"
msgstr "Yhdistä täyttölinjat"

msgctxt "connect_infill_polygons label"
msgid "Connect Infill Polygons"
msgstr ""

msgctxt "zig_zaggify_support label"
msgid "Connect Support Lines"
msgstr ""

msgctxt "support_connect_zigzags label"
msgid "Connect Support ZigZags"
msgstr "Yhdistä tuki-siksakit"

msgctxt "connect_skin_polygons label"
msgid "Connect Top/Bottom Polygons"
msgstr ""

msgctxt "connect_infill_polygons description"
msgid "Connect infill paths where they run next to each other. For infill patterns which consist of several closed polygons, enabling this setting greatly reduces the travel time."
msgstr ""

msgctxt "support_connect_zigzags description"
msgid "Connect the ZigZags. This will increase the strength of the zig zag support structure."
msgstr "Yhdistä siksakit. Tämä lisää siksak-tukirakenteen lujuutta."

msgctxt "zig_zaggify_support description"
msgid "Connect the ends of the support lines together. Enabling this setting can make your support more sturdy and reduce underextrusion, but it will cost more material."
msgstr ""

msgctxt "zig_zaggify_infill description"
msgid "Connect the ends where the infill pattern meets the inner wall using a line which follows the shape of the inner wall. Enabling this setting can make the infill adhere to the walls better and reduce the effects of infill on the quality of vertical surfaces. Disabling this setting reduces the amount of material used."
msgstr ""

msgctxt "connect_skin_polygons description"
msgid "Connect top/bottom skin paths where they run next to each other. For the concentric pattern enabling this setting greatly reduces the travel time, but because the connections can happen midway over infill this feature can reduce the top surface quality."
msgstr ""

msgctxt "z_seam_corner description"
msgid "Control whether corners on the model outline influence the position of the seam. None means that corners have no influence on the seam position. Hide Seam makes the seam more likely to occur on an inside corner. Expose Seam makes the seam more likely to occur on an outside corner. Hide or Expose Seam makes the seam more likely to occur at an inside or outside corner. Smart Hiding allows both inside and outside corners, but chooses inside corners more frequently, if appropriate."
msgstr ""

msgctxt "infill_multiplier description"
msgid "Convert each infill line to this many lines. The extra lines do not cross over each other, but avoid each other. This makes the infill stiffer, but increases print time and material usage."
msgstr ""

msgctxt "machine_nozzle_cool_down_speed label"
msgid "Cool Down Speed"
msgstr ""

msgctxt "cooling description"
msgid "Cooling"
msgstr "Jäähdytys"

msgctxt "cooling label"
msgid "Cooling"
msgstr "Jäähdytys"

msgctxt "infill_pattern option cross"
msgid "Cross"
msgstr "Risti"

msgctxt "support_pattern option cross"
msgid "Cross"
msgstr "Risti"

msgctxt "infill_pattern option cross_3d"
msgid "Cross 3D"
msgstr "Risti 3D"

msgctxt "cross_infill_pocket_size label"
msgid "Cross 3D Pocket Size"
msgstr "Risti 3D:n taskujen koko"

msgctxt "cross_support_density_image label"
msgid "Cross Fill Density Image for Support"
msgstr ""

msgctxt "cross_infill_density_image label"
msgid "Cross Infill Density Image"
msgstr ""

msgctxt "material_crystallinity label"
msgid "Crystalline Material"
msgstr ""

msgctxt "infill_pattern option cubic"
msgid "Cubic"
msgstr "Kuutio"

msgctxt "infill_pattern option cubicsubdiv"
msgid "Cubic Subdivision"
msgstr "Kuution alajako"

msgctxt "sub_div_rad_add label"
msgid "Cubic Subdivision Shell"
msgstr "Kuution alajakokuori"

msgctxt "cutting_mesh label"
msgid "Cutting Mesh"
msgstr "Leikkaava verkko"

msgctxt "material_flow_temp_graph description"
msgid "Data linking material flow (in mm3 per second) to temperature (degrees Celsius)."
msgstr "Tiedot, jotka yhdistävät materiaalivirran (mm3 sekunnissa) lämpötilaan (celsiusastetta)."

msgctxt "machine_acceleration label"
msgid "Default Acceleration"
msgstr "Oletuskiihtyvyys"

msgctxt "default_material_bed_temperature label"
msgid "Default Build Plate Temperature"
msgstr ""

msgctxt "machine_max_jerk_e label"
msgid "Default Filament Jerk"
msgstr "Oletusarvoinen tulostuslangan nykäisy"

msgctxt "default_material_print_temperature label"
msgid "Default Printing Temperature"
msgstr "Oletustulostuslämpötila"

msgctxt "machine_max_jerk_xy label"
msgid "Default X-Y Jerk"
msgstr "Oletusarvoinen X-Y-nykäisy"

msgctxt "machine_max_jerk_z label"
msgid "Default Z Jerk"
msgstr "Oletusarvoinen Z-nykäisy"

msgctxt "machine_max_jerk_xy description"
msgid "Default jerk for movement in the horizontal plane."
msgstr "Vaakatasoisen liikkeen oletusnykäisy."

msgctxt "machine_max_jerk_z description"
msgid "Default jerk for the motor of the Z-direction."
msgstr "Z-suunnan moottorin oletusnykäisy."

msgctxt "machine_max_jerk_e description"
msgid "Default jerk for the motor of the filament."
msgstr "Tulostuslangan moottorin oletusnykäisy."

msgctxt "bridge_settings_enabled description"
msgid "Detect bridges and modify print speed, flow and fan settings while bridges are printed."
msgstr ""

msgctxt "inset_direction description"
msgid "Determines the order in which walls are printed. Printing outer walls earlier helps with dimensional accuracy, as faults from inner walls cannot propagate to the outside. However printing them later allows them to stack better when overhangs are printed. When there is an uneven amount of total innner walls, the 'center last line' is always printed last."
msgstr ""

msgctxt "infill_mesh_order description"
msgid "Determines the priority of this mesh when considering multiple overlapping infill meshes. Areas where multiple infill meshes overlap will take on the settings of the mesh with the highest rank. An infill mesh with a higher rank will modify the infill of infill meshes with lower rank and normal meshes."
msgstr ""

msgctxt "lightning_infill_support_angle description"
msgid "Determines when a lightning infill layer has to support anything above it. Measured in the angle given the thickness of a layer."
msgstr ""

msgctxt "lightning_infill_overhang_angle description"
msgid "Determines when a lightning infill layer has to support the model above it. Measured in the angle given the thickness."
msgstr ""

msgctxt "material_diameter label"
msgid "Diameter"
msgstr "Läpimitta"

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model label"
msgid "Diameter Increase To Model"
msgstr ""

msgctxt "support_tree_bp_diameter description"
msgid "Diameter every branch tries to achieve when reaching the buildplate. Improves bed adhesion."
msgstr ""

msgctxt "adhesion_type description"
msgid "Different options that help to improve both priming your extrusion and adhesion to the build plate. Brim adds a single layer flat area around the base of your model to prevent warping. Raft adds a thick grid with a roof below the model. Skirt is a line printed around the model, but not connected to the model."
msgstr "Erilaisia vaihtoehtoja, jotka auttavat pursotuksen esitäytössä ja mallin kiinnityksessä alustaan. Reunus lisää mallin pohjan ympärille yksittäisen tasaisen alueen, joka estää vääntymistä. Pohjaristikko lisää paksun, katolla varustetun ristikon mallin alle. Helma on mallin ympärille piirrettävä viiva, joka ei kosketa mallia."

msgctxt "machine_disallowed_areas label"
msgid "Disallowed Areas"
msgstr ""

msgctxt "infill_line_distance description"
msgid "Distance between the printed infill lines. This setting is calculated by the infill density and the infill line width."
msgstr "Etäisyys tulostettujen täyttölinjojen välillä. Tämä asetus lasketaan täytön tiheydestä ja täyttölinjan leveydestä."

msgctxt "support_initial_layer_line_distance description"
msgid "Distance between the printed initial layer support structure lines. This setting is calculated by the support density."
msgstr ""

msgctxt "support_bottom_line_distance description"
msgid "Distance between the printed support floor lines. This setting is calculated by the Support Floor Density, but can be adjusted separately."
msgstr "Tulostettujen tukilattialinjojen välinen etäisyys. Tämä asetus lasketaan tukilattian tiheysarvosta, mutta sitä voidaan säätää erikseen."

msgctxt "support_roof_line_distance description"
msgid "Distance between the printed support roof lines. This setting is calculated by the Support Roof Density, but can be adjusted separately."
msgstr "Tulostettujen tukikattolinjojen välinen etäisyys. Tämä asetus lasketaan tukikaton tiheysarvosta, mutta sitä voidaan säätää erikseen."

msgctxt "support_line_distance description"
msgid "Distance between the printed support structure lines. This setting is calculated by the support density."
msgstr "Tulostettujen tukirakenteiden linjojen välinen etäisyys. Tämä asetus lasketaan tuen tiheyden perusteella."

msgctxt "support_bottom_distance description"
msgid "Distance from the print to the bottom of the support. Note that this is rounded up to the next layer height."
msgstr ""

msgctxt "support_top_distance description"
msgid "Distance from the top of the support to the print."
msgstr "Etäisyys tuen yläosasta tulosteeseen."

msgctxt "support_z_distance description"
msgid "Distance from the top/bottom of the support structure to the print. This gap provides clearance to remove the supports after the model is printed. The topmost support layer below the model might be a fraction of regular layers."
msgstr ""

msgctxt "infill_wipe_dist description"
msgid "Distance of a travel move inserted after every infill line, to make the infill stick to the walls better. This option is similar to infill overlap, but without extrusion and only on one end of the infill line."
msgstr "Siirtoliikkeen pituus jokaisen täyttölinjan jälkeen, jotta täyttö tarttuu seinämiin paremmin. Tämä vaihtoehto on samanlainen kuin täytön limitys, mutta ilman pursotusta ja tapahtuu vain toisessa päässä täyttölinjaa."

msgctxt "wall_0_wipe_dist description"
msgid "Distance of a travel move inserted after the outer wall, to hide the Z seam better."
msgstr "Siirtoliikkeen etäisyys ulkoseinämän jälkeen Z-sauman piilottamiseksi paremmin."

msgctxt "draft_shield_dist description"
msgid "Distance of the draft shield from the print, in the X/Y directions."
msgstr "Vetosuojuksen etäisyys tulosteesta X-/Y-suunnissa."

msgctxt "ooze_shield_dist description"
msgid "Distance of the ooze shield from the print, in the X/Y directions."
msgstr "Tihkusuojuksen etäisyys tulosteesta X-/Y-suunnissa."

msgctxt "support_xy_distance_overhang description"
msgid "Distance of the support structure from the overhang in the X/Y directions."
msgstr ""

msgctxt "support_xy_distance description"
msgid "Distance of the support structure from the print in the X/Y directions."
msgstr "Tukirakenteen etäisyys tulosteesta X-/Y-suunnissa."

msgctxt "meshfix_fluid_motion_shift_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr ""

msgctxt "meshfix_fluid_motion_small_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr ""

msgctxt "min_infill_area description"
msgid "Don't generate areas of infill smaller than this (use skin instead)."
msgstr "Älä muodosta tätä pienempiä täyttöalueita (käytä sen sijaan pintakalvoa)."

msgctxt "draft_shield_height label"
msgid "Draft Shield Height"
msgstr "Vetosuojuksen korkeus"

msgctxt "draft_shield_height_limitation label"
msgid "Draft Shield Limitation"
msgstr "Vetosuojuksen rajoitus"

msgctxt "draft_shield_dist label"
msgid "Draft Shield X/Y Distance"
msgstr "Vetosuojuksen X/Y-etäisyys"

msgctxt "support_mesh_drop_down label"
msgid "Drop Down Support Mesh"
msgstr "Tukiverkon pudottaminen alaspäin"

msgctxt "dual label"
msgid "Dual Extrusion"
msgstr "Kaksoispursotus"

msgctxt "machine_shape option elliptic"
msgid "Elliptic"
msgstr "Soikea"

msgctxt "acceleration_enabled label"
msgid "Enable Acceleration Control"
msgstr "Ota kiihtyvyyden hallinta käyttöön"

msgctxt "bridge_settings_enabled label"
msgid "Enable Bridge Settings"
msgstr ""

msgctxt "coasting_enable label"
msgid "Enable Coasting"
msgstr "Ota vapaaliuku käyttöön"

msgctxt "support_conical_enabled label"
msgid "Enable Conical Support"
msgstr "Ota kartiomainen tuki käyttöön"

msgctxt "draft_shield_enabled label"
msgid "Enable Draft Shield"
msgstr "Ota vetosuojus käyttöön"

msgctxt "meshfix_fluid_motion_enabled label"
msgid "Enable Fluid Motion"
msgstr ""

msgctxt "ironing_enabled label"
msgid "Enable Ironing"
msgstr "Ota silitys käyttöön"

msgctxt "jerk_enabled label"
msgid "Enable Jerk Control"
msgstr "Ota nykäisyn hallinta käyttöön"

msgctxt "machine_nozzle_temp_enabled label"
msgid "Enable Nozzle Temperature Control"
msgstr "Ota suuttimen lämpötilan hallinta käyttöön"

msgctxt "ooze_shield_enabled label"
msgid "Enable Ooze Shield"
msgstr "Ota tihkusuojus käyttöön"

msgctxt "prime_blob_enable label"
msgid "Enable Prime Blob"
msgstr "Ota esitäyttöpisara käyttöön"

msgctxt "prime_tower_enable label"
msgid "Enable Prime Tower"
msgstr "Ota esitäyttötorni käyttöön"

msgctxt "cool_fan_enabled label"
msgid "Enable Print Cooling"
msgstr "Ota tulostuksen jäähdytys käyttöön"

msgctxt "retraction_enable label"
msgid "Enable Retraction"
msgstr "Ota takaisinveto käyttöön"

msgctxt "support_brim_enable label"
msgid "Enable Support Brim"
msgstr ""

msgctxt "support_bottom_enable label"
msgid "Enable Support Floor"
msgstr "Ota tukilattia käyttöön"

msgctxt "support_interface_enable label"
msgid "Enable Support Interface"
msgstr "Ota tukiliittymä käyttöön"

msgctxt "support_roof_enable label"
msgid "Enable Support Roof"
msgstr "Ota tukikatto käyttöön"

msgctxt "acceleration_travel_enabled label"
msgid "Enable Travel Acceleration"
msgstr ""

msgctxt "jerk_travel_enabled label"
msgid "Enable Travel Jerk"
msgstr ""

msgctxt "ooze_shield_enabled description"
msgid "Enable exterior ooze shield. This will create a shell around the model which is likely to wipe a second nozzle if it's at the same height as the first nozzle."
msgstr "Ottaa ulkoisen tihkusuojuksen käyttöön. Tämä luo mallin ympärille kuoren, joka pyyhkii todennäköisesti toisen suuttimen, jos se on samalla korkeudella kuin ensimmäinen suutin."

msgctxt "small_skin_on_surface description"
msgid "Enable small (up to 'Small Top/Bottom Width') regions on the topmost skinned layer (exposed to air) to be filled with walls instead of the default pattern."
msgstr ""

msgctxt "jerk_enabled description"
msgid "Enables adjusting the jerk of print head when the velocity in the X or Y axis changes. Increasing the jerk can reduce printing time at the cost of print quality."
msgstr "Ottaa tulostuspään nykäisyn säädön käyttöön X- tai Y-akselin nopeuden muuttuessa. Nykäisyn suurentaminen saattaa vähentää tulostusaikaa tulostuslaadun kustannuksella."

msgctxt "acceleration_enabled description"
msgid "Enables adjusting the print head acceleration. Increasing the accelerations can reduce printing time at the cost of print quality."
msgstr "Ottaa tulostuspään kiihtyvyyden säädön käyttöön. Kiihtyvyyksien suurentaminen saattaa vähentää tulostusaikaa tulostuslaadun kustannuksella."

msgctxt "cool_fan_enabled description"
msgid "Enables the print cooling fans while printing. The fans improve print quality on layers with short layer times and bridging / overhangs."
msgstr "Ottaa tulostuksen jäähdytystuulettimet käyttöön tulostettaessa. Tuulettimet parantavat tulostuslaatua kerroksilla, joilla on lyhyet kerrosajat ja tukisiltoja/ulokkeita."

msgctxt "machine_end_gcode label"
msgid "End G-code"
msgstr ""

msgctxt "material_end_of_filament_purge_length label"
msgid "End of Filament Purge Length"
msgstr ""

msgctxt "material_end_of_filament_purge_speed label"
msgid "End of Filament Purge Speed"
msgstr ""

msgctxt "brim_replaces_support description"
msgid "Enforce brim to be printed around the model even if that space would otherwise be occupied by support. This replaces some regions of the first layer of support by brim regions."
msgstr ""

msgctxt "support_type option everywhere"
msgid "Everywhere"
msgstr "Kaikkialla"

msgctxt "slicing_tolerance option exclusive"
msgid "Exclusive"
msgstr ""

msgctxt "experimental label"
msgid "Experimental"
msgstr "Kokeellinen"

msgctxt "z_seam_corner option z_seam_corner_outer"
msgid "Expose Seam"
msgstr "Paljasta sauma"

msgctxt "meshfix_extensive_stitching label"
msgid "Extensive Stitching"
msgstr "Laaja silmukointi"

msgctxt "meshfix_extensive_stitching description"
msgid "Extensive stitching tries to stitch up open holes in the mesh by closing the hole with touching polygons. This option can introduce a lot of processing time."
msgstr "Laaja silmukointi yrittää peittää avonaisia reikiä verkosta sulkemalla reiän toisiaan koskettavilla monikulmioilla. Tämä vaihtoehto voi kuluttaa paljon prosessointiaikaa."

msgctxt "infill_wall_line_count label"
msgid "Extra Infill Wall Count"
msgstr ""

msgctxt "skin_outline_count label"
msgid "Extra Skin Wall Count"
msgstr "Pintakalvojen ulkopuolisten lisäseinämien määrä"

msgctxt "switch_extruder_extra_prime_amount description"
msgid "Extra material to prime after nozzle switching."
msgstr ""

msgctxt "extruder_prime_pos_x label"
msgid "Extruder Prime X Position"
msgstr "Suulakkeen esitäytön X-sijainti"

msgctxt "extruder_prime_pos_y label"
msgid "Extruder Prime Y Position"
msgstr "Suulakkeen esitäytön Y-sijainti"

msgctxt "extruder_prime_pos_z label"
msgid "Extruder Prime Z Position"
msgstr "Suulakkeen esitäytön Z-sijainti"

msgctxt "machine_extruders_share_heater label"
msgid "Extruders Share Heater"
msgstr ""

msgctxt "machine_extruders_share_nozzle label"
msgid "Extruders Share Nozzle"
msgstr ""

msgctxt "material_extrusion_cool_down_speed label"
msgid "Extrusion Cool Down Speed Modifier"
msgstr "Pursotuksen jäähtymisnopeuden lisämääre"

msgctxt "speed_equalize_flow_width_factor description"
msgid "Extrusion width based correction factor on the speed. At 0% the movement speed is kept constant at the Print Speed. At 100% the movement speed is adjusted so that the flow (in mm³/s) is kept constant, i.e. lines half the normal Line Width are printed twice as fast and lines twice as wide are printed half as fast. A value larger than 100% can help to compensate for the higher pressure required to extrude wide lines."
msgstr ""

msgctxt "cool_fan_speed label"
msgid "Fan Speed"
msgstr "Tuulettimen nopeus"

msgctxt "support_fan_enable label"
msgid "Fan Speed Override"
msgstr ""

msgctxt "small_feature_max_length description"
msgid "Feature outlines that are shorter than this length will be printed using Small Feature Speed."
msgstr ""

msgctxt "experimental description"
msgid "Features that haven't completely been fleshed out yet."
msgstr ""

msgctxt "machine_feeder_wheel_diameter label"
msgid "Feeder Wheel Diameter"
msgstr ""

msgctxt "material_final_print_temperature label"
msgid "Final Printing Temperature"
msgstr "Tulostuslämpötila lopussa"

msgctxt "machine_firmware_retract label"
msgid "Firmware Retraction"
msgstr ""

msgctxt "support_extruder_nr_layer_0 label"
msgid "First Layer Support Extruder"
msgstr "Tuen ensimmäisen kerroksen suulake"

msgctxt "material_flow label"
msgid "Flow"
msgstr "Virtaus"

msgctxt "speed_equalize_flow_width_factor label"
msgid "Flow Equalization Ratio"
msgstr ""

msgctxt "flow_rate_extrusion_offset_factor label"
msgid "Flow Rate Compensation Factor"
msgstr ""

msgctxt "flow_rate_max_extrusion_offset label"
msgid "Flow Rate Compensation Max Extrusion Offset"
msgstr ""

msgctxt "material_flow_temp_graph label"
msgid "Flow Temperature Graph"
msgstr "Virtauksen lämpötilakaavio"

msgctxt "material_flow_layer_0 description"
msgid "Flow compensation for the first layer: the amount of material extruded on the initial layer is multiplied by this value."
msgstr ""

msgctxt "skin_material_flow_layer_0 description"
msgid "Flow compensation on bottom lines of the first layer"
msgstr ""

msgctxt "infill_material_flow description"
msgid "Flow compensation on infill lines."
msgstr ""

msgctxt "support_interface_material_flow description"
msgid "Flow compensation on lines of support roof or floor."
msgstr ""

msgctxt "roofing_material_flow description"
msgid "Flow compensation on lines of the areas at the top of the print."
msgstr ""

msgctxt "prime_tower_flow description"
msgid "Flow compensation on prime tower lines."
msgstr ""

msgctxt "skirt_brim_material_flow description"
msgid "Flow compensation on skirt or brim lines."
msgstr ""

msgctxt "support_bottom_material_flow description"
msgid "Flow compensation on support floor lines."
msgstr ""

msgctxt "support_roof_material_flow description"
msgid "Flow compensation on support roof lines."
msgstr ""

msgctxt "support_material_flow description"
msgid "Flow compensation on support structure lines."
msgstr ""

msgctxt "wall_0_material_flow_layer_0 description"
msgid "Flow compensation on the outermost wall line of the first layer."
msgstr ""

msgctxt "wall_0_material_flow description"
msgid "Flow compensation on the outermost wall line."
msgstr ""

msgctxt "wall_0_material_flow_roofing description"
msgid "Flow compensation on the top surface outermost wall line."
msgstr "Virtauksen kompensointi yläpinnan uloimman seinän linjalla."

msgctxt "wall_x_material_flow_roofing description"
msgid "Flow compensation on top surface wall lines for all wall lines except the outermost one."
msgstr "Virtauksen kompensointi yläpinnan seinälinjoilla kaikille seinälinjoille paitsi uloimman linjalle."

msgctxt "skin_material_flow description"
msgid "Flow compensation on top/bottom lines."
msgstr ""

msgctxt "wall_x_material_flow_layer_0 description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one, but only for the first layer"
msgstr ""

msgctxt "wall_x_material_flow description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one."
msgstr ""

msgctxt "wall_material_flow description"
msgid "Flow compensation on wall lines."
msgstr ""

msgctxt "material_flow description"
msgid "Flow compensation: the amount of material extruded is multiplied by this value."
msgstr "Virtauksen kompensointi: pursotetun materiaalin määrä kerrotaan tällä arvolla."

msgctxt "meshfix_fluid_motion_angle label"
msgid "Fluid Motion Angle"
msgstr ""

msgctxt "meshfix_fluid_motion_shift_distance label"
msgid "Fluid Motion Shift Distance"
msgstr ""

msgctxt "meshfix_fluid_motion_small_distance label"
msgid "Fluid Motion Small Distance"
msgstr ""

msgctxt "material_flush_purge_length label"
msgid "Flush Purge Length"
msgstr ""

msgctxt "material_flush_purge_speed label"
msgid "Flush Purge Speed"
msgstr ""

msgctxt "min_wall_line_width description"
msgid "For thin structures around once or twice the nozzle size, the line widths need to be altered to adhere to the thickness of the model. This setting controls the minimum line width allowed for the walls. The minimum line widths inherently also determine the maximum line widths, since we transition from N to N+1 walls at some geometry thickness where the N walls are wide and the N+1 walls are narrow. The widest possible wall line is twice the Minimum Wall Line Width."
msgstr ""

msgctxt "z_seam_position option front"
msgid "Front"
msgstr ""

msgctxt "z_seam_position option frontleft"
msgid "Front Left"
msgstr ""

msgctxt "z_seam_position option frontright"
msgid "Front Right"
msgstr ""

msgctxt "draft_shield_height_limitation option full"
msgid "Full"
msgstr "Täysi"

msgctxt "magic_fuzzy_skin_enabled label"
msgid "Fuzzy Skin"
msgstr "Karhea pintakalvo"

msgctxt "magic_fuzzy_skin_point_density label"
msgid "Fuzzy Skin Density"
msgstr "Karhean pintakalvon tiheys"

msgctxt "magic_fuzzy_skin_outside_only label"
msgid "Fuzzy Skin Outside Only"
msgstr ""

msgctxt "magic_fuzzy_skin_point_dist label"
msgid "Fuzzy Skin Point Distance"
msgstr "Karhean pintakalvon piste-etäisyys"

msgctxt "magic_fuzzy_skin_thickness label"
msgid "Fuzzy Skin Thickness"
msgstr "Karhean pintakalvon paksuus"

msgctxt "machine_gcode_flavor label"
msgid "G-code Flavor"
msgstr ""

msgctxt "machine_end_gcode description"
msgid ""
"G-code commands to be executed at the very end - separated by \n"
"."
msgstr ""

msgctxt "machine_start_gcode description"
msgid ""
"G-code commands to be executed at the very start - separated by \n"
"."
msgstr ""

msgctxt "material_guid description"
msgid "GUID of the material. This is set automatically."
msgstr ""

msgctxt "gantry_height label"
msgid "Gantry Height"
msgstr ""

msgctxt "interlocking_enable label"
msgid "Generate Interlocking Structure"
msgstr ""

msgctxt "support_enable label"
msgid "Generate Support"
msgstr "Muodosta tuki"

msgctxt "support_brim_enable description"
msgid "Generate a brim within the support infill regions of the first layer. This brim is printed underneath the support, not around it. Enabling this setting increases the adhesion of support to the build plate."
msgstr ""

msgctxt "support_interface_enable description"
msgid "Generate a dense interface between the model and the support. This will create a skin at the top of the support on which the model is printed and at the bottom of the support, where it rests on the model."
msgstr "Muodostaa tiheän liittymän mallin ja tuen väliin. Tällä luodaan pintakalvo tulostettavan mallin tuen yläosaan ja alaosaan, jossa se lepää mallin päällä."

msgctxt "support_bottom_enable description"
msgid "Generate a dense slab of material between the bottom of the support and the model. This will create a skin between the model and support."
msgstr "Muodosta tiheä materiaalilaatta tuen alaosan ja mallin välille. Se luo pintakalvon mallin ja tuen välille."

msgctxt "support_roof_enable description"
msgid "Generate a dense slab of material between the top of support and the model. This will create a skin between the model and support."
msgstr "Muodosta tiheä materiaalilaatta tuen yläosan ja mallin välille. Se luo pintakalvon mallin ja tuen välille."

msgctxt "support_enable description"
msgid "Generate structures to support parts of the model which have overhangs. Without these structures, such parts would collapse during printing."
msgstr "Muodosta rakenteita, jotka tukevat mallin ulokkeita sisältäviä osia. Ilman tukirakenteita kyseiset osat luhistuvat tulostuksen aikana."

msgctxt "machine_buildplate_type option glass"
msgid "Glass"
msgstr ""

msgctxt "ironing_enabled description"
msgid "Go over the top surface one additional time, but this time extruding very little material. This is meant to melt the plastic on top further, creating a smoother surface. The pressure in the nozzle chamber is kept high so that the creases in the surface are filled with material."
msgstr ""

msgctxt "gradual_infill_step_height label"
msgid "Gradual Infill Step Height"
msgstr "Asteittaisen täyttöarvon korkeus"

msgctxt "gradual_infill_steps label"
msgid "Gradual Infill Steps"
msgstr "Asteittainen täyttöarvo"

msgctxt "gradual_support_infill_step_height label"
msgid "Gradual Support Infill Step Height"
msgstr "Asteittaisen tuen täyttöarvon korkeus"

msgctxt "gradual_support_infill_steps label"
msgid "Gradual Support Infill Steps"
msgstr "Asteittainen tuen täyttöarvo"

msgctxt "cool_min_temperature description"
msgid "Gradually reduce to this temperature when printing at reduced speeds because of minimum layer time."
msgstr ""

msgctxt "infill_pattern option grid"
msgid "Grid"
msgstr "Ristikko"

msgctxt "support_bottom_pattern option grid"
msgid "Grid"
msgstr "Ristikko"

msgctxt "support_interface_pattern option grid"
msgid "Grid"
msgstr "Ristikko"

msgctxt "support_pattern option grid"
msgid "Grid"
msgstr "Ristikko"

msgctxt "support_roof_pattern option grid"
msgid "Grid"
msgstr "Ristikko"

msgctxt "machine_gcode_flavor option Griffin"
msgid "Griffin"
msgstr "Griffin"

msgctxt "group_outer_walls label"
msgid "Group Outer Walls"
msgstr "Ryhmittele ulkoseinät"

msgctxt "infill_pattern option gyroid"
msgid "Gyroid"
msgstr ""

msgctxt "support_pattern option gyroid"
msgid "Gyroid"
msgstr ""

msgctxt "machine_heated_build_volume label"
msgid "Has Build Volume Temperature Stabilization"
msgstr ""

msgctxt "machine_heated_bed label"
msgid "Has Heated Build Plate"
msgstr "Sisältää lämmitettävän alustan"

msgctxt "machine_nozzle_heat_up_speed label"
msgid "Heat Up Speed"
msgstr ""

msgctxt "machine_heat_zone_length label"
msgid "Heat Zone Length"
msgstr ""

msgctxt "draft_shield_height description"
msgid "Height limitation of the draft shield. Above this height no draft shield will be printed."
msgstr "Vetosuojuksen korkeusrajoitus. Tämän korkeuden ylittävälle osalle ei tulosteta vetosuojusta."

msgctxt "z_seam_corner option z_seam_corner_inner"
msgid "Hide Seam"
msgstr "Piilota sauma"

msgctxt "z_seam_corner option z_seam_corner_any"
msgid "Hide or Expose Seam"
msgstr "Piilota tai paljasta sauma"

msgctxt "hole_xy_offset label"
msgid "Hole Horizontal Expansion"
msgstr ""

msgctxt "hole_xy_offset_max_diameter label"
msgid "Hole Horizontal Expansion Max Diameter"
msgstr ""

msgctxt "small_hole_max_size description"
msgid "Holes and part outlines with a diameter smaller than this will be printed using Small Feature Speed."
msgstr ""

msgctxt "xy_offset label"
msgid "Horizontal Expansion"
msgstr "Vaakalaajennus"

msgctxt "material_shrinkage_percentage_xy label"
msgid "Horizontal Scaling Factor Shrinkage Compensation"
msgstr ""

msgctxt "material_break_preparation_retracted_position description"
msgid "How far the filament can be stretched before it breaks, while heated."
msgstr ""

msgctxt "material_anti_ooze_retracted_position description"
msgid "How far the material needs to be retracted before it stops oozing."
msgstr ""

msgctxt "flow_rate_extrusion_offset_factor description"
msgid "How far to move the filament in order to compensate for changes in flow rate, as a percentage of how far the filament would move in one second of extrusion."
msgstr ""

msgctxt "material_break_retracted_position description"
msgid "How far to retract the filament in order to break it cleanly."
msgstr ""

msgctxt "material_break_preparation_speed description"
msgid "How fast the filament needs to be retracted just before breaking it off in a retraction."
msgstr ""

msgctxt "material_anti_ooze_retraction_speed description"
msgid "How fast the material needs to be retracted during a filament switch to prevent oozing."
msgstr ""

msgctxt "material_end_of_filament_purge_speed description"
msgid "How fast to prime the material after replacing an empty spool with a fresh spool of the same material."
msgstr ""

msgctxt "material_flush_purge_speed description"
msgid "How fast to prime the material after switching to a different material."
msgstr ""

msgctxt "material_maximum_park_duration description"
msgid "How long the material can be kept out of dry storage safely."
msgstr ""

msgctxt "machine_steps_per_mm_x description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the X direction."
msgstr ""

msgctxt "machine_steps_per_mm_y description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Y direction."
msgstr ""

msgctxt "machine_steps_per_mm_z description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Z direction."
msgstr ""

msgctxt "machine_steps_per_mm_e description"
msgid "How many steps of the stepper motors will result in moving the feeder wheel by one millimeter around its circumference."
msgstr ""

msgctxt "material_end_of_filament_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when replacing an empty spool with a fresh spool of the same material."
msgstr ""

msgctxt "material_flush_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when switching to a different material."
msgstr ""

msgctxt "machine_extruders_shared_nozzle_initial_retraction description"
msgid "How much the filament of each extruder is assumed to have been retracted from the shared nozzle tip at the completion of the printer-start gcode script; the value should be equal to or greater than the length of the common part of the nozzle's ducts."
msgstr ""

msgctxt "support_interface_priority description"
msgid "How support interface and support will interact when they overlap. Currently only implemented for support roof."
msgstr ""

msgctxt "support_tree_min_height_to_model description"
msgid "How tall a branch has to be if it is placed on the model. Prevents small blobs of support. This setting is ignored when a branch is supporting a support roof."
msgstr ""

msgctxt "bridge_skin_support_threshold description"
msgid "If a skin region is supported for less than this percentage of its area, print it using the bridge settings. Otherwise it is printed using the normal skin settings."
msgstr ""

msgctxt "meshfix_fluid_motion_angle description"
msgid "If a toolpath-segment deviates more than this angle from the general motion it is smoothed."
msgstr ""

msgctxt "bridge_enable_more_layers description"
msgid "If enabled, the second and third layers above the air are printed using the following settings. Otherwise, those layers are printed using the normal settings."
msgstr ""

msgctxt "wall_transition_filter_distance description"
msgid "If it would be transitioning back and forth between different numbers of walls in quick succession, don't transition at all. Remove transitions if they are closer together than this distance."
msgstr ""

msgctxt "raft_margin description"
msgid "If the raft is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Jos pohjaristikko on otettu käyttöön, tämä on ylimääräinen ristikkoalue malli ympärillä, jolle myös annetaan pohjaristikko. Tämän marginaalin kasvattaminen vahvistaa pohjaristikkoa, jolloin käytetään enemmän materiaalia ja tulosteelle jää vähemmän tilaa."

msgctxt "meshfix_union_all description"
msgid "Ignore the internal geometry arising from overlapping volumes within a mesh and print the volumes as one. This may cause unintended internal cavities to disappear."
msgstr "Jätetään limittyvistä ainemääristä koostuva verkon sisäinen geometria huomiotta ja tulostetaan ainemäärät yhtenä. Tämä saattaa poistaa tahattomia sisäisiä onkaloita."

msgctxt "material_bed_temp_prepend label"
msgid "Include Build Plate Temperature"
msgstr "Sisällytä alustan lämpötila"

msgctxt "material_print_temp_prepend label"
msgid "Include Material Temperatures"
msgstr "Sisällytä materiaalilämpötilat"

msgctxt "slicing_tolerance option inclusive"
msgid "Inclusive"
msgstr ""

msgctxt "infill description"
msgid "Infill"
msgstr "Täyttö"

msgctxt "infill label"
msgid "Infill"
msgstr "Täyttö"

msgctxt "acceleration_infill label"
msgid "Infill Acceleration"
msgstr "Täytön kiihtyvyys"

msgctxt "infill_before_walls label"
msgid "Infill Before Walls"
msgstr "Täyttö ennen seinämiä"

msgctxt "infill_sparse_density label"
msgid "Infill Density"
msgstr "Täytön tiheys"

msgctxt "infill_extruder_nr label"
msgid "Infill Extruder"
msgstr "Täytön suulake"

msgctxt "infill_material_flow label"
msgid "Infill Flow"
msgstr ""

msgctxt "jerk_infill label"
msgid "Infill Jerk"
msgstr "Täytön nykäisy"

msgctxt "infill_sparse_thickness label"
msgid "Infill Layer Thickness"
msgstr "Täyttökerroksen paksuus"

msgctxt "infill_angles label"
msgid "Infill Line Directions"
msgstr "Täyttölinjojen suunnat"

msgctxt "infill_line_distance label"
msgid "Infill Line Distance"
msgstr "Täyttölinjan etäisyys"

msgctxt "infill_multiplier label"
msgid "Infill Line Multiplier"
msgstr ""

msgctxt "infill_line_width label"
msgid "Infill Line Width"
msgstr "Täyttölinjan leveys"

msgctxt "infill_mesh label"
msgid "Infill Mesh"
msgstr "Täyttöverkko"

msgctxt "infill_support_angle label"
msgid "Infill Overhang Angle"
msgstr ""

msgctxt "infill_overlap_mm label"
msgid "Infill Overlap"
msgstr "Täytön limitys"

msgctxt "infill_overlap label"
msgid "Infill Overlap Percentage"
msgstr "Täytön limityksen prosentti"

msgctxt "infill_pattern label"
msgid "Infill Pattern"
msgstr "Täyttökuvio"

msgctxt "speed_infill label"
msgid "Infill Speed"
msgstr "Täyttönopeus"

msgctxt "infill_support_enabled label"
msgid "Infill Support"
msgstr ""

msgctxt "infill_enable_travel_optimization label"
msgid "Infill Travel Optimization"
msgstr ""

msgctxt "infill_wipe_dist label"
msgid "Infill Wipe Distance"
msgstr "Täyttöliikkeen etäisyys"

msgctxt "infill_offset_x label"
msgid "Infill X Offset"
msgstr ""

msgctxt "infill_offset_y label"
msgid "Infill Y Offset"
msgstr ""

msgctxt "initial_bottom_layers label"
msgid "Initial Bottom Layers"
msgstr ""

msgctxt "cool_fan_speed_0 label"
msgid "Initial Fan Speed"
msgstr "Tuulettimen nopeus alussa"

msgctxt "acceleration_layer_0 label"
msgid "Initial Layer Acceleration"
msgstr "Alkukerroksen kiihtyvyys"

msgctxt "skin_material_flow_layer_0 label"
msgid "Initial Layer Bottom Flow"
msgstr ""

msgctxt "support_tree_bp_diameter label"
msgid "Initial Layer Diameter"
msgstr ""

msgctxt "material_flow_layer_0 label"
msgid "Initial Layer Flow"
msgstr ""

msgctxt "layer_height_0 label"
msgid "Initial Layer Height"
msgstr "Alkukerroksen korkeus"

msgctxt "xy_offset_layer_0 label"
msgid "Initial Layer Horizontal Expansion"
msgstr "Alkukerroksen vaakalaajennus"

msgctxt "wall_x_material_flow_layer_0 label"
msgid "Initial Layer Inner Wall Flow"
msgstr ""

msgctxt "jerk_layer_0 label"
msgid "Initial Layer Jerk"
msgstr "Alkukerroksen nykäisy"

msgctxt "initial_layer_line_width_factor label"
msgid "Initial Layer Line Width"
msgstr "Alkukerroksen linjaleveys"

msgctxt "wall_0_material_flow_layer_0 label"
msgid "Initial Layer Outer Wall Flow"
msgstr ""

msgctxt "acceleration_print_layer_0 label"
msgid "Initial Layer Print Acceleration"
msgstr "Alkukerroksen tulostuksen kiihtyvyys"

msgctxt "jerk_print_layer_0 label"
msgid "Initial Layer Print Jerk"
msgstr "Alkukerroksen tulostuksen nykäisy"

msgctxt "speed_print_layer_0 label"
msgid "Initial Layer Print Speed"
msgstr "Alkukerroksen tulostusnopeus"

msgctxt "speed_layer_0 label"
msgid "Initial Layer Speed"
msgstr "Alkukerroksen nopeus"

msgctxt "support_initial_layer_line_distance label"
msgid "Initial Layer Support Line Distance"
msgstr ""

msgctxt "acceleration_travel_layer_0 label"
msgid "Initial Layer Travel Acceleration"
msgstr "Alkukerroksen siirtoliikkeen kiihtyvyys"

msgctxt "jerk_travel_layer_0 label"
msgid "Initial Layer Travel Jerk"
msgstr "Alkukerroksen siirtoliikkeen nykäisy"

msgctxt "speed_travel_layer_0 label"
msgid "Initial Layer Travel Speed"
msgstr "Alkukerroksen siirtoliikkeen nopeus"

msgctxt "layer_0_z_overlap label"
msgid "Initial Layer Z Overlap"
msgstr "Z Päällekkäisyys Alkukerroksen"

msgctxt "material_initial_print_temperature label"
msgid "Initial Printing Temperature"
msgstr "Tulostuslämpötila alussa"

msgctxt "acceleration_wall_x label"
msgid "Inner Wall Acceleration"
msgstr "Sisäseinämän kiihtyvyys"

msgctxt "wall_x_extruder_nr label"
msgid "Inner Wall Extruder"
msgstr ""

msgctxt "jerk_wall_x label"
msgid "Inner Wall Jerk"
msgstr "Sisäseinämän nykäisy"

msgctxt "speed_wall_x label"
msgid "Inner Wall Speed"
msgstr "Sisäseinämänopeus"

msgctxt "wall_x_material_flow label"
msgid "Inner Wall(s) Flow"
msgstr ""

msgctxt "wall_line_width_x label"
msgid "Inner Wall(s) Line Width"
msgstr "Sisäseinämien linjaleveys"

msgctxt "wall_0_inset description"
msgid "Inset applied to the path of the outer wall. If the outer wall is smaller than the nozzle, and printed after the inner walls, use this offset to get the hole in the nozzle to overlap with the inner walls instead of the outside of the model."
msgstr "Ulkoseinämän reitille asetettu liitos. Jos ulkoseinämä on pienempi kuin suutin ja se tulostetaan sisäseinämien jälkeen, tällä siirtymällä saadaan suuttimen reikä limittymään sisäseinämiin mallin ulkopuolen sijaan."

msgctxt "inset_direction option inside_out"
msgid "Inside To Outside"
msgstr ""

msgctxt "support_interface_priority option interface_lines_overwrite_support_area"
msgid "Interface lines preferred"
msgstr ""

msgctxt "support_interface_priority option interface_area_overwrite_support_area"
msgid "Interface preferred"
msgstr ""

msgctxt "interlocking_beam_layer_count label"
msgid "Interlocking Beam Layer Count"
msgstr ""

msgctxt "interlocking_beam_width label"
msgid "Interlocking Beam Width"
msgstr ""

msgctxt "interlocking_boundary_avoidance label"
msgid "Interlocking Boundary Avoidance"
msgstr ""

msgctxt "interlocking_depth label"
msgid "Interlocking Depth"
msgstr ""

msgctxt "interlocking_orientation label"
msgid "Interlocking Structure Orientation"
msgstr ""

msgctxt "ironing_only_highest_layer label"
msgid "Iron Only Highest Layer"
msgstr "Silitä vain korkein kerros"

msgctxt "acceleration_ironing label"
msgid "Ironing Acceleration"
msgstr "Silityksen kiihtyvyys"

msgctxt "ironing_flow label"
msgid "Ironing Flow"
msgstr "Silitysvirtaus"

msgctxt "ironing_inset label"
msgid "Ironing Inset"
msgstr "Silitysliitos"

msgctxt "jerk_ironing label"
msgid "Ironing Jerk"
msgstr "Silityksen nykäisy"

msgctxt "ironing_line_spacing label"
msgid "Ironing Line Spacing"
msgstr "Silityksen linjajako"

msgctxt "ironing_pattern label"
msgid "Ironing Pattern"
msgstr "Silityskuvio"

msgctxt "speed_ironing label"
msgid "Ironing Speed"
msgstr "Silitysnopeus"

msgctxt "machine_center_is_zero label"
msgid "Is Center Origin"
msgstr "On keskikohdassa"

msgctxt "material_is_support_material label"
msgid "Is support material"
msgstr ""

msgctxt "material_crystallinity description"
msgid "Is this material the type that breaks off cleanly when heated (crystalline), or is it the type that produces long intertwined polymer chains (non-crystalline)?"
msgstr ""

msgctxt "material_is_support_material description"
msgid "Is this material typically used as a support material during printing."
msgstr ""

msgctxt "magic_fuzzy_skin_outside_only description"
msgid "Jitter only the parts' outlines and not the parts' holes."
msgstr ""

msgctxt "meshfix_keep_open_polygons label"
msgid "Keep Disconnected Faces"
msgstr "Pidä erilliset pinnat"

msgctxt "layer_height label"
msgid "Layer Height"
msgstr "Kerroksen korkeus"

msgctxt "layer_start_x label"
msgid "Layer Start X"
msgstr "Kerroksen X-aloitus"

msgctxt "layer_start_y label"
msgid "Layer Start Y"
msgstr "Kerroksen Y-aloitus"

msgctxt "raft_base_thickness description"
msgid "Layer thickness of the base raft layer. This should be a thick layer which sticks firmly to the printer build plate."
msgstr "Pohjaristikon pohjakerroksen kerrospaksuus. Tämän tulisi olla paksu kerros, joka tarttuu lujasti tulostimen alustaan."

msgctxt "raft_interface_thickness description"
msgid "Layer thickness of the middle raft layer."
msgstr "Pohjaristikon keskikerroksen kerrospaksuus."

msgctxt "raft_surface_thickness description"
msgid "Layer thickness of the top raft layers."
msgstr "Pohjaristikon pintakerrosten kerrospaksuus."

msgctxt "support_skip_zag_per_mm description"
msgid "Leave out a connection between support lines once every N millimeter to make the support structure easier to break away."
msgstr "Jätä tukilinjojen välinen yhdistäminen pois joka N. millimetri, jotta tukirakenne on helpompi rikkoa."

msgctxt "z_seam_position option left"
msgid "Left"
msgstr ""

msgctxt "cool_lift_head label"
msgid "Lift Head"
msgstr "Tulostuspään nosto"

msgctxt "infill_pattern option lightning"
msgid "Lightning"
msgstr ""

msgctxt "lightning_infill_overhang_angle label"
msgid "Lightning Infill Overhang Angle"
msgstr ""

msgctxt "lightning_infill_prune_angle label"
msgid "Lightning Infill Prune Angle"
msgstr ""

msgctxt "lightning_infill_straightening_angle label"
msgid "Lightning Infill Straightening Angle"
msgstr ""

msgctxt "lightning_infill_support_angle label"
msgid "Lightning Infill Support Angle"
msgstr ""

msgctxt "support_tree_limit_branch_reach label"
msgid "Limit Branch Reach"
msgstr ""

msgctxt "support_tree_limit_branch_reach description"
msgid "Limit how far each branch should travel from the point it supports. This can make the support more sturdy, but will increase the amount of branches (and because of that material usage/print time)"
msgstr ""

msgctxt "cutting_mesh description"
msgid "Limit the volume of this mesh to within other meshes. You can use this to make certain areas of one mesh print with different settings and with a whole different extruder."
msgstr "Rajoita tämän verkon laajuus muiden verkkojen alueelle. Tällä voit määrittää tietyt yhden verkon alueet tulostumaan eri asetuksilla ja täysin eri suulakkeella."

msgctxt "draft_shield_height_limitation option limited"
msgid "Limited"
msgstr "Rajoitettu"

msgctxt "line_width label"
msgid "Line Width"
msgstr "Linjan leveys"

msgctxt "infill_pattern option lines"
msgid "Lines"
msgstr "Linjat"

msgctxt "roofing_pattern option lines"
msgid "Lines"
msgstr "Linjat"

msgctxt "support_bottom_pattern option lines"
msgid "Lines"
msgstr "Linjat"

msgctxt "support_interface_pattern option lines"
msgid "Lines"
msgstr "Linjat"

msgctxt "support_pattern option lines"
msgid "Lines"
msgstr "Linjat"

msgctxt "support_roof_pattern option lines"
msgid "Lines"
msgstr "Linjat"

msgctxt "top_bottom_pattern option lines"
msgid "Lines"
msgstr "Linjat"

msgctxt "top_bottom_pattern_0 option lines"
msgid "Lines"
msgstr "Linjat"

msgctxt "machine_gcode_flavor option MACH3"
msgid "Mach3"
msgstr "Mach3"

msgctxt "machine_settings label"
msgid "Machine"
msgstr "Laite"

msgctxt "machine_depth label"
msgid "Machine Depth"
msgstr "Laitteen syvyys"

msgctxt "machine_head_with_fans_polygon label"
msgid "Machine Head & Fan Polygon"
msgstr ""

msgctxt "machine_height label"
msgid "Machine Height"
msgstr "Laitteen korkeus"

msgctxt "machine_name label"
msgid "Machine Type"
msgstr "Laitteen tyyppi"

msgctxt "machine_width label"
msgid "Machine Width"
msgstr "Laitteen leveys"

msgctxt "machine_settings description"
msgid "Machine specific settings"
msgstr "Laitekohtaiset asetukset"

msgctxt "conical_overhang_enabled label"
msgid "Make Overhang Printable"
msgstr "Tee ulokkeesta tulostettava"

msgctxt "multiple_mesh_overlap description"
msgid "Make meshes which are touching each other overlap a bit. This makes them bond together better."
msgstr "Toisiinsa kosketuksissa olevat verkot limittyvät hieman. Tämä sitoo ne paremmin yhteen."

msgctxt "support_conical_enabled description"
msgid "Make support areas smaller at the bottom than at the overhang."
msgstr ""

msgctxt "support_mesh_drop_down description"
msgid "Make support everywhere below the support mesh, so that there's no overhang in the support mesh."
msgstr "Muodosta tukea kaikkialle tukiverkon alla, niin ettei tukiverkossa ole ulokkeita."

msgctxt "extruder_prime_pos_abs description"
msgid "Make the extruder prime position absolute rather than relative to the last-known location of the head."
msgstr "Tekee suulakkeen esitäyttösijainnista absoluuttisen eikä suhteellisen viimeksi tunnettuun pään sijaintiin nähden."

msgctxt "layer_0_z_overlap description"
msgid "Make the first and second layer of the model overlap in the Z direction to compensate for the filament lost in the airgap. All models above the first model layer will be shifted down by this amount."
msgstr "Mallin ensimmäisen ja toisen kerroksen limitys Z-suunnassa, millä kompensoidaan ilmaraossa menetettyä tulostuslankaa. Kaikki ensimmäisen mallin kerroksen yläpuolella olevat mallit siirtyvät alas tämän määrän."

msgctxt "meshfix description"
msgid "Make the meshes more suited for 3D printing."
msgstr ""

msgctxt "machine_gcode_flavor option Makerbot"
msgid "Makerbot"
msgstr "Makerbot"

msgctxt "machine_gcode_flavor option RepRap (Marlin/Sprinter)"
msgid "Marlin"
msgstr "Marlin"

msgctxt "machine_gcode_flavor option RepRap (Volumetric)"
msgid "Marlin (Volumetric)"
msgstr "Marlin (volymetrinen)"

msgctxt "material description"
msgid "Material"
msgstr "Materiaali"

msgctxt "material label"
msgid "Material"
msgstr "Materiaali"

msgctxt "material_guid label"
msgid "Material GUID"
msgstr "Materiaalin GUID"

msgctxt "max_extrusion_before_wipe label"
msgid "Material Volume Between Wipes"
msgstr ""

msgctxt "retraction_combing_max_distance label"
msgid "Max Comb Distance With No Retract"
msgstr ""

msgctxt "machine_max_acceleration_x label"
msgid "Maximum Acceleration X"
msgstr "Maksimikiihtyvyys X"

msgctxt "machine_max_acceleration_y label"
msgid "Maximum Acceleration Y"
msgstr "Maksimikiihtyvyys Y"

msgctxt "machine_max_acceleration_z label"
msgid "Maximum Acceleration Z"
msgstr "Maksimikiihtyvyys Z"

msgctxt "support_tree_angle label"
msgid "Maximum Branch Angle"
msgstr ""

msgctxt "meshfix_maximum_deviation label"
msgid "Maximum Deviation"
msgstr ""

msgctxt "meshfix_maximum_extrusion_area_deviation label"
msgid "Maximum Extrusion Area Deviation"
msgstr ""

msgctxt "cool_fan_speed_max label"
msgid "Maximum Fan Speed"
msgstr "Tuulettimen maksiminopeus"

msgctxt "machine_max_acceleration_e label"
msgid "Maximum Filament Acceleration"
msgstr "Tulostuslangan maksimikiihtyvyys"

msgctxt "conical_overhang_angle label"
msgid "Maximum Model Angle"
msgstr "Mallin maksimikulma"

msgctxt "conical_overhang_hole_size label"
msgid "Maximum Overhang Hole Area"
msgstr ""

msgctxt "material_maximum_park_duration label"
msgid "Maximum Park Duration"
msgstr ""

msgctxt "meshfix_maximum_resolution label"
msgid "Maximum Resolution"
msgstr ""

msgctxt "retraction_count_max label"
msgid "Maximum Retraction Count"
msgstr "Takaisinvedon maksimiluku"

msgctxt "max_skin_angle_for_expansion label"
msgid "Maximum Skin Angle for Expansion"
msgstr "Pintakalvon maksimikulma laajennuksessa"

msgctxt "machine_max_feedrate_e label"
msgid "Maximum Speed E"
msgstr ""

msgctxt "machine_max_feedrate_x label"
msgid "Maximum Speed X"
msgstr "Maksiminopeus X"

msgctxt "machine_max_feedrate_y label"
msgid "Maximum Speed Y"
msgstr "Maksiminopeus Y"

msgctxt "machine_max_feedrate_z label"
msgid "Maximum Speed Z"
msgstr "Maksiminopeus Z"

msgctxt "support_tower_maximum_supported_diameter label"
msgid "Maximum Tower-Supported Diameter"
msgstr ""

msgctxt "meshfix_maximum_travel_resolution label"
msgid "Maximum Travel Resolution"
msgstr ""

msgctxt "machine_max_acceleration_x description"
msgid "Maximum acceleration for the motor of the X-direction"
msgstr "X-suunnan moottorin maksimikiihtyvyys"

msgctxt "machine_max_acceleration_y description"
msgid "Maximum acceleration for the motor of the Y-direction."
msgstr "Y-suunnan moottorin maksimikiihtyvyys."

msgctxt "machine_max_acceleration_z description"
msgid "Maximum acceleration for the motor of the Z-direction."
msgstr "Z-suunnan moottorin maksimikiihtyvyys."

msgctxt "machine_max_acceleration_e description"
msgid "Maximum acceleration for the motor of the filament."
msgstr "Tulostuslangan moottorin maksimikiihtyvyys."

msgctxt "bridge_sparse_infill_max_density description"
msgid "Maximum density of infill considered to be sparse. Skin over sparse infill is considered to be unsupported and so may be treated as a bridge skin."
msgstr ""

msgctxt "support_tower_maximum_supported_diameter description"
msgid "Maximum diameter in the X/Y directions of a small area which is to be supported by a specialized support tower."
msgstr ""

msgctxt "max_extrusion_before_wipe description"
msgid "Maximum material that can be extruded before another nozzle wipe is initiated. If this value is less than the volume of material required in a layer, the setting has no effect in this layer, i.e. it is limited to one wipe per layer."
msgstr ""

msgctxt "multiple_mesh_overlap label"
msgid "Merged Meshes Overlap"
msgstr "Yhdistettyjen verkkojen limitys"

msgctxt "meshfix label"
msgid "Mesh Fixes"
msgstr "Verkkokorjaukset"

msgctxt "mesh_position_x label"
msgid "Mesh Position X"
msgstr ""

msgctxt "mesh_position_y label"
msgid "Mesh Position Y"
msgstr ""

msgctxt "mesh_position_z label"
msgid "Mesh Position Z"
msgstr ""

msgctxt "infill_mesh_order label"
msgid "Mesh Processing Rank"
msgstr ""

msgctxt "mesh_rotation_matrix label"
msgid "Mesh Rotation Matrix"
msgstr "Verkon pyöritysmatriisi"

msgctxt "slicing_tolerance option middle"
msgid "Middle"
msgstr ""

msgctxt "mold_width label"
msgid "Minimal Mold Width"
msgstr "Muotin vähimmäisleveys"

msgctxt "machine_min_cool_heat_time_window label"
msgid "Minimal Time Standby Temperature"
msgstr "Valmiuslämpötilan minimiaika"

msgctxt "bridge_wall_min_length label"
msgid "Minimum Bridge Wall Length"
msgstr ""

msgctxt "min_even_wall_line_width label"
msgid "Minimum Even Wall Line Width"
msgstr ""

msgctxt "retraction_extrusion_window label"
msgid "Minimum Extrusion Distance Window"
msgstr "Pursotuksen minimietäisyyden ikkuna"

msgctxt "min_feature_size label"
msgid "Minimum Feature Size"
msgstr ""

msgctxt "machine_minimum_feedrate label"
msgid "Minimum Feedrate"
msgstr "Minimisyöttönopeus"

msgctxt "support_tree_min_height_to_model label"
msgid "Minimum Height To Model"
msgstr ""

msgctxt "min_infill_area label"
msgid "Minimum Infill Area"
msgstr "Minimitäyttöalue"

msgctxt "cool_min_layer_time label"
msgid "Minimum Layer Time"
msgstr "Kerroksen minimiaika"

msgctxt "min_odd_wall_line_width label"
msgid "Minimum Odd Wall Line Width"
msgstr ""

msgctxt "minimum_polygon_circumference label"
msgid "Minimum Polygon Circumference"
msgstr ""

msgctxt "min_skin_width_for_expansion label"
msgid "Minimum Skin Width for Expansion"
msgstr "Pintakalvon minimileveys laajennuksessa"

msgctxt "cool_min_speed label"
msgid "Minimum Speed"
msgstr "Miniminopeus"

msgctxt "minimum_support_area label"
msgid "Minimum Support Area"
msgstr ""

msgctxt "minimum_bottom_area label"
msgid "Minimum Support Floor Area"
msgstr ""

msgctxt "minimum_interface_area label"
msgid "Minimum Support Interface Area"
msgstr ""

msgctxt "minimum_roof_area label"
msgid "Minimum Support Roof Area"
msgstr ""

msgctxt "support_xy_distance_overhang label"
msgid "Minimum Support X/Y Distance"
msgstr "Tuen X-/Y-minimietäisyys"

msgctxt "min_bead_width label"
msgid "Minimum Thin Wall Line Width"
msgstr ""

msgctxt "coasting_min_volume label"
msgid "Minimum Volume Before Coasting"
msgstr "Vähimmäisainemäärä ennen vapaaliukua"

msgctxt "min_wall_line_width label"
msgid "Minimum Wall Line Width"
msgstr ""

msgctxt "minimum_interface_area description"
msgid "Minimum area size for support interface polygons. Polygons which have an area smaller than this value will be printed as normal support."
msgstr ""

msgctxt "minimum_support_area description"
msgid "Minimum area size for support polygons. Polygons which have an area smaller than this value will not be generated."
msgstr ""

msgctxt "minimum_bottom_area description"
msgid "Minimum area size for the floors of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr ""

msgctxt "minimum_roof_area description"
msgid "Minimum area size for the roofs of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr ""

msgctxt "min_feature_size description"
msgid "Minimum thickness of thin features. Model features that are thinner than this value will not be printed, while features thicker than the Minimum Feature Size will be widened to the Minimum Wall Line Width."
msgstr ""

msgctxt "support_conical_min_width description"
msgid "Minimum width to which the base of the conical support area is reduced. Small widths can lead to unstable support structures."
msgstr "Minimileveys, johon kartiomaisen tukialueen perusta pienennetään. Pienet leveydet voivat johtaa epävakaisiin tukirakenteisiin."

msgctxt "mold_enabled label"
msgid "Mold"
msgstr "Muotti"

msgctxt "mold_angle label"
msgid "Mold Angle"
msgstr "Muotin kulma"

msgctxt "mold_roof_height label"
msgid "Mold Roof Height"
msgstr "Muotin katon korkeus"

msgctxt "ironing_monotonic label"
msgid "Monotonic Ironing Order"
msgstr ""

msgctxt "roofing_monotonic label"
msgid "Monotonic Top Surface Order"
msgstr ""

msgctxt "skin_monotonic label"
msgid "Monotonic Top/Bottom Order"
msgstr ""

msgctxt "skirt_line_count description"
msgid "Multiple skirt lines help to prime your extrusion better for small models. Setting this to 0 will disable the skirt."
msgstr "Useammat helmalinjat auttavat pursotuksen esitäytössä pienillä malleilla. Helma poistetaan käytöstä, jos arvoksi asetetaan 0."

msgctxt "initial_layer_line_width_factor description"
msgid "Multiplier of the line width on the first layer. Increasing this could improve bed adhesion."
msgstr "Ensimmäisen kerroksen linjaleveyden kerroin. Sen suurentaminen voi parantaa tarttuvuutta pöytään."

msgctxt "material_no_load_move_factor label"
msgid "No Load Move Factor"
msgstr ""

msgctxt "skin_no_small_gaps_heuristic label"
msgid "No Skin in Z Gaps"
msgstr ""

msgctxt "blackmagic description"
msgid "Non-traditional ways to print your models."
msgstr ""

msgctxt "adhesion_type option none"
msgid "None"
msgstr "Ei mikään"

msgctxt "z_seam_corner option z_seam_corner_none"
msgid "None"
msgstr "Ei mitään"

msgctxt "magic_mesh_surface_mode option normal"
msgid "Normal"
msgstr "Normaali"

msgctxt "support_structure option normal"
msgid "Normal"
msgstr ""

msgctxt "meshfix_keep_open_polygons description"
msgid "Normally Cura tries to stitch up small holes in the mesh and remove parts of a layer with big holes. Enabling this option keeps those parts which cannot be stitched. This option should be used as a last resort option when everything else fails to produce proper g-code."
msgstr ""

msgctxt "retraction_combing option noskin"
msgid "Not in Skin"
msgstr ""

msgctxt "retraction_combing option no_outer_surfaces"
msgid "Not on Outer Surface"
msgstr ""

msgctxt "machine_nozzle_expansion_angle label"
msgid "Nozzle Angle"
msgstr ""

msgctxt "machine_nozzle_size label"
msgid "Nozzle Diameter"
msgstr "Suuttimen läpimitta"

msgctxt "nozzle_disallowed_areas label"
msgid "Nozzle Disallowed Areas"
msgstr "Suuttimen kielletyt alueet"

msgctxt "machine_nozzle_id label"
msgid "Nozzle ID"
msgstr "Suuttimen tunnus"

msgctxt "machine_nozzle_head_distance label"
msgid "Nozzle Length"
msgstr ""

msgctxt "switch_extruder_extra_prime_amount label"
msgid "Nozzle Switch Extra Prime Amount"
msgstr ""

msgctxt "switch_extruder_prime_speed label"
msgid "Nozzle Switch Prime Speed"
msgstr "Suuttimen vaihdon esitäyttönopeus"

msgctxt "switch_extruder_retraction_speed label"
msgid "Nozzle Switch Retract Speed"
msgstr "Suuttimen vaihdon takaisinvetonopeus"

msgctxt "switch_extruder_retraction_amount label"
msgid "Nozzle Switch Retraction Distance"
msgstr "Suuttimen vaihdon takaisinvetoetäisyys"

msgctxt "switch_extruder_retraction_speeds label"
msgid "Nozzle Switch Retraction Speed"
msgstr "Suuttimen vaihdon takaisinvetonopeus"

msgctxt "machine_extruder_count label"
msgid "Number of Extruders"
msgstr "Suulakkeiden määrä"

msgctxt "extruders_enabled_count label"
msgid "Number of Extruders That Are Enabled"
msgstr ""

msgctxt "speed_slowdown_layers label"
msgid "Number of Slower Layers"
msgstr "Hitaampien kerrosten määrä"

msgctxt "extruders_enabled_count description"
msgid "Number of extruder trains that are enabled; automatically set in software"
msgstr ""

msgctxt "machine_extruder_count description"
msgid "Number of extruder trains. An extruder train is the combination of a feeder, bowden tube, and nozzle."
msgstr "Suulakeryhmien määrä. Suulakeryhmä on syöttölaitteen, Bowden-putken ja suuttimen yhdistelmä."

msgctxt "wipe_repeat_count description"
msgid "Number of times to move the nozzle across the brush."
msgstr ""

msgctxt "gradual_infill_steps description"
msgid "Number of times to reduce the infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Infill Density."
msgstr "Määrä kertoja, joilla täytön tiheyttä vähennetään puolella kauemmaksi yläpintojen alle siirryttäessä. Yläpintoja lähempänä olevista alueista tulee tiheämpiä enintään täytön tiheyden arvoon asti."

msgctxt "gradual_support_infill_steps description"
msgid "Number of times to reduce the support infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Support Infill Density."
msgstr "Määrä kertoja, joilla tuen täytön tiheyttä vähennetään puolella kauemmaksi yläpintojen alle siirryttäessä. Yläpintoja lähempänä olevista alueista tulee tiheämpiä enintään tuen täytön tiheyden arvoon asti."

msgctxt "infill_pattern option tetrahedral"
msgid "Octet"
msgstr "Oktetti"

msgctxt "retraction_combing option off"
msgid "Off"
msgstr "Pois"

msgctxt "mesh_position_x description"
msgid "Offset applied to the object in the x direction."
msgstr "Siirtymää sovelletaan kohteeseen X-suunnassa."

msgctxt "mesh_position_y description"
msgid "Offset applied to the object in the y direction."
msgstr "Siirtymää sovelletaan kohteeseen Y-suunnassa."

msgctxt "mesh_position_z description"
msgid "Offset applied to the object in the z direction. With this you can perform what was used to be called 'Object Sink'."
msgstr "Kappaleessa käytetty siirtymä z-suunnassa. Tällä toiminnolla voit suorittaa aiemmin ”kappaleen upotukseksi” kutsutun toiminnon."

msgctxt "machine_use_extruder_offset_to_offset_coords label"
msgid "Offset with Extruder"
msgstr ""

msgctxt "support_tree_rest_preference option buildplate"
msgid "On buildplate when possible"
msgstr ""

msgctxt "support_tree_rest_preference option graceful"
msgid "On model if required"
msgstr ""

msgctxt "print_sequence option one_at_a_time"
msgid "One at a Time"
msgstr "Yksi kerrallaan"

msgctxt "retraction_hop_only_when_collides description"
msgid "Only perform a Z Hop when moving over printed parts which cannot be avoided by horizontal motion by Avoid Printed Parts when Traveling."
msgstr "Suorita Z-hyppy vain siirryttäessä sellaisten tulostettujen osien yli, jota ei voi välttää vaakaliikkeellä toiminnolla ”Vältä tulostettuja osia siirtoliikkeen yhteydessä”."

msgctxt "ironing_only_highest_layer description"
msgid "Only perform ironing on the very last layer of the mesh. This saves time if the lower layers don't need a smooth surface finish."
msgstr "Suorita silitys vain verkon viimeisessä kerroksessa. Tämä säästää aikaa, jos alemmat kerrokset eivät edellytä sileää pintaviimeistelyä."

msgctxt "brim_outside_only description"
msgid "Only print the brim on the outside of the model. This reduces the amount of brim you need to remove afterwards, while it doesn't reduce the bed adhesion that much."
msgstr "Tulostaa reunuksen vain mallin ulkopuolelle. Tämä vähentää myöhemmin poistettavan reunuksen määrää, mutta se ei juurikaan vähennä pöydän tarttuvuutta."

msgctxt "ooze_shield_angle label"
msgid "Ooze Shield Angle"
msgstr "Tihkusuojuksen kulma"

msgctxt "ooze_shield_dist label"
msgid "Ooze Shield Distance"
msgstr "Tihkusuojuksen etäisyys"

msgctxt "support_tree_branch_reach_limit label"
msgid "Optimal Branch Range"
msgstr ""

msgctxt "optimize_wall_printing_order label"
msgid "Optimize Wall Printing Order"
msgstr "Optimoi seinämien tulostusjärjestys"

msgctxt "optimize_wall_printing_order description"
msgid "Optimize the order in which walls are printed so as to reduce the number of retractions and the distance travelled. Most parts will benefit from this being enabled but some may actually take longer so please compare the print time estimates with and without optimization. First layer is not optimized when choosing brim as build plate adhesion type."
msgstr ""

msgctxt "machine_nozzle_tip_outer_diameter label"
msgid "Outer Nozzle Diameter"
msgstr ""

msgctxt "acceleration_wall_0 label"
msgid "Outer Wall Acceleration"
msgstr "Ulkoseinämän kiihtyvyys"

msgctxt "wall_0_extruder_nr label"
msgid "Outer Wall Extruder"
msgstr "Ulkoseinämän suulake"

msgctxt "wall_0_material_flow label"
msgid "Outer Wall Flow"
msgstr ""

msgctxt "wall_0_inset label"
msgid "Outer Wall Inset"
msgstr "Ulkoseinämän liitos"

msgctxt "jerk_wall_0 label"
msgid "Outer Wall Jerk"
msgstr "Ulkoseinämän nykäisy"

msgctxt "wall_line_width_0 label"
msgid "Outer Wall Line Width"
msgstr "Ulkoseinämän linjaleveys"

msgctxt "speed_wall_0 label"
msgid "Outer Wall Speed"
msgstr "Ulkoseinämänopeus"

msgctxt "wall_0_wipe_dist label"
msgid "Outer Wall Wipe Distance"
msgstr "Ulkoseinämän täyttöliikkeen etäisyys"

msgctxt "group_outer_walls description"
msgid "Outer walls of different islands in the same layer are printed in sequence. When enabled the amount of flow changes is limited because walls are printed one type at a time, when disabled the number of travels between islands is reduced because walls in the same islands are grouped."
msgstr "Samaan kerrokseen kuuluvat eri saarten ulkoseinät tulostetaan peräkkäin. Kun toiminto on käytössä, virtauksen muutosten määrä on rajoitettu, koska seinät tulostetaan yksi kerrallaan. Kun toiminto on pois päältä, matkojen määrä saarten välillä vähenee, koska saman saaren seinät ryhmitellään."

msgctxt "inset_direction option outside_in"
msgid "Outside To Inside"
msgstr ""

msgctxt "wall_overhang_angle label"
msgid "Overhanging Wall Angle"
msgstr ""

msgctxt "wall_overhang_speed_factor label"
msgid "Overhanging Wall Speed"
msgstr ""

msgctxt "wall_overhang_speed_factor description"
msgid "Overhanging walls will be printed at this percentage of their normal print speed."
msgstr ""

msgctxt "wipe_pause description"
msgid "Pause after the unretract."
msgstr ""

msgctxt "bridge_fan_speed description"
msgid "Percentage fan speed to use when printing bridge walls and skin."
msgstr ""

msgctxt "bridge_fan_speed_2 description"
msgid "Percentage fan speed to use when printing the second bridge skin layer."
msgstr ""

msgctxt "support_supported_skin_fan_speed description"
msgid "Percentage fan speed to use when printing the skin regions immediately above the support. Using a high fan speed can make the support easier to remove."
msgstr ""

msgctxt "bridge_fan_speed_3 description"
msgid "Percentage fan speed to use when printing the third bridge skin layer."
msgstr ""

msgctxt "minimum_polygon_circumference description"
msgid "Polygons in sliced layers that have a circumference smaller than this amount will be filtered out. Lower values lead to higher resolution mesh at the cost of slicing time. It is meant mostly for high resolution SLA printers and very tiny 3D models with a lot of details."
msgstr ""

msgctxt "support_tree_angle_slow label"
msgid "Preferred Branch Angle"
msgstr ""

msgctxt "wall_transition_filter_deviation description"
msgid "Prevent transitioning back and forth between one extra wall and one less. This margin extends the range of line widths which follow to [Minimum Wall Line Width - Margin, 2 * Minimum Wall Line Width + Margin]. Increasing this margin reduces the number of transitions, which reduces the number of extrusion starts/stops and travel time. However, large line width variation can lead to under- or overextrusion problems."
msgstr ""

msgctxt "acceleration_prime_tower label"
msgid "Prime Tower Acceleration"
msgstr "Esitäyttötornin kiihtyvyys"

msgctxt "prime_tower_brim_enable label"
msgid "Prime Tower Base"
msgstr ""

msgctxt "prime_tower_base_height label"
msgid "Prime Tower Base Height"
msgstr ""

msgctxt "prime_tower_base_size label"
msgid "Prime Tower Base Size"
msgstr ""

msgctxt "prime_tower_base_curve_magnitude label"
msgid "Prime Tower Base Slope"
msgstr ""

msgctxt "prime_tower_flow label"
msgid "Prime Tower Flow"
msgstr "Esitäyttötornin virtaus"

msgctxt "jerk_prime_tower label"
msgid "Prime Tower Jerk"
msgstr "Esitäyttötornin nykäisy"

msgctxt "prime_tower_line_width label"
msgid "Prime Tower Line Width"
msgstr "Esitäyttötornin linjan leveys"

msgctxt "prime_tower_min_volume label"
msgid "Prime Tower Minimum Volume"
msgstr "Esitäyttötornin minimiainemäärä"

msgctxt "prime_tower_raft_base_line_spacing label"
msgid "Prime Tower Raft Line Spacing"
msgstr ""

msgctxt "prime_tower_size label"
msgid "Prime Tower Size"
msgstr "Esitäyttötornin koko"

msgctxt "speed_prime_tower label"
msgid "Prime Tower Speed"
msgstr "Esitäyttötornin nopeus"

msgctxt "prime_tower_position_x label"
msgid "Prime Tower X Position"
msgstr "Esitäyttötornin X-sijainti"

msgctxt "prime_tower_position_y label"
msgid "Prime Tower Y Position"
msgstr "Esitäyttötornin Y-sijainti"

msgctxt "acceleration_print label"
msgid "Print Acceleration"
msgstr "Tulostuksen kiihtyvyys"

msgctxt "jerk_print label"
msgid "Print Jerk"
msgstr "Tulostuksen nykäisy"

msgctxt "print_sequence label"
msgid "Print Sequence"
msgstr "Tulostusjärjestys"

msgctxt "speed_print label"
msgid "Print Speed"
msgstr "Tulostusnopeus"

msgctxt "fill_outline_gaps label"
msgid "Print Thin Walls"
msgstr "Tulosta ohuet seinämät"

msgctxt "prime_tower_enable description"
msgid "Print a tower next to the print which serves to prime the material after each nozzle switch."
msgstr "Tulosta tulosteen viereen torni, jolla materiaali esitäytetään aina suuttimen vaihdon jälkeen."

msgctxt "infill_support_enabled description"
msgid "Print infill structures only where tops of the model should be supported. Enabling this reduces print time and material usage, but leads to ununiform object strength."
msgstr ""

msgctxt "ironing_monotonic description"
msgid "Print ironing lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr ""

msgctxt "mold_enabled description"
msgid "Print models as a mold, which can be cast in order to get a model which resembles the models on the build plate."
msgstr "Tulosta malleja muotteina, jotka voidaan valaa niin, että saadaan alustalla olevia malleja muistuttava malli."

msgctxt "fill_outline_gaps description"
msgid "Print pieces of the model which are horizontally thinner than the nozzle size."
msgstr "Tulostaa mallin kohtia, jotka ovat vaakasuunnassa suuttimen kokoa ohuempia."

msgctxt "bridge_skin_speed_2 description"
msgid "Print speed to use when printing the second bridge skin layer."
msgstr ""

msgctxt "bridge_skin_speed_3 description"
msgid "Print speed to use when printing the third bridge skin layer."
msgstr ""

msgctxt "infill_before_walls description"
msgid "Print the infill before printing the walls. Printing the walls first may lead to more accurate walls, but overhangs print worse. Printing the infill first leads to sturdier walls, but the infill pattern might sometimes show through the surface."
msgstr "Tulostetaan täyttö ennen seinien tulostamista. Seinien tulostaminen ensin saattaa johtaa tarkempiin seiniin, mutta ulokkeet tulostuvat huonommin. Täytön tulostaminen ensin johtaa tukevampiin seiniin, mutta täyttökuvio saattaa joskus näkyä pinnan läpi."

msgctxt "roofing_monotonic description"
msgid "Print top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr ""

msgctxt "skin_monotonic description"
msgid "Print top/bottom lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr ""

msgctxt "material_print_temperature label"
msgid "Printing Temperature"
msgstr "Tulostuslämpötila"

msgctxt "material_print_temperature_layer_0 label"
msgid "Printing Temperature Initial Layer"
msgstr "Alkukerroksen tulostuslämpötila"

msgctxt "skirt_height description"
msgid "Printing the innermost skirt line with multiple layers makes it easy to remove the skirt."
msgstr ""

msgctxt "alternate_extra_perimeter description"
msgid "Prints an extra wall at every other layer. This way infill gets caught between these extra walls, resulting in stronger prints."
msgstr "Tulostaa ylimääräisen seinämän joka toiseen kerrokseen. Näin täyttömateriaali jää kiinni näiden lisäseinämien väliin, mikä johtaa vahvempiin tulosteisiin."

msgctxt "resolution label"
msgid "Quality"
msgstr "Laatu"

msgctxt "infill_pattern option quarter_cubic"
msgid "Quarter Cubic"
msgstr "Neljänneskuutio"

msgctxt "adhesion_type option raft"
msgid "Raft"
msgstr "Pohjaristikko"

msgctxt "raft_airgap label"
msgid "Raft Air Gap"
msgstr "Pohjaristikon ilmarako"

msgctxt "raft_base_extruder_nr label"
msgid "Raft Base Extruder"
msgstr ""

msgctxt "raft_base_fan_speed label"
msgid "Raft Base Fan Speed"
msgstr "Pohjaristikon pohjan tuulettimen nopeus"

msgctxt "raft_base_line_spacing label"
msgid "Raft Base Line Spacing"
msgstr ""

msgctxt "raft_base_line_width label"
msgid "Raft Base Line Width"
msgstr "Pohjaristikon pohjan linjaleveys"

msgctxt "raft_base_acceleration label"
msgid "Raft Base Print Acceleration"
msgstr "Pohjaristikon pohjan tulostuksen kiihtyvyys"

msgctxt "raft_base_jerk label"
msgid "Raft Base Print Jerk"
msgstr "Pohjaristikon pohjan tulostuksen nykäisy"

msgctxt "raft_base_speed label"
msgid "Raft Base Print Speed"
msgstr "Pohjaristikon pohjan tulostusnopeus"

msgctxt "raft_base_thickness label"
msgid "Raft Base Thickness"
msgstr "Pohjaristikon pohjan paksuus"

msgctxt "raft_base_wall_count label"
msgid "Raft Base Wall Count"
msgstr ""

msgctxt "raft_margin label"
msgid "Raft Extra Margin"
msgstr "Pohjaristikon lisämarginaali"

msgctxt "raft_fan_speed label"
msgid "Raft Fan Speed"
msgstr "Pohjaristikon tuulettimen nopeus"

msgctxt "raft_interface_extruder_nr label"
msgid "Raft Middle Extruder"
msgstr ""

msgctxt "raft_interface_fan_speed label"
msgid "Raft Middle Fan Speed"
msgstr "Pohjaristikon keskikerroksen tuulettimen nopeus"

msgctxt "raft_interface_layers label"
msgid "Raft Middle Layers"
msgstr ""

msgctxt "raft_interface_line_width label"
msgid "Raft Middle Line Width"
msgstr "Pohjaristikon keskikerroksen linjaleveys"

msgctxt "raft_interface_acceleration label"
msgid "Raft Middle Print Acceleration"
msgstr "Pohjaristikon keskikerroksen tulostuksen kiihtyvyys"

msgctxt "raft_interface_jerk label"
msgid "Raft Middle Print Jerk"
msgstr "Pohjaristikon keskikerroksen tulostuksen nykäisy"

msgctxt "raft_interface_speed label"
msgid "Raft Middle Print Speed"
msgstr "Pohjaristikon keskikerroksen tulostusnopeus"

msgctxt "raft_interface_line_spacing label"
msgid "Raft Middle Spacing"
msgstr "Pohjaristikon keskikerroksen linjajako"

msgctxt "raft_interface_thickness label"
msgid "Raft Middle Thickness"
msgstr "Pohjaristikon keskikerroksen paksuus"

msgctxt "raft_acceleration label"
msgid "Raft Print Acceleration"
msgstr "Pohjaristikon tulostuksen kiihtyvyys"

msgctxt "raft_jerk label"
msgid "Raft Print Jerk"
msgstr "Pohjaristikon tulostuksen nykäisy"

msgctxt "raft_speed label"
msgid "Raft Print Speed"
msgstr "Pohjaristikon tulostusnopeus"

msgctxt "raft_smoothing label"
msgid "Raft Smoothing"
msgstr "Pohjaristikon tasoitus"

msgctxt "raft_surface_extruder_nr label"
msgid "Raft Top Extruder"
msgstr ""

msgctxt "raft_surface_fan_speed label"
msgid "Raft Top Fan Speed"
msgstr "Pohjaristikon pinnan tuulettimen nopeus"

msgctxt "raft_surface_thickness label"
msgid "Raft Top Layer Thickness"
msgstr "Pohjaristikon pintakerroksen paksuus"

msgctxt "raft_surface_layers label"
msgid "Raft Top Layers"
msgstr "Pohjaristikon pintakerrokset"

msgctxt "raft_surface_line_width label"
msgid "Raft Top Line Width"
msgstr "Pohjaristikon pinnan linjaleveys"

msgctxt "raft_surface_acceleration label"
msgid "Raft Top Print Acceleration"
msgstr "Pohjaristikon pinnan tulostuksen kiihtyvyys"

msgctxt "raft_surface_jerk label"
msgid "Raft Top Print Jerk"
msgstr "Pohjaristikon pinnan tulostuksen nykäisy"

msgctxt "raft_surface_speed label"
msgid "Raft Top Print Speed"
msgstr "Pohjaristikon pinnan tulostusnopeus"

msgctxt "raft_surface_line_spacing label"
msgid "Raft Top Spacing"
msgstr "Pohjaristikon pinnan linjajako"

msgctxt "z_seam_type option random"
msgid "Random"
msgstr "Satunnainen"

msgctxt "infill_randomize_start_location label"
msgid "Randomize Infill Start"
msgstr ""

msgctxt "infill_randomize_start_location description"
msgid "Randomize which infill line is printed first. This prevents one segment becoming the strongest, but it does so at the cost of an additional travel move."
msgstr ""

msgctxt "magic_fuzzy_skin_enabled description"
msgid "Randomly jitter while printing the outer wall, so that the surface has a rough and fuzzy look."
msgstr "Satunnainen värinä tulostettaessa ulkoseinämää, jotta pinta näyttää viimeistelemättömältä ja karhealta."

msgctxt "machine_shape option rectangular"
msgid "Rectangular"
msgstr "Suorakulmainen"

msgctxt "cool_fan_speed_min label"
msgid "Regular Fan Speed"
msgstr "Normaali tuulettimen nopeus"

msgctxt "cool_fan_full_at_height label"
msgid "Regular Fan Speed at Height"
msgstr "Normaali tuulettimen nopeus korkeudella"

msgctxt "cool_fan_full_layer label"
msgid "Regular Fan Speed at Layer"
msgstr "Normaali tuulettimen nopeus kerroksessa"

msgctxt "cool_min_layer_time_fan_speed_max label"
msgid "Regular/Maximum Fan Speed Threshold"
msgstr "Tuulettimen normaali-/maksiminopeuden raja-arvo"

msgctxt "relative_extrusion label"
msgid "Relative Extrusion"
msgstr "Suhteellinen pursotus"

msgctxt "meshfix_union_all_remove_holes label"
msgid "Remove All Holes"
msgstr "Poista kaikki reiät"

msgctxt "remove_empty_first_layers label"
msgid "Remove Empty First Layers"
msgstr ""

msgctxt "carve_multiple_volumes label"
msgid "Remove Mesh Intersection"
msgstr "Poista verkon leikkauspiste"

msgctxt "raft_remove_inside_corners label"
msgid "Remove Raft Inside Corners"
msgstr ""

msgctxt "carve_multiple_volumes description"
msgid "Remove areas where multiple meshes are overlapping with each other. This may be used if merged dual material objects overlap with each other."
msgstr "Poistaa alueet, joissa useat verkot ovat limittäin toistensa kanssa. Tätä voidaan käyttää, jos yhdistetyt kaksoismateriaalikappaleet ovat limittäin toistensa kanssa."

msgctxt "remove_empty_first_layers description"
msgid "Remove empty layers beneath the first printed layer if they are present. Disabling this setting can cause empty first layers if the Slicing Tolerance setting is set to Exclusive or Middle."
msgstr ""

msgctxt "raft_remove_inside_corners description"
msgid "Remove inside corners from the raft, causing the raft to become convex."
msgstr ""

msgctxt "meshfix_union_all_remove_holes description"
msgid "Remove the holes in each layer and keep only the outside shape. This will ignore any invisible internal geometry. However, it also ignores layer holes which can be viewed from above or below."
msgstr "Poistaa kaikki reiät kustakin kerroksesta ja pitää vain ulkopuolisen muodon. Tällä jätetään näkymätön sisäinen geometria huomiotta. Se kuitenkin jättää huomiotta myös kerrosten reiät, jotka voidaan nähdä ylä- tai alapuolelta."

msgctxt "machine_gcode_flavor option RepRap (RepRap)"
msgid "RepRap"
msgstr "RepRap"

msgctxt "machine_gcode_flavor option Repetier"
msgid "Repetier"
msgstr "Repetier"

msgctxt "skin_outline_count description"
msgid "Replaces the outermost part of the top/bottom pattern with a number of concentric lines. Using one or two lines improves roofs that start on infill material."
msgstr "Korvaa ylä-/alakuvion uloimman osan samankeskisillä linjoilla. Yhden tai kahden linjan käyttäminen parantaa kattoja, jotka alkavat täyttömateriaalin keskeltä."

msgctxt "support_tree_rest_preference label"
msgid "Rest Preference"
msgstr ""

msgctxt "travel_retract_before_outer_wall label"
msgid "Retract Before Outer Wall"
msgstr "Vedä takaisin ennen ulkoseinämää"

msgctxt "retract_at_layer_change label"
msgid "Retract at Layer Change"
msgstr "Takaisinveto kerroksen muuttuessa"

msgctxt "retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr ""

msgctxt "wipe_retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr ""

msgctxt "retract_at_layer_change description"
msgid "Retract the filament when the nozzle is moving to the next layer."
msgstr "Vedä tulostuslanka takaisin, kun suutin on siirtymässä seuraavaan kerrokseen."

msgctxt "retraction_amount label"
msgid "Retraction Distance"
msgstr "Takaisinvetoetäisyys"

msgctxt "retraction_extra_prime_amount label"
msgid "Retraction Extra Prime Amount"
msgstr "Takaisinvedon esitäytön lisäys"

msgctxt "retraction_min_travel label"
msgid "Retraction Minimum Travel"
msgstr "Takaisinvedon minimiliike"

msgctxt "retraction_prime_speed label"
msgid "Retraction Prime Speed"
msgstr "Takaisinvedon esitäyttönopeus"

msgctxt "retraction_retract_speed label"
msgid "Retraction Retract Speed"
msgstr "Takaisinvedon vetonopeus"

msgctxt "retraction_speed label"
msgid "Retraction Speed"
msgstr "Takaisinvetonopeus"

msgctxt "z_seam_position option right"
msgid "Right"
msgstr ""

msgctxt "machine_scale_fan_speed_zero_to_one label"
msgid "Scale Fan Speed To 0-1"
msgstr ""

msgctxt "machine_scale_fan_speed_zero_to_one description"
msgid "Scale the fan speed to be between 0 and 1 instead of between 0 and 256."
msgstr ""

msgctxt "material_shrinkage_percentage label"
msgid "Scaling Factor Shrinkage Compensation"
msgstr ""

msgctxt "support_meshes_present label"
msgid "Scene Has Support Meshes"
msgstr ""

msgctxt "z_seam_corner label"
msgid "Seam Corner Preference"
msgstr "Saumakulmien asetus"

msgctxt "draft_shield_height_limitation description"
msgid "Set the height of the draft shield. Choose to print the draft shield at the full height of the model or at a limited height."
msgstr "Aseta vetosuojuksen korkeus. Valitse, tulostetaanko vetosuojus koko mallin korkuisena vai rajoitetun korkuisena."

msgctxt "dual description"
msgid "Settings used for printing with multiple extruders."
msgstr "Asetukset, joita käytetään monilla suulakkeilla tulostukseen."

msgctxt "command_line_settings description"
msgid "Settings which are only used if CuraEngine isn't called from the Cura frontend."
msgstr "Asetukset, joita käytetään vain jos CuraEnginea ei kutsuta Cura-edustaohjelmasta."

msgctxt "machine_extruders_shared_nozzle_initial_retraction label"
msgid "Shared Nozzle Initial Retraction"
msgstr ""

msgctxt "z_seam_type option sharpest_corner"
msgid "Sharpest Corner"
msgstr "Terävin kulma"

msgctxt "shell description"
msgid "Shell"
msgstr "Kuori"

msgctxt "z_seam_type option shortest"
msgid "Shortest"
msgstr "Lyhin"

msgctxt "machine_show_variants label"
msgid "Show Machine Variants"
msgstr "Näytä laitteen variantit"

msgctxt "skin_edge_support_layers label"
msgid "Skin Edge Support Layers"
msgstr ""

msgctxt "skin_edge_support_thickness label"
msgid "Skin Edge Support Thickness"
msgstr ""

msgctxt "expand_skins_expand_distance label"
msgid "Skin Expand Distance"
msgstr "Pintakalvon laajennuksen etäisyys"

msgctxt "skin_overlap_mm label"
msgid "Skin Overlap"
msgstr "Pintakalvon limitys"

msgctxt "skin_overlap label"
msgid "Skin Overlap Percentage"
msgstr "Pintakalvon limityksen prosentti"

msgctxt "skin_preshrink label"
msgid "Skin Removal Width"
msgstr "Pintakalvon poistoleveys"

msgctxt "min_skin_width_for_expansion description"
msgid "Skin areas narrower than this are not expanded. This avoids expanding the narrow skin areas that are created when the model surface has a slope close to the vertical."
msgstr "Tätä kapeampia pintakalvoja ei laajenneta. Tällä vältetään laajentamasta kapeita pintakalvoja, jotka syntyvät, kun mallin pinnalla on rinne lähellä pystysuoraa osuutta."

msgctxt "support_zag_skip_count description"
msgid "Skip one in every N connection lines to make the support structure easier to break away."
msgstr "Ohita joka N. yhdistämislinja, jotta tukirakenne on helpompi rikkoa."

msgctxt "support_skip_some_zags description"
msgid "Skip some support line connections to make the support structure easier to break away. This setting is applicable to the Zig Zag support infill pattern."
msgstr "Ohita jotkin tukilinjojen yhdistämiset, jotta tukirakenne on helpompi rikkoa. Tämä asetus soveltuu siksak-tukitäyttökuvioon."

msgctxt "adhesion_type option skirt"
msgid "Skirt"
msgstr "Helma"

msgctxt "skirt_gap label"
msgid "Skirt Distance"
msgstr "Helman etäisyys"

msgctxt "skirt_height label"
msgid "Skirt Height"
msgstr ""

msgctxt "skirt_line_count label"
msgid "Skirt Line Count"
msgstr "Helman linjaluku"

msgctxt "acceleration_skirt_brim label"
msgid "Skirt/Brim Acceleration"
msgstr "Helman/reunuksen kiihtyvyys"

msgctxt "skirt_brim_extruder_nr label"
msgid "Skirt/Brim Extruder"
msgstr ""

msgctxt "skirt_brim_material_flow label"
msgid "Skirt/Brim Flow"
msgstr ""

msgctxt "jerk_skirt_brim label"
msgid "Skirt/Brim Jerk"
msgstr "Helman/reunuksen nykäisy"

msgctxt "skirt_brim_line_width label"
msgid "Skirt/Brim Line Width"
msgstr "Helma-/reunuslinjan leveys"

msgctxt "skirt_brim_minimal_length label"
msgid "Skirt/Brim Minimum Length"
msgstr "Helman/reunuksen minimipituus"

msgctxt "skirt_brim_speed label"
msgid "Skirt/Brim Speed"
msgstr "Helman/reunuksen nopeus"

msgctxt "slicing_tolerance label"
msgid "Slicing Tolerance"
msgstr ""

msgctxt "small_feature_speed_factor_0 label"
msgid "Small Feature Initial Layer Speed"
msgstr ""

msgctxt "small_feature_max_length label"
msgid "Small Feature Max Length"
msgstr ""

msgctxt "small_feature_speed_factor label"
msgid "Small Feature Speed"
msgstr ""

msgctxt "small_hole_max_size label"
msgid "Small Hole Max Size"
msgstr ""

msgctxt "cool_min_temperature label"
msgid "Small Layer Printing Temperature"
msgstr "Tulostuslämpötila lopussa"

msgctxt "small_skin_on_surface label"
msgid "Small Top/Bottom On Surface"
msgstr ""

msgctxt "small_skin_width label"
msgid "Small Top/Bottom Width"
msgstr ""

msgctxt "small_feature_speed_factor_0 description"
msgid "Small features on the first layer will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr ""

msgctxt "small_feature_speed_factor description"
msgid "Small features will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr ""

msgctxt "small_skin_width description"
msgid "Small top/bottom regions are filled with walls instead of the default top/bottom pattern. This helps to avoids jerky motions. Off for the topmost (air-exposed) layer by default (see 'Small Top/Bottom On Surface')."
msgstr ""

msgctxt "brim_smart_ordering label"
msgid "Smart Brim"
msgstr ""

msgctxt "z_seam_corner option z_seam_corner_weighted"
msgid "Smart Hiding"
msgstr ""

msgctxt "smooth_spiralized_contours label"
msgid "Smooth Spiralized Contours"
msgstr "Kierukoitujen ääriviivojen tasoittaminen"

msgctxt "smooth_spiralized_contours description"
msgid "Smooth the spiralized contours to reduce the visibility of the Z seam (the Z seam should be barely visible on the print but will still be visible in the layer view). Note that smoothing will tend to blur fine surface details."
msgstr ""

msgctxt "retraction_extra_prime_amount description"
msgid "Some material can ooze away during a travel move, which can be compensated for here."
msgstr "Siirtoliikkeen yhteydessä materiaalia voi tihkua pois. Sitä voidaan kompensoida tässä."

msgctxt "wipe_retraction_extra_prime_amount description"
msgid "Some material can ooze away during a wipe travel moves, which can be compensated for here."
msgstr ""

msgctxt "blackmagic label"
msgid "Special Modes"
msgstr "Erikoistilat"

msgctxt "speed description"
msgid "Speed"
msgstr "Nopeus"

msgctxt "speed label"
msgid "Speed"
msgstr "Nopeus"

msgctxt "wipe_hop_speed description"
msgid "Speed to move the z-axis during the hop."
msgstr ""

msgctxt "magic_spiralize label"
msgid "Spiralize Outer Contour"
msgstr "Kierukoi ulompi ääriviiva"

msgctxt "magic_spiralize description"
msgid "Spiralize smooths out the Z move of the outer edge. This will create a steady Z increase over the whole print. This feature turns a solid model into a single walled print with a solid bottom. This feature should only be enabled when each layer only contains a single part."
msgstr "Kierukointi pehmentää ulkoreunan Z-liikettä. Se muodostaa tasaisen Z-lisän koko tulosteelle. Tämä toiminto muuttaa umpinaisen mallin yksiseinäiseksi tulosteeksi, jossa on umpinainen pohja. Tämä toiminto kannattaa ottaa käyttöön vain, jos jokaisessa kerroksessa on vain yksi osa."

msgctxt "material_standby_temperature label"
msgid "Standby Temperature"
msgstr "Valmiuslämpötila"

msgctxt "machine_start_gcode label"
msgid "Start G-code"
msgstr ""

msgctxt "z_seam_type description"
msgid "Starting point of each path in a layer. When paths in consecutive layers start at the same point a vertical seam may show on the print. When aligning these near a user specified location, the seam is easiest to remove. When placed randomly the inaccuracies at the paths' start will be less noticeable. When taking the shortest path the print will be quicker."
msgstr "Kerroksen kunkin reitin aloituskohta. Kun peräkkäisissä kerroksissa olevat reitit alkavat samasta kohdasta, tulosteessa saattaa näkyä pystysauma. Kun nämä kohdistetaan lähelle käyttäjän määrittämää kohtaa, sauma on helpompi poistaa. Satunnaisesti sijoittuneina reitin aloituskohdan epätarkkuudet ovat vähemmän silmiinpistäviä. Lyhintä reittiä käyttäen tulostus on nopeampaa."

msgctxt "machine_steps_per_mm_e label"
msgid "Steps per Millimeter (E)"
msgstr ""

msgctxt "machine_steps_per_mm_x label"
msgid "Steps per Millimeter (X)"
msgstr ""

msgctxt "machine_steps_per_mm_y label"
msgid "Steps per Millimeter (Y)"
msgstr ""

msgctxt "machine_steps_per_mm_z label"
msgid "Steps per Millimeter (Z)"
msgstr ""

msgctxt "support description"
msgid "Support"
msgstr "Tuki"

msgctxt "support label"
msgid "Support"
msgstr "Tuki"

msgctxt "acceleration_support label"
msgid "Support Acceleration"
msgstr "Tuen kiihtyvyys"

msgctxt "support_bottom_distance label"
msgid "Support Bottom Distance"
msgstr "Tuen alaosan etäisyys"

msgctxt "support_bottom_wall_count label"
msgid "Support Bottom Wall Line Count"
msgstr "Tukilohkolinjaluku"

msgctxt "support_brim_line_count label"
msgid "Support Brim Line Count"
msgstr ""

msgctxt "support_brim_width label"
msgid "Support Brim Width"
msgstr ""

msgctxt "support_zag_skip_count label"
msgid "Support Chunk Line Count"
msgstr "Tukilohkolinjaluku"

msgctxt "support_skip_zag_per_mm label"
msgid "Support Chunk Size"
msgstr "Tukilohkon koko"

msgctxt "support_infill_rate label"
msgid "Support Density"
msgstr "Tuen tiheys"

msgctxt "support_xy_overrides_z label"
msgid "Support Distance Priority"
msgstr "Tuen etäisyyden prioriteetti"

msgctxt "support_extruder_nr label"
msgid "Support Extruder"
msgstr "Tuen suulake"

msgctxt "acceleration_support_bottom label"
msgid "Support Floor Acceleration"
msgstr "Tukilattian kiihtyvyys"

msgctxt "support_bottom_density label"
msgid "Support Floor Density"
msgstr "Tukilattian tiheys"

msgctxt "support_bottom_extruder_nr label"
msgid "Support Floor Extruder"
msgstr "Tukilattian suulake"

msgctxt "support_bottom_material_flow label"
msgid "Support Floor Flow"
msgstr ""

msgctxt "support_bottom_offset label"
msgid "Support Floor Horizontal Expansion"
msgstr ""

msgctxt "jerk_support_bottom label"
msgid "Support Floor Jerk"
msgstr "Tukilattian nykäisy"

msgctxt "support_bottom_angles label"
msgid "Support Floor Line Directions"
msgstr ""

msgctxt "support_bottom_line_distance label"
msgid "Support Floor Line Distance"
msgstr "Tukilattian linjaetäisyys"

msgctxt "support_bottom_line_width label"
msgid "Support Floor Line Width"
msgstr "Tukilattian linjaleveys"

msgctxt "support_bottom_pattern label"
msgid "Support Floor Pattern"
msgstr "Tukilattian kuvio"

msgctxt "speed_support_bottom label"
msgid "Support Floor Speed"
msgstr "Tukilattian nopeus"

msgctxt "support_bottom_height label"
msgid "Support Floor Thickness"
msgstr "Tukilattian paksuus"

msgctxt "support_material_flow label"
msgid "Support Flow"
msgstr ""

msgctxt "support_offset label"
msgid "Support Horizontal Expansion"
msgstr "Tuen vaakalaajennus"

msgctxt "acceleration_support_infill label"
msgid "Support Infill Acceleration"
msgstr "Tuen täytön kiihtyvyys"

msgctxt "support_infill_extruder_nr label"
msgid "Support Infill Extruder"
msgstr "Tuen täytön suulake"

msgctxt "jerk_support_infill label"
msgid "Support Infill Jerk"
msgstr "Tuen täytön nykäisy"

msgctxt "support_infill_sparse_thickness label"
msgid "Support Infill Layer Thickness"
msgstr "Tuen täyttökerroksen paksuus"

msgctxt "support_infill_angles label"
msgid "Support Infill Line Directions"
msgstr ""

msgctxt "speed_support_infill label"
msgid "Support Infill Speed"
msgstr "Tuen täytön nopeus"

msgctxt "acceleration_support_interface label"
msgid "Support Interface Acceleration"
msgstr "Tukiliittymän kiihtyvyys"

msgctxt "support_interface_density label"
msgid "Support Interface Density"
msgstr "Tukiliittymän tiheys"

msgctxt "support_interface_extruder_nr label"
msgid "Support Interface Extruder"
msgstr "Tukiliittymän suulake"

msgctxt "support_interface_material_flow label"
msgid "Support Interface Flow"
msgstr ""

msgctxt "support_interface_offset label"
msgid "Support Interface Horizontal Expansion"
msgstr ""

msgctxt "jerk_support_interface label"
msgid "Support Interface Jerk"
msgstr "Tukiliittymän nykäisy"

msgctxt "support_interface_angles label"
msgid "Support Interface Line Directions"
msgstr ""

msgctxt "support_interface_line_width label"
msgid "Support Interface Line Width"
msgstr "Tukiliittymän linjan leveys"

msgctxt "support_interface_pattern label"
msgid "Support Interface Pattern"
msgstr "Tukiliittymän kuvio"

msgctxt "support_interface_priority label"
msgid "Support Interface Priority"
msgstr ""

msgctxt "support_interface_skip_height label"
msgid "Support Interface Resolution"
msgstr "Tukiliittymän resoluutio"

msgctxt "speed_support_interface label"
msgid "Support Interface Speed"
msgstr "Tukiliittymän nopeus"

msgctxt "support_interface_height label"
msgid "Support Interface Thickness"
msgstr "Tukiliittymän paksuus"

msgctxt "support_interface_wall_count label"
msgid "Support Interface Wall Line Count"
msgstr "Tukiliittymän linjan leveys"

msgctxt "jerk_support label"
msgid "Support Jerk"
msgstr "Tuen nykäisy"

msgctxt "support_join_distance label"
msgid "Support Join Distance"
msgstr "Tuen liitosetäisyys"

msgctxt "support_line_distance label"
msgid "Support Line Distance"
msgstr "Tukilinjojen etäisyys"

msgctxt "support_line_width label"
msgid "Support Line Width"
msgstr "Tukilinjan leveys"

msgctxt "support_mesh label"
msgid "Support Mesh"
msgstr "Tukiverkko"

msgctxt "support_angle label"
msgid "Support Overhang Angle"
msgstr "Tuen ulokkeen kulma"

msgctxt "support_pattern label"
msgid "Support Pattern"
msgstr "Tukikuvio"

msgctxt "support_type label"
msgid "Support Placement"
msgstr "Tuen sijoittelu"

msgctxt "acceleration_support_roof label"
msgid "Support Roof Acceleration"
msgstr "Tukikaton kiihtyvyys"

msgctxt "support_roof_density label"
msgid "Support Roof Density"
msgstr "Tukikaton tiheys"

msgctxt "support_roof_extruder_nr label"
msgid "Support Roof Extruder"
msgstr "Tukikaton suulake"

msgctxt "support_roof_material_flow label"
msgid "Support Roof Flow"
msgstr ""

msgctxt "support_roof_offset label"
msgid "Support Roof Horizontal Expansion"
msgstr ""

msgctxt "jerk_support_roof label"
msgid "Support Roof Jerk"
msgstr "Tukikaton nykäisy"

msgctxt "support_roof_angles label"
msgid "Support Roof Line Directions"
msgstr ""

msgctxt "support_roof_line_distance label"
msgid "Support Roof Line Distance"
msgstr "Tukikaton linjaetäisyys"

msgctxt "support_roof_line_width label"
msgid "Support Roof Line Width"
msgstr "Tukikaton linjaleveys"

msgctxt "support_roof_pattern label"
msgid "Support Roof Pattern"
msgstr "Tukikaton kuvio"

msgctxt "speed_support_roof label"
msgid "Support Roof Speed"
msgstr "Tukikaton nopeus"

msgctxt "support_roof_height label"
msgid "Support Roof Thickness"
msgstr "Tukikaton paksuus"

msgctxt "support_roof_wall_count label"
msgid "Support Roof Wall Line Count"
msgstr "Tukikaton linjaleveys"

msgctxt "speed_support label"
msgid "Support Speed"
msgstr "Tukirakenteen nopeus"

msgctxt "support_bottom_stair_step_height label"
msgid "Support Stair Step Height"
msgstr "Tuen porrasnousun korkeus"

msgctxt "support_bottom_stair_step_width label"
msgid "Support Stair Step Maximum Width"
msgstr "Tukiportaiden askelman enimmäisleveys"

msgctxt "support_bottom_stair_step_min_slope label"
msgid "Support Stair Step Minimum Slope Angle"
msgstr ""

msgctxt "support_structure label"
msgid "Support Structure"
msgstr ""

msgctxt "support_top_distance label"
msgid "Support Top Distance"
msgstr "Tuen yläosan etäisyys"

msgctxt "support_wall_count label"
msgid "Support Wall Line Count"
msgstr ""

msgctxt "support_xy_distance label"
msgid "Support X/Y Distance"
msgstr "Tuen X-/Y-etäisyys"

msgctxt "support_z_distance label"
msgid "Support Z Distance"
msgstr "Tuen Z-etäisyys"

msgctxt "support_interface_priority option support_lines_overwrite_interface_area"
msgid "Support lines preferred"
msgstr ""

msgctxt "support_interface_priority option support_area_overwrite_interface_area"
msgid "Support preferred"
msgstr ""

msgctxt "support_supported_skin_fan_speed label"
msgid "Supported Skin Fan Speed"
msgstr ""

msgctxt "magic_mesh_surface_mode option surface"
msgid "Surface"
msgstr "Pinta"

msgctxt "material_surface_energy label"
msgid "Surface Energy"
msgstr ""

msgctxt "magic_mesh_surface_mode label"
msgid "Surface Mode"
msgstr "Pintatila"

msgctxt "material_adhesion_tendency description"
msgid "Surface adhesion tendency."
msgstr ""

msgctxt "material_surface_energy description"
msgid "Surface energy."
msgstr ""

msgctxt "brim_smart_ordering description"
msgid "Swap print order of the innermost and second innermost brim lines. This improves brim removal."
msgstr ""

msgctxt "alternate_carve_order description"
msgid "Switch to which mesh intersecting volumes will belong with every layer, so that the overlapping meshes become interwoven. Turning this setting off will cause one of the meshes to obtain all of the volume in the overlap, while it is removed from the other meshes."
msgstr "Määrittää, mitkä verkon leikkaustilavuudet kuuluvat jokaiseen kerrokseen, jotta limittäiset verkot yhdistetään. Jos tämä asetus poistetaan käytöstä, yksi verkoista saa kaiken tilavuuden limityksessä, ja verkko poistetaan muista verkoista."

msgctxt "adaptive_layer_height_threshold description"
msgid "Target horizontal distance between two adjacent layers. Reducing this setting causes thinner layers to be used to bring the edges of the layers closer together."
msgstr ""

msgctxt "layer_start_x description"
msgid "The X coordinate of the position near where to find the part to start printing each layer."
msgstr "X-koordinaatti kohdalle, jonka läheltä aloitetaan kunkin kerroksen tulostus."

msgctxt "z_seam_x description"
msgid "The X coordinate of the position near where to start printing each part in a layer."
msgstr "X-koordinaatti kohdalle, jonka läheltä aloitetaan kunkin kerroksen osuuden tulostus."

msgctxt "extruder_prime_pos_x description"
msgid "The X coordinate of the position where the nozzle primes at the start of printing."
msgstr "X-koordinaatti sijainnille, jossa suutin esitäytetään tulostusta aloitettaessa."

msgctxt "layer_start_y description"
msgid "The Y coordinate of the position near where to find the part to start printing each layer."
msgstr "Y-koordinaatti kohdalle, jonka läheltä aloitetaan kunkin kerroksen tulostus."

msgctxt "z_seam_y description"
msgid "The Y coordinate of the position near where to start printing each part in a layer."
msgstr "Y-koordinaatti kohdalle, jonka läheltä aloitetaan kunkin kerroksen osuuden tulostus."

msgctxt "extruder_prime_pos_y description"
msgid "The Y coordinate of the position where the nozzle primes at the start of printing."
msgstr "Y-koordinaatti sijainnille, jossa suutin esitäytetään tulostusta aloitettaessa."

msgctxt "extruder_prime_pos_z description"
msgid "The Z coordinate of the position where the nozzle primes at the start of printing."
msgstr "Z-koordinaatti sijainnille, jossa suutin esitäytetään tulostusta aloitettaessa."

msgctxt "acceleration_print_layer_0 description"
msgid "The acceleration during the printing of the initial layer."
msgstr "Alkukerroksen tulostuksen aikainen kiihtyvyys."

msgctxt "acceleration_layer_0 description"
msgid "The acceleration for the initial layer."
msgstr "Alkukerroksen kiihtyvyys."

msgctxt "acceleration_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "Alkukerroksen siirtoliikkeiden kiihtyvyys."

msgctxt "jerk_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "Alkukerroksen siirtoliikkeiden kiihtyvyys."

msgctxt "acceleration_wall_x description"
msgid "The acceleration with which all inner walls are printed."
msgstr "Kiihtyvyys, jolla kaikki sisäseinämät tulostetaan."

msgctxt "acceleration_infill description"
msgid "The acceleration with which infill is printed."
msgstr "Kiihtyvyys, jolla täyttö tulostetaan."

msgctxt "acceleration_ironing description"
msgid "The acceleration with which ironing is performed."
msgstr "Kiihtyvyys, jolla silitys suoritetaan."

msgctxt "acceleration_print description"
msgid "The acceleration with which printing happens."
msgstr "Kiihtyvyys, jolla tulostetaan."

msgctxt "raft_base_acceleration description"
msgid "The acceleration with which the base raft layer is printed."
msgstr "Kiihtyvyys, jolla pohjaristikon pohjakerros tulostetaan."

msgctxt "acceleration_support_bottom description"
msgid "The acceleration with which the floors of support are printed. Printing them at lower acceleration can improve adhesion of support on top of your model."
msgstr "Kiihtyvyys, jolla tuen lattiat tulostetaan. Niiden tulostus hitaammalla kiihtyvyydellä voi parantaa mallin yläosan tuen kiinnittymistä."

msgctxt "acceleration_support_infill description"
msgid "The acceleration with which the infill of support is printed."
msgstr "Kiihtyvyys, jolla tuen täyttö tulostetaan."

msgctxt "raft_interface_acceleration description"
msgid "The acceleration with which the middle raft layer is printed."
msgstr "Kiihtyvyys, jolla pohjaristikon keskikerros tulostetaan."

msgctxt "acceleration_wall_0 description"
msgid "The acceleration with which the outermost walls are printed."
msgstr "Kiihtyvyys, jolla ulkoseinämät tulostetaan."

msgctxt "acceleration_prime_tower description"
msgid "The acceleration with which the prime tower is printed."
msgstr "Kiihtyvyys, jolla esitäyttötorni tulostetaan."

msgctxt "raft_acceleration description"
msgid "The acceleration with which the raft is printed."
msgstr "Kiihtyvyys, jolla pohjaristikko tulostetaan."

msgctxt "acceleration_support_interface description"
msgid "The acceleration with which the roofs and floors of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "Kiihtyvyys, jolla tuen katot ja lattiat tulostetaan. Niiden tulostus hitaammalla kiihtyvyydellä voi parantaa ulokkeen laatua."

msgctxt "acceleration_support_roof description"
msgid "The acceleration with which the roofs of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "Kiihtyvyys, jolla tuen katot tulostetaan. Niiden tulostus hitaammalla kiihtyvyydellä voi parantaa ulokkeen laatua."

msgctxt "acceleration_skirt_brim description"
msgid "The acceleration with which the skirt and brim are printed. Normally this is done with the initial layer acceleration, but sometimes you might want to print the skirt or brim at a different acceleration."
msgstr "Kiihtyvyys, jolla helma ja reunus tulostetaan. Yleensä se tehdään alkukerroksen kiihtyvyydellä. Joskus helma tai reunus halutaan kuitenkin tulostaa eri kiihtyvyydellä."

msgctxt "acceleration_support description"
msgid "The acceleration with which the support structure is printed."
msgstr "Kiihtyvyys, jolla tukirakenne tulostetaan."

msgctxt "raft_surface_acceleration description"
msgid "The acceleration with which the top raft layers are printed."
msgstr "Kiihtyvyys, jolla pohjaristikon pintakerrokset tulostetaan."

msgctxt "acceleration_wall_x_roofing description"
msgid "The acceleration with which the top surface inner walls are printed."
msgstr "Yläpinnan sisäseinien tulostamisen kiihtyvyys."

msgctxt "acceleration_wall_0_roofing description"
msgid "The acceleration with which the top surface outermost walls are printed."
msgstr "Yläpinnan uloimpien seinien tulostamisen kiihtyvyys."

msgctxt "acceleration_wall description"
msgid "The acceleration with which the walls are printed."
msgstr "Kiihtyvyys, jolla seinämät tulostetaan."

msgctxt "acceleration_roofing description"
msgid "The acceleration with which top surface skin layers are printed."
msgstr "Kiihtyvyys, jolla yläpinnan pintakalvokerrokset tulostetaan."

msgctxt "acceleration_topbottom description"
msgid "The acceleration with which top/bottom layers are printed."
msgstr "Kiihtyvyys, jolla ylä-/alakerrokset tulostetaan."

msgctxt "acceleration_travel description"
msgid "The acceleration with which travel moves are made."
msgstr "Kiihtyvyys, jolla siirtoliikkeet tehdään."

msgctxt "ironing_flow description"
msgid "The amount of material, relative to a normal skin line, to extrude during ironing. Keeping the nozzle filled helps filling some of the crevices of the top surface, but too much results in overextrusion and blips on the side of the surface."
msgstr "Silityksen aikana pursotettavan materiaalin määrä suhteessa normaaliin pintakalvon linjaan. Suuttimen pitäminen täytettynä auttaa joidenkin yläpinnan halkeamien täyttämisessä, mutta liiallinen määrä johtaa ylipursotukseen ja täpliin pinnan sivulla."

msgctxt "infill_overlap description"
msgid "The amount of overlap between the infill and the walls as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr ""

msgctxt "infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Limityksen määrä täytön ja seinämien välillä. Pienellä limityksellä seinämät liittyvät tukevasti täyttöön."

msgctxt "switch_extruder_retraction_amount description"
msgid "The amount of retraction when switching extruders. Set to 0 for no retraction at all. This should generally be the same as the length of the heat zone."
msgstr ""

msgctxt "machine_nozzle_expansion_angle description"
msgid "The angle between the horizontal plane and the conical part right above the tip of the nozzle."
msgstr "Vaakatason ja suuttimen kärjen yllä olevan kartiomaisen osan välinen kulma."

msgctxt "support_tower_roof_angle description"
msgid "The angle of a rooftop of a tower. A higher value results in pointed tower roofs, a lower value results in flattened tower roofs."
msgstr "Tornin katon kulma. Korkeampi arvo johtaa teräväkärkisiin tornien kattoihin, matalampi arvo litteämpiin tornien kattoihin."

msgctxt "mold_angle description"
msgid "The angle of overhang of the outer walls created for the mold. 0° will make the outer shell of the mold vertical, while 90° will make the outside of the model follow the contour of the model."
msgstr "Muottia varten luotujen ulkoseinämien ulokkeiden kulma. 0° tekee muotin ulkokuoresta pystysuoran ja 90° saa muotin ulkopuolen seuraamaan mallin muotoja."

msgctxt "support_tree_branch_diameter_angle description"
msgid "The angle of the branches' diameter as they gradually become thicker towards the bottom. An angle of 0 will cause the branches to have uniform thickness over their length. A bit of an angle can increase stability of the tree support."
msgstr ""

msgctxt "support_conical_angle description"
msgid "The angle of the tilt of conical support. With 0 degrees being vertical, and 90 degrees being horizontal. Smaller angles cause the support to be more sturdy, but consist of more material. Negative angles cause the base of the support to be wider than the top."
msgstr "Kartiomaisen tuen kallistuskulma. 0 astetta on pystysuora ja 90 astetta on vaakasuora. Pienemmillä kulmilla tuki on tukevampi, mutta siihen käytetään enemmän materiaalia. Negatiivisilla kulmilla tuen perusta on leveämpi kuin yläosa."

msgctxt "magic_fuzzy_skin_point_density description"
msgid "The average density of points introduced on each polygon in a layer. Note that the original points of the polygon are discarded, so a low density results in a reduction of the resolution."
msgstr "Kerroksen kuhunkin monikulmioon tehtävien pisteiden keskimääräinen tiheys. Huomaa, että monikulmion alkuperäiset pisteet poistetaan käytöstä, joten pieni tiheys alentaa resoluutiota."

msgctxt "magic_fuzzy_skin_point_dist description"
msgid "The average distance between the random points introduced on each line segment. Note that the original points of the polygon are discarded, so a high smoothness results in a reduction of the resolution. This value must be higher than half the Fuzzy Skin Thickness."
msgstr "Keskimääräinen etäisyys kunkin linjasegmentin satunnaisten pisteiden välillä. Huomaa, että alkuperäiset monikulmion pisteet poistetaan käytöstä, joten korkea sileysarvo alentaa resoluutiota. Tämän arvon täytyy olla suurempi kuin puolet karhean pintakalvon paksuudesta."

msgctxt "machine_acceleration description"
msgid "The default acceleration of print head movement."
msgstr "Tulostuspään liikkeen oletuskiihtyvyys."

msgctxt "default_material_print_temperature description"
msgid "The default temperature used for printing. This should be the \"base\" temperature of a material. All other print temperatures should use offsets based on this value"
msgstr "Tulostuksessa käytettävä oletuslämpötila. Tämän tulee olla materiaalin ”pohjalämpötila”. Kaikkien muiden tulostuslämpötilojen tulee käyttää tähän arvoon perustuvia siirtymiä"

msgctxt "default_material_bed_temperature description"
msgid "The default temperature used for the heated build plate. This should be the \"base\" temperature of a build plate. All other print temperatures should use offsets based on this value"
msgstr ""

msgctxt "bridge_skin_density description"
msgid "The density of the bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr ""

msgctxt "support_bottom_density description"
msgid "The density of the floors of the support structure. A higher value results in better adhesion of the support on top of the model."
msgstr "Tukirakenteen lattioiden tiheys. Korkeammalla arvolla mallin yläosan tuki kiinnittyy paremmin."

msgctxt "support_roof_density description"
msgid "The density of the roofs of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Tukirakenteen lattian tiheys. Korkeammat arvot tuottavat parempia ulokkeita, mutta tuet on vaikeampi poistaa."

msgctxt "bridge_skin_density_2 description"
msgid "The density of the second bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr ""

msgctxt "bridge_skin_density_3 description"
msgid "The density of the third bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr ""

msgctxt "machine_depth description"
msgid "The depth (Y-direction) of the printable area."
msgstr "Tulostettavan alueen syvyys (Y-suunta)."

msgctxt "support_tower_diameter description"
msgid "The diameter of a special tower."
msgstr "Erityistornin läpimitta."

msgctxt "support_tree_branch_diameter description"
msgid "The diameter of the thinnest branches of tree support. Thicker branches are more sturdy. Branches towards the base will be thicker than this."
msgstr ""

msgctxt "support_tree_tip_diameter description"
msgid "The diameter of the top of the tip of the branches of tree support."
msgstr ""

msgctxt "machine_feeder_wheel_diameter description"
msgid "The diameter of the wheel that drives the material in the feeder."
msgstr ""

msgctxt "support_tree_max_diameter description"
msgid "The diameter of the widest branches of tree support. A thicker trunk is more sturdy; a thinner trunk takes up less space on the build plate."
msgstr ""

msgctxt "adaptive_layer_height_variation_step description"
msgid "The difference in height of the next layer height compared to the previous one."
msgstr ""

msgctxt "ironing_line_spacing description"
msgid "The distance between the lines of ironing."
msgstr "Silityslinjojen välinen etäisyys."

msgctxt "travel_avoid_distance description"
msgid "The distance between the nozzle and already printed parts when avoiding during travel moves."
msgstr "Suuttimen ja aiemmin tulostetun osan välinen etäisyys siirtoliikkeiden yhteydessä."

msgctxt "raft_base_line_spacing description"
msgid "The distance between the raft lines for the base raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "Pohjaristikon pohjakerroksen linjojen välinen etäisyys. Leveä linjajako helpottaa pohjaristikon poistoa alustalta."

msgctxt "raft_interface_line_spacing description"
msgid "The distance between the raft lines for the middle raft layer. The spacing of the middle should be quite wide, while being dense enough to support the top raft layers."
msgstr "Pohjaristikon keskikerroksen linjojen välinen etäisyys. Keskikerroksen linjajaon tulisi olla melko leveä ja samalla riittävän tiheä, jotta se tukee pohjaristikon pintakerroksia."

msgctxt "raft_surface_line_spacing description"
msgid "The distance between the raft lines for the top raft layers. The spacing should be equal to the line width, so that the surface is solid."
msgstr "Pohjaristikon pintakerrosten linjojen välinen etäisyys. Linjajaon tulisi olla sama kuin linjaleveys, jotta pinta on kiinteä."

msgctxt "prime_tower_raft_base_line_spacing description"
msgid "The distance between the raft lines for the unique prime tower raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr ""

msgctxt "interlocking_depth description"
msgid "The distance from the boundary between models to generate interlocking structure, measured in cells. Too few cells will result in poor adhesion."
msgstr ""

msgctxt "brim_width description"
msgid "The distance from the model to the outermost brim line. A larger brim enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "Etäisyys mallista ulommaiseen reunuslinjaan. Suurempi reunus parantaa kiinnitystä alustaan, mutta rajoittaa tehokasta tulostusaluetta."

msgctxt "interlocking_boundary_avoidance description"
msgid "The distance from the outside of a model where interlocking structures will not be generated, measured in cells."
msgstr "Suuttimen kärjestä mitattu etäisyys, jonka päähän tulostuslanka asetetaan säilytykseen, kun suulaketta ei enää käytetä."

msgctxt "machine_heat_zone_length description"
msgid "The distance from the tip of the nozzle in which heat from the nozzle is transferred to the filament."
msgstr "Suuttimen kärjestä mitattu etäisyys, jonka suuttimen lämpö siirtyy tulostuslankaan."

msgctxt "bottom_skin_expand_distance description"
msgid "The distance the bottom skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the skin adhere better to the walls on the layer below. Lower values save amount of material used."
msgstr "Etäisyys, jonka verran alapintakalvot laajentuvat täyttöön. Suuremmat arvot saavat pintakalvon kiinnittymään paremmin täyttökuvioon ja tarttumaan paremmin alla olevan kerroksen seinämiin. Pienemmät arvot vähentävät käytettävän materiaalin määrää."

msgctxt "expand_skins_expand_distance description"
msgid "The distance the skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on neighboring layers adhere better to the skin. Lower values save amount of material used."
msgstr "Etäisyys, jonka verran pintakalvot laajentuvat täyttöön. Suuremmat arvot saavat pintakalvon kiinnittymään paremmin täyttökuvioon ja viereisten kerrosten seinämät tarttumaan paremmin pintakalvoon. Pienemmät arvot vähentävät käytettävän materiaalin määrää."

msgctxt "top_skin_expand_distance description"
msgid "The distance the top skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on the layer above adhere better to the skin. Lower values save amount of material used."
msgstr "Etäisyys, jonka verran yläpintakalvot laajentuvat täyttöön. Suuremmat arvot saavat pintakalvon kiinnittymään paremmin täyttökuvioon ja yllä olevan kerroksen seinämät tarttumaan paremmin pintakalvoon. Pienemmät arvot vähentävät käytettävän materiaalin määrää."

msgctxt "wipe_move_distance description"
msgid "The distance to move the head back and forth across the brush."
msgstr ""

msgctxt "lightning_infill_prune_angle description"
msgid "The endpoints of infill lines are shortened to save on material. This setting is the angle of overhang of the endpoints of these lines."
msgstr ""

msgctxt "material_extrusion_cool_down_speed description"
msgid "The extra speed by which the nozzle cools while extruding. The same value is used to signify the heat up speed lost when heating up while extruding."
msgstr "Lisänopeus, jonka verran suutin jäähtyy pursotuksen aikana. Samaa arvoa käytetään merkitsemään menetettyä kuumentumisnopeutta pursotuksen aikaisen kuumennuksen aikana."

msgctxt "support_extruder_nr_layer_0 description"
msgid "The extruder train to use for printing the first layer of support infill. This is used in multi-extrusion."
msgstr "Tuen täytön ensimmäisen kerroksen tulostukseen käytettävä suulakeryhmä. Tätä käytetään monipursotuksessa."

msgctxt "raft_base_extruder_nr description"
msgid "The extruder train to use for printing the first layer of the raft. This is used in multi-extrusion."
msgstr ""

msgctxt "support_bottom_extruder_nr description"
msgid "The extruder train to use for printing the floors of the support. This is used in multi-extrusion."
msgstr "Tuen lattioiden tulostukseen käytettävä suulakeryhmä. Tätä käytetään monipursotuksessa."

msgctxt "support_infill_extruder_nr description"
msgid "The extruder train to use for printing the infill of the support. This is used in multi-extrusion."
msgstr "Tuen täytön tulostukseen käytettävä suulakeryhmä. Tätä käytetään monipursotuksessa."

msgctxt "raft_interface_extruder_nr description"
msgid "The extruder train to use for printing the middle layer of the raft. This is used in multi-extrusion."
msgstr ""

msgctxt "support_interface_extruder_nr description"
msgid "The extruder train to use for printing the roofs and floors of the support. This is used in multi-extrusion."
msgstr "Tuen kattojen ja lattioiden tulostukseen käytettävä suulakeryhmä. Tätä käytetään monipursotuksessa."

msgctxt "support_roof_extruder_nr description"
msgid "The extruder train to use for printing the roofs of the support. This is used in multi-extrusion."
msgstr "Tuen kattojen tulostukseen käytettävä suulakeryhmä. Tätä käytetään monipursotuksessa."

msgctxt "skirt_brim_extruder_nr description"
msgid "The extruder train to use for printing the skirt or brim. This is used in multi-extrusion."
msgstr ""

msgctxt "adhesion_extruder_nr description"
msgid "The extruder train to use for printing the skirt/brim/raft. This is used in multi-extrusion."
msgstr "Helman/reunuksen/pohjaristikon tulostukseen käytettävä suulakeryhmä. Tätä käytetään monipursotuksessa."

msgctxt "support_extruder_nr description"
msgid "The extruder train to use for printing the support. This is used in multi-extrusion."
msgstr "Tuen tulostukseen käytettävä suulakeryhmä. Tätä käytetään monipursotuksessa."

msgctxt "raft_surface_extruder_nr description"
msgid "The extruder train to use for printing the top layer(s) of the raft. This is used in multi-extrusion."
msgstr ""

msgctxt "infill_extruder_nr description"
msgid "The extruder train used for printing infill. This is used in multi-extrusion."
msgstr "Täytön tulostukseen käytettävä suulakeryhmä. Tätä käytetään monipursotuksessa."

msgctxt "wall_x_extruder_nr description"
msgid "The extruder train used for printing the inner walls. This is used in multi-extrusion."
msgstr "Sisäseinämien tulostukseen käytettävä suulakeryhmä. Tätä käytetään monipursotuksessa."

msgctxt "wall_0_extruder_nr description"
msgid "The extruder train used for printing the outer wall. This is used in multi-extrusion."
msgstr "Ulkoseinämän tulostukseen käytettävä suulakeryhmä. Tätä käytetään monipursotuksessa."

msgctxt "top_bottom_extruder_nr description"
msgid "The extruder train used for printing the top and bottom skin. This is used in multi-extrusion."
msgstr "Ylä- ja alapuolen pintakalvon tulostukseen käytettävä suulakeryhmä. Tätä käytetään monipursotuksessa."

msgctxt "roofing_extruder_nr description"
msgid "The extruder train used for printing the top most skin. This is used in multi-extrusion."
msgstr "Ylimmän pintakalvon tulostukseen käytettävä suulakeryhmä. Tätä käytetään monipursotuksessa."

msgctxt "wall_extruder_nr description"
msgid "The extruder train used for printing the walls. This is used in multi-extrusion."
msgstr "Seinämien tulostukseen käytettävä suulakeryhmä. Tätä käytetään monipursotuksessa."

msgctxt "raft_base_fan_speed description"
msgid "The fan speed for the base raft layer."
msgstr "Tuulettimen nopeus pohjaristikon pohjakerrosta varten."

msgctxt "raft_interface_fan_speed description"
msgid "The fan speed for the middle raft layer."
msgstr "Tuulettimen nopeus pohjaristikon keskikerrosta varten."

msgctxt "raft_fan_speed description"
msgid "The fan speed for the raft."
msgstr "Pohjaristikon tuulettimen nopeus."

msgctxt "raft_surface_fan_speed description"
msgid "The fan speed for the top raft layers."
msgstr "Tuulettimen nopeus pohjaristikon pintakerroksia varten."

msgctxt "cross_infill_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the infill of the print."
msgstr ""

msgctxt "cross_support_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the support."
msgstr ""

msgctxt "speed_slowdown_layers description"
msgid "The first few layers are printed slower than the rest of the model, to get better adhesion to the build plate and improve the overall success rate of prints. The speed is gradually increased over these layers."
msgstr "Muutama ensimmäinen kerros tulostetaan hitaammin kuin loput mallista, jolloin saadaan parempi tarttuvuus alustaan ja parannetaan tulosteiden yleistä onnistumista. Näiden kerrosten jälkeen nopeutta lisätään asteittain."

msgctxt "raft_airgap description"
msgid "The gap between the final raft layer and the first layer of the model. Only the first layer is raised by this amount to lower the bonding between the raft layer and the model. Makes it easier to peel off the raft."
msgstr "Rako pohjaristikon viimeisen kerroksen ja mallin ensimmäisen kerroksen välillä. Vain ensimmäistä kerrosta nostetaan tällä määrällä pohjaristikkokerroksen ja mallin välisen sidoksen vähentämiseksi. Se helpottaa pohjaristikon irti kuorimista."

msgctxt "machine_height description"
msgid "The height (Z-direction) of the printable area."
msgstr "Tulostettavan alueen korkeus (Z-suunta)."

msgctxt "mold_roof_height description"
msgid "The height above horizontal parts in your model which to print mold."
msgstr "Mallin vaakasuuntaisten osien yläpuolinen korkeus, jonka mukaan muotti tulostetaan."

msgctxt "cool_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "Korkeus, jolla tuulettimet pyörivät normaalilla nopeudella. Alemmilla kerroksilla tuulettimen nopeus kasvaa asteittain tuulettimen nopeudesta alussa normaaliin tuulettimen nopeuteen."

msgctxt "gantry_height description"
msgid "The height difference between the tip of the nozzle and the gantry system (X and Y axes)."
msgstr "Suuttimen kärjen ja korokejärjestelmän (X- ja Y-akselit) välinen korkeusero."

msgctxt "machine_nozzle_head_distance description"
msgid "The height difference between the tip of the nozzle and the lowest part of the print head."
msgstr "Suuttimen kärjen ja tulostuspään alimman osan välinen korkeusero."

msgctxt "retraction_hop_after_extruder_switch_height description"
msgid "The height difference when performing a Z Hop after extruder switch."
msgstr ""

msgctxt "retraction_hop description"
msgid "The height difference when performing a Z Hop."
msgstr "Z-hypyn suorituksen korkeusero."

msgctxt "wipe_hop_amount description"
msgid "The height difference when performing a Z Hop."
msgstr ""

msgctxt "layer_height description"
msgid "The height of each layer in mm. Higher values produce faster prints in lower resolution, lower values produce slower prints in higher resolution."
msgstr "Kunkin kerroksen korkeus milleinä. Korkeammat arvot tuottavat nopeampia tulosteita alhaisemmalla resoluutiolla, alemmat arvot tuottavat hitaampia tulosteita korkeammalla resoluutiolla."

msgctxt "gradual_infill_step_height description"
msgid "The height of infill of a given density before switching to half the density."
msgstr "Tietyn tiheysarvon täytön korkeus ennen puoleen tiheyteen vaihtamista."

msgctxt "gradual_support_infill_step_height description"
msgid "The height of support infill of a given density before switching to half the density."
msgstr "Tietyn tiheysarvon tuen täytön korkeus ennen puoleen tiheyteen vaihtamista."

msgctxt "interlocking_beam_layer_count description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr ""

msgctxt "interlocking_orientation description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr ""

msgctxt "layer_height_0 description"
msgid "The height of the initial layer in mm. A thicker initial layer makes adhesion to the build plate easier."
msgstr "Alkukerroksen korkeus milleinä. Paksumpi alkukerros helpottaa alustaan kiinnittymistä."

msgctxt "prime_tower_base_height description"
msgid "The height of the prime tower base. Increasing this value will result in a more sturdy prime tower because the base will be wider. If this setting is too low, the prime tower will not have a sturdy base."
msgstr ""

msgctxt "support_bottom_stair_step_height description"
msgid "The height of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures. Set to zero to turn off the stair-like behaviour."
msgstr "Mallin päällä olevan porrasmaisen tuen pohjan portaiden korkeus. Matala arvo tekee tuesta vaikeamman poistaa, mutta liian korkeat arvot voivat johtaa epävakaisiin tukirakenteisiin. Poista porrasmainen ominaisuus käytöstä valitsemalla asetukseksi nolla."

msgctxt "brim_gap description"
msgid "The horizontal distance between the first brim line and the outline of the first layer of the print. A small gap can make the brim easier to remove while still providing the thermal benefits."
msgstr ""

msgctxt "skirt_gap description"
msgid ""
"The horizontal distance between the skirt and the first layer of the print.\n"
"This is the minimum distance. Multiple skirt lines will extend outwards from this distance."
msgstr ""

msgctxt "lightning_infill_straightening_angle description"
msgid "The infill lines are straightened out to save on printing time. This is the maximum angle of overhang allowed across the length of the infill line."
msgstr ""

msgctxt "infill_offset_x description"
msgid "The infill pattern is moved this distance along the X axis."
msgstr ""

msgctxt "infill_offset_y description"
msgid "The infill pattern is moved this distance along the Y axis."
msgstr ""

msgctxt "machine_nozzle_size description"
msgid "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size."
msgstr "Suuttimen sisäläpimitta. Muuta tätä asetusta, kun käytössä on muu kuin vakiokokoinen suutin."

msgctxt "raft_base_jerk description"
msgid "The jerk with which the base raft layer is printed."
msgstr "Nykäisy, jolla pohjaristikon pohjakerros tulostetaan."

msgctxt "raft_interface_jerk description"
msgid "The jerk with which the middle raft layer is printed."
msgstr "Nykäisy, jolla pohjaristikon keskikerros tulostetaan."

msgctxt "raft_jerk description"
msgid "The jerk with which the raft is printed."
msgstr "Nykäisy, jolla pohjaristikko tulostetaan."

msgctxt "raft_surface_jerk description"
msgid "The jerk with which the top raft layers are printed."
msgstr "Nykäisy, jolla pohjaristikon pintakerrokset tulostetaan."

msgctxt "bottom_skin_preshrink description"
msgid "The largest width of bottom skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing bottom skin at slanted surfaces in the model."
msgstr "Suurin poistettavien alapintakalvoalueiden leveys. Kaikki tätä arvoa pienemmät pintakalvoalueet poistuvat. Tästä voi olla apua mallin kaltevien pintojen alapintakalvon tulostukseen käytettävän ajan ja materiaalin rajoittamisessa."

msgctxt "skin_preshrink description"
msgid "The largest width of skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top/bottom skin at slanted surfaces in the model."
msgstr "Suurin poistettavien pintakalvoalueiden leveys. Kaikki tätä arvoa pienemmät pintakalvoalueet poistuvat. Tästä voi olla apua mallin kaltevien pintojen ylä-/alapintakalvon tulostukseen käytettävän ajan ja materiaalin rajoittamisessa."

msgctxt "top_skin_preshrink description"
msgid "The largest width of top skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top skin at slanted surfaces in the model."
msgstr "Suurin poistettavien yläpintakalvoalueiden leveys. Kaikki tätä arvoa pienemmät pintakalvoalueet poistuvat. Tästä voi olla apua mallin kaltevien pintojen yläpintakalvon tulostukseen käytettävän ajan ja materiaalin rajoittamisessa."

msgctxt "cool_fan_full_layer description"
msgid "The layer at which the fans spin on regular fan speed. If regular fan speed at height is set, this value is calculated and rounded to a whole number."
msgstr "Kerros, jolla tuulettimet pyörivät normaalilla nopeudella. Jos normaali tuulettimen nopeus korkeudella on asetettu, tämä arvo lasketaan ja pyöristetään kokonaislukuun."

msgctxt "cool_min_layer_time_fan_speed_max description"
msgid "The layer time which sets the threshold between regular fan speed and maximum fan speed. Layers that print slower than this time use regular fan speed. For faster layers the fan speed gradually increases towards the maximum fan speed."
msgstr "Kerrosaika, joka määrittää tuulettimen normaalin nopeuden ja maksiminopeuden välisen raja-arvon. Kerrokset, jotka tulostuvat tätä hitaammin käyttävät normaalia tuulettimen nopeutta. Nopeammilla kerroksilla tuulettimen nopeus nousee asteittain kohti tuulettimen maksiminopeutta."

msgctxt "retraction_amount description"
msgid "The length of material retracted during a retraction move."
msgstr "Takaisinvedon yhteydessä sisään vedettävän materiaalin pituus."

msgctxt "prime_tower_base_curve_magnitude description"
msgid "The magnitude factor used for the slope of the prime tower base. If you increase this value, the base will become slimmer. If you decrease it, the base will become thicker."
msgstr ""

msgctxt "machine_buildplate_type description"
msgid "The material of the build plate installed on the printer."
msgstr ""

msgctxt "adaptive_layer_height_variation description"
msgid "The maximum allowed height different from the base layer height."
msgstr ""

msgctxt "ooze_shield_angle description"
msgid "The maximum angle a part in the ooze shield will have. With 0 degrees being vertical, and 90 degrees being horizontal. A smaller angle leads to less failed ooze shields, but more material."
msgstr "Tihkusuojuksen osan maksimikulma. 0 astetta tarkoittaa pystysuuntaa ja 90 astetta vaakasuuntaa. Pienempi kulma vähentää tihkusuojusten epäonnistumisia mutta lisää materiaalia."

msgctxt "conical_overhang_angle description"
msgid "The maximum angle of overhangs after the they have been made printable. At a value of 0° all overhangs are replaced by a piece of model connected to the build plate, 90° will not change the model in any way."
msgstr "Ulokkeiden maksimikulma, kun niistä on tehty tulostettavia. 0 asteessa kaikki ulokkeet korvataan mallikappaleella, joka on yhdistetty alustaan. 90 asteessa mallia ei muuteta millään tavalla."

msgctxt "support_tree_angle description"
msgid "The maximum angle of the branches while they grow around the model. Use a lower angle to make them more vertical and more stable. Use a higher angle to be able to have more reach."
msgstr ""

msgctxt "conical_overhang_hole_size description"
msgid "The maximum area of a hole in the base of the model before it's removed by Make Overhang Printable.  Holes smaller than this will be retained.  A value of 0 mm² will fill all holes in the models base."
msgstr ""

msgctxt "meshfix_maximum_deviation description"
msgid "The maximum deviation allowed when reducing the resolution for the Maximum Resolution setting. If you increase this, the print will be less accurate, but the g-code will be smaller. Maximum Deviation is a limit for Maximum Resolution, so if the two conflict the Maximum Deviation will always be held true."
msgstr ""

msgctxt "support_join_distance description"
msgid "The maximum distance between support structures in the X/Y directions. When separate structures are closer together than this value, the structures merge into one."
msgstr ""

msgctxt "flow_rate_max_extrusion_offset description"
msgid "The maximum distance in mm to move the filament to compensate for changes in flow rate."
msgstr ""

msgctxt "meshfix_maximum_extrusion_area_deviation description"
msgid "The maximum extrusion area deviation allowed when removing intermediate points from a straight line. An intermediate point may serve as width-changing point in a long straight line. Therefore, if it is removed, it will cause the line to have a uniform width and, as a result, lose (or gain) a bit of extrusion area. If you increase this you may notice slight under- (or over-) extrusion in between straight parallel walls, as more intermediate width-changing points will be allowed to be removed. Your print will be less accurate, but the g-code will be smaller."
msgstr ""

msgctxt "jerk_print_layer_0 description"
msgid "The maximum instantaneous velocity change during the printing of the initial layer."
msgstr "Alkukerroksen tulostuksen aikainen nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_print description"
msgid "The maximum instantaneous velocity change of the print head."
msgstr "Tulostuspään nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_ironing description"
msgid "The maximum instantaneous velocity change while performing ironing."
msgstr "Silityksen aikainen nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_wall_x description"
msgid "The maximum instantaneous velocity change with which all inner walls are printed."
msgstr "Kaikkien sisäseinämien tulostuksen nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_infill description"
msgid "The maximum instantaneous velocity change with which infill is printed."
msgstr "Täytön tulostuksen nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_support_bottom description"
msgid "The maximum instantaneous velocity change with which the floors of support are printed."
msgstr "Tuen lattioiden tulostuksen nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_support_infill description"
msgid "The maximum instantaneous velocity change with which the infill of support is printed."
msgstr "Tuen täytön tulostuksen nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_wall_0 description"
msgid "The maximum instantaneous velocity change with which the outermost walls are printed."
msgstr "Ulkoseinämien tulostuksen nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_prime_tower description"
msgid "The maximum instantaneous velocity change with which the prime tower is printed."
msgstr "Esitäyttötornin tulostuksen nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_support_interface description"
msgid "The maximum instantaneous velocity change with which the roofs and floors of support are printed."
msgstr "Tuen kattojen ja lattioiden tulostuksen nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_support_roof description"
msgid "The maximum instantaneous velocity change with which the roofs of support are printed."
msgstr "Tuen kattojen tulostuksen nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_skirt_brim description"
msgid "The maximum instantaneous velocity change with which the skirt and brim are printed."
msgstr "Helman ja reunuksen tulostuksen nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_support description"
msgid "The maximum instantaneous velocity change with which the support structure is printed."
msgstr "Tukirakenteen tulostuksen nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_wall_x_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface inner walls are printed."
msgstr "Yläpinnan uloimman seinän tulostuksessa tapahtuva suurin välitön nopeuden muutos."

msgctxt "jerk_wall_0_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface outermost walls are printed."
msgstr "Yläpinnan sisäseinien tulostuksessa tapahtuva suurin välitön nopeuden muutos."

msgctxt "jerk_wall description"
msgid "The maximum instantaneous velocity change with which the walls are printed."
msgstr "Seinämien tulostuksen nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_roofing description"
msgid "The maximum instantaneous velocity change with which top surface skin layers are printed."
msgstr "Yläpinnan pintakalvokerrosten tulostuksen nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_topbottom description"
msgid "The maximum instantaneous velocity change with which top/bottom layers are printed."
msgstr "Ylä-/alakerrosten tulostuksen nopeuden hetkellinen maksimimuutos."

msgctxt "jerk_travel description"
msgid "The maximum instantaneous velocity change with which travel moves are made."
msgstr "Siirtoliikkeiden nopeuden hetkellinen maksimimuutos."

msgctxt "machine_max_feedrate_x description"
msgid "The maximum speed for the motor of the X-direction."
msgstr "X-suunnan moottorin maksiminopeus."

msgctxt "machine_max_feedrate_y description"
msgid "The maximum speed for the motor of the Y-direction."
msgstr "Y-suunnan moottorin maksiminopeus."

msgctxt "machine_max_feedrate_z description"
msgid "The maximum speed for the motor of the Z-direction."
msgstr "Z-suunnan moottorin maksiminopeus."

msgctxt "machine_max_feedrate_e description"
msgid "The maximum speed of the filament."
msgstr "Tulostuslangan maksiminopeus."

msgctxt "support_bottom_stair_step_width description"
msgid "The maximum width of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures."
msgstr "Mallin päällä olevan porrasmaisen tuen pohjan portaiden enimmäisleveys. Matala arvo tekee tuesta vaikeamman poistaa, mutta liian korkeat arvot voivat johtaa epävakaisiin tukirakenteisiin."

msgctxt "mold_width description"
msgid "The minimal distance between the outside of the mold and the outside of the model."
msgstr ""

msgctxt "machine_minimum_feedrate description"
msgid "The minimal movement speed of the print head."
msgstr "Tulostuspään liikkeen miniminopeus."

msgctxt "material_initial_print_temperature description"
msgid "The minimal temperature while heating up to the Printing Temperature at which printing can already start."
msgstr "Minimilämpötila lämmitettäessä tulostuslämpötilaan, jossa tulostus voidaan aloittaa."

msgctxt "machine_min_cool_heat_time_window description"
msgid "The minimal time an extruder has to be inactive before the nozzle is cooled. Only when an extruder is not used for longer than this time will it be allowed to cool down to the standby temperature."
msgstr "Minimiaika, jonka suulakkeen on oltava ei-aktiivinen, ennen kuin suutin jäähdytetään. Suulakkeen annetaan jäähtyä valmiustilaan vain, kun sitä ei käytetä tätä aikaa kauemmin."

msgctxt "infill_support_angle description"
msgid "The minimum angle of internal overhangs for which infill is added. At a value of 0° objects are totally filled with infill, 90° will not provide any infill."
msgstr ""

msgctxt "support_angle description"
msgid "The minimum angle of overhangs for which support is added. At a value of 0° all overhangs are supported, 90° will not provide any support."
msgstr "Ulokkeen minimikulma, jonka jälkeen tuki lisätään. Arvolla 0 ° kaikki ulokkeet tuetaan, asetuksella 90 ° tukia ei tuoteta."

msgctxt "retraction_min_travel description"
msgid "The minimum distance of travel needed for a retraction to happen at all. This helps to get fewer retractions in a small area."
msgstr "Tarvittavan siirtoliikkeen minimietäisyys, jotta takaisinveto yleensäkin tapahtuu. Tällä varmistetaan, ettei takaisinvetoja tapahdu runsaasti pienellä alueella."

msgctxt "skirt_brim_minimal_length description"
msgid "The minimum length of the skirt or brim. If this length is not reached by all skirt or brim lines together, more skirt or brim lines will be added until the minimum length is reached. Note: If the line count is set to 0 this is ignored."
msgstr "Helman tai reunuksen minimipituus. Jos kaikki helma- tai reunuslinjat yhdessä eivät saavuta tätä minimipituutta, lisätään useampia helma- tai reunuslinjoja, jotta tähän minimipituuteen päästään. Huomaa: jos linjalukuna on 0, tämä jätetään huomiotta."

msgctxt "min_odd_wall_line_width description"
msgid "The minimum line width for middle line gap filler polyline walls. This setting determines at which model thickness we switch from printing two wall lines, to printing two outer walls and a single central wall in the middle. A higher Minimum Odd Wall Line Width leads to a higher maximum even wall line width. The maximum odd wall line width is calculated as 2 * Minimum Even Wall Line Width."
msgstr ""

msgctxt "min_even_wall_line_width description"
msgid "The minimum line width for normal polygonal walls. This setting determines at which model thickness we switch from printing a single thin wall line, to printing two wall lines. A higher Minimum Even Wall Line Width leads to a higher maximum odd wall line width. The maximum even wall line width is calculated as Outer Wall Line Width + 0.5 * Minimum Odd Wall Line Width."
msgstr ""

msgctxt "cool_min_speed description"
msgid "The minimum print speed, despite slowing down due to the minimum layer time. When the printer would slow down too much, the pressure in the nozzle would be too low and result in bad print quality."
msgstr "Tulostuksen miniminopeus riippumatta kerroksen minimiajan aiheuttamasta hidastuksesta. Jos tulostin hidastaisi liikaa, paine suuttimessa olisi liian alhainen ja tulostuksen laatu kärsisi."

msgctxt "meshfix_maximum_resolution description"
msgid "The minimum size of a line segment after slicing. If you increase this, the mesh will have a lower resolution. This may allow the printer to keep up with the speed it has to process g-code and will increase slice speed by removing details of the mesh that it can't process anyway."
msgstr ""

msgctxt "meshfix_maximum_travel_resolution description"
msgid "The minimum size of a travel line segment after slicing. If you increase this, the travel moves will have less smooth corners. This may allow the printer to keep up with the speed it has to process g-code, but it may cause model avoidance to become less accurate."
msgstr ""

msgctxt "support_bottom_stair_step_min_slope description"
msgid "The minimum slope of the area for stair-stepping to take effect. Low values should make support easier to remove on shallower slopes, but really low values may result in some very counter-intuitive results on other parts of the model."
msgstr ""

msgctxt "cool_min_layer_time description"
msgid "The minimum time spent in a layer. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "Kerrokseen käytetty minimiaika. Tämä pakottaa tulostimen hidastamaan ja käyttämään vähintään tässä määritellyn ajan yhdellä kerroksella. Näin tulostettu materiaali saa jäähtyä kunnolla ennen seuraavan kerroksen tulostamista. Kerrosten tulostus saattaa silti tapahtua minimikerrosnopeutta nopeammin, jos tulostuspään nosto ei ole käytössä ja jos miniminopeuden käyttäminen edellyttää tätä."

msgctxt "prime_tower_min_volume description"
msgid "The minimum volume for each layer of the prime tower in order to purge enough material."
msgstr "Esitäyttötornin kunkin kerroksen minimitilavuus, jotta voidaan poistaa riittävästi materiaalia."

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model description"
msgid "The most the diameter of a branch that has to connect to the model may increase by merging with branches that could reach the buildplate. Increasing this reduces print time, but increases the area of support that rests on model"
msgstr ""

msgctxt "machine_name description"
msgid "The name of your 3D printer model."
msgstr "3D-tulostinmallin nimi."

msgctxt "machine_nozzle_id description"
msgid "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\"."
msgstr "Suulakeryhmän suulakkeen tunnus, kuten \"AA 0.4\" ja \"BB 0.8\"."

msgctxt "travel_avoid_other_parts description"
msgid "The nozzle avoids already printed parts when traveling. This option is only available when combing is enabled."
msgstr "Suutin välttää aiemmin tulostettuja osia siirtoliikkeiden yhteydessä. Tämä vaihtoehto on valittavissa vain, kun pyyhkäisy on käytössä."

msgctxt "travel_avoid_supports description"
msgid "The nozzle avoids already printed supports when traveling. This option is only available when combing is enabled."
msgstr ""

msgctxt "bottom_layers description"
msgid "The number of bottom layers. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "Alakerrosten lukumäärä. Kun se lasketaan alaosan paksuudesta, arvo pyöristetään kokonaislukuun."

msgctxt "raft_base_wall_count description"
msgid "The number of contours to print around the linear pattern in the base layer of the raft."
msgstr ""

msgctxt "skin_edge_support_layers description"
msgid "The number of infill layers that supports skin edges."
msgstr ""

msgctxt "initial_bottom_layers description"
msgid "The number of initial bottom layers, from the build-plate upwards. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr ""

msgctxt "raft_interface_layers description"
msgid "The number of layers between the base and the surface of the raft. These comprise the main thickness of the raft. Increasing this creates a thicker, sturdier raft."
msgstr ""

msgctxt "brim_line_count description"
msgid "The number of lines used for a brim. More brim lines enhance adhesion to the build plate, but also reduces the effective print area."
msgstr "Reunukseen käytettävien linjojen lukumäärä. Useampi reunuslinja parantaa kiinnitystä alustaan, mutta rajoittaa tehokasta tulostusaluetta."

msgctxt "support_brim_line_count description"
msgid "The number of lines used for the support brim. More brim lines enhance adhesion to the build plate, at the cost of some extra material."
msgstr ""

msgctxt "raft_surface_layers description"
msgid "The number of top layers on top of the 2nd raft layer. These are fully filled layers that the model sits on. 2 layers result in a smoother top surface than 1."
msgstr "Pohjaristikon toisen kerroksen päällä olevien pintakerrosten lukumäärä. Ne ovat täysin täytettyjä kerroksia, joilla malli lepää. Kaksi kerrosta tuottaa sileämmän pinnan kuin yksi kerros."

msgctxt "top_layers description"
msgid "The number of top layers. When calculated by the top thickness, this value is rounded to a whole number."
msgstr "Yläkerrosten lukumäärä. Kun se lasketaan yläosan paksuudesta, arvo pyöristetään kokonaislukuun."

msgctxt "roofing_layer_count description"
msgid "The number of top most skin layers. Usually only one top most layer is sufficient to generate higher quality top surfaces."
msgstr "Ylimpien pintakalvokerrosten määrä. Yleensä vain yksi ylin kerros riittää tuottamaan korkeampilaatuisia yläpintoja."

msgctxt "support_wall_count description"
msgid "The number of walls with which to surround support infill. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr ""

msgctxt "support_bottom_wall_count description"
msgid "The number of walls with which to surround support interface floor. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr ""

msgctxt "support_roof_wall_count description"
msgid "The number of walls with which to surround support interface roof. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr ""

msgctxt "support_interface_wall_count description"
msgid "The number of walls with which to surround support interface. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr ""

msgctxt "wall_distribution_count description"
msgid "The number of walls, counted from the center, over which the variation needs to be spread. Lower values mean that the outer walls don't change in width."
msgstr ""

msgctxt "wall_line_count description"
msgid "The number of walls. When calculated by the wall thickness, this value is rounded to a whole number."
msgstr "Seinämien lukumäärä. Kun se lasketaan seinämän paksuudesta, arvo pyöristetään kokonaislukuun."

msgctxt "machine_nozzle_tip_outer_diameter description"
msgid "The outer diameter of the tip of the nozzle."
msgstr "Suuttimen kärjen ulkoläpimitta."

msgctxt "infill_pattern description"
msgid "The pattern of the infill material of the print. The line and zig zag infill swap direction on alternate layers, reducing material cost. The grid, triangle, tri-hexagon, cubic, octet, quarter cubic, cross and concentric patterns are fully printed every layer. Gyroid, cubic, quarter cubic and octet infill change with every layer to provide a more equal distribution of strength over each direction. Lightning infill tries to minimize the infill, by only supporting the ceiling of the object."
msgstr ""

msgctxt "support_pattern description"
msgid "The pattern of the support structures of the print. The different options available result in sturdy or easy to remove support."
msgstr "Tukirakenteiden tulostuskuvio. Eri vaihtoehdot tuottavat jämäköitä tai helposti poistettavia tukia."

msgctxt "roofing_pattern description"
msgid "The pattern of the top most layers."
msgstr "Ylimpien kerrosten kuvio."

msgctxt "top_bottom_pattern description"
msgid "The pattern of the top/bottom layers."
msgstr "Ylä-/alakerrosten kuvio."

msgctxt "top_bottom_pattern_0 description"
msgid "The pattern on the bottom of the print on the first layer."
msgstr "Tulosteen alaosan kuvio ensimmäisellä kerroksella."

msgctxt "ironing_pattern description"
msgid "The pattern to use for ironing top surfaces."
msgstr "Yläpintojen silitykseen käytettävä kuvio."

msgctxt "support_bottom_pattern description"
msgid "The pattern with which the floors of the support are printed."
msgstr "Tuen lattioiden tulostuskuvio."

msgctxt "support_interface_pattern description"
msgid "The pattern with which the interface of the support with the model is printed."
msgstr "Kuvio, jolla tuen ja mallin liittymä tulostetaan."

msgctxt "support_roof_pattern description"
msgid "The pattern with which the roofs of the support are printed."
msgstr "Tuen kattojen tulostuskuvio."

msgctxt "z_seam_position description"
msgid "The position near where to start printing each part in a layer."
msgstr ""

msgctxt "support_tree_angle_slow description"
msgid "The preferred angle of the branches, when they do not have to avoid the model. Use a lower angle to make them more vertical and more stable. Use a higher angle for branches to merge faster."
msgstr ""

msgctxt "support_tree_rest_preference description"
msgid "The preferred placement of the support structures. If structures can't be placed at the preferred location, they will be place elsewhere, even if that means placing them on the model."
msgstr ""

msgctxt "jerk_layer_0 description"
msgid "The print maximum instantaneous velocity change for the initial layer."
msgstr "Alkukerroksen tulostuksen nopeuden hetkellinen maksimimuutos."

msgctxt "machine_shape description"
msgid "The shape of the build plate without taking unprintable areas into account."
msgstr "Alustan muoto ottamatta huomioon alueita, joihin ei voi tulostaa."

msgctxt "machine_head_with_fans_polygon description"
msgid "The shape of the print head. These are coordinates relative to the position of the print head, which is usually the position of its first extruder. The dimensions left and in front of the print head must be negative coordinates."
msgstr ""

msgctxt "cross_infill_pocket_size description"
msgid "The size of pockets at four-way crossings in the cross 3D pattern at heights where the pattern is touching itself."
msgstr "Taskujen koko nelisuuntaisissa risteyksissä risti 3D -kuviossa korkeuksissa, joissa kuvio koskettaa itseään."

msgctxt "coasting_min_volume description"
msgid "The smallest volume an extrusion path should have before allowing coasting. For smaller extrusion paths, less pressure has been built up in the bowden tube and so the coasted volume is scaled linearly. This value should always be larger than the Coasting Volume."
msgstr "Pienin ainemäärä, joka pursotusreitillä tulisi olla ennen kuin vapaaliuku sallitaan. Lyhyemmillä pursotusreiteillä Bowden-putkeen on muodostunut vähemmän painetta, joten vapaaliu'un ainemäärää skaalataan lineaarisesti. Tämän arvon on aina oltava suurempi kuin vapaaliu'un ainemäärä."

msgctxt "machine_nozzle_cool_down_speed description"
msgid "The speed (°C/s) by which the nozzle cools down averaged over the window of normal printing temperatures and the standby temperature."
msgstr "Nopeus (°C/s), jolla suutin jäähtyy, mitattuna keskiarvona normaaleista tulostuslämpötiloista ja valmiuslämpötilasta."

msgctxt "machine_nozzle_heat_up_speed description"
msgid "The speed (°C/s) by which the nozzle heats up averaged over the window of normal printing temperatures and the standby temperature."
msgstr "Nopeus (°C/s), jolla suutin lämpenee, mitattuna keskiarvona normaaleista tulostuslämpötiloista ja valmiuslämpötilasta."

msgctxt "speed_wall_x description"
msgid "The speed at which all inner walls are printed. Printing the inner wall faster than the outer wall will reduce printing time. It works well to set this in between the outer wall speed and the infill speed."
msgstr "Nopeus, jolla kaikki sisäseinämät tulostetaan. Sisäseinämän tulostus ulkoseinämää nopeammin lyhentää tulostusaikaa. Tämä arvo kannattaa asettaa ulkoseinämän nopeuden ja täyttönopeuden väliin."

msgctxt "bridge_skin_speed description"
msgid "The speed at which bridge skin regions are printed."
msgstr ""

msgctxt "speed_infill description"
msgid "The speed at which infill is printed."
msgstr "Täytön tulostamiseen käytettävä nopeus."

msgctxt "speed_print description"
msgid "The speed at which printing happens."
msgstr "Tulostamiseen käytettävä nopeus."

msgctxt "raft_base_speed description"
msgid "The speed at which the base raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "Nopeus, jolla pohjaristikon pohjakerros tulostetaan. Tämä tulisi tulostaa melko hitaasti, sillä suuttimesta tulevan materiaalin määrä on varsin suuri."

msgctxt "bridge_wall_speed description"
msgid "The speed at which the bridge walls are printed."
msgstr ""

msgctxt "cool_fan_speed_0 description"
msgid "The speed at which the fans spin at the start of the print. In subsequent layers the fan speed is gradually increased up to the layer corresponding to Regular Fan Speed at Height."
msgstr "Tuulettimien pyörimisnopeus tulostuksen alussa. Seuraavilla kerroksilla tuulettimen nopeus kasvaa asteittain, kunnes saavutetaan kerros, joka vastaa Normaali tuulettimen nopeus korkeudella -arvoa."

msgctxt "cool_fan_speed_min description"
msgid "The speed at which the fans spin before hitting the threshold. When a layer prints faster than the threshold, the fan speed gradually inclines towards the maximum fan speed."
msgstr "Nopeus, jolla tuuletin pyörii ennen raja-arvon tavoittamista. Jos kerros tulostuu nopeammin kuin raja-arvo, tulostimen nopeus nousee asteittain kohti tuulettimen maksiminopeutta."

msgctxt "cool_fan_speed_max description"
msgid "The speed at which the fans spin on the minimum layer time. The fan speed gradually increases between the regular fan speed and maximum fan speed when the threshold is hit."
msgstr "Nopeus, jolla tuuletin pyörii kerroksen minimiaikana. Tuulettimen nopeus kasvaa asteittain normaalin ja maksiminopeuden välillä, kun raja-arvo ohitetaan."

msgctxt "retraction_prime_speed description"
msgid "The speed at which the filament is primed during a retraction move."
msgstr "Nopeus, jolla tulostuslanka esitäytetään takaisinvedon yhteydessä."

msgctxt "wipe_retraction_prime_speed description"
msgid "The speed at which the filament is primed during a wipe retraction move."
msgstr ""

msgctxt "switch_extruder_prime_speed description"
msgid "The speed at which the filament is pushed back after a nozzle switch retraction."
msgstr "Nopeus, jolla tulostuslanka työnnetään takaisin suuttimen vaihdon takaisinvedon jälkeen."

msgctxt "retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a retraction move."
msgstr "Nopeus, jolla tulostuslanka vedetään sisään ja esitäytetään takaisinvedon yhteydessä."

msgctxt "wipe_retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a wipe retraction move."
msgstr ""

msgctxt "switch_extruder_retraction_speed description"
msgid "The speed at which the filament is retracted during a nozzle switch retract."
msgstr "Nopeus, jolla tulostuslanka vedetään sisään suuttimen vaihdon takaisinvedon yhteydessä."

msgctxt "retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a retraction move."
msgstr "Nopeus, jolla tulostuslanka vedetään sisään takaisinvedon yhteydessä."

msgctxt "wipe_retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a wipe retraction move."
msgstr ""

msgctxt "switch_extruder_retraction_speeds description"
msgid "The speed at which the filament is retracted. A higher retraction speed works better, but a very high retraction speed can lead to filament grinding."
msgstr "Nopeus, jolla tulostuslanka vedetään sisään. Suurempi takaisinvetonopeus toimii paremmin, mutta erittäin suuri takaisinvetonopeus saattaa hiertää tulostuslankaa."

msgctxt "speed_support_bottom description"
msgid "The speed at which the floor of support is printed. Printing it at lower speed can improve adhesion of support on top of your model."
msgstr "Nopeus, jolla tuen lattiat tulostetaan. Niiden tulostus hitaammilla nopeuksilla voi parantaa mallin yläosan tuen kiinnittymistä."

msgctxt "speed_support_infill description"
msgid "The speed at which the infill of support is printed. Printing the infill at lower speeds improves stability."
msgstr "Nopeus, jolla tuen täyttö tulostetaan. Täytön tulostus hitaammilla nopeuksilla parantaa vakautta."

msgctxt "raft_interface_speed description"
msgid "The speed at which the middle raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "Nopeus, jolla pohjaristikon keskikerros tulostetaan. Tämä tulisi tulostaa melko hitaasti, sillä suuttimesta tulevan materiaalin määrä on varsin suuri."

msgctxt "speed_wall_0 description"
msgid "The speed at which the outermost walls are printed. Printing the outer wall at a lower speed improves the final skin quality. However, having a large difference between the inner wall speed and the outer wall speed will affect quality in a negative way."
msgstr "Nopeus, jolla uloimmat seinämät tulostetaan. Ulkoseinämien tulostus hitaammalla nopeudella parantaa lopullisen pintakalvon laatua. Jos sisäseinämän ja ulkoseinämän nopeuden välillä on kuitenkin suuri ero, se vaikuttaa negatiivisesti laatuun."

msgctxt "speed_prime_tower description"
msgid "The speed at which the prime tower is printed. Printing the prime tower slower can make it more stable when the adhesion between the different filaments is suboptimal."
msgstr "Nopeus, jolla esitäyttötorni tulostetaan. Esitäyttötornin tulostus hitaammin saattaa tehdä siitä vakaamman, jos eri tulostuslankojen tarttuvuus ei ole paras mahdollinen."

msgctxt "cool_fan_speed description"
msgid "The speed at which the print cooling fans spin."
msgstr "Tulostuksen jäähdytystuulettimien käyntinopeus."

msgctxt "raft_speed description"
msgid "The speed at which the raft is printed."
msgstr "Nopeus, jolla pohjaristikko tulostetaan."

msgctxt "speed_support_interface description"
msgid "The speed at which the roofs and floors of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "Nopeus, jolla tuen katot ja lattiat tulostetaan. Niiden tulostus hitaammilla nopeuksilla voi parantaa ulokkeen laatua."

msgctxt "speed_support_roof description"
msgid "The speed at which the roofs of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "Nopeus, jolla tuen katot tulostetaan. Niiden tulostus hitaammilla nopeuksilla voi parantaa ulokkeen laatua."

msgctxt "skirt_brim_speed description"
msgid "The speed at which the skirt and brim are printed. Normally this is done at the initial layer speed, but sometimes you might want to print the skirt or brim at a different speed."
msgstr "Nopeus, jolla helma ja reunus tulostetaan. Yleensä se tehdään alkukerroksen nopeudella. Joskus helma tai reunus halutaan kuitenkin tulostaa eri nopeudella."

msgctxt "speed_support description"
msgid "The speed at which the support structure is printed. Printing support at higher speeds can greatly reduce printing time. The surface quality of the support structure is not important since it is removed after printing."
msgstr "Nopeus, jolla tukirakenne tulostetaan. Tukirakenteiden tulostus korkeammilla nopeuksilla voi lyhentää tulostusaikaa merkittävästi. Tukirakenteen pinnan laadulla ei ole merkitystä, koska rakenne poistetaan tulostuksen jälkeen."

msgctxt "raft_surface_speed description"
msgid "The speed at which the top raft layers are printed. These should be printed a bit slower, so that the nozzle can slowly smooth out adjacent surface lines."
msgstr "Nopeus, jolla pohjaristikon pintakerrokset tulostetaan. Nämä tulisi tulostaa hieman hitaammin, jotta suutin voi hitaasti tasoittaa vierekkäisiä pintalinjoja."

msgctxt "speed_wall_x_roofing description"
msgid "The speed at which the top surface inner walls are printed."
msgstr "Yläpinnan sisäseinien tulostusnopeus."

msgctxt "speed_wall_0_roofing description"
msgid "The speed at which the top surface outermost wall is printed."
msgstr "Yläpinnan uloimpien seinien tulostusnopeus."

msgctxt "speed_z_hop description"
msgid "The speed at which the vertical Z movement is made for Z Hops. This is typically lower than the print speed since the build plate or machine's gantry is harder to move."
msgstr ""

msgctxt "speed_wall description"
msgid "The speed at which the walls are printed."
msgstr "Seinämien tulostamiseen käytettävä nopeus."

msgctxt "speed_ironing description"
msgid "The speed at which to pass over the top surface."
msgstr "Yläpinnan ylikulkuun käytettävä nopeus."

msgctxt "material_break_speed description"
msgid "The speed at which to retract the filament in order to break it cleanly."
msgstr ""

msgctxt "speed_roofing description"
msgid "The speed at which top surface skin layers are printed."
msgstr "Yläpinnan pintakalvokerrosten tulostamiseen käytettävä nopeus."

msgctxt "speed_topbottom description"
msgid "The speed at which top/bottom layers are printed."
msgstr "Ylä-/alakerrosten tulostamiseen käytettävä nopeus."

msgctxt "speed_travel description"
msgid "The speed at which travel moves are made."
msgstr "Nopeus, jolla siirtoliikkeet tehdään."

msgctxt "coasting_speed description"
msgid "The speed by which to move during coasting, relative to the speed of the extrusion path. A value slightly under 100% is advised, since during the coasting move the pressure in the bowden tube drops."
msgstr "Nopeus, jolla siirrytään vapaaliu'un aikana, suhteessa pursotusreitin nopeuteen. Arvoksi suositellaan hieman alle 100 %, sillä vapaaliukusiirron aikana paine Bowden-putkessa laskee."

msgctxt "speed_layer_0 description"
msgid "The speed for the initial layer. A lower value is advised to improve adhesion to the build plate. Does not affect the build plate adhesion structures themselves, like brim and raft."
msgstr ""

msgctxt "speed_print_layer_0 description"
msgid "The speed of printing for the initial layer. A lower value is advised to improve adhesion to the build plate."
msgstr "Alkukerroksen tulostusnopeus. Alhaisempi arvo on suositeltava, jotta tarttuvuus alustaan on parempi."

msgctxt "speed_travel_layer_0 description"
msgid "The speed of travel moves in the initial layer. A lower value is advised to prevent pulling previously printed parts away from the build plate. The value of this setting can automatically be calculated from the ratio between the Travel Speed and the Print Speed."
msgstr "Alkukerroksen siirtoliikkeiden nopeus. Alhaisempi arvo on suositeltava, jotta aikaisemmin tulostettuja osia ei vedetä pois alustasta. Tämän asetuksen arvo voidaan laskea automaattisesti siirtoliikkeen nopeuden ja tulostusnopeuden suhteen perusteella."

msgctxt "material_break_temperature description"
msgid "The temperature at which the filament is broken for a clean break."
msgstr ""

msgctxt "build_volume_temperature description"
msgid "The temperature of the environment to print in. If this is 0, the build volume temperature will not be adjusted."
msgstr ""

msgctxt "material_standby_temperature description"
msgid "The temperature of the nozzle when another nozzle is currently used for printing."
msgstr "Suuttimen lämpötila, kun toista suutinta käytetään tulostukseen."

msgctxt "material_final_print_temperature description"
msgid "The temperature to which to already start cooling down just before the end of printing."
msgstr "Lämpötila, johon jäähdytetään jo ennen tulostuksen loppumista."

msgctxt "material_print_temperature_layer_0 description"
msgid "The temperature used for printing the first layer."
msgstr ""

msgctxt "material_print_temperature description"
msgid "The temperature used for printing."
msgstr "Tulostukseen käytettävä lämpötila."

msgctxt "material_bed_temperature_layer_0 description"
msgid "The temperature used for the heated build plate at the first layer. If this is 0, the build plate is left unheated during the first layer."
msgstr ""

msgctxt "material_bed_temperature description"
msgid "The temperature used for the heated build plate. If this is 0, the build plate is left unheated."
msgstr ""

msgctxt "material_break_preparation_temperature description"
msgid "The temperature used to purge material, should be roughly equal to the highest possible printing temperature."
msgstr ""

msgctxt "bottom_thickness description"
msgid "The thickness of the bottom layers in the print. This value divided by the layer height defines the number of bottom layers."
msgstr "Alakerrosten paksuus tulosteessa. Tämä arvo jaettuna kerroksen korkeusarvolla määrittää alakerrosten lukumäärän."

msgctxt "skin_edge_support_thickness description"
msgid "The thickness of the extra infill that supports skin edges."
msgstr ""

msgctxt "support_interface_height description"
msgid "The thickness of the interface of the support where it touches with the model on the bottom or the top."
msgstr "Tukiliittymän paksuus kohdassa, jossa se koskettaa mallia ylä- tai alaosassa."

msgctxt "support_bottom_height description"
msgid "The thickness of the support floors. This controls the number of dense layers that are printed on top of places of a model on which support rests."
msgstr "Tuen lattioiden paksuus. Tällä hallitaan sellaisten tiheiden kerrosten määrää, jotka tulostetaan mallin tukea kannattelevien kohtien päälle."

msgctxt "support_roof_height description"
msgid "The thickness of the support roofs. This controls the amount of dense layers at the top of the support on which the model rests."
msgstr "Tukikattojen paksuus. Tällä hallitaan tiheiden kerrosten määrää sen tuen päällä, jolla malli lepää."

msgctxt "top_thickness description"
msgid "The thickness of the top layers in the print. This value divided by the layer height defines the number of top layers."
msgstr "Yläkerrosten paksuus tulosteessa. Tämä arvo jaettuna kerroksen korkeusarvolla määrittää yläkerrosten lukumäärän."

msgctxt "top_bottom_thickness description"
msgid "The thickness of the top/bottom layers in the print. This value divided by the layer height defines the number of top/bottom layers."
msgstr "Ylä-/alakerrosten paksuus tulosteessa. Tämä arvo jaettuna kerroksen korkeusarvolla määrittää ylä-/alakerrosten lukumäärän."

msgctxt "wall_thickness description"
msgid "The thickness of the walls in the horizontal direction. This value divided by the wall line width defines the number of walls."
msgstr "Seinämien paksuus vaakatasossa. Tämä arvo jaettuna seinämälinjan leveysarvolla määrittää seinämien lukumäärän."

msgctxt "infill_sparse_thickness description"
msgid "The thickness per layer of infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "Täyttömateriaalin paksuus kerrosta kohti. Tämän arvon tulisi aina olla kerroksen korkeuden kerrannainen. Muissa tapauksissa se pyöristetään."

msgctxt "support_infill_sparse_thickness description"
msgid "The thickness per layer of support infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "Tuen täyttömateriaalin paksuus kerrosta kohti. Tämän arvon tulee aina olla kerroksen korkeuden kerrannainen. Muissa tapauksissa se pyöristetään."

msgctxt "machine_gcode_flavor description"
msgid "The type of g-code to be generated."
msgstr ""

msgctxt "coasting_volume description"
msgid "The volume otherwise oozed. This value should generally be close to the nozzle diameter cubed."
msgstr "Aineen määrä, joka muutoin on tihkunut. Tämän arvon tulisi yleensä olla lähellä suuttimen läpimittaa korotettuna kuutioon."

msgctxt "machine_width description"
msgid "The width (X-direction) of the printable area."
msgstr "Tulostettavan alueen leveys (X-suunta)."

msgctxt "support_brim_width description"
msgid "The width of the brim to print underneath the support. A larger brim enhances adhesion to the build plate, at the cost of some extra material."
msgstr ""

msgctxt "interlocking_beam_width description"
msgid "The width of the interlocking structure beams."
msgstr "Esitäyttötornin leveys."

msgctxt "prime_tower_base_size description"
msgid "The width of the prime tower brim/base. A larger base enhances adhesion to the build plate, but also reduces the effective print area."
msgstr ""

msgctxt "prime_tower_size description"
msgid "The width of the prime tower."
msgstr "Esitäyttötornin leveys."

msgctxt "magic_fuzzy_skin_thickness description"
msgid "The width within which to jitter. It's advised to keep this below the outer wall width, since the inner walls are unaltered."
msgstr "Leveys, jolla värinä tapahtuu. Tämä suositellaan pidettäväksi ulkoseinämän leveyttä pienempänä, koska sisäseinämiä ei muuteta."

msgctxt "retraction_extrusion_window description"
msgid "The window in which the maximum retraction count is enforced. This value should be approximately the same as the retraction distance, so that effectively the number of times a retraction passes the same patch of material is limited."
msgstr "Ikkuna, jossa takaisinvedon maksimiluku otetaan käyttöön. Tämän ikkunan tulisi olla suunnilleen takaisinvetoetäisyyden kokoinen, jotta saman kohdan sivuuttavien takaisinvetojen lukumäärää saadaan rajoitettua."

msgctxt "prime_tower_position_x description"
msgid "The x coordinate of the position of the prime tower."
msgstr "Esitäyttötornin sijainnin X-koordinaatti."

msgctxt "prime_tower_position_y description"
msgid "The y coordinate of the position of the prime tower."
msgstr "Esitäyttötornin sijainnin Y-koordinaatti."

msgctxt "support_meshes_present description"
msgid "There are support meshes present in the scene. This setting is controlled by Cura."
msgstr ""

msgctxt "bridge_wall_coast description"
msgid "This controls the distance the extruder should coast immediately before a bridge wall begins. Coasting before the bridge starts can reduce the pressure in the nozzle and may produce a flatter bridge."
msgstr ""

msgctxt "raft_smoothing description"
msgid "This setting controls how much inner corners in the raft outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr ""

msgctxt "retraction_count_max description"
msgid "This setting limits the number of retractions occurring within the minimum extrusion distance window. Further retractions within this window will be ignored. This avoids retracting repeatedly on the same piece of filament, as that can flatten the filament and cause grinding issues."
msgstr "Tämä asetus rajoittaa pursotuksen minimietäisyyden ikkunassa tapahtuvien takaisinvetojen lukumäärää. Muut tämän ikkunan takaisinvedot jätetään huomiotta. Tällä vältetään toistuvat takaisinvedot samalla tulostuslangan osalla, sillä tällöin lanka voi litistyä ja aiheuttaa hiertymisongelmia."

msgctxt "draft_shield_enabled description"
msgid "This will create a wall around the model, which traps (hot) air and shields against exterior airflow. Especially useful for materials which warp easily."
msgstr "Tämä luo mallin ympärille seinämän, joka pidättää (kuumaa) ilmaa ja suojaa ulkoiselta ilmavirtaukselta. Erityisen käyttökelpoinen materiaaleilla, jotka vääntyvät helposti."

msgctxt "support_tree_tip_diameter label"
msgid "Tip Diameter"
msgstr ""

msgctxt "material_shrinkage_percentage_xy description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the XY-direction (horizontally)."
msgstr ""

msgctxt "material_shrinkage_percentage_z description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the Z-direction (vertically)."
msgstr ""

msgctxt "material_shrinkage_percentage description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor."
msgstr ""

msgctxt "top_layers label"
msgid "Top Layers"
msgstr "Yläkerrokset"

msgctxt "top_skin_expand_distance label"
msgid "Top Skin Expand Distance"
msgstr "Yläpintakalvon laajennuksen etäisyys"

msgctxt "top_skin_preshrink label"
msgid "Top Skin Removal Width"
msgstr "Yläpintakalvon poistoleveys"

msgctxt "acceleration_wall_x_roofing label"
msgid "Top Surface Inner Wall Acceleration"
msgstr "Yläpinnan sisäseinän kiihtyvyys"

msgctxt "jerk_wall_x_roofing label"
msgid "Top Surface Inner Wall Jerk"
msgstr "Yläpinnan uloimman seinän nykäys"

msgctxt "speed_wall_x_roofing label"
msgid "Top Surface Inner Wall Speed"
msgstr "Yläpinnan sisäseinän nopeus"

msgctxt "wall_x_material_flow_roofing label"
msgid "Top Surface Inner Wall(s) Flow"
msgstr "Yläpinnan sisäseinän virtaus"

msgctxt "acceleration_wall_0_roofing label"
msgid "Top Surface Outer Wall Acceleration"
msgstr "Yläpinnan ulkoseinän kiihtyvyys"

msgctxt "wall_0_material_flow_roofing label"
msgid "Top Surface Outer Wall Flow"
msgstr "Yläpinnan uloimman seinän virtaus"

msgctxt "jerk_wall_0_roofing label"
msgid "Top Surface Outer Wall Jerk"
msgstr "Yläpinnan sisäseinän nykäys"

msgctxt "speed_wall_0_roofing label"
msgid "Top Surface Outer Wall Speed"
msgstr ""

msgctxt "acceleration_roofing label"
msgid "Top Surface Skin Acceleration"
msgstr "Yläpinnan pintakalvon kiihtyvyys"

msgctxt "roofing_extruder_nr label"
msgid "Top Surface Skin Extruder"
msgstr "Yläpinnan pintakalvon suulake"

msgctxt "roofing_material_flow label"
msgid "Top Surface Skin Flow"
msgstr ""

msgctxt "jerk_roofing label"
msgid "Top Surface Skin Jerk"
msgstr "Yläpinnan pintakalvon nykäisy"

msgctxt "roofing_layer_count label"
msgid "Top Surface Skin Layers"
msgstr "Yläpinnan pintakalvokerrokset"

msgctxt "roofing_angles label"
msgid "Top Surface Skin Line Directions"
msgstr "Yläpinnan pintakalvon linjojen suunnat"

msgctxt "roofing_line_width label"
msgid "Top Surface Skin Line Width"
msgstr "Yläpinnan pintakalvon linjan leveys"

msgctxt "roofing_pattern label"
msgid "Top Surface Skin Pattern"
msgstr "Yläpinnan pintakalvokuvio"

msgctxt "speed_roofing label"
msgid "Top Surface Skin Speed"
msgstr "Yläpinnan pintakalvonopeus"

msgctxt "top_thickness label"
msgid "Top Thickness"
msgstr "Yläosan paksuus"

msgctxt "max_skin_angle_for_expansion description"
msgid "Top and/or bottom surfaces of your object with an angle larger than this setting, won't have their top/bottom skin expanded. This avoids expanding the narrow skin areas that are created when the model surface has a near vertical slope. An angle of 0° is horizontal and will cause no skin to be expanded, while an angle of 90° is vertical and will cause all skin to be expanded."
msgstr ""

msgctxt "top_bottom description"
msgid "Top/Bottom"
msgstr ""

msgctxt "top_bottom label"
msgid "Top/Bottom"
msgstr ""

msgctxt "acceleration_topbottom label"
msgid "Top/Bottom Acceleration"
msgstr "Ylä-/alakerrosten kiihtyvyys"

msgctxt "top_bottom_extruder_nr label"
msgid "Top/Bottom Extruder"
msgstr "Ylä- ja alapuolen suulake"

msgctxt "skin_material_flow label"
msgid "Top/Bottom Flow"
msgstr ""

msgctxt "jerk_topbottom label"
msgid "Top/Bottom Jerk"
msgstr "Ylä-/alaosan nykäisy"

msgctxt "skin_angles label"
msgid "Top/Bottom Line Directions"
msgstr "Yläosan/alaosan linjojen suunnat"

msgctxt "skin_line_width label"
msgid "Top/Bottom Line Width"
msgstr "Ylä-/alalinjan leveys"

msgctxt "top_bottom_pattern label"
msgid "Top/Bottom Pattern"
msgstr "Ylä-/alaosan kuvio"

msgctxt "speed_topbottom label"
msgid "Top/Bottom Speed"
msgstr "Ylä-/alaosan nopeus"

msgctxt "top_bottom_thickness label"
msgid "Top/Bottom Thickness"
msgstr "Ylä-/alaosan paksuus"

msgctxt "support_type option buildplate"
msgid "Touching Buildplate"
msgstr "Alustaa koskettava"

msgctxt "support_tower_diameter label"
msgid "Tower Diameter"
msgstr "Tornin läpimitta"

msgctxt "support_tower_roof_angle label"
msgid "Tower Roof Angle"
msgstr "Tornin kattokulma"

msgctxt "mesh_rotation_matrix description"
msgid "Transformation matrix to be applied to the model when loading it from file."
msgstr "Mallissa käytettävä muunnosmatriisi, kun malli ladataan tiedostosta."

msgctxt "travel label"
msgid "Travel"
msgstr "Siirtoliike"

msgctxt "acceleration_travel label"
msgid "Travel Acceleration"
msgstr "Siirtoliikkeen kiihtyvyys"

msgctxt "travel_avoid_distance label"
msgid "Travel Avoid Distance"
msgstr "Siirtoliikkeen vältettävä etäisyys"

msgctxt "jerk_travel label"
msgid "Travel Jerk"
msgstr "Siirtoliikkeen nykäisy"

msgctxt "speed_travel label"
msgid "Travel Speed"
msgstr "Siirtoliikkeen nopeus"

msgctxt "magic_mesh_surface_mode description"
msgid "Treat the model as a surface only, a volume, or volumes with loose surfaces. The normal print mode only prints enclosed volumes. \"Surface\" prints a single wall tracing the mesh surface with no infill and no top/bottom skin. \"Both\" prints enclosed volumes like normal and any remaining polygons as surfaces."
msgstr "Käsittelee mallia vain pintana, ainemääränä tai löysillä pinnoilla varustettuina ainemäärinä. Normaali tulostustila tulostaa vain suljetut ainemäärät. Pinta-tila tulostaa yhden verkkopintaa seuraavan seinämän ilman täyttöä ja ilman ylä-/alapintakalvoa. Molemmat-tila tulostaa suljetut ainemäärät normaalisti ja jäljellä olevat monikulmiot pintoina."

msgctxt "support_structure option tree"
msgid "Tree"
msgstr ""

msgctxt "infill_pattern option trihexagon"
msgid "Tri-Hexagon"
msgstr ""

msgctxt "infill_pattern option triangles"
msgid "Triangles"
msgstr "Kolmiot"

msgctxt "support_bottom_pattern option triangles"
msgid "Triangles"
msgstr "Kolmiot"

msgctxt "support_interface_pattern option triangles"
msgid "Triangles"
msgstr "Kolmiot"

msgctxt "support_pattern option triangles"
msgid "Triangles"
msgstr "Kolmiot"

msgctxt "support_roof_pattern option triangles"
msgid "Triangles"
msgstr "Kolmiot"

msgctxt "support_tree_max_diameter label"
msgid "Trunk Diameter"
msgstr ""

msgctxt "machine_gcode_flavor option UltiGCode"
msgid "Ultimaker 2"
msgstr "Ultimaker 2"

msgctxt "meshfix_union_all label"
msgid "Union Overlapping Volumes"
msgstr "Yhdistä limittyvät ainemäärät"

msgctxt "bridge_wall_min_length description"
msgid "Unsupported walls shorter than this will be printed using the normal wall settings. Longer unsupported walls will be printed using the bridge wall settings."
msgstr ""

msgctxt "adaptive_layer_height_enabled label"
msgid "Use Adaptive Layers"
msgstr ""

msgctxt "support_use_towers label"
msgid "Use Towers"
msgstr "Käytä torneja"

msgctxt "acceleration_travel_enabled description"
msgid "Use a separate acceleration rate for travel moves. If disabled, travel moves will use the acceleration value of the printed line at their destination."
msgstr ""

msgctxt "jerk_travel_enabled description"
msgid "Use a separate jerk rate for travel moves. If disabled, travel moves will use the jerk value of the printed line at their destination."
msgstr ""

msgctxt "relative_extrusion description"
msgid "Use relative extrusion rather than absolute extrusion. Using relative E-steps makes for easier post-processing of the g-code. However, it's not supported by all printers and it may produce very slight deviations in the amount of deposited material compared to absolute E-steps. Irrespective of this setting, the extrusion mode will always be set to absolute before any g-code script is output."
msgstr ""

msgctxt "support_use_towers description"
msgid "Use specialized towers to support tiny overhang areas. These towers have a larger diameter than the region they support. Near the overhang the towers' diameter decreases, forming a roof."
msgstr "Pieniä ulokealueita tuetaan erityisillä torneilla. Näiden tornien läpimitta on niiden tukemaa aluetta suurempi. Ulokkeen lähellä tornien läpimitta pienenee muodostaen katon."

msgctxt "infill_mesh description"
msgid "Use this mesh to modify the infill of other meshes with which it overlaps. Replaces infill regions of other meshes with regions for this mesh. It's suggested to only print one Wall and no Top/Bottom Skin for this mesh."
msgstr "Tällä verkolla muokataan sen kanssa limittyvien toisten verkkojen täyttöä. Asetuksella korvataan toisten verkkojen täyttöalueet tämän verkon alueilla. Tälle verkolle on suositeltavaa tulostaa vain yksi seinämä ja ei ylä-/alapintakalvoa."

msgctxt "support_mesh description"
msgid "Use this mesh to specify support areas. This can be used to generate support structure."
msgstr "Käytä tätä verkkoa tukialueiden valintaan. Sen avulla voidaan luoda tukirakenne."

msgctxt "anti_overhang_mesh description"
msgid "Use this mesh to specify where no part of the model should be detected as overhang. This can be used to remove unwanted support structure."
msgstr "Käytä tätä verkkoa määrittääksesi, missä mitään mallin osaa ei tule tunnistaa ulokkeeksi. Tätä toimintoa voidaan käyttää ei-toivotun tukirakenteen poistamiseksi."

msgctxt "z_seam_type option back"
msgid "User Specified"
msgstr "Käyttäjän määrittämä"

msgctxt "material_shrinkage_percentage_z label"
msgid "Vertical Scaling Factor Shrinkage Compensation"
msgstr ""

msgctxt "slicing_tolerance description"
msgid "Vertical tolerance in the sliced layers. The contours of a layer are normally generated by taking cross sections through the middle of each layer's thickness (Middle). Alternatively each layer can have the areas which fall inside of the volume throughout the entire thickness of the layer (Exclusive) or a layer has the areas which fall inside anywhere within the layer (Inclusive). Inclusive retains the most details, Exclusive makes for the best fit and Middle stays closest to the original surface."
msgstr ""

msgctxt "material_bed_temp_wait label"
msgid "Wait for Build Plate Heatup"
msgstr "Odota alustan lämpenemistä"

msgctxt "material_print_temp_wait label"
msgid "Wait for Nozzle Heatup"
msgstr "Odota suuttimen lämpenemistä"

msgctxt "acceleration_wall label"
msgid "Wall Acceleration"
msgstr "Seinämän kiihtyvyys"

msgctxt "wall_distribution_count label"
msgid "Wall Distribution Count"
msgstr ""

msgctxt "wall_extruder_nr label"
msgid "Wall Extruder"
msgstr "Seinämien suulake"

msgctxt "wall_material_flow label"
msgid "Wall Flow"
msgstr ""

msgctxt "jerk_wall label"
msgid "Wall Jerk"
msgstr "Seinämän nykäisy"

msgctxt "wall_line_count label"
msgid "Wall Line Count"
msgstr "Seinämälinjaluku"

msgctxt "wall_line_width label"
msgid "Wall Line Width"
msgstr "Seinämälinjan leveys"

msgctxt "inset_direction label"
msgid "Wall Ordering"
msgstr ""

msgctxt "speed_wall label"
msgid "Wall Speed"
msgstr "Seinämänopeus"

msgctxt "wall_thickness label"
msgid "Wall Thickness"
msgstr "Seinämän paksuus"

msgctxt "wall_transition_length label"
msgid "Wall Transition Length"
msgstr ""

msgctxt "wall_transition_filter_distance label"
msgid "Wall Transitioning Filter Distance"
msgstr ""

msgctxt "wall_transition_filter_deviation label"
msgid "Wall Transitioning Filter Margin"
msgstr ""

msgctxt "wall_transition_angle label"
msgid "Wall Transitioning Threshold Angle"
msgstr ""

msgctxt "shell label"
msgid "Walls"
msgstr ""

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either."
msgstr ""

msgctxt "support_interface_skip_height description"
msgid "When checking where there's model above and below the support, take steps of the given height. Lower values will slice slower, while higher values may cause normal support to be printed in some places where there should have been support interface."
msgstr "Kun tarkistat tuen päällä ja alla olevaa mallia, toimi annetun korkeuden mukaisesti. Pienemmillä arvoilla viipalointi tapahtuu hitaammin, ja korkeammat arvot saattavat aiheuttaa normaalin tuen tulostumisen paikkoihin, joissa olisi pitänyt olla tukiliittymä."

msgctxt "meshfix_fluid_motion_enabled description"
msgid "When enabled tool paths are corrected for printers with smooth motion planners. Small movements that deviate from the general tool path direction are smoothed to improve fluid motions."
msgstr ""

msgctxt "infill_enable_travel_optimization description"
msgid "When enabled, the order in which the infill lines are printed is optimized to reduce the distance travelled. The reduction in travel time achieved very much depends on the model being sliced, infill pattern, density, etc. Note that, for some models that have many small areas of infill, the time to slice the model may be greatly increased."
msgstr ""

msgctxt "support_fan_enable description"
msgid "When enabled, the print cooling fan speed is altered for the skin regions immediately above the support."
msgstr ""

msgctxt "z_seam_relative description"
msgid "When enabled, the z seam coordinates are relative to each part's centre. When disabled, the coordinates define an absolute position on the build plate."
msgstr "Kun tämä on käytössä, Z-sauman koordinaatit ovat suhteessa kunkin osan keskikohtaan. Kun asetus on pois käytöstä, koordinaatit määrittävät absoluuttisen sijainnin alustalla."

msgctxt "retraction_combing_max_distance description"
msgid "When greater than zero, combing travel moves that are longer than this distance will use retraction. If set to zero, there is no maximum and combing moves will not use retraction."
msgstr ""

msgctxt "hole_xy_offset_max_diameter description"
msgid "When greater than zero, the Hole Horizontal Expansion is gradually applied on small holes (small holes are expanded more). When set to zero the Hole Horizontal Expansion will be applied to all holes. Holes larger than the Hole Horizontal Expansion Max Diameter are not expanded."
msgstr ""

msgctxt "hole_xy_offset description"
msgid "When greater than zero, the Hole Horizontal Expansion is the amount of offset applied to all holes in each layer. Positive values increase the size of the holes, negative values reduce the size of the holes. When this setting is enabled it can be further tuned with Hole Horizontal Expansion Max Diameter."
msgstr ""

msgctxt "bridge_skin_material_flow description"
msgid "When printing bridge skin regions, the amount of material extruded is multiplied by this value."
msgstr ""

msgctxt "bridge_wall_material_flow description"
msgid "When printing bridge walls, the amount of material extruded is multiplied by this value."
msgstr ""

msgctxt "bridge_skin_material_flow_2 description"
msgid "When printing the second bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr ""

msgctxt "bridge_skin_material_flow_3 description"
msgid "When printing the third bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr ""

msgctxt "cool_lift_head description"
msgid "When the minimum speed is hit because of minimum layer time, lift the head away from the print and wait the extra time until the minimum layer time is reached."
msgstr "Kun miniminopeuteen päädytään kerroksen minimiajan johdosta, nosta pää pois tulosteesta ja odota, kunnes kerroksen minimiaika täyttyy."

msgctxt "skin_no_small_gaps_heuristic description"
msgid "When the model has small vertical gaps of only a few layers, there should normally be skin around those layers in the narrow space. Enable this setting to not generate skin if the vertical gap is very small. This improves printing time and slicing time, but technically leaves infill exposed to the air."
msgstr ""

msgctxt "wall_transition_angle description"
msgid "When to create transitions between even and odd numbers of walls. A wedge shape with an angle greater than this setting will not have transitions and no walls will be printed in the center to fill the remaining space. Reducing this setting reduces the number and length of these center walls, but may leave gaps or overextrude."
msgstr ""

msgctxt "wall_transition_length description"
msgid "When transitioning between different numbers of walls as the part becomes thinner, a certain amount of space is allotted to split or join the wall lines."
msgstr ""

msgctxt "wipe_hop_enable description"
msgid "When wiping, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr ""

msgctxt "retraction_hop_enabled description"
msgid "Whenever a retraction is done, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "Alustaa lasketaan aina kun takaisinveto tehdään, jotta suuttimen ja tulosteen väliin jää tilaa. Tämä estää suuttimen osumisen tulosteeseen siirtoliikkeen yhteydessä ja vähentää näin sen vaaraa, että tuloste työnnetään pois alustalta."

msgctxt "support_xy_overrides_z description"
msgid "Whether the Support X/Y Distance overrides the Support Z Distance or vice versa. When X/Y overrides Z the X/Y distance can push away the support from the model, influencing the actual Z distance to the overhang. We can disable this by not applying the X/Y distance around overhangs."
msgstr "Kumoaako tuen X-/Y-etäisyys tuen Z-etäisyyden vai päinvastoin. Kun X/Y kumoaa Z:n, X-/Y-etäisyys saattaa työntää tuen pois mallista, mikä vaikuttaa todelliseen Z-etäisyyteen ulokkeeseen. Tämä voidaan estää poistamalla X-/Y-etäisyyden käyttö ulokkeiden lähellä."

msgctxt "machine_center_is_zero description"
msgid "Whether the X/Y coordinates of the zero position of the printer is at the center of the printable area."
msgstr "Ovatko tulostimen nollasijainnin X-/Y-koordinaatit tulostettavan alueen keskellä."

msgctxt "machine_endstop_positive_direction_x description"
msgid "Whether the endstop of the X axis is in the positive direction (high X coordinate) or negative (low X coordinate)."
msgstr ""

msgctxt "machine_endstop_positive_direction_y description"
msgid "Whether the endstop of the Y axis is in the positive direction (high Y coordinate) or negative (low Y coordinate)."
msgstr ""

msgctxt "machine_endstop_positive_direction_z description"
msgid "Whether the endstop of the Z axis is in the positive direction (high Z coordinate) or negative (low Z coordinate)."
msgstr ""

msgctxt "machine_extruders_share_heater description"
msgid "Whether the extruders share a single heater rather than each extruder having its own heater."
msgstr ""

msgctxt "machine_extruders_share_nozzle description"
msgid "Whether the extruders share a single nozzle rather than each extruder having its own nozzle. When set to true, it is expected that the printer-start gcode script properly sets up all extruders in an initial retraction state that is known and mutually compatible (either zero or one filament not retracted); in that case the initial retraction status is described, per extruder, by the 'machine_extruders_shared_nozzle_initial_retraction' parameter."
msgstr ""

msgctxt "machine_heated_bed description"
msgid "Whether the machine has a heated build plate present."
msgstr "Sisältääkö laite lämmitettävän alustan."

msgctxt "machine_heated_build_volume description"
msgid "Whether the machine is able to stabilize the build volume temperature."
msgstr ""

msgctxt "center_object description"
msgid "Whether to center the object on the middle of the build platform (0,0), instead of using the coordinate system in which the object was saved."
msgstr "Määrittää, keskitetäänkö kappale alustan keskelle (0,0) sen sijasta, että käytettäisiin koordinaattijärjestelmää, jolla kappale on tallennettu."

msgctxt "machine_nozzle_temp_enabled description"
msgid "Whether to control temperature from Cura. Turn this off to control nozzle temperature from outside of Cura."
msgstr "Lämpötilan hallinta Curan kautta. Kytke tämä pois, niin voit hallita suuttimen lämpötilaa Curan ulkopuolella."

msgctxt "material_bed_temp_prepend description"
msgid "Whether to include build plate temperature commands at the start of the gcode. When the start_gcode already contains build plate temperature commands Cura frontend will automatically disable this setting."
msgstr "Sisällytetäänkö alustan lämpötilakomennot GCoden alkuun. Kun aloitus-GCode sisältää jo alustan lämpötilakomennot, Cura-edustaohjelma poistaa tämän asetuksen automaattisesti käytöstä."

msgctxt "material_print_temp_prepend description"
msgid "Whether to include nozzle temperature commands at the start of the gcode. When the start_gcode already contains nozzle temperature commands Cura frontend will automatically disable this setting."
msgstr "Sisällytetäänkö suuttimen lämpötilakomennot GCoden alkuun. Kun start_gcode sisältää jo suuttimen lämpötilakomennot, Cura-edustaohjelma poistaa tämän asetuksen automaattisesti käytöstä."

msgctxt "clean_between_layers description"
msgid "Whether to include nozzle wipe G-Code between layers (maximum 1 per layer). Enabling this setting could influence behavior of retract at layer change. Please use Wipe Retraction settings to control retraction at layers where the wipe script will be working."
msgstr ""

msgctxt "material_bed_temp_wait description"
msgid "Whether to insert a command to wait until the build plate temperature is reached at the start."
msgstr "Lisätäänkö komento, jolla odotetaan alustan lämpötilan saavuttamista alussa."

msgctxt "prime_blob_enable description"
msgid "Whether to prime the filament with a blob before printing. Turning this setting on will ensure that the extruder will have material ready at the nozzle before printing. Printing Brim or Skirt can act like priming too, in which case turning this setting off saves some time."
msgstr "Tulostuslangan esitäyttö materiaalipisaralla ennen tulostusta. Tämän asetuksen käyttöönotolla varmistat, että suulakkeen suuttimessa on materiaalia valmiina ennen tulostusta. Myös helman tai reunuksen tulostaminen voi toimia esitäyttönä, jolloin tämän asetuksen käytöstä poisto säästää hieman aikaa."

msgctxt "print_sequence description"
msgid "Whether to print all models one layer at a time or to wait for one model to finish, before moving on to the next. One at a time mode is possible if a) only one extruder is enabled and b) all models are separated in such a way that the whole print head can move in between and all models are lower than the distance between the nozzle and the X/Y axes."
msgstr ""

msgctxt "machine_show_variants description"
msgid "Whether to show the different variants of this machine, which are described in separate json files."
msgstr "Näytetäänkö laitteen eri variantit, jotka kuvataan erillisissä json-tiedostoissa."

msgctxt "machine_firmware_retract description"
msgid "Whether to use firmware retract commands (G10/G11) instead of using the E property in G1 commands to retract the material."
msgstr ""

msgctxt "material_print_temp_wait description"
msgid "Whether to wait until the nozzle temperature is reached at the start."
msgstr "Odotetaanko suuttimen lämpötilan saavuttamista alussa."

msgctxt "infill_line_width description"
msgid "Width of a single infill line."
msgstr "Yhden täyttölinjan leveys."

msgctxt "support_interface_line_width description"
msgid "Width of a single line of support roof or floor."
msgstr "Tukikaton tai -lattian yhden linjan leveys."

msgctxt "roofing_line_width description"
msgid "Width of a single line of the areas at the top of the print."
msgstr "Tulosteen yläosan alueiden yhden linjan leveys."

msgctxt "line_width description"
msgid "Width of a single line. Generally, the width of each line should correspond to the width of the nozzle. However, slightly reducing this value could produce better prints."
msgstr "Yhden linjan leveys. Yleensä kunkin linjan leveyden tulisi vastata suuttimen leveyttä. Pienentämällä tätä arvoa hiukan voidaan kuitenkin mahdollisesti tuottaa parempia tulosteita."

msgctxt "prime_tower_line_width description"
msgid "Width of a single prime tower line."
msgstr "Yhden esitäyttötornin linjan leveys."

msgctxt "skirt_brim_line_width description"
msgid "Width of a single skirt or brim line."
msgstr "Yhden helma- tai reunuslinjan leveys."

msgctxt "support_bottom_line_width description"
msgid "Width of a single support floor line."
msgstr "Tukilattian yhden linjan leveys."

msgctxt "support_roof_line_width description"
msgid "Width of a single support roof line."
msgstr "Tukikaton yhden linjan leveys."

msgctxt "support_line_width description"
msgid "Width of a single support structure line."
msgstr "Yhden tukirakenteen linjan leveys."

msgctxt "skin_line_width description"
msgid "Width of a single top/bottom line."
msgstr "Yhden ylä-/alalinjan leveys."

msgctxt "wall_line_width_x description"
msgid "Width of a single wall line for all wall lines except the outermost one."
msgstr "Yhden seinämälinjan leveys. Koskee kaikkia muita paitsi ulommaista seinämää."

msgctxt "wall_line_width description"
msgid "Width of a single wall line."
msgstr "Yhden seinämälinjan leveys."

msgctxt "raft_base_line_width description"
msgid "Width of the lines in the base raft layer. These should be thick lines to assist in build plate adhesion."
msgstr "Pohjaristikon pohjakerroksen linjojen leveys. Näiden tulisi olla paksuja linjoja auttamassa tarttuvuutta alustaan."

msgctxt "raft_interface_line_width description"
msgid "Width of the lines in the middle raft layer. Making the second layer extrude more causes the lines to stick to the build plate."
msgstr "Pohjaristikon keskikerroksen linjojen leveys. Pursottamalla toiseen kerrokseen enemmän saa linjat tarttumaan alustaan."

msgctxt "raft_surface_line_width description"
msgid "Width of the lines in the top surface of the raft. These can be thin lines so that the top of the raft becomes smooth."
msgstr "Pohjaristikon pintakerrosten linjojen leveys. Näiden tulisi olla ohuita linjoja, jotta pohjaristikon yläosasta tulee sileä."

msgctxt "wall_line_width_0 description"
msgid "Width of the outermost wall line. By lowering this value, higher levels of detail can be printed."
msgstr "Ulommaisen seinämälinjan leveys. Tätä arvoa pienentämällä voidaan tulostaa tarkempia yksityiskohtia."

msgctxt "min_bead_width description"
msgid "Width of the wall that will replace thin features (according to the Minimum Feature Size) of the model. If the Minimum Wall Line Width is thinner than the thickness of the feature, the wall will become as thick as the feature itself."
msgstr ""

msgctxt "wipe_brush_pos_x label"
msgid "Wipe Brush X Position"
msgstr ""

msgctxt "wipe_hop_speed label"
msgid "Wipe Hop Speed"
msgstr ""

msgctxt "prime_tower_wipe_enabled label"
msgid "Wipe Inactive Nozzle on Prime Tower"
msgstr "Pyyhi esitäyttötornin ei-aktiivinen suutin"

msgctxt "wipe_move_distance label"
msgid "Wipe Move Distance"
msgstr ""

msgctxt "clean_between_layers label"
msgid "Wipe Nozzle Between Layers"
msgstr ""

msgctxt "wipe_pause label"
msgid "Wipe Pause"
msgstr ""

msgctxt "wipe_repeat_count label"
msgid "Wipe Repeat Count"
msgstr ""

msgctxt "wipe_retraction_amount label"
msgid "Wipe Retraction Distance"
msgstr ""

msgctxt "wipe_retraction_enable label"
msgid "Wipe Retraction Enable"
msgstr ""

msgctxt "wipe_retraction_extra_prime_amount label"
msgid "Wipe Retraction Extra Prime Amount"
msgstr ""

msgctxt "wipe_retraction_prime_speed label"
msgid "Wipe Retraction Prime Speed"
msgstr ""

msgctxt "wipe_retraction_retract_speed label"
msgid "Wipe Retraction Retract Speed"
msgstr ""

msgctxt "wipe_retraction_speed label"
msgid "Wipe Retraction Speed"
msgstr ""

msgctxt "wipe_hop_enable label"
msgid "Wipe Z Hop"
msgstr ""

msgctxt "wipe_hop_amount label"
msgid "Wipe Z Hop Height"
msgstr ""

msgctxt "retraction_combing option infill"
msgid "Within Infill"
msgstr ""

msgctxt "machine_always_write_active_tool description"
msgid "Write active tool after sending temp commands to inactive tool. Required for Dual Extruder printing with Smoothie or other firmware with modal tool commands."
msgstr ""

msgctxt "machine_endstop_positive_direction_x label"
msgid "X Endstop in Positive Direction"
msgstr ""

msgctxt "wipe_brush_pos_x description"
msgid "X location where wipe script will start."
msgstr ""

msgctxt "support_xy_overrides_z option xy_overrides_z"
msgid "X/Y overrides Z"
msgstr "X/Y kumoaa Z:n"

msgctxt "machine_endstop_positive_direction_y label"
msgid "Y Endstop in Positive Direction"
msgstr ""

msgctxt "machine_endstop_positive_direction_z label"
msgid "Z Endstop in Positive Direction"
msgstr ""

msgctxt "retraction_hop_after_extruder_switch label"
msgid "Z Hop After Extruder Switch"
msgstr "Z-hyppy suulakkeen vaihdon jälkeen"

msgctxt "retraction_hop_after_extruder_switch_height label"
msgid "Z Hop After Extruder Switch Height"
msgstr ""

msgctxt "retraction_hop label"
msgid "Z Hop Height"
msgstr "Z-hypyn korkeus"

msgctxt "retraction_hop_only_when_collides label"
msgid "Z Hop Only Over Printed Parts"
msgstr "Z-hyppy vain tulostettujen osien yli"

msgctxt "speed_z_hop label"
msgid "Z Hop Speed"
msgstr ""

msgctxt "retraction_hop_enabled label"
msgid "Z Hop When Retracted"
msgstr "Z-hyppy takaisinvedon yhteydessä"

msgctxt "z_seam_type label"
msgid "Z Seam Alignment"
msgstr "Z-sauman kohdistus"

msgctxt "z_seam_position label"
msgid "Z Seam Position"
msgstr ""

msgctxt "z_seam_relative label"
msgid "Z Seam Relative"
msgstr "Z-sauma suhteellinen"

msgctxt "z_seam_x label"
msgid "Z Seam X"
msgstr "Z-sauma X"

msgctxt "z_seam_y label"
msgid "Z Seam Y"
msgstr "Z-sauma Y"

msgctxt "support_xy_overrides_z option z_overrides_xy"
msgid "Z overrides X/Y"
msgstr "Z kumoaa X/Y:n"

msgctxt "infill_pattern option zigzag"
msgid "Zig Zag"
msgstr "Siksak"

msgctxt "ironing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Siksak"

msgctxt "roofing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Siksak"

msgctxt "support_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Siksak"

msgctxt "support_interface_pattern option zigzag"
msgid "Zig Zag"
msgstr "Siksak"

msgctxt "support_pattern option zigzag"
msgid "Zig Zag"
msgstr "Siksak"

msgctxt "support_roof_pattern option zigzag"
msgid "Zig Zag"
msgstr "Siksak"

msgctxt "top_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Siksak"

msgctxt "top_bottom_pattern_0 option zigzag"
msgid "Zig Zag"
msgstr "Siksak"

msgctxt "travel description"
msgid "travel"
msgstr "siirtoliike"

msgctxt "gradual_flow_discretisation_step_size description"
msgid "Duration of each step in the gradual flow change"
msgstr ""

msgctxt "gradual_flow_enabled description"
msgid "Enable gradual flow changes. When enabled, the flow is gradually increased/decreased to the target flow. This is useful for printers with a bowden tube where the flow is not immediately changed when the extruder motor starts/stops."
msgstr ""

msgctxt "reset_flow_duration description"
msgid "For any travel move longer than this value, the material flow is reset to the paths target flow"
msgstr ""

msgctxt "gradual_flow_discretisation_step_size label"
msgid "Gradual flow discretisation step size"
msgstr ""

msgctxt "gradual_flow_enabled label"
msgid "Gradual flow enabled"
msgstr ""

msgctxt "max_flow_acceleration label"
msgid "Gradual flow max acceleration"
msgstr ""

msgctxt "layer_0_max_flow_acceleration label"
msgid "Initial layer max flow acceleration"
msgstr ""

msgctxt "max_flow_acceleration description"
msgid "Maximum acceleration for gradual flow changes"
msgstr ""

msgctxt "layer_0_max_flow_acceleration description"
msgid "Minimum speed for gradual flow changes for the first layer"
msgstr ""

msgctxt "reset_flow_duration label"
msgid "Reset flow duration"
msgstr ""
