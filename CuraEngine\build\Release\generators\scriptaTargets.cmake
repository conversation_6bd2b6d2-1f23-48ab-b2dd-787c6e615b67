# Load the debug and release variables
file(GLOB DATA_FILES "${CMAKE_CURRENT_LIST_DIR}/scripta-*-data.cmake")

foreach(f ${DATA_FILES})
    include(${f})
endforeach()

# Create the targets for all the components
foreach(_COMPONENT ${scripta_COMPONENT_NAMES} )
    if(NOT TARGET ${_COMPONENT})
        add_library(${_COMPONENT} INTERFACE IMPORTED)
        message(${scripta_MESSAGE_MODE} "Conan: Component target declared '${_COMPONENT}'")
    endif()
endforeach()

if(NOT TARGET scripta::scripta)
    add_library(scripta::scripta INTERFACE IMPORTED)
    message(${scripta_MESSAGE_MODE} "Conan: Target declared 'scripta::scripta'")
endif()
# Load the debug and release library finders
file(GLOB CONFIG_FILES "${CMAKE_CURRENT_LIST_DIR}/scripta-Target-*.cmake")

foreach(f ${CONFIG_FILES})
    include(${f})
endforeach()