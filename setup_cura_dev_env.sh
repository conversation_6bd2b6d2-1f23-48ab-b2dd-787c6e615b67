#!/bin/bash
# Cura and Uranium Parallel Development Environment Setup Script
# This script sets up a cross-platform development environment for Cura and Uranium
# with system suffixes to avoid conflicts between macOS and Windows

set -e

FORCE=false
PYTHON_VERSION="3.12"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --force)
            FORCE=true
            shift
            ;;
        --python-version)
            PYTHON_VERSION="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            echo "Usage: $0 [--force] [--python-version VERSION]"
            exit 1
            ;;
    esac
done

echo "=== Cura & Uranium Parallel Development Environment Setup ==="
echo "Setting up cross-platform development environment with system suffixes..."

# Detect system
if [[ "$OSTYPE" == "darwin"* ]]; then
    SYSTEM_SUFFIX="macos"
    VENV_ACTIVATE_SCRIPT="bin/activate"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    SYSTEM_SUFFIX="linux"
    VENV_ACTIVATE_SCRIPT="bin/activate"
else
    echo "Error: This script is designed for macOS and Linux. Please use the PowerShell version for Windows."
    exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check Python version
echo "Checking Python version..."
if command_exists python3; then
    PYTHON_CMD="python3"
elif command_exists python; then
    PYTHON_CMD="python"
else
    echo "Error: Python not found. Please install Python $PYTHON_VERSION or higher."
    exit 1
fi

PYTHON_VERSION_OUTPUT=$($PYTHON_CMD --version 2>&1)
echo "Found: $PYTHON_VERSION_OUTPUT"

# Extract version number and check
CURRENT_VERSION=$(echo "$PYTHON_VERSION_OUTPUT" | grep -oE '[0-9]+\.[0-9]+' | head -1)
REQUIRED_VERSION="3.11"
RECOMMENDED_VERSION="3.12"

if [[ $(echo "$CURRENT_VERSION $REQUIRED_VERSION" | awk '{print ($1 < $2)}') == 1 ]]; then
    echo "Error: Python $REQUIRED_VERSION or higher is required. Current version: $CURRENT_VERSION"
    echo "Please install Python 3.11 or higher."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        echo "On macOS, you can install using: brew install python@3.12"
    fi
    exit 1
elif [[ $(echo "$CURRENT_VERSION $RECOMMENDED_VERSION" | awk '{print ($1 < $2)}') == 1 ]]; then
    echo "Warning: Python 3.12+ is recommended by Ultimaker, but 3.11+ should work. Current version: $CURRENT_VERSION"
fi

# Create main virtual environment for Conan
CONAN_VENV_PATH="cura_conan_venv_$SYSTEM_SUFFIX"
echo "Creating Conan virtual environment: $CONAN_VENV_PATH"

if [[ -d "$CONAN_VENV_PATH" ]]; then
    if [[ "$FORCE" == true ]]; then
        echo "Removing existing virtual environment..."
        rm -rf "$CONAN_VENV_PATH"
    else
        echo "Virtual environment already exists. Use --force to recreate."
    fi
fi

if [[ ! -d "$CONAN_VENV_PATH" ]]; then
    $PYTHON_CMD -m venv "$CONAN_VENV_PATH"
fi

# Activate virtual environment
echo "Activating virtual environment..."
source "$CONAN_VENV_PATH/$VENV_ACTIVATE_SCRIPT"

# Install Conan 2.7.0
echo "Installing Conan 2.7.0..."
python -m pip install --upgrade pip
python -m pip install "conan==2.7.0"

# Verify Conan installation
CONAN_VERSION=$(conan --version)
echo "Installed: $CONAN_VERSION"

# Configure Conan
echo "Configuring Conan..."
conan config install https://github.com/ultimaker/conan-config.git
conan profile detect --force

# Set up Uranium as editable
echo "Setting up Uranium in editable mode..."
cd Uranium
conan editable add . "uranium/5.11.0-alpha.0@ultimaker/testing"
cd ..

# Install Cura dependencies with system suffix
echo "Installing Cura dependencies with system suffix..."
cd Cura

# Run conan install with PyCharm generator and system suffix
echo "Running conan install with system suffix..."
conan install . \
    --build=missing \
    --update \
    -g VirtualPythonEnv \
    -g PyCharmRunEnv \
    -c user.generator.virtual_python_env:dev_tools=True \
    --require-override=uranium/5.11.0-alpha.0@ultimaker/testing

cd ..

echo "=== Setup Complete ==="
echo ""
echo "Next steps:"
echo "1. Open PyCharm and open the Cura project folder"
echo "2. In PyCharm, go to File > Open and select the Uranium folder to attach it"
echo "3. Configure the Python interpreter to use: Cura/build_$SYSTEM_SUFFIX/generators/cura_venv_$SYSTEM_SUFFIX"
echo "4. Use the generated run configurations in PyCharm"
echo ""
echo "To activate the Cura development environment manually:"
echo "cd Cura"
echo "source build_$SYSTEM_SUFFIX/generators/virtual_python_env.sh"
echo "python cura_app.py"
echo ""
echo "Virtual environments created with system suffix '$SYSTEM_SUFFIX' for cross-platform compatibility."
