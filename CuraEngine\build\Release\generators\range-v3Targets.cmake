# Load the debug and release variables
file(GLOB DATA_FILES "${CMAKE_CURRENT_LIST_DIR}/range-v3-*-data.cmake")

foreach(f ${DATA_FILES})
    include(${f})
endforeach()

# Create the targets for all the components
foreach(_COMPONENT ${range-v3_COMPONENT_NAMES} )
    if(NOT TARGET ${_COMPONENT})
        add_library(${_COMPONENT} INTERFACE IMPORTED)
        message(${range-v3_MESSAGE_MODE} "Conan: Component target declared '${_COMPONENT}'")
    endif()
endforeach()

if(NOT TARGET range-v3::range-v3)
    add_library(range-v3::range-v3 INTERFACE IMPORTED)
    message(${range-v3_MESSAGE_MODE} "Conan: Target declared 'range-v3::range-v3'")
endif()
# Load the debug and release library finders
file(GLOB CONFIG_FILES "${CMAKE_CURRENT_LIST_DIR}/range-v3-Target-*.cmake")

foreach(f ${CONFIG_FILES})
    include(${f})
endforeach()