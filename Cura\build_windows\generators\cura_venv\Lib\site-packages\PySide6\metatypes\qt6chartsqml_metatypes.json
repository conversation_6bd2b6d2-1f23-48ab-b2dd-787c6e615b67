[{"classes": [{"classInfos": [{"name": "QML.Foreign", "value": "QBarModelMapper"}, {"name": "QML.Element", "value": "BarModelMapper"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Uncreatable base type"}], "className": "CppQBarModelMapper", "gadget": true, "lineNumber": 71, "qualifiedClassName": "CppQBarModelMapper"}, {"classInfos": [{"name": "QML.Foreign", "value": "QHBarModelMapper"}, {"name": "QML.Element", "value": "HBarModelMapper"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "CppQHBarModelMapper", "gadget": true, "lineNumber": 81, "qualifiedClassName": "CppQHBarModelMapper"}, {"classInfos": [{"name": "QML.Foreign", "value": "QVBarModelMapper"}, {"name": "QML.Element", "value": "VBarModelMapper"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "CppQVBarModelMapper", "gadget": true, "lineNumber": 90, "qualifiedClassName": "CppQVBarModelMapper"}, {"classInfos": [{"name": "QML.Foreign", "value": "QBarCategoryAxis"}, {"name": "QML.Element", "value": "BarCategoryAxis"}, {"name": "QML.Element", "value": "BarCategoriesAxis"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "CppQBarCategoryAxis", "gadget": true, "lineNumber": 99, "qualifiedClassName": "CppQBarCategoryAxis"}, {"classInfos": [{"name": "QML.Foreign", "value": "QAbstractBarSeries"}, {"name": "QML.Element", "value": "AbstractBarSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Uncreatable base type"}], "className": "CppQAbstractBarSeries", "gadget": true, "lineNumber": 109, "qualifiedClassName": "CppQAbstractBarSeries"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QList<QBarSet*>"}, {"name": "QML.Sequence", "value": "QBarSet*"}], "className": "CppQListBarSet", "gadget": true, "lineNumber": 119, "qualifiedClassName": "CppQListBarSet"}, {"classInfos": [{"name": "QML.Foreign", "value": "QPieModelMapper"}, {"name": "QML.Element", "value": "PieModelMapper"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Uncreatable base type"}], "className": "CppQPieModelMapper", "gadget": true, "lineNumber": 129, "qualifiedClassName": "CppQPieModelMapper"}, {"classInfos": [{"name": "QML.Foreign", "value": "QHPieModelMapper"}, {"name": "QML.Element", "value": "HPieModelMapper"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "CppQHPieModelMapper", "gadget": true, "lineNumber": 139, "qualifiedClassName": "CppQHPieModelMapper"}, {"classInfos": [{"name": "QML.Foreign", "value": "QVPieModelMapper"}, {"name": "QML.Element", "value": "VPieModelMapper"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "CppQVPieModelMapper", "gadget": true, "lineNumber": 148, "qualifiedClassName": "CppQVPieModelMapper"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QList<QPieSlice*>"}, {"name": "QML.Sequence", "value": "QPieSlice*"}], "className": "CppQListPieSlice", "gadget": true, "lineNumber": 157, "qualifiedClassName": "CppQListPieSlice"}, {"classInfos": [{"name": "QML.Foreign", "value": "QBoxPlotModelMapper"}, {"name": "QML.Element", "value": "BoxPlotModelMapper"}, {"name": "QML.AddedInVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Uncreatable base type"}], "className": "CppQBoxPlotModelMapper", "gadget": true, "lineNumber": 167, "qualifiedClassName": "CppQBoxPlotModelMapper"}, {"classInfos": [{"name": "QML.Foreign", "value": "QHBoxPlotModelMapper"}, {"name": "QML.Element", "value": "HBoxPlotModelMapper"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "CppQHBoxPlotModelMapper", "gadget": true, "lineNumber": 176, "qualifiedClassName": "CppQHBoxPlotModelMapper"}, {"classInfos": [{"name": "QML.Foreign", "value": "QVBoxPlotModelMapper"}, {"name": "QML.Element", "value": "VBoxPlotModelMapper"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "CppQVBoxPlotModelMapper", "gadget": true, "lineNumber": 184, "qualifiedClassName": "CppQVBoxPlotModelMapper"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QList<QBoxSet*>"}, {"name": "QML.Sequence", "value": "QBoxSet*"}], "className": "CppQListBoxSet", "gadget": true, "lineNumber": 192, "qualifiedClassName": "CppQListBoxSet"}, {"classInfos": [{"name": "QML.Foreign", "value": "QCandlestickModelMapper"}, {"name": "QML.Element", "value": "CandlestickModelMapper"}, {"name": "QML.AddedInVersion", "value": "514"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Uncreatable base type"}], "className": "CppQCandlestickModelMapper", "gadget": true, "lineNumber": 202, "qualifiedClassName": "CppQCandlestickModelMapper"}, {"classInfos": [{"name": "QML.Foreign", "value": "QHCandlestickModelMapper"}, {"name": "QML.Element", "value": "HCandlestickModelMapper"}, {"name": "QML.AddedInVersion", "value": "514"}], "className": "CppQHCandlestickModelMapper", "gadget": true, "lineNumber": 211, "qualifiedClassName": "CppQHCandlestickModelMapper"}, {"classInfos": [{"name": "QML.Foreign", "value": "QVCandlestickModelMapper"}, {"name": "QML.Element", "value": "VCandlestickModelMapper"}, {"name": "QML.AddedInVersion", "value": "514"}], "className": "CppQVCandlestickModelMapper", "gadget": true, "lineNumber": 219, "qualifiedClassName": "CppQVCandlestickModelMapper"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QList<QCandlestickSet*>"}, {"name": "QML.Sequence", "value": "QCandlestickSet*"}], "className": "CppQListCandlestickSet", "gadget": true, "lineNumber": 227, "qualifiedClassName": "CppQListCandlestickSet"}, {"classInfos": [{"name": "QML.Foreign", "value": "QDateTimeAxis"}, {"name": "QML.Element", "value": "DateTimeAxis"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "CppQDateTimeAxis", "gadget": true, "lineNumber": 237, "qualifiedClassName": "CppQDateTimeAxis"}, {"classInfos": [{"name": "QML.Foreign", "value": "QXYModelMapper"}, {"name": "QML.Element", "value": "XYModelMapper"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Uncreatable base type"}], "className": "CppQXYModelMapper", "gadget": true, "lineNumber": 247, "qualifiedClassName": "CppQXYModelMapper"}, {"classInfos": [{"name": "QML.Foreign", "value": "QHXYModelMapper"}, {"name": "QML.Element", "value": "HXYModelMapper"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "CppQHXYModelMapper", "gadget": true, "lineNumber": 257, "qualifiedClassName": "CppQHXYModelMapper"}, {"classInfos": [{"name": "QML.Foreign", "value": "QVXYModelMapper"}, {"name": "QML.Element", "value": "VXYModelMapper"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "CppQVXYModelMapper", "gadget": true, "lineNumber": 266, "qualifiedClassName": "CppQVXYModelMapper"}, {"classInfos": [{"name": "QML.Foreign", "value": "QAbstractAxis"}, {"name": "QML.Element", "value": "AbstractAxis"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Uncreatable base type"}], "className": "CppQAbstractAxis", "gadget": true, "lineNumber": 275, "qualifiedClassName": "CppQAbstractAxis"}, {"classInfos": [{"name": "QML.Foreign", "value": "QValueAxis"}, {"name": "QML.Element", "value": "ValueAxis"}, {"name": "QML.Element", "value": "ValuesAxis"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "CppQValueAxis", "gadget": true, "lineNumber": 285, "qualifiedClassName": "CppQValueAxis"}, {"classInfos": [{"name": "QML.Foreign", "value": "QLogValueAxis"}, {"name": "QML.Element", "value": "LogValueAxis"}, {"name": "QML.AddedInVersion", "value": "259"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "CppQLogValueAxis", "gadget": true, "lineNumber": 295, "qualifiedClassName": "CppQLogValueAxis"}, {"classInfos": [{"name": "QML.Foreign", "value": "QAbstractSeries"}, {"name": "QML.Element", "value": "AbstractSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Uncreatable base type"}], "className": "CppQAbstractSeries", "gadget": true, "lineNumber": 304, "qualifiedClassName": "CppQAbstractSeries"}, {"classInfos": [{"name": "QML.Foreign", "value": "QXYSeries"}, {"name": "QML.Element", "value": "XYSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Uncreatable base type"}], "className": "CppQXYSeries", "gadget": true, "lineNumber": 314, "qualifiedClassName": "CppQXYSeries"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QXYSeries::PointsConfigurationHash"}, {"name": "QML.Extended", "value": "CppQHashPointConfiguration"}], "className": "CppQHashPointConfiguration", "gadget": true, "lineNumber": 324, "qualifiedClassName": "CppQHashPointConfiguration"}, {"classInfos": [{"name": "QML.Foreign", "value": "QLegend"}, {"name": "QML.Element", "value": "Legend"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Uncreatable base type"}], "className": "CppQLegend", "gadget": true, "lineNumber": 332, "qualifiedClassName": "CppQLegend"}, {"classInfos": [{"name": "QML.Foreign", "value": "QGraphicsLayout"}, {"name": "QML.Using", "value": "void"}], "className": "CppQGraphicsLayout", "gadget": true, "lineNumber": 344, "qualifiedClassName": "CppQGraphicsLayout"}, {"classInfos": [{"name": "QML.Foreign", "value": "QPen"}, {"name": "QML.Extended", "value": "CppQPen"}, {"name": "QML.Element", "value": "anonymous"}], "className": "CppQPen", "gadget": true, "lineNumber": 351, "qualifiedClassName": "CppQPen"}, {"classInfos": [{"name": "QML.Foreign", "value": "QBrush"}, {"name": "QML.Extended", "value": "CppQBrush"}, {"name": "QML.Element", "value": "anonymous"}], "className": "CppQBrush", "gadget": true, "lineNumber": 359, "qualifiedClassName": "CppQBrush"}], "inputFile": "declarativeforeigntypes_p.h", "outputRevision": 69}, {"classes": [{"className": "DeclarativeOpenGLRenderNode", "lineNumber": 30, "object": true, "qualifiedClassName": "DeclarativeOpenGLRenderNode", "slots": [{"access": "public", "index": 0, "name": "render", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "DeclarativeAbstractRenderNode"}, {"access": "public", "name": "QOpenGLFunctions"}]}], "inputFile": "declarativeopenglrendernode_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "AreaSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativeAreaSeries", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "upperSeries", "read": "upperSeries", "required": false, "scriptable": true, "stored": true, "type": "DeclarativeLineSeries*", "user": false, "write": "setUpperSeries"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "lowerSeries", "read": "lowerSeries", "required": false, "scriptable": true, "stored": true, "type": "DeclarativeLineSeries*", "user": false, "write": "setLowerSeries"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "axisXTop", "notify": "axisXTopChanged", "read": "axisXTop", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisXTop"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "axisYRight", "notify": "axisYRightChanged", "read": "axisYRight", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisYRight"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "axisAngular", "notify": "axisAngularChanged", "read": "axisAngular", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisAngular"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "axisRadial", "notify": "axisRadialChanged", "read": "axisRadial", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisRadial"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "borderWidth", "notify": "borderWidthChanged", "read": "borderWidth", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setBorder<PERSON>idth"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "brushFilename", "notify": "brushFilenameChanged", "read": "brushFilename", "required": false, "revision": 260, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setBrushFilename"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "brush", "notify": "brushChanged", "read": "brush", "required": false, "revision": 260, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setBrush"}], "qualifiedClassName": "DeclarativeAreaSeries", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 0, "name": "axisXChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 1, "name": "axisYChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "width", "type": "qreal"}], "index": 2, "name": "borderWidthChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 3, "name": "axisXTopChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 4, "name": "axisYRightChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 5, "name": "axisAngularChanged", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 6, "name": "axisRadialChanged", "returnType": "void", "revision": 259}, {"access": "public", "index": 7, "name": "brushChanged", "returnType": "void", "revision": 260}, {"access": "public", "arguments": [{"name": "brushFilename", "type": "QString"}], "index": 8, "name": "brushFilenameChanged", "returnType": "void", "revision": 260}], "slots": [{"access": "private", "index": 9, "name": "handleBrushChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAreaSeries"}]}], "inputFile": "declarativeareaseries_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "DeclarativeAxes"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Abstract base type"}], "className": "DeclarativeAxes", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisXTop", "notify": "axisXTopChanged", "read": "axisXTop", "required": false, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisXTop"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "axisYRight", "notify": "axisYRightChanged", "read": "axisYRight", "required": false, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisYRight"}], "qualifiedClassName": "DeclarativeAxes", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 0, "name": "axisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 1, "name": "axisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 2, "name": "axisXTopChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 3, "name": "axisYRightChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "declarativeaxes_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "BarSet"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativeBarSet", "lineNumber": 34, "methods": [{"access": "public", "arguments": [{"name": "value", "type": "qreal"}], "index": 5, "name": "append", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}], "index": 6, "name": "remove", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 7, "isCloned": true, "name": "remove", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "value", "type": "qreal"}], "index": 8, "name": "replace", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 9, "name": "at", "returnType": "qreal"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "values", "read": "values", "required": false, "scriptable": true, "stored": true, "type": "QVariantList", "user": false, "write": "set<PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "borderWidth", "notify": "borderWidthChanged", "read": "borderWidth", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setBorder<PERSON>idth"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "count", "notify": "countChanged", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "brushFilename", "notify": "brushFilenameChanged", "read": "brushFilename", "required": false, "revision": 260, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setBrushFilename"}], "qualifiedClassName": "DeclarativeBarSet", "signals": [{"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 0, "name": "countChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "width", "type": "qreal"}], "index": 1, "name": "borderWidthChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "brushFilename", "type": "QString"}], "index": 2, "name": "brushFilenameChanged", "returnType": "void", "revision": 260}], "slots": [{"access": "private", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}], "index": 3, "name": "handleCountChanged", "returnType": "void"}, {"access": "private", "index": 4, "name": "handleBrushChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QBarSet"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "seriesChildren"}, {"name": "QML.Element", "value": "BarSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativeBarSeries", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 74, "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 4, "name": "at", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "label", "type": "QString"}, {"name": "values", "type": "QVariantList"}], "index": 5, "name": "append", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "label", "type": "QString"}, {"name": "values", "type": "QVariantList"}], "index": 6, "name": "insert", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "barset", "type": "QBarSet*"}], "index": 7, "name": "remove", "returnType": "bool"}, {"access": "public", "index": 8, "name": "clear", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisXTop", "notify": "axisXTopChanged", "read": "axisXTop", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisXTop"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "axisYRight", "notify": "axisYRightChanged", "read": "axisYRight", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisYRight"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "seriesChildren", "read": "seriesChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}], "qualifiedClassName": "DeclarativeBarSeries", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 0, "name": "axisXChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 1, "name": "axisYChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 2, "name": "axisXTopChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 3, "name": "axisYRightChanged", "returnType": "void", "revision": 258}], "superClasses": [{"access": "public", "name": "QBarSeries"}, {"access": "public", "name": "QQmlParserStatus"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "seriesChildren"}, {"name": "QML.Element", "value": "StackedBarSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativeStackedBarSeries", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 125, "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 4, "name": "at", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "label", "type": "QString"}, {"name": "values", "type": "QVariantList"}], "index": 5, "name": "append", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "label", "type": "QString"}, {"name": "values", "type": "QVariantList"}], "index": 6, "name": "insert", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "barset", "type": "QBarSet*"}], "index": 7, "name": "remove", "returnType": "bool"}, {"access": "public", "index": 8, "name": "clear", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisXTop", "notify": "axisXTopChanged", "read": "axisXTop", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisXTop"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "axisYRight", "notify": "axisYRightChanged", "read": "axisYRight", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisYRight"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "seriesChildren", "read": "seriesChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}], "qualifiedClassName": "DeclarativeStackedBarSeries", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 0, "name": "axisXChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 1, "name": "axisYChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 2, "name": "axisXTopChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 3, "name": "axisYRightChanged", "returnType": "void", "revision": 258}], "superClasses": [{"access": "public", "name": "QStackedBarSeries"}, {"access": "public", "name": "QQmlParserStatus"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "seriesChildren"}, {"name": "QML.Element", "value": "PercentBarSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativePercentBarSeries", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 175, "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 4, "name": "at", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "label", "type": "QString"}, {"name": "values", "type": "QVariantList"}], "index": 5, "name": "append", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "label", "type": "QString"}, {"name": "values", "type": "QVariantList"}], "index": 6, "name": "insert", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "barset", "type": "QBarSet*"}], "index": 7, "name": "remove", "returnType": "bool"}, {"access": "public", "index": 8, "name": "clear", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisXTop", "notify": "axisXTopChanged", "read": "axisXTop", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisXTop"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "axisYRight", "notify": "axisYRightChanged", "read": "axisYRight", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisYRight"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "seriesChildren", "read": "seriesChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}], "qualifiedClassName": "DeclarativePercentBarSeries", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 0, "name": "axisXChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 1, "name": "axisYChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 2, "name": "axisXTopChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 3, "name": "axisYRightChanged", "returnType": "void", "revision": 258}], "superClasses": [{"access": "public", "name": "QPercentBarSeries"}, {"access": "public", "name": "QQmlParserStatus"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "seriesChildren"}, {"name": "QML.Element", "value": "HorizontalBarSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativeHorizontalBarSeries", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 225, "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 4, "name": "at", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "label", "type": "QString"}, {"name": "values", "type": "QVariantList"}], "index": 5, "name": "append", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "label", "type": "QString"}, {"name": "values", "type": "QVariantList"}], "index": 6, "name": "insert", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "barset", "type": "QBarSet*"}], "index": 7, "name": "remove", "returnType": "bool"}, {"access": "public", "index": 8, "name": "clear", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisXTop", "notify": "axisXTopChanged", "read": "axisXTop", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisXTop"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "axisYRight", "notify": "axisYRightChanged", "read": "axisYRight", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisYRight"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "seriesChildren", "read": "seriesChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}], "qualifiedClassName": "DeclarativeHorizontalBarSeries", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 0, "name": "axisXChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 1, "name": "axisYChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 2, "name": "axisXTopChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 3, "name": "axisYRightChanged", "returnType": "void", "revision": 258}], "superClasses": [{"access": "public", "name": "QHorizontalBarSeries"}, {"access": "public", "name": "QQmlParserStatus"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "seriesChildren"}, {"name": "QML.Element", "value": "HorizontalStackedBarSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativeHorizontalStackedBarSeries", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 275, "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 4, "name": "at", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "label", "type": "QString"}, {"name": "values", "type": "QVariantList"}], "index": 5, "name": "append", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "label", "type": "QString"}, {"name": "values", "type": "QVariantList"}], "index": 6, "name": "insert", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "barset", "type": "QBarSet*"}], "index": 7, "name": "remove", "returnType": "bool"}, {"access": "public", "index": 8, "name": "clear", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisXTop", "notify": "axisXTopChanged", "read": "axisXTop", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisXTop"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "axisYRight", "notify": "axisYRightChanged", "read": "axisYRight", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisYRight"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "seriesChildren", "read": "seriesChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}], "qualifiedClassName": "DeclarativeHorizontalStackedBarSeries", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 0, "name": "axisXChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 1, "name": "axisYChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 2, "name": "axisXTopChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 3, "name": "axisYRightChanged", "returnType": "void", "revision": 258}], "superClasses": [{"access": "public", "name": "QHorizontalStackedBarSeries"}, {"access": "public", "name": "QQmlParserStatus"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "seriesChildren"}, {"name": "QML.Element", "value": "HorizontalPercentBarSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativeHorizontalPercentBarSeries", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 325, "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 4, "name": "at", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "label", "type": "QString"}, {"name": "values", "type": "QVariantList"}], "index": 5, "name": "append", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "label", "type": "QString"}, {"name": "values", "type": "QVariantList"}], "index": 6, "name": "insert", "returnType": "DeclarativeBarSet*"}, {"access": "public", "arguments": [{"name": "barset", "type": "QBarSet*"}], "index": 7, "name": "remove", "returnType": "bool"}, {"access": "public", "index": 8, "name": "clear", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisXTop", "notify": "axisXTopChanged", "read": "axisXTop", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisXTop"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "axisYRight", "notify": "axisYRightChanged", "read": "axisYRight", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisYRight"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "seriesChildren", "read": "seriesChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}], "qualifiedClassName": "DeclarativeHorizontalPercentBarSeries", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 0, "name": "axisXChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 1, "name": "axisYChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 2, "name": "axisXTopChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 3, "name": "axisYRightChanged", "returnType": "void", "revision": 258}], "superClasses": [{"access": "public", "name": "QHorizontalPercentBarSeries"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "declarativebarseries_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "BoxSet"}, {"name": "QML.AddedInVersion", "value": "259"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativeBoxSet", "enums": [{"isClass": false, "isFlag": false, "name": "ValuePositions", "values": ["LowerExtreme", "LowerQuartile", "Median", "UpperQuartile", "UpperExtreme"]}], "lineNumber": 27, "methods": [{"access": "public", "arguments": [{"name": "value", "type": "qreal"}], "index": 4, "name": "append", "returnType": "void"}, {"access": "public", "index": 5, "name": "clear", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 6, "name": "at", "returnType": "qreal"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "value", "type": "qreal"}], "index": 7, "name": "setValue", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "values", "read": "values", "required": false, "scriptable": true, "stored": true, "type": "QVariantList", "user": false, "write": "set<PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "label", "read": "label", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "count", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "brushFilename", "notify": "brushFilenameChanged", "read": "brushFilename", "required": false, "revision": 260, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setBrushFilename"}], "qualifiedClassName": "DeclarativeBoxSet", "signals": [{"access": "public", "index": 0, "name": "changedValues", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 1, "name": "changedValue", "returnType": "void"}, {"access": "public", "arguments": [{"name": "brushFilename", "type": "QString"}], "index": 2, "name": "brushFilenameChanged", "returnType": "void", "revision": 260}], "slots": [{"access": "private", "index": 3, "name": "handleBrushChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QBoxSet"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "seriesChildren"}, {"name": "QML.Element", "value": "BoxPlotSeries"}, {"name": "QML.AddedInVersion", "value": "259"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativeBoxPlotSeries", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 74, "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 16, "name": "at", "returnType": "DeclarativeBoxSet*"}, {"access": "public", "arguments": [{"name": "label", "type": "QString"}, {"name": "values", "type": "QVariantList"}], "index": 17, "name": "append", "returnType": "DeclarativeBoxSet*"}, {"access": "public", "arguments": [{"name": "box", "type": "DeclarativeBoxSet*"}], "index": 18, "name": "append", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "label", "type": "QString"}, {"name": "values", "type": "QVariantList"}], "index": 19, "name": "insert", "returnType": "DeclarativeBoxSet*"}, {"access": "public", "arguments": [{"name": "box", "type": "DeclarativeBoxSet*"}], "index": 20, "name": "remove", "returnType": "bool"}, {"access": "public", "index": 21, "name": "clear", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisXTop", "notify": "axisXTopChanged", "read": "axisXTop", "required": false, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisXTop"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "axisYRight", "notify": "axisYRightChanged", "read": "axisYRight", "required": false, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisYRight"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "seriesChildren", "read": "seriesChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "brushFilename", "notify": "brushFilenameChanged", "read": "brushFilename", "required": false, "revision": 260, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setBrushFilename"}], "qualifiedClassName": "DeclarativeBoxPlotSeries", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 0, "name": "axisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 1, "name": "axisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 2, "name": "axisXTopChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 3, "name": "axisYRightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "boxset", "type": "DeclarativeBoxSet*"}], "index": 4, "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "bool"}, {"name": "boxset", "type": "DeclarativeBoxSet*"}], "index": 5, "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "boxset", "type": "DeclarativeBoxSet*"}], "index": 6, "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "boxset", "type": "DeclarativeBoxSet*"}], "index": 7, "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "boxset", "type": "DeclarativeBoxSet*"}], "index": 8, "name": "doubleClicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "brushFilename", "type": "QString"}], "index": 9, "name": "brushFilenameChanged", "returnType": "void", "revision": 260}], "slots": [{"access": "public", "arguments": [{"name": "status", "type": "bool"}, {"name": "boxset", "type": "QBoxSet*"}], "index": 10, "name": "onHovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "boxset", "type": "QBoxSet*"}], "index": 11, "name": "onClicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "boxset", "type": "QBoxSet*"}], "index": 12, "name": "onPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "boxset", "type": "QBoxSet*"}], "index": 13, "name": "onReleased", "returnType": "void"}, {"access": "public", "arguments": [{"name": "boxset", "type": "QBoxSet*"}], "index": 14, "name": "onDoubleClicked", "returnType": "void"}, {"access": "private", "index": 15, "name": "handleBrushChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QBoxPlotSeries"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "declarativeboxplotseries_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "CandlestickSet"}, {"name": "QML.AddedInVersion", "value": "514"}], "className": "DeclarativeCandlestickSet", "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "brushFilename", "notify": "brushFilenameChanged", "read": "brushFilename", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setBrushFilename"}], "qualifiedClassName": "DeclarativeCandlestickSet", "signals": [{"access": "public", "arguments": [{"name": "brushFilename", "type": "QString"}], "index": 0, "name": "brushFilenameChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 1, "name": "handleBrushChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QCandlestickSet"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "seriesChildren"}, {"name": "QML.Element", "value": "CandlestickSeries"}, {"name": "QML.AddedInVersion", "value": "514"}], "className": "DeclarativeCandlestickSeries", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 51, "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 16, "name": "at", "returnType": "DeclarativeCandlestickSet*"}, {"access": "public", "arguments": [{"name": "set", "type": "DeclarativeCandlestickSet*"}], "index": 17, "name": "append", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "set", "type": "DeclarativeCandlestickSet*"}], "index": 18, "name": "remove", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "open", "type": "qreal"}, {"name": "high", "type": "qreal"}, {"name": "low", "type": "qreal"}, {"name": "close", "type": "qreal"}, {"name": "timestamp", "type": "qreal"}], "index": 19, "name": "append", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "timestamp", "type": "qreal"}], "index": 20, "name": "remove", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "set", "type": "DeclarativeCandlestickSet*"}], "index": 21, "name": "insert", "returnType": "bool"}, {"access": "public", "index": 22, "name": "clear", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisXTop", "notify": "axisXTopChanged", "read": "axisXTop", "required": false, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisXTop"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "axisYRight", "notify": "axisYRightChanged", "read": "axisYRight", "required": false, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisYRight"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "seriesChildren", "read": "seriesChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "brushFilename", "notify": "brushFilenameChanged", "read": "brushFilename", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setBrushFilename"}], "qualifiedClassName": "DeclarativeCandlestickSeries", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 0, "name": "axisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 1, "name": "axisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 2, "name": "axisXTopChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 3, "name": "axisYRightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "set", "type": "DeclarativeCandlestickSet*"}], "index": 4, "name": "clicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "bool"}, {"name": "set", "type": "DeclarativeCandlestickSet*"}], "index": 5, "name": "hovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "set", "type": "DeclarativeCandlestickSet*"}], "index": 6, "name": "pressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "set", "type": "DeclarativeCandlestickSet*"}], "index": 7, "name": "released", "returnType": "void"}, {"access": "public", "arguments": [{"name": "set", "type": "DeclarativeCandlestickSet*"}], "index": 8, "name": "doubleClicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "brushFilename", "type": "QString"}], "index": 9, "name": "brushFilenameChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "set", "type": "QCandlestickSet*"}], "index": 10, "name": "onClicked", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "bool"}, {"name": "set", "type": "QCandlestickSet*"}], "index": 11, "name": "onHovered", "returnType": "void"}, {"access": "public", "arguments": [{"name": "set", "type": "QCandlestickSet*"}], "index": 12, "name": "onPressed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "set", "type": "QCandlestickSet*"}], "index": 13, "name": "onReleased", "returnType": "void"}, {"access": "public", "arguments": [{"name": "set", "type": "QCandlestickSet*"}], "index": 14, "name": "onDoubleClicked", "returnType": "void"}, {"access": "private", "index": 15, "name": "handleBrushChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QCandlestickSeries"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "declarativecandlestickseries_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "CategoryRange"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativeCategoryRange", "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "endValue", "read": "endValue", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setEndValue"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "label", "read": "label", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "DeclarativeCategoryRange", "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "axisChildren"}, {"name": "QML.Element", "value": "CategoryAxis"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativeCategoryAxis", "enums": [{"isClass": false, "isFlag": false, "name": "AxisLabelsPosition", "values": ["AxisLabelsPositionCenter", "AxisLabelsPositionOnValue"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 46, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisChildren", "read": "axisChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "labelsPosition", "notify": "labelsPositionChanged", "read": "labelsPosition", "required": false, "revision": 513, "scriptable": true, "stored": true, "type": "AxisLabelsPosition", "user": false, "write": "setLabelsPosition"}], "qualifiedClassName": "DeclarativeCategoryAxis", "signals": [{"access": "public", "arguments": [{"name": "position", "type": "AxisLabelsPosition"}], "index": 0, "name": "labelsPositionChanged", "returnType": "void", "revision": 513}], "slots": [{"access": "public", "arguments": [{"name": "label", "type": "QString"}, {"name": "categoryEndValue", "type": "qreal"}], "index": 1, "name": "append", "returnType": "void"}, {"access": "public", "arguments": [{"name": "label", "type": "QString"}], "index": 2, "name": "remove", "returnType": "void"}, {"access": "public", "arguments": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "QString"}, {"name": "new<PERSON>abel", "type": "QString"}], "index": 3, "name": "replace", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QCategoryAxis"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "declarativecategoryaxis_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ChartView"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "Declarative<PERSON>hart", "enums": [{"isClass": false, "isFlag": false, "name": "Theme", "values": ["ChartThemeLight", "ChartThemeBlueCerulean", "ChartThemeDark", "ChartThemeBrownSand", "ChartThemeBlueNcs", "ChartThemeHighContrast", "ChartThemeBlueIcy", "ChartThemeQt"]}, {"isClass": false, "isFlag": false, "name": "Animation", "values": ["NoAnimation", "GridAxisAnimations", "SeriesAnimations", "AllAnimations"]}, {"isClass": false, "isFlag": false, "name": "SeriesType", "values": ["SeriesTypeLine", "SeriesTypeArea", "SeriesTypeBar", "SeriesTypeStackedBar", "SeriesTypePercentBar", "SeriesTypePie", "SeriesTypeScatter", "SeriesTypeSpline", "SeriesTypeHorizontalBar", "SeriesTypeHorizontalStackedBar", "SeriesTypeHorizontalPercentBar", "SeriesTypeBoxPlot", "SeriesTypeCandlestick"]}], "lineNumber": 38, "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 26, "name": "series", "returnType": "QAbstractSeries*"}, {"access": "public", "arguments": [{"name": "seriesName", "type": "QString"}], "index": 27, "name": "series", "returnType": "QAbstractSeries*"}, {"access": "public", "arguments": [{"name": "type", "type": "int"}, {"name": "name", "type": "QString"}, {"name": "axisX", "type": "QAbstractAxis*"}, {"name": "axisY", "type": "QAbstractAxis*"}], "index": 28, "name": "createSeries", "returnType": "QAbstractSeries*"}, {"access": "public", "arguments": [{"name": "type", "type": "int"}, {"name": "name", "type": "QString"}, {"name": "axisX", "type": "QAbstractAxis*"}], "index": 29, "isCloned": true, "name": "createSeries", "returnType": "QAbstractSeries*"}, {"access": "public", "arguments": [{"name": "type", "type": "int"}, {"name": "name", "type": "QString"}], "index": 30, "isCloned": true, "name": "createSeries", "returnType": "QAbstractSeries*"}, {"access": "public", "arguments": [{"name": "type", "type": "int"}], "index": 31, "isCloned": true, "name": "createSeries", "returnType": "QAbstractSeries*"}, {"access": "public", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "index": 32, "name": "removeSeries", "returnType": "void"}, {"access": "public", "index": 33, "name": "removeAllSeries", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}, {"name": "series", "type": "QAbstractSeries*"}], "index": 34, "name": "setAxisX", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 35, "isCloned": true, "name": "setAxisX", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}, {"name": "series", "type": "QAbstractSeries*"}], "index": 36, "name": "setAxisY", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 37, "isCloned": true, "name": "setAxisY", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "index": 38, "name": "axisX", "returnType": "QAbstractAxis*"}, {"access": "public", "index": 39, "isCloned": true, "name": "axisX", "returnType": "QAbstractAxis*"}, {"access": "public", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "index": 40, "name": "axisY", "returnType": "QAbstractAxis*"}, {"access": "public", "index": 41, "isCloned": true, "name": "axisY", "returnType": "QAbstractAxis*"}, {"access": "public", "arguments": [{"name": "factor", "type": "qreal"}], "index": 42, "name": "zoom", "returnType": "void"}, {"access": "public", "index": 43, "name": "zoomIn", "returnType": "void", "revision": 513}, {"access": "public", "arguments": [{"name": "rectangle", "type": "QRectF"}], "index": 44, "name": "zoomIn", "returnType": "void", "revision": 513}, {"access": "public", "index": 45, "name": "zoomOut", "returnType": "void", "revision": 513}, {"access": "public", "index": 46, "name": "zoomReset", "returnType": "void", "revision": 513}, {"access": "public", "index": 47, "name": "isZoomed", "returnType": "bool", "revision": 513}, {"access": "public", "arguments": [{"name": "pixels", "type": "qreal"}], "index": 48, "name": "scrollLeft", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pixels", "type": "qreal"}], "index": 49, "name": "scrollRight", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pixels", "type": "qreal"}], "index": 50, "name": "scrollUp", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pixels", "type": "qreal"}], "index": 51, "name": "scrollDown", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QPointF"}, {"name": "series", "type": "QAbstractSeries*"}], "index": 52, "name": "mapToValue", "returnType": "QPointF", "revision": 513}, {"access": "public", "arguments": [{"name": "position", "type": "QPointF"}], "index": 53, "isCloned": true, "name": "mapToValue", "returnType": "QPointF", "revision": 513}, {"access": "public", "arguments": [{"name": "value", "type": "QPointF"}, {"name": "series", "type": "QAbstractSeries*"}], "index": 54, "name": "mapToPosition", "returnType": "QPointF", "revision": 513}, {"access": "public", "arguments": [{"name": "value", "type": "QPointF"}], "index": 55, "isCloned": true, "name": "mapToPosition", "returnType": "QPointF", "revision": 513}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "theme", "read": "theme", "required": false, "scriptable": true, "stored": true, "type": "Theme", "user": false, "write": "setTheme"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "animationOptions", "read": "animationOptions", "required": false, "scriptable": true, "stored": true, "type": "Animation", "user": false, "write": "setAnimationOptions"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "animationDuration", "notify": "animationDurationChanged", "read": "animationDuration", "required": false, "revision": 513, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setAnimationDuration"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "animationEasingCurve", "notify": "animationEasingCurveChanged", "read": "animationEasingCurve", "required": false, "revision": 513, "scriptable": true, "stored": true, "type": "QEasingCurve", "user": false, "write": "setAnimationEasingCurve"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "title", "read": "title", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTitle"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "titleFont", "read": "titleFont", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setTitleFont"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "titleColor", "notify": "titleColorChanged", "read": "titleColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setTitleColor"}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "legend", "read": "legend", "required": false, "scriptable": true, "stored": true, "type": "QLegend*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "count", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "backgroundColor", "notify": "backgroundColorChanged", "read": "backgroundColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setBackgroundColor"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "dropShadowEnabled", "notify": "dropShadowEnabledChanged", "read": "dropShadowEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setDropShadowEnabled"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "backgroundRoundness", "notify": "backgroundRoundnessChanged", "read": "backgroundRoundness", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setBackgroundRoundness"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "margins", "notify": "marginsChanged", "read": "margins", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "DeclarativeMargins*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "<PERSON><PERSON><PERSON>", "notify": "plotAreaChanged", "read": "<PERSON><PERSON><PERSON>", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QRectF", "user": false, "write": "setPlotArea"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "plotAreaColor", "notify": "plotAreaColorChanged", "read": "plotAreaColor", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setPlotAreaColor"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "axes", "read": "axes", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QQmlListProperty<QAbstractAxis>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "localizeNumbers", "notify": "localizeNumbersChanged", "read": "localizeNumbers", "required": false, "revision": 512, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLocalizeNumbers"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "locale", "notify": "localeChanged", "read": "locale", "required": false, "revision": 512, "scriptable": true, "stored": true, "type": "QLocale", "user": false, "write": "setLocale"}], "qualifiedClassName": "Declarative<PERSON>hart", "signals": [{"access": "public", "index": 0, "name": "axisLabelsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 1, "name": "titleColorChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "backgroundColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 3, "name": "dropShadowEnabledChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "marginsChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "<PERSON><PERSON><PERSON>", "type": "QRectF"}], "index": 5, "name": "plotAreaChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "index": 6, "name": "seriesAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "index": 7, "name": "seriesRemoved", "returnType": "void"}, {"access": "public", "index": 8, "name": "plotAreaColorChanged", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "diameter", "type": "qreal"}], "index": 9, "name": "backgroundRoundnessChanged", "returnType": "void", "revision": 259}, {"access": "public", "index": 10, "name": "localizeNumbersChanged", "returnType": "void", "revision": 512}, {"access": "public", "index": 11, "name": "localeChanged", "returnType": "void", "revision": 512}, {"access": "public", "arguments": [{"name": "msecs", "type": "int"}], "index": 12, "name": "animationDurationChanged", "returnType": "void", "revision": 513}, {"access": "public", "arguments": [{"name": "curve", "type": "QEasingCurve"}], "index": 13, "name": "animationEasingCurveChanged", "returnType": "void", "revision": 513}, {"access": "public", "index": 14, "name": "needRender", "returnType": "void"}, {"access": "public", "index": 15, "name": "pendingRenderNodeMouseEventResponses", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "enable", "type": "bool"}], "index": 16, "name": "handleAntialiasingChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "region", "type": "QList<QRectF>"}], "index": 17, "name": "sceneChanged", "returnType": "void"}, {"access": "private", "index": 18, "name": "renderScene", "returnType": "void"}, {"access": "private", "arguments": [{"name": "top", "type": "int"}, {"name": "bottom", "type": "int"}, {"name": "left", "type": "int"}, {"name": "right", "type": "int"}], "index": 19, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "private", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 20, "name": "handleAxisXSet", "returnType": "void"}, {"access": "private", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 21, "name": "handleAxisYSet", "returnType": "void"}, {"access": "private", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 22, "name": "handleAxisXTopSet", "returnType": "void"}, {"access": "private", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 23, "name": "handleAxisYRightSet", "returnType": "void"}, {"access": "private", "arguments": [{"name": "series", "type": "QAbstractSeries*"}], "index": 24, "name": "handleSeriesAdded", "returnType": "void"}, {"access": "private", "index": 25, "name": "handlePendingRenderNodeMouseEventResponses", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "declarativechart_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "declarative<PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.Element", "value": "LineSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativeLineSeries", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 27, "methods": [{"access": "public", "arguments": [{"name": "x", "type": "qreal"}, {"name": "y", "type": "qreal"}], "index": 11, "name": "append", "returnType": "void"}, {"access": "public", "arguments": [{"name": "oldX", "type": "qreal"}, {"name": "oldY", "type": "qreal"}, {"name": "newX", "type": "qreal"}, {"name": "newY", "type": "qreal"}], "index": 12, "name": "replace", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "newX", "type": "qreal"}, {"name": "newY", "type": "qreal"}], "index": 13, "name": "replace", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "x", "type": "qreal"}, {"name": "y", "type": "qreal"}], "index": 14, "name": "remove", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 15, "name": "remove", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}], "index": 16, "name": "removePoints", "returnType": "void", "revision": 513}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "x", "type": "qreal"}, {"name": "y", "type": "qreal"}], "index": 17, "name": "insert", "returnType": "void"}, {"access": "public", "index": 18, "name": "clear", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 19, "name": "at", "returnType": "QPointF"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "count", "notify": "countChanged", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "axisXTop", "notify": "axisXTopChanged", "read": "axisXTop", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisXTop"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "axisYRight", "notify": "axisYRightChanged", "read": "axisYRight", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisYRight"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "axisAngular", "notify": "axisAngularChanged", "read": "axisAngular", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisAngular"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "axisRadial", "notify": "axisRadialChanged", "read": "axisRadial", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisRadial"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "style", "notify": "styleChanged", "read": "style", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "Qt::PenStyle", "user": false, "write": "setStyle"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "capStyle", "notify": "capStyleChanged", "read": "capStyle", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "Qt::PenCapStyle", "user": false, "write": "setCapStyle"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "declarative<PERSON><PERSON><PERSON><PERSON>", "read": "declarative<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}], "qualifiedClassName": "DeclarativeLineSeries", "signals": [{"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 0, "name": "countChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 1, "name": "axisXChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 2, "name": "axisYChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 3, "name": "axisXTopChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 4, "name": "axisYRightChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 5, "name": "axisAngularChanged", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 6, "name": "axisRadialChanged", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "width", "type": "qreal"}], "index": 7, "name": "widthChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "style", "type": "Qt::PenStyle"}], "index": 8, "name": "styleChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "capStyle", "type": "Qt::PenCapStyle"}], "index": 9, "name": "capStyleChanged", "returnType": "void", "revision": 257}], "slots": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 10, "name": "handleCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QLineSeries"}, {"access": "public", "name": "DeclarativeXySeries"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "declarativelineseries_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "<PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "257"}, {"name": "QML.ExtraVersion", "value": "512"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Abstract base type"}], "className": "Declarative<PERSON><PERSON><PERSON>", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "top", "notify": "topChanged", "read": "top", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTop"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "bottom", "notify": "bottomChanged", "read": "bottom", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBottom"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "left", "notify": "leftChanged", "read": "left", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLeft"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "right", "notify": "rightChanged", "read": "right", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRight"}], "qualifiedClassName": "Declarative<PERSON><PERSON><PERSON>", "signals": [{"access": "public", "arguments": [{"name": "top", "type": "int"}, {"name": "bottom", "type": "int"}, {"name": "left", "type": "int"}, {"name": "right", "type": "int"}], "index": 0, "name": "topChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "top", "type": "int"}, {"name": "bottom", "type": "int"}, {"name": "left", "type": "int"}, {"name": "right", "type": "int"}], "index": 1, "name": "bottomChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "top", "type": "int"}, {"name": "bottom", "type": "int"}, {"name": "left", "type": "int"}, {"name": "right", "type": "int"}], "index": 2, "name": "leftChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "top", "type": "int"}, {"name": "bottom", "type": "int"}, {"name": "left", "type": "int"}, {"name": "right", "type": "int"}], "index": 3, "name": "rightChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "<PERSON><PERSON><PERSON><PERSON>"}]}], "inputFile": "declarativemargins_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "PieSlice"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativePieSlice", "lineNumber": 26, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "brushFilename", "notify": "brushFilenameChanged", "read": "brushFilename", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setBrushFilename"}], "qualifiedClassName": "DeclarativePieSlice", "signals": [{"access": "public", "arguments": [{"name": "brushFilename", "type": "QString"}], "index": 0, "name": "brushFilenameChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 1, "name": "handleBrushChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPieSlice"}]}, {"classInfos": [{"name": "DefaultProperty", "value": "seriesChildren"}, {"name": "QML.Element", "value": "PieSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativePieSeries", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 50, "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 4, "name": "at", "returnType": "QPieSlice*"}, {"access": "public", "arguments": [{"name": "label", "type": "QString"}], "index": 5, "name": "find", "returnType": "QPieSlice*"}, {"access": "public", "arguments": [{"name": "label", "type": "QString"}, {"name": "value", "type": "qreal"}], "index": 6, "name": "append", "returnType": "DeclarativePieSlice*"}, {"access": "public", "arguments": [{"name": "slice", "type": "QPieSlice*"}], "index": 7, "name": "remove", "returnType": "bool"}, {"access": "public", "index": 8, "name": "clear", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "seriesChildren", "read": "seriesChildren", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}], "qualifiedClassName": "DeclarativePieSeries", "signals": [{"access": "public", "arguments": [{"name": "slice", "type": "QPieSlice*"}], "index": 0, "name": "sliceAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slice", "type": "QPieSlice*"}], "index": 1, "name": "sliceRemoved", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "slices", "type": "QList<QPieSlice*>"}], "index": 2, "name": "handleAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "slices", "type": "QList<QPieSlice*>"}], "index": 3, "name": "handleRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPieSeries"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "declarativepieseries_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "PolarChartView"}, {"name": "QML.AddedInVersion", "value": "259"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativePolarChart", "lineNumber": 24, "object": true, "qualifiedClassName": "DeclarativePolarChart", "superClasses": [{"access": "public", "name": "Declarative<PERSON>hart"}]}], "inputFile": "declarativepolarchart_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "declarative<PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.Element", "value": "ScatterSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativeScatterSeries", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 27, "methods": [{"access": "public", "arguments": [{"name": "x", "type": "qreal"}, {"name": "y", "type": "qreal"}], "index": 12, "name": "append", "returnType": "void"}, {"access": "public", "arguments": [{"name": "oldX", "type": "qreal"}, {"name": "oldY", "type": "qreal"}, {"name": "newX", "type": "qreal"}, {"name": "newY", "type": "qreal"}], "index": 13, "name": "replace", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "newX", "type": "qreal"}, {"name": "newY", "type": "qreal"}], "index": 14, "name": "replace", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "x", "type": "qreal"}, {"name": "y", "type": "qreal"}], "index": 15, "name": "remove", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 16, "name": "remove", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}], "index": 17, "name": "removePoints", "returnType": "void", "revision": 513}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "x", "type": "qreal"}, {"name": "y", "type": "qreal"}], "index": 18, "name": "insert", "returnType": "void"}, {"access": "public", "index": 19, "name": "clear", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 20, "name": "at", "returnType": "QPointF"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "count", "notify": "countChanged", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "axisXTop", "notify": "axisXTopChanged", "read": "axisXTop", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisXTop"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "axisYRight", "notify": "axisYRightChanged", "read": "axisYRight", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisYRight"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "axisAngular", "notify": "axisAngularChanged", "read": "axisAngular", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisAngular"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "axisRadial", "notify": "axisRadialChanged", "read": "axisRadial", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisRadial"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "borderWidth", "notify": "borderWidthChanged", "read": "borderWidth", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setBorder<PERSON>idth"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "declarative<PERSON><PERSON><PERSON><PERSON>", "read": "declarative<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "brushFilename", "notify": "brushFilenameChanged", "read": "brushFilename", "required": false, "revision": 260, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setBrushFilename"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "brush", "notify": "brushChanged", "read": "brush", "required": false, "revision": 260, "scriptable": true, "stored": true, "type": "QBrush", "user": false, "write": "setBrush"}], "qualifiedClassName": "DeclarativeScatterSeries", "signals": [{"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 0, "name": "countChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 1, "name": "axisXChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 2, "name": "axisYChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "width", "type": "qreal"}], "index": 3, "name": "borderWidthChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 4, "name": "axisXTopChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 5, "name": "axisYRightChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 6, "name": "axisAngularChanged", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 7, "name": "axisRadialChanged", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "brushFilename", "type": "QString"}], "index": 8, "name": "brushFilenameChanged", "returnType": "void", "revision": 260}, {"access": "public", "index": 9, "name": "brushChanged", "returnType": "void", "revision": 260}], "slots": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 10, "name": "handleCountChanged", "returnType": "void"}, {"access": "private", "index": 11, "name": "handleBrushChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QScatterSeries"}, {"access": "public", "name": "DeclarativeXySeries"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "declarativescatterseries_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "declarative<PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.Element", "value": "SplineSeries"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativeSplineSeries", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 27, "methods": [{"access": "public", "arguments": [{"name": "x", "type": "qreal"}, {"name": "y", "type": "qreal"}], "index": 11, "name": "append", "returnType": "void"}, {"access": "public", "arguments": [{"name": "oldX", "type": "qreal"}, {"name": "oldY", "type": "qreal"}, {"name": "newX", "type": "qreal"}, {"name": "newY", "type": "qreal"}], "index": 12, "name": "replace", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "newX", "type": "qreal"}, {"name": "newY", "type": "qreal"}], "index": 13, "name": "replace", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "x", "type": "qreal"}, {"name": "y", "type": "qreal"}], "index": 14, "name": "remove", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 15, "name": "remove", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "count", "type": "int"}], "index": 16, "name": "removePoints", "returnType": "void", "revision": 513}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "x", "type": "qreal"}, {"name": "y", "type": "qreal"}], "index": 17, "name": "insert", "returnType": "void"}, {"access": "public", "index": 18, "name": "clear", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 19, "name": "at", "returnType": "QPointF"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "count", "notify": "countChanged", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "axisXTop", "notify": "axisXTopChanged", "read": "axisXTop", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisXTop"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "axisYRight", "notify": "axisYRightChanged", "read": "axisYRight", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisYRight"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "axisAngular", "notify": "axisAngularChanged", "read": "axisAngular", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisAngular"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "axisRadial", "notify": "axisRadialChanged", "read": "axisRadial", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "QAbstractAxis*", "user": false, "write": "setAxisRadial"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "style", "notify": "styleChanged", "read": "style", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "Qt::PenStyle", "user": false, "write": "setStyle"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "capStyle", "notify": "capStyleChanged", "read": "capStyle", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "Qt::PenCapStyle", "user": false, "write": "setCapStyle"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "declarative<PERSON><PERSON><PERSON><PERSON>", "read": "declarative<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QObject>", "user": false}], "qualifiedClassName": "DeclarativeSplineSeries", "signals": [{"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 0, "name": "countChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 1, "name": "axisXChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 2, "name": "axisYChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 3, "name": "axisXTopChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 4, "name": "axisYRightChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 5, "name": "axisAngularChanged", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstractAxis*"}], "index": 6, "name": "axisRadialChanged", "returnType": "void", "revision": 259}, {"access": "public", "arguments": [{"name": "width", "type": "qreal"}], "index": 7, "name": "widthChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "style", "type": "Qt::PenStyle"}], "index": 8, "name": "styleChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "capStyle", "type": "Qt::PenCapStyle"}], "index": 9, "name": "capStyleChanged", "returnType": "void", "revision": 257}], "slots": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 10, "name": "handleCountChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QSplineSeries"}, {"access": "public", "name": "DeclarativeXySeries"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "declarativesplineseries_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "XYPoint"}, {"name": "QML.AddedInVersion", "value": "256"}, {"name": "QML.ExtraVersion", "value": "512"}], "className": "DeclarativeXYPoint", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "x", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "y", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setY"}], "qualifiedClassName": "DeclarativeXYPoint", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QPointF"}]}], "inputFile": "declarativexypoint_p.h", "outputRevision": 69}]