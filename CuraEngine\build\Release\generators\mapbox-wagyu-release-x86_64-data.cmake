########### AGGREGATED COMPONENTS AND DEPENDENCIES FOR THE MULTI CONFIG #####################
#############################################################################################

set(mapbox-wagyu_COMPONENT_NAMES "")
if(DEFINED mapbox-wagyu_FIND_DEPENDENCY_NAMES)
  list(APPEND mapbox-wagyu_FIND_DEPENDENCY_NAMES mapbox-geometry)
  list(REMOVE_DUPLICATES mapbox-wagyu_FIND_DEPENDENCY_NAMES)
else()
  set(mapbox-wagyu_FIND_DEPENDENCY_NAMES mapbox-geometry)
endif()
set(mapbox-geometry_FIND_MODE "NO_MODULE")

########### VARIABLES #######################################################################
#############################################################################################
set(mapbox-wagyu_PACKAGE_FOLDER_RELEASE "C:/Users/<USER>/.conan2/p/mapbo34ee9aeb574f9/p")
set(mapbox-wagyu_BUILD_MODULES_PATHS_RELEASE )


set(mapbox-wagyu_INCLUDE_DIRS_RELEASE "${mapbox-wagyu_PACKAGE_FOLDER_RELEASE}/include")
set(mapbox-wagyu_RES_DIRS_RELEASE )
set(mapbox-wagyu_DEFINITIONS_RELEASE )
set(mapbox-wagyu_SHARED_LINK_FLAGS_RELEASE )
set(mapbox-wagyu_EXE_LINK_FLAGS_RELEASE )
set(mapbox-wagyu_OBJECTS_RELEASE )
set(mapbox-wagyu_COMPILE_DEFINITIONS_RELEASE )
set(mapbox-wagyu_COMPILE_OPTIONS_C_RELEASE )
set(mapbox-wagyu_COMPILE_OPTIONS_CXX_RELEASE )
set(mapbox-wagyu_LIB_DIRS_RELEASE )
set(mapbox-wagyu_BIN_DIRS_RELEASE )
set(mapbox-wagyu_LIBRARY_TYPE_RELEASE UNKNOWN)
set(mapbox-wagyu_IS_HOST_WINDOWS_RELEASE 1)
set(mapbox-wagyu_LIBS_RELEASE )
set(mapbox-wagyu_SYSTEM_LIBS_RELEASE )
set(mapbox-wagyu_FRAMEWORK_DIRS_RELEASE )
set(mapbox-wagyu_FRAMEWORKS_RELEASE )
set(mapbox-wagyu_BUILD_DIRS_RELEASE )
set(mapbox-wagyu_NO_SONAME_MODE_RELEASE FALSE)


# COMPOUND VARIABLES
set(mapbox-wagyu_COMPILE_OPTIONS_RELEASE
    "$<$<COMPILE_LANGUAGE:CXX>:${mapbox-wagyu_COMPILE_OPTIONS_CXX_RELEASE}>"
    "$<$<COMPILE_LANGUAGE:C>:${mapbox-wagyu_COMPILE_OPTIONS_C_RELEASE}>")
set(mapbox-wagyu_LINKER_FLAGS_RELEASE
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,SHARED_LIBRARY>:${mapbox-wagyu_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,MODULE_LIBRARY>:${mapbox-wagyu_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,EXECUTABLE>:${mapbox-wagyu_EXE_LINK_FLAGS_RELEASE}>")


set(mapbox-wagyu_COMPONENTS_RELEASE )