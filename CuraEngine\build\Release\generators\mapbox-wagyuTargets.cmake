# Load the debug and release variables
file(GLOB DATA_FILES "${CMAKE_CURRENT_LIST_DIR}/mapbox-wagyu-*-data.cmake")

foreach(f ${DATA_FILES})
    include(${f})
endforeach()

# Create the targets for all the components
foreach(_COMPONENT ${mapbox-wagyu_COMPONENT_NAMES} )
    if(NOT TARGET ${_COMPONENT})
        add_library(${_COMPONENT} INTERFACE IMPORTED)
        message(${mapbox-wagyu_MESSAGE_MODE} "Conan: Component target declared '${_COMPONENT}'")
    endif()
endforeach()

if(NOT TARGET mapbox-wagyu::mapbox-wagyu)
    add_library(mapbox-wagyu::mapbox-wagyu INTERFACE IMPORTED)
    message(${mapbox-wagyu_MESSAGE_MODE} "Conan: Target declared 'mapbox-wagyu::mapbox-wagyu'")
endif()
# Load the debug and release library finders
file(GLOB CONFIG_FILES "${CMAKE_CURRENT_LIST_DIR}/mapbox-wagyu-Target-*.cmake")

foreach(f ${CONFIG_FILES})
    include(${f})
endforeach()