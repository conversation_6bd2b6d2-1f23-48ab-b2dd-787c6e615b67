# Avoid multiple calls to find_package to append duplicated properties to the targets
include_guard()########### VARIABLES #######################################################################
#############################################################################################
set(mapbox-wagyu_FRAMEWORKS_FOUND_RELEASE "") # Will be filled later
conan_find_apple_frameworks(mapbox-wagyu_FRAMEWORKS_FOUND_RELEASE "${mapbox-wagyu_FRAMEWORKS_RELEASE}" "${mapbox-wagyu_FRAMEWORK_DIRS_RELEASE}")

set(mapbox-wagyu_LIBRARIES_TARGETS "") # Will be filled later


######## Create an interface target to contain all the dependencies (frameworks, system and conan deps)
if(NOT TARGET mapbox-wagyu_DEPS_TARGET)
    add_library(mapbox-wagyu_DEPS_TARGET INTERFACE IMPORTED)
endif()

set_property(TARGET mapbox-wagyu_DEPS_TARGET
             APPEND PROPERTY INTERFACE_LINK_LIBRARIES
             $<$<CONFIG:Release>:${mapbox-wagyu_FRAMEWORKS_FOUND_RELEASE}>
             $<$<CONFIG:Release>:${mapbox-wagyu_SYSTEM_LIBS_RELEASE}>
             $<$<CONFIG:Release>:mapbox-geometry::mapbox-geometry>)

####### Find the libraries declared in cpp_info.libs, create an IMPORTED target for each one and link the
####### mapbox-wagyu_DEPS_TARGET to all of them
conan_package_library_targets("${mapbox-wagyu_LIBS_RELEASE}"    # libraries
                              "${mapbox-wagyu_LIB_DIRS_RELEASE}" # package_libdir
                              "${mapbox-wagyu_BIN_DIRS_RELEASE}" # package_bindir
                              "${mapbox-wagyu_LIBRARY_TYPE_RELEASE}"
                              "${mapbox-wagyu_IS_HOST_WINDOWS_RELEASE}"
                              mapbox-wagyu_DEPS_TARGET
                              mapbox-wagyu_LIBRARIES_TARGETS  # out_libraries_targets
                              "_RELEASE"
                              "mapbox-wagyu"    # package_name
                              "${mapbox-wagyu_NO_SONAME_MODE_RELEASE}")  # soname

# FIXME: What is the result of this for multi-config? All configs adding themselves to path?
set(CMAKE_MODULE_PATH ${mapbox-wagyu_BUILD_DIRS_RELEASE} ${CMAKE_MODULE_PATH})

########## GLOBAL TARGET PROPERTIES Release ########################################
    set_property(TARGET mapbox-wagyu::mapbox-wagyu
                 APPEND PROPERTY INTERFACE_LINK_LIBRARIES
                 $<$<CONFIG:Release>:${mapbox-wagyu_OBJECTS_RELEASE}>
                 $<$<CONFIG:Release>:${mapbox-wagyu_LIBRARIES_TARGETS}>
                 )

    if("${mapbox-wagyu_LIBS_RELEASE}" STREQUAL "")
        # If the package is not declaring any "cpp_info.libs" the package deps, system libs,
        # frameworks etc are not linked to the imported targets and we need to do it to the
        # global target
        set_property(TARGET mapbox-wagyu::mapbox-wagyu
                     APPEND PROPERTY INTERFACE_LINK_LIBRARIES
                     mapbox-wagyu_DEPS_TARGET)
    endif()

    set_property(TARGET mapbox-wagyu::mapbox-wagyu
                 APPEND PROPERTY INTERFACE_LINK_OPTIONS
                 $<$<CONFIG:Release>:${mapbox-wagyu_LINKER_FLAGS_RELEASE}>)
    set_property(TARGET mapbox-wagyu::mapbox-wagyu
                 APPEND PROPERTY INTERFACE_INCLUDE_DIRECTORIES
                 $<$<CONFIG:Release>:${mapbox-wagyu_INCLUDE_DIRS_RELEASE}>)
    # Necessary to find LINK shared libraries in Linux
    set_property(TARGET mapbox-wagyu::mapbox-wagyu
                 APPEND PROPERTY INTERFACE_LINK_DIRECTORIES
                 $<$<CONFIG:Release>:${mapbox-wagyu_LIB_DIRS_RELEASE}>)
    set_property(TARGET mapbox-wagyu::mapbox-wagyu
                 APPEND PROPERTY INTERFACE_COMPILE_DEFINITIONS
                 $<$<CONFIG:Release>:${mapbox-wagyu_COMPILE_DEFINITIONS_RELEASE}>)
    set_property(TARGET mapbox-wagyu::mapbox-wagyu
                 APPEND PROPERTY INTERFACE_COMPILE_OPTIONS
                 $<$<CONFIG:Release>:${mapbox-wagyu_COMPILE_OPTIONS_RELEASE}>)

########## For the modules (FindXXX)
set(mapbox-wagyu_LIBRARIES_RELEASE mapbox-wagyu::mapbox-wagyu)
