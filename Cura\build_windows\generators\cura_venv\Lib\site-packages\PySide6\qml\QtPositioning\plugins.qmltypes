import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/locationsingleton_p.h"
        name: "LocationSingleton"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtPositioning/QtPositioning 5.0",
            "QtPositioning/QtPositioning 5.12",
            "QtPositioning/QtPositioning 6.0"
        ]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [1280, 1292, 1536]
        Method { name: "coordinate"; type: "QGeoCoordinate"; isMethodConstant: true }
        Method {
            name: "coordinate"
            type: "QGeoCoordinate"
            isMethodConstant: true
            Parameter { name: "latitude"; type: "double" }
            Parameter { name: "longitude"; type: "double" }
            Parameter { name: "altitude"; type: "double" }
        }
        Method {
            name: "coordinate"
            type: "QGeoCoordinate"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "latitude"; type: "double" }
            Parameter { name: "longitude"; type: "double" }
        }
        Method { name: "shape"; type: "QGeoShape"; isMethodConstant: true }
        Method { name: "rectangle"; type: "QGeoRectangle"; isMethodConstant: true }
        Method {
            name: "rectangle"
            type: "QGeoRectangle"
            isMethodConstant: true
            Parameter { name: "center"; type: "QGeoCoordinate" }
            Parameter { name: "width"; type: "double" }
            Parameter { name: "height"; type: "double" }
        }
        Method {
            name: "rectangle"
            type: "QGeoRectangle"
            isMethodConstant: true
            Parameter { name: "topLeft"; type: "QGeoCoordinate" }
            Parameter { name: "bottomRight"; type: "QGeoCoordinate" }
        }
        Method {
            name: "rectangle"
            type: "QGeoRectangle"
            isMethodConstant: true
            Parameter { name: "coordinates"; type: "QVariantList" }
        }
        Method { name: "circle"; type: "QGeoCircle"; isMethodConstant: true }
        Method {
            name: "circle"
            type: "QGeoCircle"
            isMethodConstant: true
            Parameter { name: "center"; type: "QGeoCoordinate" }
            Parameter { name: "radius"; type: "double" }
        }
        Method {
            name: "circle"
            type: "QGeoCircle"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "center"; type: "QGeoCoordinate" }
        }
        Method { name: "path"; type: "QGeoPath"; isMethodConstant: true }
        Method {
            name: "path"
            type: "QGeoPath"
            isMethodConstant: true
            Parameter { name: "value"; type: "QJSValue" }
            Parameter { name: "width"; type: "double" }
        }
        Method {
            name: "path"
            type: "QGeoPath"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "value"; type: "QJSValue" }
        }
        Method { name: "polygon"; type: "QGeoPolygon"; isMethodConstant: true }
        Method {
            name: "polygon"
            type: "QGeoPolygon"
            isMethodConstant: true
            Parameter { name: "value"; type: "QVariantList" }
        }
        Method {
            name: "polygon"
            type: "QGeoPolygon"
            isMethodConstant: true
            Parameter { name: "perimeter"; type: "QVariantList" }
            Parameter { name: "holes"; type: "QVariantList" }
        }
        Method {
            name: "shapeToCircle"
            type: "QGeoCircle"
            isMethodConstant: true
            Parameter { name: "shape"; type: "QGeoShape" }
        }
        Method {
            name: "shapeToRectangle"
            type: "QGeoRectangle"
            isMethodConstant: true
            Parameter { name: "shape"; type: "QGeoShape" }
        }
        Method {
            name: "shapeToPath"
            type: "QGeoPath"
            isMethodConstant: true
            Parameter { name: "shape"; type: "QGeoShape" }
        }
        Method {
            name: "shapeToPolygon"
            type: "QGeoPolygon"
            isMethodConstant: true
            Parameter { name: "shape"; type: "QGeoShape" }
        }
        Method {
            name: "mercatorToCoord"
            revision: 1292
            type: "QGeoCoordinate"
            isMethodConstant: true
            Parameter { name: "mercator"; type: "QPointF" }
        }
        Method {
            name: "coordToMercator"
            revision: 1292
            type: "QPointF"
            isMethodConstant: true
            Parameter { name: "coord"; type: "QGeoCoordinate" }
        }
    }
    Component {
        file: "private/qdeclarativegeoaddress_p.h"
        name: "QDeclarativeGeoAddress"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtPositioning/Address 5.0",
            "QtPositioning/Address 6.0",
            "QtPositioning/Address 6.2"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1538]
        Property { name: "address"; type: "QGeoAddress"; read: "address"; write: "setAddress"; index: 0 }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 1
        }
        Property {
            name: "country"
            type: "QString"
            read: "country"
            write: "setCountry"
            notify: "countryChanged"
            index: 2
        }
        Property {
            name: "countryCode"
            type: "QString"
            read: "countryCode"
            write: "setCountryCode"
            notify: "countryCodeChanged"
            index: 3
        }
        Property {
            name: "state"
            type: "QString"
            read: "state"
            write: "setState"
            notify: "stateChanged"
            index: 4
        }
        Property {
            name: "county"
            type: "QString"
            read: "county"
            write: "setCounty"
            notify: "countyChanged"
            index: 5
        }
        Property {
            name: "city"
            type: "QString"
            read: "city"
            write: "setCity"
            notify: "cityChanged"
            index: 6
        }
        Property {
            name: "district"
            type: "QString"
            read: "district"
            write: "setDistrict"
            notify: "districtChanged"
            index: 7
        }
        Property {
            name: "street"
            type: "QString"
            read: "street"
            write: "setStreet"
            notify: "streetChanged"
            index: 8
        }
        Property {
            name: "streetNumber"
            revision: 1538
            type: "QString"
            read: "streetNumber"
            write: "setStreetNumber"
            notify: "streetNumberChanged"
            index: 9
        }
        Property {
            name: "postalCode"
            type: "QString"
            read: "postalCode"
            write: "setPostalCode"
            notify: "postalCodeChanged"
            index: 10
        }
        Property {
            name: "isTextGenerated"
            type: "bool"
            read: "isTextGenerated"
            notify: "isTextGeneratedChanged"
            index: 11
            isReadonly: true
        }
        Signal { name: "textChanged" }
        Signal { name: "countryChanged" }
        Signal { name: "countryCodeChanged" }
        Signal { name: "stateChanged" }
        Signal { name: "countyChanged" }
        Signal { name: "cityChanged" }
        Signal { name: "districtChanged" }
        Signal { name: "streetChanged" }
        Signal { name: "streetNumberChanged" }
        Signal { name: "postalCodeChanged" }
        Signal { name: "isTextGeneratedChanged" }
    }
    Component {
        file: "private/qdeclarativegeolocation_p.h"
        name: "QDeclarativeGeoLocation"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtPositioning/Location 5.0",
            "QtPositioning/Location 5.13",
            "QtPositioning/Location 6.0",
            "QtPositioning/Location 6.2"
        ]
        exportMetaObjectRevisions: [1280, 1293, 1536, 1538]
        Property {
            name: "location"
            type: "QGeoLocation"
            read: "location"
            write: "setLocation"
            index: 0
        }
        Property {
            name: "address"
            type: "QDeclarativeGeoAddress"
            isPointer: true
            bindable: "bindableAddress"
            read: "address"
            write: "setAddress"
            index: 1
        }
        Property {
            name: "coordinate"
            type: "QGeoCoordinate"
            bindable: "bindableCoordinate"
            read: "coordinate"
            write: "setCoordinate"
            index: 2
        }
        Property {
            name: "boundingShape"
            revision: 1538
            type: "QGeoShape"
            bindable: "bindableBoundingShape"
            read: "boundingShape"
            write: "setBoundingShape"
            index: 3
        }
        Property {
            name: "extendedAttributes"
            revision: 1293
            type: "QVariantMap"
            bindable: "bindableExtendedAttributes"
            read: "extendedAttributes"
            write: "setExtendedAttributes"
            index: 4
        }
    }
    Component {
        file: "private/qdeclarativepluginparameter_p.h"
        name: "QDeclarativePluginParameter"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtPositioning/PluginParameter 5.14",
            "QtPositioning/PluginParameter 6.0"
        ]
        exportMetaObjectRevisions: [1294, 1536]
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 0
        }
        Property {
            name: "value"
            type: "QVariant"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 1
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "QVariant" }
        }
        Signal { name: "initialized" }
    }
    Component {
        file: "private/qdeclarativeposition_p.h"
        name: "QDeclarativePosition"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtPositioning/Position 5.0",
            "QtPositioning/Position 5.1",
            "QtPositioning/Position 5.2",
            "QtPositioning/Position 6.0",
            "QtPositioning/Position 6.3"
        ]
        exportMetaObjectRevisions: [1280, 1281, 1282, 1536, 1539]
        Property {
            name: "latitudeValid"
            type: "bool"
            bindable: "bindableLatitudeValid"
            read: "isLatitudeValid"
            index: 0
            isReadonly: true
        }
        Property {
            name: "longitudeValid"
            type: "bool"
            bindable: "bindableLongitudeValid"
            read: "isLongitudeValid"
            index: 1
            isReadonly: true
        }
        Property {
            name: "altitudeValid"
            type: "bool"
            bindable: "bindableAltitudeValid"
            read: "isAltitudeValid"
            index: 2
            isReadonly: true
        }
        Property {
            name: "coordinate"
            type: "QGeoCoordinate"
            bindable: "bindableCoordinate"
            read: "coordinate"
            index: 3
            isReadonly: true
        }
        Property {
            name: "timestamp"
            type: "QDateTime"
            bindable: "bindableTimestamp"
            read: "timestamp"
            index: 4
            isReadonly: true
        }
        Property {
            name: "speed"
            type: "double"
            bindable: "bindableSpeed"
            read: "speed"
            index: 5
            isReadonly: true
        }
        Property {
            name: "speedValid"
            type: "bool"
            bindable: "bindableSpeedValid"
            read: "isSpeedValid"
            index: 6
            isReadonly: true
        }
        Property {
            name: "horizontalAccuracy"
            type: "double"
            bindable: "bindableHorizontalAccuracy"
            read: "horizontalAccuracy"
            index: 7
            isReadonly: true
        }
        Property {
            name: "verticalAccuracy"
            type: "double"
            bindable: "binableVerticalAccuracy"
            read: "verticalAccuracy"
            index: 8
            isReadonly: true
        }
        Property {
            name: "horizontalAccuracyValid"
            type: "bool"
            bindable: "bindableHorizontalAccuracyValid"
            read: "isHorizontalAccuracyValid"
            index: 9
            isReadonly: true
        }
        Property {
            name: "verticalAccuracyValid"
            type: "bool"
            bindable: "bindableVerticalAccuracyValid"
            read: "isVerticalAccuracyValid"
            index: 10
            isReadonly: true
        }
        Property {
            name: "directionValid"
            revision: 1281
            type: "bool"
            bindable: "bindableDirectionValid"
            read: "isDirectionValid"
            index: 11
            isReadonly: true
        }
        Property {
            name: "direction"
            revision: 1281
            type: "double"
            bindable: "bindableDirection"
            read: "direction"
            index: 12
            isReadonly: true
        }
        Property {
            name: "verticalSpeedValid"
            revision: 1281
            type: "bool"
            bindable: "bindableVerticalSpeedValid"
            read: "isVerticalSpeedValid"
            index: 13
            isReadonly: true
        }
        Property {
            name: "verticalSpeed"
            revision: 1281
            type: "double"
            bindable: "bindableVerticalSpeed"
            read: "verticalSpeed"
            index: 14
            isReadonly: true
        }
        Property {
            name: "magneticVariation"
            revision: 1282
            type: "double"
            bindable: "bindableMagneticVariation"
            read: "magneticVariation"
            index: 15
            isReadonly: true
        }
        Property {
            name: "magneticVariationValid"
            revision: 1282
            type: "bool"
            bindable: "bindableMagneticVariationValid"
            read: "isMagneticVariationValid"
            index: 16
            isReadonly: true
        }
        Property {
            name: "directionAccuracy"
            revision: 1539
            type: "double"
            bindable: "bindableDirectionAccuracy"
            read: "directionAccuracy"
            index: 17
            isReadonly: true
        }
        Property {
            name: "directionAccuracyValid"
            revision: 1539
            type: "bool"
            bindable: "bindableDirectionAccuracyValid"
            read: "isDirectionAccuracyValid"
            index: 18
            isReadonly: true
        }
    }
    Component {
        file: "private/qdeclarativepositionsource_p.h"
        name: "QDeclarativePositionSource"
        accessSemantics: "reference"
        defaultProperty: "parameters"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtPositioning/PositionSource 5.0",
            "QtPositioning/PositionSource 5.14",
            "QtPositioning/PositionSource 6.0"
        ]
        exportMetaObjectRevisions: [1280, 1294, 1536]
        Enum {
            name: "PositioningMethod"
            values: [
                "NoPositioningMethods",
                "SatellitePositioningMethods",
                "NonSatellitePositioningMethods",
                "AllPositioningMethods"
            ]
        }
        Enum {
            name: "PositioningMethods"
            alias: "PositioningMethod"
            isFlag: true
            values: [
                "NoPositioningMethods",
                "SatellitePositioningMethods",
                "NonSatellitePositioningMethods",
                "AllPositioningMethods"
            ]
        }
        Enum {
            name: "SourceError"
            values: [
                "AccessError",
                "ClosedError",
                "UnknownSourceError",
                "NoError",
                "UpdateTimeoutError"
            ]
        }
        Property {
            name: "position"
            type: "QDeclarativePosition"
            isPointer: true
            bindable: "bindablePosition"
            read: "position"
            notify: "positionChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "active"
            type: "bool"
            bindable: "bindableActive"
            read: "isActive"
            write: "setActive"
            notify: "activeChanged"
            index: 1
        }
        Property {
            name: "valid"
            type: "bool"
            bindable: "bindableIsValid"
            read: "isValid"
            notify: "validityChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "updateInterval"
            type: "int"
            read: "updateInterval"
            write: "setUpdateInterval"
            notify: "updateIntervalChanged"
            index: 3
        }
        Property {
            name: "supportedPositioningMethods"
            type: "PositioningMethods"
            bindable: "bindableSupportedPositioningMethods"
            read: "supportedPositioningMethods"
            notify: "supportedPositioningMethodsChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "preferredPositioningMethods"
            type: "PositioningMethods"
            read: "preferredPositioningMethods"
            write: "setPreferredPositioningMethods"
            notify: "preferredPositioningMethodsChanged"
            index: 5
        }
        Property {
            name: "sourceError"
            type: "SourceError"
            bindable: "bindableSourceError"
            read: "sourceError"
            notify: "sourceErrorChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "name"
            type: "QString"
            bindable: "bindableName"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 7
        }
        Property {
            name: "parameters"
            revision: 1294
            type: "QDeclarativePluginParameter"
            isList: true
            read: "parameters"
            index: 8
            isReadonly: true
        }
        Signal { name: "positionChanged" }
        Signal { name: "activeChanged" }
        Signal { name: "updateIntervalChanged" }
        Signal { name: "supportedPositioningMethodsChanged" }
        Signal { name: "preferredPositioningMethodsChanged" }
        Signal { name: "sourceErrorChanged" }
        Signal { name: "nameChanged" }
        Signal { name: "validityChanged" }
        Method {
            name: "update"
            Parameter { name: "timeout"; type: "int" }
        }
        Method { name: "update"; isCloned: true }
        Method { name: "start" }
        Method { name: "stop" }
        Method {
            name: "positionUpdateReceived"
            Parameter { name: "update"; type: "QGeoPositionInfo" }
        }
        Method {
            name: "sourceErrorReceived"
            Parameter { name: "error"; type: "QGeoPositionInfoSource::Error" }
        }
        Method { name: "onParameterInitialized" }
        Method { name: "notifySupportedPositioningMethodsChanged" }
        Method {
            name: "setBackendProperty"
            revision: 1294
            type: "bool"
            Parameter { name: "name"; type: "QString" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "backendProperty"
            revision: 1294
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "name"; type: "QString" }
        }
    }
    Component {
        file: "private/qdeclarativesatellitesource_p.h"
        name: "QDeclarativeSatelliteSource"
        accessSemantics: "reference"
        defaultProperty: "parameters"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtPositioning/SatelliteSource 6.5"]
        exportMetaObjectRevisions: [1541]
        Enum {
            name: "SourceError"
            values: [
                "AccessError",
                "ClosedError",
                "NoError",
                "UnknownSourceError",
                "UpdateTimeoutError"
            ]
        }
        Property {
            name: "active"
            type: "bool"
            read: "isActive"
            write: "setActive"
            notify: "activeChanged"
            index: 0
        }
        Property {
            name: "valid"
            type: "bool"
            read: "isValid"
            notify: "validityChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "updateInterval"
            type: "int"
            read: "updateInterval"
            write: "setUpdateInterval"
            notify: "updateIntervalChanged"
            index: 2
        }
        Property {
            name: "sourceError"
            type: "SourceError"
            read: "sourceError"
            notify: "sourceErrorChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 4
        }
        Property {
            name: "parameters"
            type: "QDeclarativePluginParameter"
            isList: true
            read: "parameters"
            index: 5
            isReadonly: true
        }
        Property {
            name: "satellitesInUse"
            type: "QGeoSatelliteInfo"
            isList: true
            read: "satellitesInUse"
            notify: "satellitesInUseChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "satellitesInView"
            type: "QGeoSatelliteInfo"
            isList: true
            read: "satellitesInView"
            notify: "satellitesInViewChanged"
            index: 7
            isReadonly: true
        }
        Signal { name: "activeChanged" }
        Signal { name: "validityChanged" }
        Signal { name: "updateIntervalChanged" }
        Signal { name: "sourceErrorChanged" }
        Signal { name: "nameChanged" }
        Signal { name: "satellitesInUseChanged" }
        Signal { name: "satellitesInViewChanged" }
        Method {
            name: "update"
            Parameter { name: "timeout"; type: "int" }
        }
        Method { name: "update"; isCloned: true }
        Method { name: "start" }
        Method { name: "stop" }
        Method {
            name: "sourceErrorReceived"
            Parameter { name: "error"; type: "QGeoSatelliteInfoSource::Error" }
        }
        Method { name: "onParameterInitialized" }
        Method {
            name: "satellitesInViewUpdateReceived"
            Parameter { name: "satellites"; type: "QGeoSatelliteInfo"; isList: true }
        }
        Method {
            name: "satellitesInUseUpdateReceived"
            Parameter { name: "satellites"; type: "QGeoSatelliteInfo"; isList: true }
        }
        Method {
            name: "setBackendProperty"
            type: "bool"
            Parameter { name: "name"; type: "QString" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "backendProperty"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "name"; type: "QString" }
        }
    }
    Component {
        file: "private/qpositioningquickmodule_p.h"
        name: "QGeoAddress"
        accessSemantics: "value"
        exports: [
            "QtPositioning/geoAddress 5.0",
            "QtPositioning/geoAddress 6.0"
        ]
        isStructured: true
        exportMetaObjectRevisions: [1280, 1536]
    }
    Component {
        file: "private/qpositioningquickmodule_p.h"
        name: "QGeoCircle"
        accessSemantics: "value"
        prototype: "QGeoShape"
        exports: [
            "QtPositioning/geoCircle 5.0",
            "QtPositioning/geoCircle 6.0"
        ]
        isStructured: true
        exportMetaObjectRevisions: [1280, 1536]
        Property { name: "center"; type: "QGeoCoordinate"; read: "center"; write: "setCenter"; index: 0 }
        Property { name: "radius"; type: "double"; read: "radius"; write: "setRadius"; index: 1 }
        Method {
            name: "translate"
            Parameter { name: "degreesLatitude"; type: "double" }
            Parameter { name: "degreesLongitude"; type: "double" }
        }
        Method {
            name: "translated"
            type: "QGeoCircle"
            isMethodConstant: true
            Parameter { name: "degreesLatitude"; type: "double" }
            Parameter { name: "degreesLongitude"; type: "double" }
        }
        Method {
            name: "extendCircle"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
    }
    Component {
        file: "private/qpositioningquickmodule_p.h"
        name: "QGeoCoordinate"
        accessSemantics: "value"
        exports: [
            "QtPositioning/geoCoordinate 5.0",
            "QtPositioning/geoCoordinate 6.0"
        ]
        isStructured: true
        exportMetaObjectRevisions: [1280, 1536]
        Enum {
            name: "CoordinateFormat"
            values: [
                "Degrees",
                "DegreesWithHemisphere",
                "DegreesMinutes",
                "DegreesMinutesWithHemisphere",
                "DegreesMinutesSeconds",
                "DegreesMinutesSecondsWithHemisphere"
            ]
        }
        Property { name: "latitude"; type: "double"; read: "latitude"; write: "setLatitude"; index: 0 }
        Property { name: "longitude"; type: "double"; read: "longitude"; write: "setLongitude"; index: 1 }
        Property { name: "altitude"; type: "double"; read: "altitude"; write: "setAltitude"; index: 2 }
        Property { name: "isValid"; type: "bool"; read: "isValid"; index: 3; isReadonly: true }
        Method {
            name: "distanceTo"
            type: "double"
            isMethodConstant: true
            Parameter { name: "other"; type: "QGeoCoordinate" }
        }
        Method {
            name: "azimuthTo"
            type: "double"
            isMethodConstant: true
            Parameter { name: "other"; type: "QGeoCoordinate" }
        }
        Method {
            name: "atDistanceAndAzimuth"
            type: "QGeoCoordinate"
            isMethodConstant: true
            Parameter { name: "distance"; type: "double" }
            Parameter { name: "azimuth"; type: "double" }
            Parameter { name: "distanceUp"; type: "double" }
        }
        Method {
            name: "atDistanceAndAzimuth"
            type: "QGeoCoordinate"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "distance"; type: "double" }
            Parameter { name: "azimuth"; type: "double" }
        }
        Method {
            name: "toString"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "format"; type: "CoordinateFormat" }
        }
        Method { name: "toString"; type: "QString"; isCloned: true; isMethodConstant: true }
    }
    Component {
        file: "private/qpositioningquickmodule_p.h"
        name: "QGeoCoordinateObject"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "coordinate"
            type: "QGeoCoordinate"
            bindable: "bindableCoordinate"
            read: "coordinate"
            write: "setCoordinate"
            notify: "coordinateChanged"
            index: 0
        }
        Signal { name: "coordinateChanged" }
    }
    Component {
        file: "private/qpositioningquickmodule_p.h"
        name: "QGeoLocation"
        accessSemantics: "value"
        exports: [
            "QtPositioning/geoLocation 5.0",
            "QtPositioning/geoLocation 6.0"
        ]
        isStructured: true
        exportMetaObjectRevisions: [1280, 1536]
    }
    Component {
        file: "private/qpositioningquickmodule_p.h"
        name: "QGeoPath"
        accessSemantics: "value"
        prototype: "QGeoShape"
        exports: ["QtPositioning/geoPath 5.0", "QtPositioning/geoPath 6.0"]
        isStructured: true
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "path"
            type: "QVariantList"
            read: "variantPath"
            write: "setVariantPath"
            index: 0
        }
        Property { name: "width"; type: "double"; read: "width"; write: "setWidth"; index: 1 }
        Method {
            name: "translate"
            Parameter { name: "degreesLatitude"; type: "double" }
            Parameter { name: "degreesLongitude"; type: "double" }
        }
        Method {
            name: "translated"
            type: "QGeoPath"
            isMethodConstant: true
            Parameter { name: "degreesLatitude"; type: "double" }
            Parameter { name: "degreesLongitude"; type: "double" }
        }
        Method {
            name: "length"
            type: "double"
            isMethodConstant: true
            Parameter { name: "indexFrom"; type: "qsizetype" }
            Parameter { name: "indexTo"; type: "qsizetype" }
        }
        Method {
            name: "length"
            type: "double"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "indexFrom"; type: "qsizetype" }
        }
        Method { name: "length"; type: "double"; isCloned: true; isMethodConstant: true }
        Method { name: "size"; type: "qsizetype"; isMethodConstant: true }
        Method {
            name: "addCoordinate"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "insertCoordinate"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "replaceCoordinate"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "coordinateAt"
            type: "QGeoCoordinate"
            isMethodConstant: true
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "containsCoordinate"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "removeCoordinate"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "removeCoordinate"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
    }
    Component {
        file: "private/qpositioningquickmodule_p.h"
        name: "QGeoPolygon"
        accessSemantics: "value"
        prototype: "QGeoShape"
        exports: [
            "QtPositioning/geoPolygon 5.0",
            "QtPositioning/geoPolygon 5.12",
            "QtPositioning/geoPolygon 6.0"
        ]
        isStructured: true
        exportMetaObjectRevisions: [1280, 1292, 1536]
        Property {
            name: "perimeter"
            revision: 1292
            type: "QGeoCoordinate"
            isList: true
            read: "perimeter"
            write: "setPerimeter"
            index: 0
        }
        Method {
            name: "addHole"
            Parameter { name: "holePath"; type: "QVariant" }
        }
        Method {
            name: "hole"
            type: "QVariantList"
            isMethodConstant: true
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "removeHole"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method { name: "holesCount"; type: "qsizetype"; isMethodConstant: true }
        Method {
            name: "translate"
            Parameter { name: "degreesLatitude"; type: "double" }
            Parameter { name: "degreesLongitude"; type: "double" }
        }
        Method {
            name: "translated"
            type: "QGeoPolygon"
            isMethodConstant: true
            Parameter { name: "degreesLatitude"; type: "double" }
            Parameter { name: "degreesLongitude"; type: "double" }
        }
        Method {
            name: "length"
            type: "double"
            isMethodConstant: true
            Parameter { name: "indexFrom"; type: "qsizetype" }
            Parameter { name: "indexTo"; type: "qsizetype" }
        }
        Method {
            name: "length"
            type: "double"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "indexFrom"; type: "qsizetype" }
        }
        Method { name: "length"; type: "double"; isCloned: true; isMethodConstant: true }
        Method { name: "size"; type: "qsizetype"; isMethodConstant: true }
        Method {
            name: "addCoordinate"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "insertCoordinate"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "replaceCoordinate"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "coordinateAt"
            type: "QGeoCoordinate"
            isMethodConstant: true
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "containsCoordinate"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "removeCoordinate"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "removeCoordinate"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
    }
    Component {
        file: "private/qpositioningquickmodule_p.h"
        name: "QGeoPositionInfo"
        accessSemantics: "value"
        exports: [
            "QtPositioning/geoPositionInfo 5.0",
            "QtPositioning/geoPositionInfo 6.0"
        ]
        isStructured: true
        exportMetaObjectRevisions: [1280, 1536]
    }
    Component {
        file: "private/qpositioningquickmodule_p.h"
        name: "QGeoRectangle"
        accessSemantics: "value"
        prototype: "QGeoShape"
        exports: [
            "QtPositioning/geoRectangle 5.0",
            "QtPositioning/geoRectangle 6.0"
        ]
        isStructured: true
        exportMetaObjectRevisions: [1280, 1536]
        Property {
            name: "bottomLeft"
            type: "QGeoCoordinate"
            read: "bottomLeft"
            write: "setBottomLeft"
            index: 0
        }
        Property {
            name: "bottomRight"
            type: "QGeoCoordinate"
            read: "bottomRight"
            write: "setBottomRight"
            index: 1
        }
        Property { name: "topLeft"; type: "QGeoCoordinate"; read: "topLeft"; write: "setTopLeft"; index: 2 }
        Property {
            name: "topRight"
            type: "QGeoCoordinate"
            read: "topRight"
            write: "setTopRight"
            index: 3
        }
        Property { name: "center"; type: "QGeoCoordinate"; read: "center"; write: "setCenter"; index: 4 }
        Property { name: "height"; type: "double"; read: "height"; write: "setHeight"; index: 5 }
        Property { name: "width"; type: "double"; read: "width"; write: "setWidth"; index: 6 }
        Method {
            name: "intersects"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "rectangle"; type: "QGeoRectangle" }
        }
        Method {
            name: "translate"
            Parameter { name: "degreesLatitude"; type: "double" }
            Parameter { name: "degreesLongitude"; type: "double" }
        }
        Method {
            name: "translated"
            type: "QGeoRectangle"
            isMethodConstant: true
            Parameter { name: "degreesLatitude"; type: "double" }
            Parameter { name: "degreesLongitude"; type: "double" }
        }
        Method {
            name: "extendRectangle"
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method {
            name: "united"
            type: "QGeoRectangle"
            isMethodConstant: true
            Parameter { name: "rectangle"; type: "QGeoRectangle" }
        }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
    }
    Component {
        file: "private/qpositioningquickmodule_p.h"
        name: "QGeoSatelliteInfo"
        accessSemantics: "value"
        exports: ["QtPositioning/geoSatelliteInfo 6.5"]
        isCreatable: false
        exportMetaObjectRevisions: [1541]
        Enum {
            name: "Attribute"
            values: ["Elevation", "Azimuth"]
        }
        Enum {
            name: "SatelliteSystem"
            values: [
                "Undefined",
                "GPS",
                "GLONASS",
                "GALILEO",
                "BEIDOU",
                "QZSS",
                "Multiple",
                "CustomType"
            ]
        }
        Property {
            name: "satelliteSystem"
            type: "SatelliteSystem"
            read: "satelliteSystem"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "satelliteIdentifier"
            type: "int"
            read: "satelliteIdentifier"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "signalStrength"
            type: "double"
            read: "signalStrength"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Method {
            name: "attribute"
            type: "double"
            isMethodConstant: true
            Parameter { name: "attribute"; type: "Attribute" }
        }
        Method {
            name: "hasAttribute"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "attribute"; type: "Attribute" }
        }
    }
    Component {
        file: "private/qpositioningquickmodule_p.h"
        name: "QGeoSatelliteInfoDerived"
        accessSemantics: "none"
        prototype: "QGeoSatelliteInfo"
        exports: ["QtPositioning/GeoSatelliteInfo 6.5"]
        isCreatable: false
        exportMetaObjectRevisions: [1541]
    }
    Component {
        file: "private/qpositioningquickmodule_p.h"
        name: "QGeoShape"
        accessSemantics: "value"
        exports: ["QtPositioning/geoShape 5.0", "QtPositioning/geoShape 6.0"]
        exportMetaObjectRevisions: [1280, 1536]
        Enum {
            name: "ShapeType"
            values: [
                "UnknownType",
                "RectangleType",
                "CircleType",
                "PathType",
                "PolygonType"
            ]
        }
        Property { name: "type"; type: "ShapeType"; read: "type"; index: 0; isReadonly: true }
        Property { name: "isValid"; type: "bool"; read: "isValid"; index: 1; isReadonly: true }
        Property { name: "isEmpty"; type: "bool"; read: "isEmpty"; index: 2; isReadonly: true }
        Property { name: "center"; type: "QGeoCoordinate"; read: "center"; index: 3; isReadonly: true }
        Method {
            name: "contains"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "coordinate"; type: "QGeoCoordinate" }
        }
        Method { name: "boundingGeoRectangle"; type: "QGeoRectangle"; isMethodConstant: true }
        Method { name: "toString"; type: "QString"; isMethodConstant: true }
        Method {
            name: "QGeoShape"
            isConstructor: true
            Parameter { name: "other"; type: "QGeoShape" }
        }
    }
    Component {
        file: "private/qquickgeocoordinateanimation_p.h"
        name: "QQuickGeoCoordinateAnimation"
        accessSemantics: "reference"
        prototype: "QQuickPropertyAnimation"
        exports: [
            "QtPositioning/CoordinateAnimation 5.3",
            "QtPositioning/CoordinateAnimation 6.0"
        ]
        exportMetaObjectRevisions: [1283, 1536]
        Enum {
            name: "Direction"
            values: ["Shortest", "West", "East"]
        }
        Property { name: "from"; type: "QGeoCoordinate"; read: "from"; write: "setFrom"; index: 0 }
        Property { name: "to"; type: "QGeoCoordinate"; read: "to"; write: "setTo"; index: 1 }
        Property {
            name: "direction"
            type: "Direction"
            bindable: "bindableDirection"
            read: "direction"
            write: "setDirection"
            notify: "directionChanged"
            index: 2
        }
        Signal { name: "directionChanged" }
    }
}
