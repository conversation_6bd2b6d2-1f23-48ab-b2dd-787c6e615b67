<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtCore">
  <extra-includes>
    <include file-name="QtCore/qt_windows.h" location="global"/>
  </extra-includes>
  <primitive-type name="HWND">
      <!-- Qt5: Add this include there to insert ti in the global qtcore header (needed by qprocess_wrapper) -->
      <include file-name="wtypes.h" location="global"/>
      <conversion-rule>
        <native-to-target>
        return PyCapsule_New(%in, nullptr, nullptr);
        </native-to-target>
        <target-to-native>
            <add-conversion type="PyNone">
            %out = 0;
            </add-conversion>
            <add-conversion check="checkPyCapsuleOrPyCObject(%in)" type="PyObject">
            %out = (%OUTTYPE)PyCapsule_GetPointer(%in, nullptr);
            </add-conversion>
        </target-to-native>
      </conversion-rule>
  </primitive-type>
  <inject-code class="native" position="beginning">
    <insert-template name="checkPyCapsuleOrPyCObject_func"/>
  </inject-code>

  <object-type name="QWinEventNotifier"/>
</typesystem>
