# Cura
# Copyright (C) 2022 UltiMaker.
# This file is distributed under the same license as the Cura package.
# <AUTHOR> <EMAIL>, 2022.
#
msgid ""
msgstr ""
"Project-Id-Version: Cura 5.1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-31 19:13+0100\n"
"PO-Revision-Date: 2022-07-15 10:53+0200\n"
"Last-Translator: Bothof <<EMAIL>>\n"
"Language-Team: Finnish\n"
"Language: fi_FI\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.1.1\n"

#, python-format
msgctxt "@info 'width', 'depth' and 'height' are variable names that must NOT be translated; just translate the format of ##x##x## mm."
msgid "%(width).1f x %(depth).1f x %(height).1f mm"
msgstr "%(width).1f x %(depth).1f x %(height).1f mm"

msgctxt "@action:label"
msgid "%1 & material"
msgstr "%1 & materiaali"

msgctxt "@action:label"
msgid "%1 out of %2"
msgstr "%1/%2"

msgctxt "@action:label"
msgid "%1 override"
msgid_plural "%1 overrides"
msgstr[0] "%1 ohitus"
msgstr[1] "%1 ohitusta"

msgctxt "@action:label"
msgid "%1, %2 override"
msgid_plural "%1, %2 overrides"
msgstr[0] "%1, %2 ohitus"
msgstr[1] "%1, %2 ohitusta"

msgctxt "@label g for grams"
msgid "%1g"
msgstr ""

msgctxt "@label m for meter"
msgid "%1m"
msgstr ""

msgctxt "@action:inmenu menubar:printer"
msgid "&Add Printer..."
msgstr "L&isää tulostin..."

msgctxt "@action:inmenu menubar:view"
msgid "&Camera position"
msgstr ""

msgctxt "@action:inmenu menubar:profile"
msgid "&Create profile from current settings/overrides..."
msgstr "&Luo profiili nykyisten asetusten tai ohitusten perusteella..."

msgctxt "@action:inmenu menubar:profile"
msgid "&Discard current changes"
msgstr "&Hylkää tehdyt muutokset"

msgctxt "@title:menu menubar:toplevel"
msgid "&Edit"
msgstr "&Muokkaa"

msgctxt "@title:menu menubar:file"
msgid "&Export..."
msgstr ""

msgctxt "@title:menu menubar:toplevel"
msgid "&File"
msgstr "Tie&dosto"

msgctxt "@action:inmenu menubar:edit"
msgid "&Group Models"
msgstr "&Ryhmittele mallit"

msgctxt "@title:menu menubar:toplevel"
msgid "&Help"
msgstr "&Ohje"

msgctxt "@title:menu"
msgid "&Material"
msgstr "&Materiaali"

msgctxt "@action:inmenu menubar:edit"
msgid "&Merge Models"
msgstr "&Yhdistä mallit"

msgctxt "@action:inmenu"
msgid "&Multiply Model..."
msgstr "&Kerro malli..."

msgctxt "@action:inmenu menubar:file"
msgid "&New Project..."
msgstr "&Uusi projekti..."

msgctxt "@action:inmenu menubar:file"
msgid "&Open File(s)..."
msgstr "&Avaa tiedosto(t)..."

msgctxt "@title:menu menubar:settings"
msgid "&Printer"
msgstr "&Tulostin"

msgctxt "@action:inmenu menubar:file"
msgid "&Quit"
msgstr "&Lopeta"

msgctxt "@action:inmenu menubar:edit"
msgid "&Redo"
msgstr "Tee &uudelleen"

msgctxt "@title:menu menubar:file"
msgid "&Save Project..."
msgstr ""

msgctxt "@title:menu menubar:toplevel"
msgid "&Settings"
msgstr ""

msgctxt "@action:inmenu menubar:edit"
msgid "&Undo"
msgstr "&Kumoa"

msgctxt "@action:inmenu menubar:profile"
msgid "&Update profile with current settings/overrides"
msgstr "&Päivitä nykyiset asetukset tai ohitukset profiiliin"

msgctxt "@title:menu menubar:toplevel"
msgid "&View"
msgstr "&Näytä"

msgctxt "@label"
msgid "*You will need to restart the application for these changes to have effect."
msgstr ""

msgctxt "@text"
msgid ""
"- Add material profiles and plug-ins from the Marketplace\n"
"- Back-up and sync your material profiles and plug-ins\n"
"- Share ideas and get help from 48,000+ users in the UltiMaker community"
msgstr ""

msgctxt "@heading"
msgid "-- incomplete --"
msgstr ""

msgctxt "@action:label"
msgid "1mm Transmittance (%)"
msgstr ""

msgctxt "@info:title"
msgid "3D Model Assistant"
msgstr ""

msgctxt "@action:inmenu menubar:view"
msgid "3D View"
msgstr ""

msgctxt "@info:tooltip"
msgid "3D View"
msgstr ""

msgctxt "@item:inlistbox"
msgid "3MF File"
msgstr "3MF-tiedosto"

msgctxt "name"
msgid "3MF Reader"
msgstr "3MF-lukija"

msgctxt "name"
msgid "3MF Writer"
msgstr "3MF-kirjoitin"

msgctxt "@error:zip"
msgid "3MF Writer plug-in is corrupt."
msgstr ""

msgctxt "@item:inlistbox"
msgid "3MF file"
msgstr "3MF-tiedosto"

msgctxt "@info, %1 is the name of the custom profile"
msgid "<b>%1</b> custom profile is active and you overwrote some settings."
msgstr ""

msgctxt "@info, %1 is the name of the custom profile"
msgid "<b>%1</b> custom profile is overriding some settings."
msgstr ""

msgctxt "@label %i will be replaced with a profile name"
msgid "<b>Only user changed settings will be saved in the custom profile.</b><br/>For materials that support it, the new custom profile will inherit properties from <b>%1</b>."
msgstr ""

#, python-brace-format
msgctxt "@label OpenGL renderer"
msgid "<li>OpenGL Renderer: {renderer}</li>"
msgstr ""

#, python-brace-format
msgctxt "@label OpenGL vendor"
msgid "<li>OpenGL Vendor: {vendor}</li>"
msgstr ""

#, python-brace-format
msgctxt "@label OpenGL version"
msgid "<li>OpenGL Version: {version}</li>"
msgstr ""

msgctxt "@label crash message"
msgid ""
"<p><b>A fatal error has occurred in Cura. Please send us this Crash Report to fix the problem</p></b>\n"
"            <p>Please use the \"Send report\" button to post a bug report automatically to our servers</p>\n"
"        "
msgstr ""

msgctxt "@label crash message"
msgid ""
"<p><b>Oops, UltiMaker Cura has encountered something that doesn't seem right.</p></b>\n"
"                    <p>We encountered an unrecoverable error during start up. It was possibly caused by some incorrect configuration files. We suggest to backup and reset your configuration.</p>\n"
"                    <p>Backups can be found in the configuration folder.</p>\n"
"                    <p>Please send us this Crash Report to fix the problem.</p>\n"
"                "
msgstr ""

#, python-brace-format
msgctxt "@info:status"
msgid ""
"<p>One or more 3D models may not print optimally due to the model size and material configuration:</p>\n"
"<p>{model_names}</p>\n"
"<p>Find out how to ensure the best possible print quality and reliability.</p>\n"
"<p><a href=\"https://ultimaker.com/3D-model-assistant\">View print quality guide</a></p>"
msgstr ""

msgctxt "@label"
msgid "A USB print is in progress, closing Cura will stop this print. Are you sure?"
msgstr ""

msgctxt "info:status"
msgid "A cloud connection is not available for a printer"
msgid_plural "A cloud connection is not available for some printers"
msgstr[0] ""
msgstr[1] ""

msgctxt "@message"
msgid "A print is still in progress. Cura cannot start another print via USB until the previous print has completed."
msgstr ""

msgctxt "@item:inlistbox"
msgid "AMF File"
msgstr ""

msgctxt "name"
msgid "AMF Reader"
msgstr ""

msgctxt "@label"
msgid "Abort"
msgstr ""

msgctxt "@label"
msgid "Abort Print"
msgstr ""

msgctxt "@window:title"
msgid "Abort print"
msgstr "Keskeytä tulostus"

msgctxt "@label:status"
msgid "Aborted"
msgstr ""

msgctxt "@label"
msgid "Aborting..."
msgstr ""

msgctxt "@label:status"
msgid "Aborting..."
msgstr ""

msgctxt "@title:window The argument is the application name."
msgid "About %1"
msgstr ""

msgctxt "@action:inmenu menubar:help"
msgid "About..."
msgstr "Tietoja..."

msgctxt "@button"
msgid "Accept"
msgstr ""

msgctxt "description"
msgid "Accepts G-Code and sends them to a printer. Plugin can also update firmware."
msgstr "Hyväksyy GCode-määrittelyt ja lähettää ne tulostimeen. Lisäosa voi myös päivittää laiteohjelmiston."

msgctxt "@label"
msgid "Account synced"
msgstr ""

msgctxt "@label:status"
msgid "Action required"
msgstr "Vaatii toimenpiteitä"

msgctxt "@action:button"
msgid "Activate"
msgstr "Aktivoi"

msgctxt "@label"
msgid "Active print"
msgstr "Aktiivinen tulostustyö"

msgctxt "@action:button"
msgid "Add"
msgstr "Lisää"

msgctxt "@button"
msgid "Add"
msgstr ""

msgctxt "@action:button"
msgid "Add New"
msgstr ""

msgctxt "@title:window"
msgid "Add Printer"
msgstr "Lisää tulostin"

msgctxt "@button"
msgid "Add UltiMaker printer via Digital Factory"
msgstr ""

msgctxt "@label"
msgid "Add a Cloud printer"
msgstr ""

msgctxt "@label"
msgid "Add a networked printer"
msgstr ""

msgctxt "@label"
msgid "Add a non-networked printer"
msgstr ""

msgctxt "@action"
msgid "Add a script"
msgstr "Lisää komentosarja"

msgctxt "@option:check"
msgid "Add icon to system tray *"
msgstr ""

msgctxt "@button"
msgid "Add local printer"
msgstr ""

msgctxt "@option:check"
msgid "Add machine prefix to job name"
msgstr "Lisää laitteen etuliite työn nimeen"

msgctxt "@text"
msgid "Add material settings and plugins from the Marketplace"
msgstr ""

msgctxt "@action:inmenu Marketplace is a brand name of UltiMaker's, so don't translate."
msgid "Add more materials from Marketplace"
msgstr ""

msgctxt "@button"
msgid "Add printer"
msgstr ""

msgctxt "@label"
msgid "Add printer"
msgstr "Lisää tulostin"

msgctxt "@label"
msgid "Add printer by IP"
msgstr ""

msgctxt "@label"
msgid "Add printer by IP address"
msgstr ""

msgctxt "@button"
msgid "Add printer manually"
msgstr ""

#, python-brace-format
msgctxt "info:status Filled in with printer name and printer model."
msgid "Adding printer {name} ({model}) from your account"
msgstr ""

msgctxt "@label"
msgid "Address"
msgstr "Osoite"

msgctxt "@label"
msgid "Adhesion"
msgstr ""

msgctxt "@label"
msgid "Adhesion Information"
msgstr "Tarttuvuustiedot"

msgctxt "@label"
msgid "Adjusts the density of infill of the print."
msgstr ""

msgctxt "support_type description"
msgid "Adjusts the placement of the support structures. The placement can be set to touching build plate or everywhere. When set to everywhere the support structures will also be printed on the model."
msgstr ""

msgctxt "@label Header for list of settings."
msgid "Affected By"
msgstr "Riippuu seuraavista"

msgctxt "@label Header for list of settings."
msgid "Affects"
msgstr "Koskee seuraavia"

msgctxt "@button"
msgid "Agree"
msgstr ""

msgctxt "@item:inlistbox"
msgid "All Files (*)"
msgstr ""

#, python-brace-format
msgctxt "@item:inlistbox"
msgid "All Supported Types ({0})"
msgstr ""

msgctxt "@text:window"
msgid "Allow sending anonymous data"
msgstr ""

msgctxt "description"
msgid "Allows loading and displaying G-code files."
msgstr "Mahdollistaa GCode-tiedostojen lataamisen ja näyttämisen."

msgctxt "@option:discardOrKeep"
msgid "Always ask me this"
msgstr "Kysy aina"

msgctxt "@option:openProject"
msgid "Always ask me this"
msgstr ""

msgctxt "@option:discardOrKeep"
msgid "Always discard changed settings"
msgstr ""

msgctxt "@option:openProject"
msgid "Always import models"
msgstr "Tuo mallit aina"

msgctxt "@option:openProject"
msgid "Always open as a project"
msgstr "Avaa aina projektina"

msgctxt "@option:discardOrKeep"
msgid "Always transfer changed settings to new profile"
msgstr ""

msgctxt "@info:tooltip"
msgid "An model may appear extremely small if its unit is for example in meters rather than millimeters. Should these models be scaled up?"
msgstr "Malli voi vaikuttaa erittäin pieneltä, jos sen koko on ilmoitettu esimerkiksi metreissä eikä millimetreissä. Pitäisikö nämä mallit suurentaa?"

msgctxt "@label"
msgid "Annealing"
msgstr ""

msgctxt "@label"
msgid "Anonymous"
msgstr ""

msgctxt "@label Description for application component"
msgid "Application framework"
msgstr "Sovelluskehys"

msgctxt "@label"
msgid "Apply Extruder offsets to GCode"
msgstr ""

msgctxt "@info:title"
msgid "Are you ready for cloud printing?"
msgstr ""

msgctxt "@label %1 is the name of a print job."
msgid "Are you sure you want to abort %1?"
msgstr ""

msgctxt "@label"
msgid "Are you sure you want to abort the print?"
msgstr "Haluatko varmasti keskeyttää tulostuksen?"

msgctxt "@label %1 is the name of a print job."
msgid "Are you sure you want to delete %1?"
msgstr ""

msgctxt "@dialog:info"
msgid "Are you sure you want to delete this backup? This cannot be undone."
msgstr ""

msgctxt "@label %1 is the application name"
msgid "Are you sure you want to exit %1?"
msgstr ""

msgctxt "@label %1 is the name of a print job."
msgid "Are you sure you want to move %1 to the top of the queue?"
msgstr ""

#, python-brace-format
msgctxt "@message {printer_name} is replaced with the name of the printer"
msgid "Are you sure you want to remove {printer_name} temporarily?"
msgstr ""

msgctxt "@info:question"
msgid "Are you sure you want to start a new project? This will clear the build plate and any unsaved settings."
msgstr "Haluatko varmasti aloittaa uuden projektin? Se tyhjentää alustan ja kaikki tallentamattomat asetukset."

msgctxt "@label (%1 is object name)"
msgid "Are you sure you wish to remove %1? This cannot be undone!"
msgstr ""

#, python-brace-format
msgctxt "@label {0} is the name of a printer that's about to be deleted."
msgid "Are you sure you wish to remove {0}? This cannot be undone!"
msgstr ""

msgctxt "@action:inmenu menubar:edit"
msgid "Arrange All Models"
msgstr "Järjestä kaikki mallit"

msgctxt "@action:inmenu menubar:edit"
msgid "Arrange All Models in a grid"
msgstr ""

msgctxt "@action:inmenu menubar:edit"
msgid "Arrange Selection"
msgstr "Järjestä valinta"

msgctxt "@label:button"
msgid "Ask a question"
msgstr ""

msgctxt "@checkbox:description"
msgid "Auto Backup"
msgstr ""

msgctxt "@checkbox:description"
msgid "Automatically create a backup each day that Cura is started."
msgstr ""

msgctxt "@option:check"
msgid "Automatically drop models to the build plate"
msgstr "Pudota mallit automaattisesti alustalle"

msgctxt "@action:button"
msgid "Automatically upgrade Firmware"
msgstr "Päivitä laiteohjelmisto automaattisesti"

msgctxt "@label"
msgid "Available networked printers"
msgstr ""

msgctxt "@item:inlistbox"
msgid "BMP Image"
msgstr "BMP-kuva"

msgctxt "@button"
msgid "Back"
msgstr ""

msgctxt "@button:tooltip"
msgid "Back"
msgstr ""

msgctxt "@info:title"
msgid "Backup"
msgstr ""

msgctxt "@button"
msgid "Backup Now"
msgstr ""

msgctxt "@action:button"
msgid "Backup and Reset Configuration"
msgstr ""

msgctxt "description"
msgid "Backup and restore your configuration."
msgstr ""

msgctxt "@text"
msgid "Backup and sync your material settings and plugins"
msgstr ""

msgctxt "@description"
msgid "Backup and synchronize your Cura settings."
msgstr ""

msgctxt "@info:title"
msgid "Backups"
msgstr ""

msgctxt "@label"
msgid "Balanced"
msgstr "Tasapainotettu"

msgctxt "@action:label"
msgid "Base (mm)"
msgstr "Pohja (mm)"

msgctxt "@action:inmenu menubar:view"
msgid "Bottom View"
msgstr ""

msgctxt "@label"
msgid "Brand"
msgstr "Merkki"

msgctxt "@title"
msgid "Build Plate Leveling"
msgstr "Alustan tasaaminen"

msgctxt "@info:title"
msgid "Build Volume"
msgstr "Tulostustilavuus"

msgctxt "@label"
msgid "Build plate"
msgstr "Alusta"

msgctxt "@label"
msgid "Build plate shape"
msgstr "Alustan muoto"

msgctxt "@label"
msgid "Bundled Materials"
msgstr ""

msgctxt "@label"
msgid "Bundled Plugins"
msgstr ""

msgctxt "@button"
msgid "Buy spool"
msgstr ""

msgctxt "@label Is followed by the name of an author"
msgid "By"
msgstr ""

msgctxt "@label Description for application dependency"
msgid "C/C++ Binding library"
msgstr "C/C++ -sidontakirjasto"

msgctxt "@item:inlistbox"
msgid "COLLADA Digital Asset Exchange"
msgstr ""

msgctxt "@info:status"
msgid "Calculated"
msgstr "Laskettu"

msgctxt "@window:text"
msgid "Camera rendering:"
msgstr ""

msgctxt "@action:inmenu menubar:view"
msgid "Camera view"
msgstr ""

msgctxt "@info:title"
msgid "Can't Find Location"
msgstr "Paikkaa ei löydy"

msgctxt "@info:title"
msgid "Can't Open Project File"
msgstr ""

msgctxt "@label"
msgid "Can't connect to your UltiMaker printer?"
msgstr ""

#, python-brace-format
msgctxt "@info:status Don't translate the XML tags <filename>!"
msgid "Can't import profile from <filename>{0}</filename> before a printer is added."
msgstr ""

#, python-brace-format
msgctxt "@info:status"
msgid "Can't open any other file if G-code is loading. Skipped importing {0}"
msgstr "Muita tiedostoja ei voida ladata, kun G-code latautuu. Tiedoston {0} tuonti ohitettiin."

msgctxt "@info:error"
msgid "Can't write to UFP file:"
msgstr ""

msgctxt "@action:button"
msgid "Cancel"
msgstr "Peruuta"

msgctxt "@button"
msgid "Cancel"
msgstr ""

msgctxt "@button Cancel pre-heating"
msgid "Cancel"
msgstr "Peruuta"

msgctxt "@option:check"
msgid "Caution message in g-code reader"
msgstr ""

msgctxt "@action:inmenu"
msgid "Ce&nter Model on Platform"
msgstr "Ke&skitä malli alustalle"

msgctxt "@action:inmenu menubar:edit"
msgid "Center Selected"
msgstr ""

msgctxt "@action:button"
msgid "Center camera when item is selected"
msgstr "Keskitä kamera kun kohde on valittu"

msgctxt "@info:tooltip"
msgid "Change active post-processing scripts."
msgstr ""

msgctxt "@label"
msgid "Change material %1 from %2 to %3."
msgstr ""

msgctxt "@label"
msgid "Change print core %1 from %2 to %3."
msgstr ""

msgctxt "@info:title"
msgid "Changes detected from your UltiMaker account"
msgstr ""

msgctxt "@title"
msgid "Changes from your account"
msgstr ""

msgctxt "@label:textbox"
msgid "Check all"
msgstr "Tarkista kaikki"

msgctxt "@button"
msgid "Check for account updates"
msgstr ""

msgctxt "@option:check"
msgid "Check for updates on start"
msgstr "Tarkista päivitykset käynnistettäessä"

msgctxt "@label"
msgid "Checking..."
msgstr ""

msgctxt "description"
msgid "Checks for firmware updates."
msgstr "Tarkistaa laiteohjelmistopäivitykset."

msgctxt "description"
msgid "Checks models and print configuration for possible printing issues and give suggestions."
msgstr ""

msgctxt "@label"
msgid ""
"Chooses between the techniques available to generate support. \n"
"\n"
"\"Normal\" support creates a support structure directly below the overhanging parts and drops those areas straight down. \n"
"\n"
"\"Tree\" support creates branches towards the overhanging areas that support the model on the tips of those branches, and allows the branches to crawl around the model to support it from the build plate as much as possible."
msgstr ""

msgctxt "@action:inmenu menubar:edit"
msgid "Clear Build Plate"
msgstr "Tyhjennä tulostusalusta"

msgctxt "@option:check"
msgid "Clear buildplate before loading model into the single instance"
msgstr ""

msgctxt "@text"
msgid "Click the export material archive button."
msgstr ""

msgctxt "@action:button"
msgid "Close"
msgstr "Sulje"

msgctxt "@title:window %1 is the application name"
msgid "Closing %1"
msgstr ""

msgctxt "@action:inmenu"
msgid "Collapse All Categories"
msgstr ""

msgctxt "@label"
msgid "Color"
msgstr "Väri"

msgctxt "@action:label"
msgid "Color Model"
msgstr ""

msgctxt "@label"
msgid "Color scheme"
msgstr "Värimalli"

msgctxt "@info"
msgid "Compare and save."
msgstr ""

msgctxt "@label"
msgid "Compatibility Mode"
msgstr "Yhteensopivuustila"

msgctxt "@label Description for application dependency"
msgid "Compatibility between Python 2 and 3"
msgstr ""

msgctxt "@title:label"
msgid "Compatible Printers"
msgstr ""

msgctxt "@label"
msgid "Compatible material diameter"
msgstr ""

msgctxt "@header"
msgid "Compatible printers"
msgstr ""

msgctxt "@header"
msgid "Compatible support materials"
msgstr ""

msgctxt "@header"
msgid "Compatible with Material Station"
msgstr ""

msgctxt "@item:inlistbox"
msgid "Compressed COLLADA Digital Asset Exchange"
msgstr ""

msgctxt "@item:inlistbox"
msgid "Compressed G-code File"
msgstr ""

msgctxt "name"
msgid "Compressed G-code Reader"
msgstr ""

msgctxt "name"
msgid "Compressed G-code Writer"
msgstr ""

msgctxt "@title:window"
msgid "Configuration Changes"
msgstr ""

msgctxt "@error"
msgid "Configuration not supported"
msgstr ""

msgctxt "@header"
msgid "Configurations"
msgstr ""

msgctxt "@label"
msgid "Configurations"
msgstr ""

msgctxt "@action:inmenu"
msgid "Configure Cura..."
msgstr "Määritä Curan asetukset..."

msgctxt "@info:tooltip"
msgid "Configure Per Model Settings"
msgstr "Määritä mallikohtaiset asetukset"

msgctxt "@action"
msgid "Configure group"
msgstr ""

msgctxt "@action:menu"
msgid "Configure setting visibility..."
msgstr "Määritä asetusten näkyvyys..."

msgctxt "@title:window"
msgid "Confirm Diameter Change"
msgstr ""

msgctxt "@title:window"
msgid "Confirm Remove"
msgstr ""

msgctxt "@action:button"
msgid "Connect"
msgstr "Yhdistä"

msgctxt "@button"
msgid "Connect"
msgstr ""

msgctxt "@title:window"
msgid "Connect to Networked Printer"
msgstr "Yhdistä verkkotulostimeen"

msgctxt "@action"
msgid "Connect via Network"
msgstr "Yhdistä verkon kautta"

msgctxt "@info:status"
msgid "Connected over the network"
msgstr ""

msgctxt "@label"
msgid "Connected printers"
msgstr ""

msgctxt "@info:status"
msgid "Connected via USB"
msgstr "Yhdistetty USB:n kautta"

msgctxt "@info:status"
msgid "Connected via cloud"
msgstr ""

msgctxt "description"
msgid "Connects to the Digital Library, allowing Cura to open files from and save files to the Digital Library."
msgstr ""

msgctxt "@tooltip:button"
msgid "Consult the UltiMaker Community."
msgstr ""

msgctxt "@title:window"
msgid "Convert Image"
msgstr ""

msgctxt "@label"
msgid "Cooling Fan Number"
msgstr ""

msgctxt "@action:menu"
msgid "Copy all changed values to all extruders"
msgstr ""

msgctxt "@action:inmenu menubar:edit"
msgid "Copy to clipboard"
msgstr ""

msgctxt "@action:menu"
msgid "Copy value to all extruders"
msgstr "Kopioi arvo kaikkiin suulakepuristimiin"

msgctxt "@label"
msgid "Cost per Meter"
msgstr "Hinta metriä kohden"

msgctxt "@info"
msgid "Could not access update information."
msgstr "Päivitystietoja ei löytynyt."

msgctxt "@label"
msgid "Could not connect to device."
msgstr ""

msgctxt "@info:backup_failed"
msgid "Could not create archive from user data directory: {}"
msgstr ""

#, python-brace-format
msgctxt "@info:status Don't translate the tag {device}!"
msgid "Could not find a file name when trying to write to {device}."
msgstr "Ei löydetty tiedostonimeä yritettäessä kirjoittaa laitteeseen {device}."

msgctxt "@info:status Don't translate the XML tags <filename> or <message>!"
msgid "Could not import material <filename>%1</filename>: <message>%2</message>"
msgstr "Materiaalin tuominen epäonnistui: <filename>%1</filename>: <message>%2</message>"

msgctxt "@info:error"
msgid "Could not interpret the server's response."
msgstr ""

msgctxt "@error:load"
msgid "Could not load GCodeWriter plugin. Try to re-enable the plugin."
msgstr ""

msgctxt "@info:error"
msgid "Could not reach Marketplace."
msgstr ""

msgctxt "@message"
msgid "Could not read response."
msgstr ""

msgctxt "@message:text"
msgid "Could not save material archive to {}:"
msgstr ""

#, python-brace-format
msgctxt "@info:status Don't translate the XML tags <filename> or <message>!"
msgid "Could not save to <filename>{0}</filename>: <message>{1}</message>"
msgstr "Ei voitu tallentaa tiedostoon <filename>{0}</filename>: <message>{1}</message>"

#, python-brace-format
msgctxt "@info:status"
msgid "Could not save to removable drive {0}: {1}"
msgstr "Ei voitu tallentaa siirrettävälle asemalle {0}: {1}"

msgctxt "@info:text"
msgid "Could not upload the data to the printer."
msgstr ""

#, python-brace-format
msgctxt "@info:plugin_failed"
msgid ""
"Couldn't start EnginePlugin: {self._plugin_id}\n"
"No permission to execute process."
msgstr ""

#, python-brace-format
msgctxt "@info:plugin_failed"
msgid ""
"Couldn't start EnginePlugin: {self._plugin_id}\n"
"Operating system is blocking it (antivirus?)"
msgstr ""

#, python-brace-format
msgctxt "@info:plugin_failed"
msgid ""
"Couldn't start EnginePlugin: {self._plugin_id}\n"
"Resource is temporarily unavailable"
msgstr ""

msgctxt "@title:window"
msgid "Crash Report"
msgstr "Kaatumisraportti"

msgctxt "@title:window"
msgid "Create Profile"
msgstr "Luo profiili"

msgctxt "@text"
msgid "Create a free UltiMaker Account"
msgstr ""

msgctxt "@button"
msgid "Create a free UltiMaker account"
msgstr ""

msgctxt "@info:tooltip"
msgid "Create a volume in which supports are not printed."
msgstr ""

msgctxt "@action:ComboBox Save settings in a new profile"
msgid "Create new"
msgstr ""

msgctxt "@action:button"
msgid "Create new"
msgstr ""

msgctxt "@button"
msgid "Create new"
msgstr "Luo uusi"

msgctxt "@action:tooltip"
msgid "Create new profile from current settings/overrides"
msgstr ""

msgctxt "@tooltip:button"
msgid "Create print projects in Digital Library."
msgstr ""

msgctxt "description"
msgid "Creates an eraser mesh to block the printing of support in certain places"
msgstr ""

msgctxt "@info:backup_status"
msgid "Creating your backup..."
msgstr ""

msgctxt "@item:inlistbox"
msgid "Cura 15.04 profiles"
msgstr "Cura 15.04 -profiilit"

msgctxt "@title:window"
msgid "Cura Backups"
msgstr ""

msgctxt "name"
msgid "Cura Backups"
msgstr ""

msgctxt "@item:inlistbox"
msgid "Cura Profile"
msgstr "Cura-profiili"

msgctxt "name"
msgid "Cura Profile Reader"
msgstr "Cura-profiilin lukija"

msgctxt "name"
msgid "Cura Profile Writer"
msgstr "Cura-profiilin kirjoitin"

msgctxt "@item:inlistbox"
msgid "Cura Project 3MF file"
msgstr "Cura-projektin 3MF-tiedosto"

msgctxt "@backuplist:label"
msgid "Cura Version"
msgstr ""

msgctxt "@title:window"
msgid "Cura can't start"
msgstr ""

#, python-brace-format
msgctxt "@info:status"
msgid "Cura has detected material profiles that were not yet installed on the host printer of group {0}."
msgstr ""

msgctxt "@info:credit"
msgid ""
"Cura is developed by UltiMaker in cooperation with the community.\n"
"Cura proudly uses the following open source projects:"
msgstr ""
"Cura-ohjelman on kehittänyt Ultimaker B.V. yhteistyössä käyttäjäyhteisön kanssa.\n"
"Cura hyödyntää seuraavia avoimeen lähdekoodiin perustuvia projekteja:"

msgctxt "@label"
msgid "Cura language"
msgstr ""

msgctxt "@label Cura version number"
msgid "Cura version"
msgstr ""

msgctxt "name"
msgid "CuraEngine Backend"
msgstr "CuraEngine-taustaosa"

msgctxt "description"
msgid "CuraEngine plugin for gradually smoothing the flow to limit high-flow jumps"
msgstr ""

msgctxt "name"
msgid "CuraEngineGradualFlow"
msgstr ""

msgctxt "@label"
msgid "Currency:"
msgstr "Valuutta:"

msgctxt "@title:column"
msgid "Current"
msgstr "Nykyinen"

msgctxt "@title:column"
msgid "Current changes"
msgstr ""

msgctxt "@header"
msgid "Custom"
msgstr ""

msgctxt "@label"
msgid "Custom"
msgstr "Mukautettu"

msgctxt "@title:tab"
msgid "Custom"
msgstr "Mukautettu"

msgctxt "@label"
msgid "Custom Material"
msgstr "Mukautettu materiaali"

msgctxt "@label"
msgid "Custom profile"
msgstr "Mukautettu profiili"

msgctxt "@info"
msgid "Custom profile name:"
msgstr ""

msgctxt "@label"
msgid "Custom profiles"
msgstr "Mukautetut profiilit"

msgctxt "@label:header"
msgid "Custom profiles"
msgstr ""

msgctxt "@action:inmenu menubar:edit"
msgid "Cut"
msgstr ""

msgctxt "@item:inlistbox"
msgid "Cutting mesh"
msgstr ""

msgctxt "@item:inlistbox"
msgid "Darker is higher"
msgstr "Tummempi on korkeampi"

msgctxt "@info:title"
msgid "Data Sent"
msgstr ""

msgctxt "@label Description for application dependency"
msgid "Data interchange format"
msgstr "Data Interchange Format"

msgctxt "@button"
msgid "Decline"
msgstr ""

msgctxt "@button"
msgid "Decline and close"
msgstr ""

msgctxt "@button"
msgid "Decline and remove from account"
msgstr ""

msgctxt "@info:No intent profile selected"
msgid "Default"
msgstr ""

msgctxt "@window:text"
msgid "Default behavior for changed setting values when switching to a different profile: "
msgstr ""

msgctxt "@info:tooltip"
msgid "Default behavior when opening a project file"
msgstr "Projektitiedoston avaamisen oletustoimintatapa"

msgctxt "@window:text"
msgid "Default behavior when opening a project file: "
msgstr "Projektitiedoston avaamisen oletustoimintatapa: "

msgctxt "@action:button"
msgid "Defaults"
msgstr ""

msgctxt "@label"
msgid "Defines the thickness of your part side walls, roof and floor."
msgstr ""

msgctxt "@label"
msgid "Delete"
msgstr ""

msgctxt "@dialog:title"
msgid "Delete Backup"
msgstr ""

msgctxt "@action:inmenu"
msgid "Delete Model"
msgstr "Poista malli"

msgctxt "@action:inmenu menubar:edit"
msgid "Delete Selected"
msgstr ""

msgctxt "@window:title"
msgid "Delete print job"
msgstr ""

msgctxt "@label"
msgid "Density"
msgstr "Tiheys"

msgctxt "@label Description for development tool"
msgid "Dependency and package manager"
msgstr ""

msgctxt "@action:label"
msgid "Depth (mm)"
msgstr "Syvyys (mm)"

msgctxt "@action:label"
msgid "Derivative from"
msgstr "Johdettu seuraavista"

msgctxt "@header"
msgid "Description"
msgstr ""

msgctxt "@label"
msgid "Description"
msgstr "Kuvaus"

msgctxt "@action:button"
msgid "Details"
msgstr ""

msgctxt "@label"
msgid "Diameter"
msgstr "Läpimitta"

msgctxt "@button"
msgid "Disable"
msgstr ""

msgctxt "@action:inmenu"
msgid "Disable Extruder"
msgstr ""

msgctxt "@option:discardOrKeep"
msgid "Discard and never ask again"
msgstr "Hylkää äläkä kysy uudelleen"

msgctxt "@action:button"
msgid "Discard changes"
msgstr ""

msgctxt "@action:button"
msgid "Discard current changes"
msgstr "Hylkää tehdyt muutokset"

msgctxt "@title:window"
msgid "Discard or Keep changes"
msgstr "Hylkää tai säilytä muutokset"

msgctxt "@button"
msgid "Dismiss"
msgstr ""

msgctxt "@label"
msgid "Display Name"
msgstr "Näytä nimi"

msgctxt "@option:check"
msgid "Display model errors"
msgstr ""

msgctxt "@option:check"
msgid "Display overhang"
msgstr "Näytä uloke"

msgctxt "@info:option_text"
msgid "Do not show this message again"
msgstr ""

msgctxt "@info:generic"
msgid "Do you want to sync material and software packages with your account?"
msgstr ""

msgctxt "@action:label"
msgid "Don't show project summary on save again"
msgstr "Älä näytä projektin yhteenvetoa tallennettaessa"

msgctxt "@action:menu"
msgid "Don't show this setting"
msgstr "Älä näytä tätä asetusta"

msgctxt "@label"
msgid "Don't support overlaps"
msgstr ""

msgctxt "@button"
msgid "Done"
msgstr ""

msgctxt "@button"
msgid "Downgrade"
msgstr ""

msgctxt "@button"
msgid "Downgrading..."
msgstr ""

msgctxt "@label"
msgid "Draft"
msgstr ""

msgctxt "@action:button"
msgid "Duplicate"
msgstr "Jäljennös"

msgctxt "@title:window"
msgid "Duplicate Profile"
msgstr "Monista profiili"

msgctxt "@backup_limit_info"
msgid "During the preview phase, you'll be limited to 5 visible backups. Remove a backup to see older ones."
msgstr ""

msgctxt "@title:menu menubar:toplevel"
msgid "E&xtensions"
msgstr "Laa&jennukset"

msgctxt "@action:button"
msgid "Edit"
msgstr "Muokkaa"

msgctxt "@action:button"
msgid "Eject"
msgstr "Poista"

#, python-brace-format
msgctxt "@action"
msgid "Eject removable device {0}"
msgstr "Poista siirrettävä asema {0}"

#, python-brace-format
msgctxt "@info:status"
msgid "Ejected {0}. You can now safely remove the drive."
msgstr "Poistettu {0}. Voit nyt poistaa aseman turvallisesti."

msgctxt "@label"
msgid "Empty"
msgstr ""

msgctxt "@button"
msgid "Enable"
msgstr ""

msgctxt "@action:inmenu"
msgid "Enable Extruder"
msgstr ""

msgctxt "@label"
msgid "Enable printing a brim or raft. This will add a flat area around or under your object which is easy to cut off afterwards. Disabling it results in a skirt around object by default."
msgstr ""

msgctxt "@label"
msgid "Enabled"
msgstr ""

msgctxt "description"
msgid "Enables ability to generate printable geometry from 2D image files."
msgstr "Mahdollistaa tulostettavien geometrioiden luomisen 2D-kuvatiedostoista."

msgctxt "@title:label"
msgid "End G-code"
msgstr ""

msgctxt "@label"
msgid "End-to-end solution for fused filament 3D printing."
msgstr "Kokonaisvaltainen sulatettavan tulostuslangan 3D-tulostusratkaisu."

msgctxt "@info:title"
msgid "EnginePlugin"
msgstr ""

msgctxt "@label"
msgid "Engineering"
msgstr ""

msgctxt "@option:check"
msgid "Ensure models are kept apart"
msgstr "Varmista, että mallit ovat erillään"

msgctxt "@label"
msgid "Enter the IP address of your printer on the network."
msgstr ""

msgctxt "@text"
msgid "Enter your printer's IP address."
msgstr ""

msgctxt "@info:title"
msgid "Error"
msgstr "Virhe"

msgctxt "@title:groupbox"
msgid "Error traceback"
msgstr ""

msgctxt "@label"
msgid "Estimated time left"
msgstr "Aikaa jäljellä arviolta"

msgctxt "@action:inmenu"
msgid "Exit Full Screen"
msgstr ""

msgctxt "@label"
msgid "Experimental"
msgstr ""

msgctxt "@action:button"
msgid "Export"
msgstr "Vie"

msgctxt "@title:window"
msgid "Export All Materials"
msgstr ""

msgctxt "@title:window"
msgid "Export Material"
msgstr "Vie materiaali"

msgctxt "@title:window"
msgid "Export Profile"
msgstr "Profiilin vienti"

msgctxt "@action:inmenu menubar:file"
msgid "Export Selection..."
msgstr ""

msgctxt "@button"
msgid "Export material archive"
msgstr ""

msgctxt "@info:title"
msgid "Export succeeded"
msgstr ""

#, python-brace-format
msgctxt "@info:status Don't translate the XML tag <filename>!"
msgid "Exported profile to <filename>{0}</filename>"
msgstr "Profiili viety tiedostoon <filename>{0}</filename>"

msgctxt "@tooltip:button"
msgid "Extend UltiMaker Cura with plugins and material profiles."
msgstr ""

msgctxt "description"
msgid "Extension that allows for user created scripts for post processing"
msgstr "Lisäosa, jonka avulla käyttäjät voivat luoda komentosarjoja jälkikäsittelyä varten"

msgctxt "@label"
msgid "Extruder"
msgstr "Suulake"

msgctxt "@action:label"
msgid "Extruder %1"
msgstr "Suulake %1"

msgctxt "@title:label"
msgid "Extruder End G-code"
msgstr ""

msgctxt "@title:label"
msgid "Extruder Start G-code"
msgstr ""

msgctxt "@info:title"
msgid "Extruder(s) Disabled"
msgstr ""

msgctxt "@label:status"
msgid "Failed"
msgstr ""

msgctxt "@text:error"
msgid "Failed to connect to Digital Factory to sync materials with some of the printers."
msgstr ""

msgctxt "@text:error"
msgid "Failed to connect to Digital Factory."
msgstr ""

msgctxt "@text:error"
msgid "Failed to create archive of materials to sync with printers."
msgstr ""

#, python-brace-format
msgctxt "@info:status"
msgid "Failed to eject {0}. Another program may be using the drive."
msgstr "Kohteen {0} poistaminen epäonnistui. Asema saattaa olla toisen ohjelman käytössä."

msgctxt "@info:status Don't translate the XML tags <filename> and <message>!"
msgid "Failed to export material to <filename>%1</filename>: <message>%2</message>"
msgstr "Materiaalin vieminen epäonnistui kohteeseen <filename>%1</filename>: <message>%2</message>"

#, python-brace-format
msgctxt "@info:status Don't translate the XML tags <filename> or <message>!"
msgid "Failed to export profile to <filename>{0}</filename>: <message>{1}</message>"
msgstr "Profiilin vienti epäonnistui tiedostoon <filename>{0}</filename>: <message>{1}</message>"

#, python-brace-format
msgctxt "@info:status Don't translate the XML tag <filename>!"
msgid "Failed to export profile to <filename>{0}</filename>: Writer plugin reported failure."
msgstr "Profiilin vienti epäonnistui tiedostoon <filename>{0}</filename>: Kirjoitin-lisäosa ilmoitti virheestä."

#, python-brace-format
msgctxt "@info:status Don't translate the XML tag <filename>!"
msgid "Failed to import profile from <filename>{0}</filename>:"
msgstr ""

#, python-brace-format
msgctxt "@info:status Don't translate the XML tags <filename>!"
msgid "Failed to import profile from <filename>{0}</filename>:"
msgstr ""

#, python-brace-format
msgctxt "@info:status Don't translate the XML tags <filename>!"
msgid "Failed to import profile from <filename>{0}</filename>: {1}"
msgstr ""

msgctxt "@button"
msgid "Failed to load packages:"
msgstr ""

msgctxt "@text:error"
msgid "Failed to load the archive of materials to sync it with printers."
msgstr ""

msgctxt "@message:title"
msgid "Failed to save material archive"
msgstr ""

msgctxt "@label:category menu label"
msgid "Favorites"
msgstr ""

msgctxt "@label"
msgid "Filament Cost"
msgstr "Tulostuslangan hinta"

msgctxt "@label"
msgid "Filament length"
msgstr "Tulostuslangan pituus"

msgctxt "@label"
msgid "Filament weight"
msgstr "Tulostuslangan paino"

msgctxt "@title:window"
msgid "File Already Exists"
msgstr "Tiedosto on jo olemassa"

msgctxt "@info:title"
msgid "File Saved"
msgstr "Tiedosto tallennettu"

#, python-brace-format
msgctxt "@info:status"
msgid "File {0} does not contain any valid profile."
msgstr ""

msgctxt "@label:textbox"
msgid "Filter..."
msgstr "Suodatin..."

msgctxt "@info:title"
msgid "Finding Location"
msgstr "Etsitään paikkaa"

msgctxt "@info:status"
msgid "Finding new location for objects"
msgstr "Uusien paikkojen etsiminen kappaleille"

msgctxt "@action:button"
msgid "Finish"
msgstr ""

msgctxt "@label:status"
msgid "Finished"
msgstr "Valmis"

msgctxt "@label:status"
msgid "Finishes %1 at %2"
msgstr ""

msgctxt "@title:window"
msgid "Firmware Update"
msgstr "Laiteohjelmiston päivitys"

msgctxt "name"
msgid "Firmware Update Checker"
msgstr "Laiteohjelmiston päivitysten tarkistus"

msgctxt "name"
msgid "Firmware Updater"
msgstr ""

msgctxt "@label"
msgid "Firmware can not be updated because the connection with the printer does not support upgrading firmware."
msgstr ""

msgctxt "@label"
msgid "Firmware can not be updated because there is no connection with the printer."
msgstr ""

msgctxt "@label"
msgid "Firmware is the piece of software running directly on your 3D printer. This firmware controls the step motors, regulates the temperature and ultimately makes your printer work."
msgstr "Laiteohjelmisto on suoraan 3D-tulostimessa toimiva ohjelma. Laiteohjelmisto ohjaa askelmoottoreita, säätää lämpötilaa ja saa tulostimen toimimaan."

msgctxt "@label"
msgid "Firmware update completed."
msgstr "Laiteohjelmiston päivitys suoritettu."

msgctxt "@label"
msgid "Firmware update failed due to an communication error."
msgstr "Laiteohjelmiston päivitys epäonnistui tietoliikennevirheen takia."

msgctxt "@label"
msgid "Firmware update failed due to an input/output error."
msgstr "Laiteohjelmiston päivitys epäonnistui tiedoston lukemiseen tai kirjoittamiseen liittyvän virheen takia."

msgctxt "@label"
msgid "Firmware update failed due to an unknown error."
msgstr "Laiteohjelmiston päivitys epäonnistui tuntemattoman virheen takia."

msgctxt "@label"
msgid "Firmware update failed due to missing firmware."
msgstr "Laiteohjelmiston päivitys epäonnistui puuttuvan laiteohjelmiston takia."

msgctxt "@label"
msgid "Firmware version"
msgstr "Laiteohjelmistoversio"

msgctxt "@label"
msgid "First available"
msgstr ""

msgctxt "@label:listbox"
msgid "Flow"
msgstr ""

msgctxt "@text In the UI this is followed by a list of steps the user needs to take."
msgid "Follow the following steps to load the new material profiles to your printer."
msgstr ""

msgctxt "@info"
msgid "Follow the procedure to add a new printer"
msgstr ""

msgctxt "@text"
msgid "Following a few simple steps, you will be able to synchronize all your material profiles with your printers."
msgstr ""

msgctxt "@label"
msgid "Font"
msgstr "Fontti"

msgctxt "@label"
msgid "For every position; insert a piece of paper under the nozzle and adjust the print build plate height. The print build plate height is right when the paper is slightly gripped by the tip of the nozzle."
msgstr "Laita paperinpala kussakin positiossa suuttimen alle ja säädä tulostusalustan korkeus. Tulostusalustan korkeus on oikea, kun suuttimen kärki juuri ja juuri osuu paperiin."

msgctxt "@info:tooltip"
msgid "For lithophanes a simple logarithmic model for translucency is available. For height maps the pixel values correspond to heights linearly."
msgstr ""

msgctxt "@info:tooltip"
msgid "For lithophanes dark pixels should correspond to thicker locations in order to block more light coming through. For height maps lighter pixels signify higher terrain, so lighter pixels should correspond to thicker locations in the generated 3D model."
msgstr ""

msgctxt "@option:check"
msgid "Force layer view compatibility mode (restart required)"
msgstr "Pakota kerrosnäkymän yhteensopivuustila (vaatii uudelleenkäynnistyksen)"

msgctxt "@action:inmenu menubar:view"
msgid "Front View"
msgstr ""

msgctxt "@info:tooltip"
msgid "Front View"
msgstr ""

msgctxt "@item:inlistbox"
msgid "G File"
msgstr "G File -tiedosto"

msgctxt "@info:title"
msgid "G-code Details"
msgstr "G-coden tiedot"

msgctxt "@item:inlistbox"
msgid "G-code File"
msgstr "GCode-tiedosto"

msgctxt "name"
msgid "G-code Profile Reader"
msgstr ""

msgctxt "name"
msgid "G-code Reader"
msgstr "GCode-lukija"

msgctxt "name"
msgid "G-code Writer"
msgstr ""

msgctxt "@label"
msgid "G-code flavor"
msgstr ""

msgctxt "@label Description for application component"
msgid "G-code generator"
msgstr ""

msgctxt "@error:not supported"
msgid "GCodeGzWriter does not support text mode."
msgstr ""

msgctxt "@error:not supported"
msgid "GCodeWriter does not support non-text mode."
msgstr ""

msgctxt "@item:inlistbox"
msgid "GIF Image"
msgstr "GIF-kuva"

msgctxt "@label Description for application dependency"
msgid "GUI framework"
msgstr "GUI-kehys"

msgctxt "@label Description for application dependency"
msgid "GUI framework bindings"
msgstr "GUI-kehyksen sidonnat"

msgctxt "@label"
msgid "Gantry Height"
msgstr ""

msgctxt "@title:tab"
msgid "General"
msgstr "Yleiset"

msgctxt "@label"
msgid "Generate structures to support parts of the model which have overhangs. Without these structures, these parts would collapse during printing."
msgstr "Muodosta rakenteita, jotka tukevat mallin ulokkeita sisältäviä osia. Ilman tukirakenteita kyseiset osat luhistuvat tulostuksen aikana."

msgctxt "@label Description for development tool"
msgid "Generating Windows installers"
msgstr ""

msgctxt "@label:category menu label"
msgid "Generic"
msgstr ""

msgctxt "@option:check"
msgid "Get notifications for plugin updates"
msgstr ""

msgctxt "@action"
msgid "Get started"
msgstr ""

msgctxt "@title:tab"
msgid "Global Settings"
msgstr "Yleiset asetukset"

msgctxt "@label Description for application component"
msgid "Graphical user interface"
msgstr "Graafinen käyttöliittymä"

msgctxt "@label"
msgid "Grid Placement"
msgstr ""

#, python-brace-format
msgctxt "@label"
msgid "Group #{group_nr}"
msgstr ""

msgctxt "@tooltip of pre-heat"
msgid "Heat the bed in advance before printing. You can continue adjusting your print while it is heating, and you won't have to wait for the bed to heat up when you're ready to print."
msgstr "Lämmitä pöytä ennen tulostusta. Voit edelleen säätää tulostinta sen lämmitessä, eikä sinun tarvitse odottaa pöydän lämpiämistä, kun olet valmis tulostamaan."

msgctxt "@tooltip of pre-heat"
msgid "Heat the hotend in advance before printing. You can continue adjusting your print while it is heating, and you won't have to wait for the hotend to heat up when you're ready to print."
msgstr ""

msgctxt "@label"
msgid "Heated Build Plate (official kit or self-built)"
msgstr "Lämmitettävä alusta (virallinen sarja tai itse rakennettu)"

msgctxt "@label"
msgid "Heated bed"
msgstr ""

msgctxt "@label"
msgid "Heated build volume"
msgstr ""

msgctxt "@action:label"
msgid "Height (mm)"
msgstr "Korkeus (mm)"

msgctxt "@label"
msgid "Helpers"
msgstr ""

msgctxt "@label"
msgid "Hide all connected printers"
msgstr ""

msgctxt "@action:menu"
msgid "Hide this setting"
msgstr "Piilota tämä asetus"

msgctxt "@info:tooltip"
msgid "Highlight missing or extraneous surfaces of the model using warning signs. The toolpaths will often be missing parts of the intended geometry."
msgstr ""

msgctxt "@info:tooltip"
msgid "Highlight unsupported areas of the model in red. Without support these areas will not print properly."
msgstr "Korosta mallin vailla tukea olevat alueet punaisella. Ilman tukea nämä alueet eivät tulostu kunnolla."

msgctxt "@button"
msgid "How to load new material profiles to my printer"
msgstr ""

msgctxt "@action:button"
msgid "How to update"
msgstr ""

msgctxt "@text:window"
msgid "I don't want to send anonymous data"
msgstr ""

msgctxt "@label:status"
msgid "Idle"
msgstr ""

msgctxt "@label"
msgid "If you are trying to add a new UltiMaker printer to Cura"
msgstr ""

msgctxt "@label"
msgid "If your printer is not listed, read the <a href='%1'>network printing troubleshooting guide</a>"
msgstr "Jos tulostinta ei ole luettelossa, lue <a href='%1'>verkkotulostuksen vianetsintäopas</a>"

msgctxt "name"
msgid "Image Reader"
msgstr "Kuvanlukija"

msgctxt "@action:button"
msgid "Import"
msgstr "Tuo"

msgctxt "@title:window"
msgid "Import Material"
msgstr "Tuo materiaali"

msgctxt "@title:window"
msgid "Import Profile"
msgstr "Profiilin tuonti"

msgctxt "@action:button"
msgid "Import all as models"
msgstr "Tuo kaikki malleina"

msgctxt "@action:button"
msgid "Import models"
msgstr "Tuo mallit"

msgctxt "@label:MonitorStatus"
msgid "In maintenance. Please check the printer"
msgstr "Huolletaan. Tarkista tulostin"

msgctxt "@info"
msgid "In order to monitor your print from Cura, please connect the printer."
msgstr ""

msgctxt "@label"
msgid "In order to start using Cura you will need to configure a printer."
msgstr ""

msgctxt "@button"
msgid "In order to use the package you will need to restart Cura"
msgstr ""

msgctxt "@label"
msgid "Infill"
msgstr "Täyttö"

msgctxt "@tooltip"
msgid "Infill"
msgstr "Täyttö"

msgctxt "infill_sparse_density description"
msgid "Infill Density"
msgstr ""

msgctxt "@action:label"
msgid "Infill Pattern"
msgstr ""

msgctxt "@item:inlistbox"
msgid "Infill mesh only"
msgstr ""

msgctxt "@label"
msgid "Infill overlapping with this model is modified."
msgstr ""

msgctxt "@info:title"
msgid "Information"
msgstr "Tiedot"

msgctxt "@title"
msgid "Information"
msgstr "Tiedot"

msgctxt "@info:progress"
msgid "Initializing Active Machine..."
msgstr ""

msgctxt "@info:progress"
msgid "Initializing build volume..."
msgstr ""

msgctxt "@info:progress"
msgid "Initializing engine..."
msgstr ""

msgctxt "@info:progress"
msgid "Initializing machine manager..."
msgstr ""

msgctxt "@label"
msgid "Inner Wall"
msgstr "Sisäseinämä"

msgctxt "@tooltip"
msgid "Inner Walls"
msgstr "Sisäseinämät"

msgctxt "@text"
msgid "Insert the USB stick into your printer and launch the procedure to load new material profiles."
msgstr ""

msgctxt "@button"
msgid "Install"
msgstr ""

msgctxt "@header"
msgid "Install Materials"
msgstr ""

msgctxt "@window:title"
msgid "Install Package"
msgstr ""

msgctxt "@action:button"
msgid "Install Packages"
msgstr ""

msgctxt "@header"
msgid "Install Packages"
msgstr ""

msgctxt "@header"
msgid "Install Plugins"
msgstr ""

msgctxt "@action:button"
msgid "Install missing packages"
msgstr ""

msgctxt "@title"
msgid "Install missing packages"
msgstr ""

msgctxt "@label"
msgid "Install missing packages from project file."
msgstr ""

msgctxt "@button"
msgid "Install pending updates"
msgstr ""

msgctxt "@label"
msgid "Installed Materials"
msgstr ""

msgctxt "@label"
msgid "Installed Plugins"
msgstr ""

msgctxt "@button"
msgid "Installing..."
msgstr ""

msgctxt "@action:label"
msgid "Intent"
msgstr ""

msgctxt "@label"
msgid "Interface"
msgstr "Käyttöliittymä"

msgctxt "@label Description for application component"
msgid "Interprocess communication library"
msgstr "Prosessien välinen tietoliikennekirjasto"

msgctxt "@title:window"
msgid "Invalid IP address"
msgstr ""

msgctxt "@info:status"
msgid "Invalid file URL:"
msgstr ""

msgctxt "@action:button"
msgid "Invert the direction of camera zoom."
msgstr "Käännä kameran zoomin suunta päinvastaiseksi."

msgctxt "@label"
msgid "Is printed as support."
msgstr ""

msgctxt "@text"
msgid "It seems like you don't have any compatible printers connected to Digital Factory. Make sure your printer is connected and it's running the latest firmware."
msgstr ""

msgctxt "@item:inlistbox"
msgid "JPEG Image"
msgstr "JPEG-kuva"

msgctxt "@item:inlistbox"
msgid "JPG Image"
msgstr "JPG-kuva"

msgctxt "@label Description for application dependency"
msgid "JSON parser"
msgstr ""

msgctxt "@label"
msgid "Job Name"
msgstr "Työn nimi"

msgctxt "@label"
msgid "Jog Distance"
msgstr ""

msgctxt "@label"
msgid "Jog Position"
msgstr ""

msgctxt "@option:discardOrKeep"
msgid "Keep and never ask again"
msgstr "Säilytä äläkä kysy uudelleen"

msgctxt "@action:button"
msgid "Keep changes"
msgstr ""

msgctxt "@action:button"
msgid "Keep printer configurations"
msgstr ""

msgctxt "@action:menu"
msgid "Keep this setting visible"
msgstr "Pidä tämä asetus näkyvissä"

msgctxt "@label The argument is a timestamp"
msgid "Last update: %1"
msgstr ""

msgctxt "@label:listbox"
msgid "Layer Thickness"
msgstr ""

msgctxt "@item:inlistbox"
msgid "Layer view"
msgstr "Kerrosnäkymä"

msgctxt "@button:label"
msgid "Learn More"
msgstr ""

msgctxt "@button"
msgid "Learn how to connect your printer to Digital Factory"
msgstr ""

msgctxt "@tooltip:button"
msgid "Learn how to get started with UltiMaker Cura."
msgstr ""

msgctxt "@action"
msgid "Learn more"
msgstr ""

msgctxt "@action:button"
msgid "Learn more"
msgstr ""

msgctxt "@button"
msgid "Learn more"
msgstr ""

msgctxt "@button:label"
msgid "Learn more"
msgstr ""

msgctxt "@action:button"
msgid "Learn more about Cura print profiles"
msgstr ""

msgctxt "@button"
msgid "Learn more about adding printers to Cura"
msgstr ""

msgctxt "@label"
msgid "Learn more about project packages."
msgstr ""

msgctxt "@action:inmenu menubar:view"
msgid "Left Side View"
msgstr ""

msgctxt "@info:tooltip"
msgid "Left View"
msgstr ""

msgctxt "name"
msgid "Legacy Cura Profile Reader"
msgstr "Aikaisempien Cura-profiilien lukija"

msgctxt "@tooltip:button"
msgid "Let developers know that something is going wrong."
msgstr ""

msgctxt "@action"
msgid "Level build plate"
msgstr "Tasaa alusta"

msgctxt "@item:inlistbox"
msgid "Lighter is higher"
msgstr "Vaaleampi on korkeampi"

msgctxt "@label:listbox"
msgid "Line Type"
msgstr "Linjojen tyyppi"

msgctxt "@label:listbox"
msgid "Line Width"
msgstr ""

msgctxt "@item:inlistbox"
msgid "Linear"
msgstr ""

msgctxt "@label Description for development tool"
msgid "Linux cross-distribution application deployment"
msgstr ""

msgctxt "@label"
msgid "Load %3 as material %1 (This cannot be overridden)."
msgstr ""

msgctxt "@button"
msgid "Load more"
msgstr ""

msgctxt "@button"
msgid "Loading"
msgstr ""

msgctxt "@action:warning"
msgid "Loading a project will clear all models on the build plate."
msgstr ""

msgctxt "@label"
msgid "Loading available configurations from the printer..."
msgstr ""

msgctxt "@info:progress"
msgid "Loading interface..."
msgstr "Ladataan käyttöliittymää..."

msgctxt "@info:progress"
msgid "Loading machines..."
msgstr "Ladataan laitteita..."

msgctxt "@label:status"
msgid "Loading..."
msgstr ""

msgctxt "@title"
msgid "Loading..."
msgstr ""

msgctxt "@label:category menu label"
msgid "Local printers"
msgstr ""

msgctxt "@info:title"
msgid "Log-in failed"
msgstr ""

msgctxt "@info:title"
msgid "Login failed"
msgstr ""

msgctxt "@title:groupbox"
msgid "Logs"
msgstr ""

msgctxt "description"
msgid "Logs certain events so that they can be used by the crash reporter"
msgstr ""

msgctxt "@label:MonitorStatus"
msgid "Lost connection with the printer"
msgstr "Yhteys tulostimeen menetetty"

msgctxt "@action"
msgid "Machine Settings"
msgstr "Laitteen asetukset"

msgctxt "name"
msgid "Machine Settings Action"
msgstr ""

msgctxt "@backuplist:label"
msgid "Machines"
msgstr ""

msgctxt "@text"
msgid "Make sure all your printers are turned ON and connected to Digital Factory."
msgstr ""

msgctxt "@info:generic"
msgid "Make sure the g-code is suitable for your printer and printer configuration before sending the file to it. The g-code representation may not be accurate."
msgstr "Varmista, että G-code on tulostimelle ja sen tulostusasetuksille soveltuva, ennen kuin lähetät tiedoston siihen. G-coden esitys ei välttämättä ole tarkka."

msgctxt "@item:inlistbox"
msgid "Makerbot Printfile"
msgstr ""

msgctxt "name"
msgid "Makerbot Printfile Writer"
msgstr ""

msgctxt "@error"
msgid "MakerbotWriter could not save to the designated path."
msgstr ""

msgctxt "@error:not supported"
msgid "MakerbotWriter does not support text mode."
msgstr ""

msgctxt "@action:inmenu"
msgid "Manage Materials..."
msgstr "Hallitse materiaaleja..."

msgctxt "@action:inmenu menubar:printer"
msgid "Manage Pr&inters..."
msgstr "Tulostinten &hallinta..."

msgctxt "@action:inmenu menubar:profile"
msgid "Manage Profiles..."
msgstr "Profiilien hallinta..."

msgctxt "@action:inmenu"
msgid "Manage Setting Visibility..."
msgstr ""

msgctxt "@item:inmenu"
msgid "Manage backups"
msgstr ""

msgctxt "@label link to connect manager"
msgid "Manage in browser"
msgstr ""

msgctxt "@header"
msgid "Manage packages"
msgstr ""

msgctxt "@info:tooltip"
msgid "Manage packages"
msgstr ""

msgctxt "@action"
msgid "Manage print jobs"
msgstr ""

msgctxt "@label link to Connect and Cloud interfaces"
msgid "Manage printer"
msgstr ""

msgctxt "@button"
msgid "Manage printers"
msgstr ""

msgctxt "@text"
msgid "Manage your UltiMaker Cura plugins and material profiles here. Make sure to keep your plugins up to date and backup your setup regularly."
msgstr ""

msgctxt "description"
msgid "Manages extensions to the application and allows browsing extensions from the UltiMaker website."
msgstr ""

msgctxt "description"
msgid "Manages network connections to UltiMaker networked printers."
msgstr ""

msgctxt "@label"
msgid "Manufacturer"
msgstr ""

msgctxt "@action:button"
msgid "Marketplace"
msgstr ""

msgctxt "@label"
msgid "Marketplace"
msgstr ""

msgctxt "name"
msgid "Marketplace"
msgstr ""

msgctxt "@action:label"
msgid "Material"
msgstr ""

msgctxt "@label"
msgid "Material"
msgstr "Materiaali"

msgctxt "@label:category menu label"
msgid "Material"
msgstr ""

msgctxt "@label:listbox"
msgid "Material Color"
msgstr "Materiaalin väri"

msgctxt "name"
msgid "Material Profiles"
msgstr "Materiaaliprofiilit"

msgctxt "@label"
msgid "Material Type"
msgstr "Materiaalin tyyppi"

msgctxt "@title"
msgid "Material color picker"
msgstr ""

msgctxt "@label"
msgid "Material estimation"
msgstr ""

msgctxt "@title:header"
msgid "Material profiles successfully synced with the following printers:"
msgstr ""

msgctxt "@action:label"
msgid "Material settings"
msgstr "Materiaaliasetukset"

msgctxt "@backuplist:label"
msgid "Materials"
msgstr ""

msgctxt "@button"
msgid "Materials"
msgstr ""

msgctxt "@title:tab"
msgid "Materials"
msgstr "Materiaalit"

msgctxt "@label"
msgid "Materials compatible with active printer:"
msgstr ""

msgctxt "@label"
msgid "Mesh Type"
msgstr ""

msgctxt "@action:label"
msgid "Mode"
msgstr "Tila"

msgctxt "name"
msgid "Model Checker"
msgstr ""

msgctxt "@info:title"
msgid "Model Errors"
msgstr ""

msgctxt "@item:inmenu"
msgid "Modify G-Code"
msgstr ""

msgctxt "@label"
msgid "Modify settings for overlaps"
msgstr ""

msgctxt "@item:inmenu"
msgid "Monitor"
msgstr ""

msgctxt "name"
msgid "Monitor Stage"
msgstr ""

msgctxt "@action:button"
msgid "Monitor print"
msgstr ""

msgctxt "@tooltip:button"
msgid "Monitor print jobs and reprint from your print history."
msgstr ""

msgctxt "@tooltip:button"
msgid "Monitor printers in Ultimaker Digital Factory."
msgstr ""

msgctxt "@info"
msgid "Monitor your printers from everywhere using Ultimaker Digital Factory"
msgstr ""

msgctxt "@title:window"
msgid "More information on anonymous data collection"
msgstr ""

msgctxt "@window:title"
msgid "Move print job to top"
msgstr ""

msgctxt "@action:button"
msgid "Move to Next Position"
msgstr "Siirry seuraavaan positioon"

msgctxt "@label"
msgid "Move to top"
msgstr ""

msgctxt "@info:tooltip"
msgid "Moves the camera so the model is in the center of the view when a model is selected"
msgstr "Siirtää kameraa siten, että valittuna oleva malli on näkymän keskellä"

msgctxt "@action:inmenu menubar:edit"
msgid "Multiply Selected"
msgstr ""

msgctxt "@title:window"
msgid "Multiply Selected Model"
msgid_plural "Multiply Selected Models"
msgstr[0] "Kerro valittu malli"
msgstr[1] "Kerro valitut mallit"

msgctxt "@info"
msgid "Multiply selected item and place them in a grid of build plate."
msgstr ""

msgctxt "@info:status"
msgid "Multiplying and placing objects"
msgstr "Kappaleiden kertominen ja sijoittelu"

msgctxt "@title"
msgid "My Backups"
msgstr ""

msgctxt "@label:button"
msgid "My printers"
msgstr ""

msgctxt "@action:label"
msgid "Name"
msgstr "Nimi"

msgctxt "@label:category menu label"
msgid "Network enabled printers"
msgstr ""

msgctxt "@info:title"
msgid "Network error"
msgstr ""

#, python-format
msgctxt "@info:title The %s gets replaced with the printer name."
msgid "New %s stable firmware available"
msgstr ""

msgctxt "@textfield:placeholder"
msgid "New Custom Profile"
msgstr ""

msgctxt "@label"
msgid "New UltiMaker printers can be connected to Digital Factory and monitored remotely."
msgstr ""

#, python-brace-format
msgctxt "@info Don't translate {machine_name}, since it gets replaced by a printer name!"
msgid "New features or bug-fixes may be available for your {machine_name}! If you haven't done so already, it is recommended to update the firmware on your printer to version {latest_version}."
msgstr ""

msgctxt "@action:button"
msgid "New materials installed"
msgstr ""

msgctxt "info:status"
msgid "New printer detected from your Ultimaker account"
msgid_plural "New printers detected from your Ultimaker account"
msgstr[0] ""
msgstr[1] ""

msgctxt "@title:window"
msgid "New project"
msgstr "Uusi projekti"

msgctxt "@action:button"
msgid "Next"
msgstr ""

msgctxt "@button"
msgid "Next"
msgstr ""

msgctxt "@info:title"
msgid "Nightly build"
msgstr ""

msgctxt "@info"
msgid "No"
msgstr ""

msgctxt "@info"
msgid "No compatibility information"
msgstr ""

msgctxt "@description"
msgid "No compatible printers, that are currently online, were found."
msgstr ""

msgctxt "@label"
msgid "No cost estimation available"
msgstr ""

#, python-brace-format
msgctxt "@info:status Don't translate the XML tags <filename>!"
msgid "No custom profile to import in file <filename>{0}</filename>"
msgstr ""

msgctxt "@label"
msgid "No items to select from"
msgstr ""

msgctxt "@info:title"
msgid "No layers to show"
msgstr ""

msgctxt "@message"
msgid "No more results to load"
msgstr ""

msgctxt "@error:zip"
msgid "No permission to write the workspace here."
msgstr ""

msgctxt "@title:header"
msgid "No printers found"
msgstr ""

msgctxt "@label"
msgid "No printers found in your account?"
msgstr ""

msgctxt "@message:text %1 is the name the printer uses for 'nozzle'."
msgid "No profiles are available for the selected material/%1 configuration. Please change your configuration."
msgstr ""

msgctxt "@message"
msgid "No results found with current filter"
msgstr ""

msgctxt "@label"
msgid "No time estimation available"
msgstr ""

msgctxt "@button"
msgid "Non UltiMaker printer"
msgstr ""

msgctxt "@info No materials"
msgid "None"
msgstr ""

msgctxt "@label"
msgid "Normal model"
msgstr ""

msgctxt "@info:title"
msgid "Not a group host"
msgstr ""

msgctxt "@label:MonitorStatus"
msgid "Not connected to a printer"
msgstr "Ei yhteyttä tulostimeen"

msgctxt "@action:label"
msgid "Not in profile"
msgstr "Ei profiilissa"

msgctxt "@menuitem"
msgid "Not overridden"
msgstr ""

msgctxt "@info:not supported profile"
msgid "Not supported"
msgstr ""

msgctxt "@label"
msgid "Not yet initialized"
msgstr ""

msgctxt "@info:status"
msgid "Nothing is shown because you need to slice first."
msgstr ""

msgctxt "@label"
msgid "Nozzle"
msgstr "Suutin"

msgctxt "@title:label"
msgid "Nozzle Settings"
msgstr ""

msgctxt "@label"
msgid "Nozzle offset X"
msgstr "Suuttimen X-siirtymä"

msgctxt "@label"
msgid "Nozzle offset Y"
msgstr "Suuttimen Y-siirtymä"

msgctxt "@label"
msgid "Nozzle size"
msgstr "Suuttimen koko"

msgctxt "@label"
msgid "Number of Copies"
msgstr "Kopioiden määrä"

msgctxt "@label"
msgid "Number of Extruders"
msgstr "Suulakkeiden määrä"

msgctxt "@action:button"
msgid "OK"
msgstr "OK"

msgctxt "@label"
msgid "OS language"
msgstr ""

msgctxt "@label"
msgid "Object list"
msgstr ""

msgctxt "@label:Should be short"
msgid "Off"
msgstr ""

msgctxt "@label:Should be short"
msgid "On"
msgstr ""

msgctxt "@label"
msgid "Only Show Top Layers"
msgstr "Näytä vain yläkerrokset"

#, python-brace-format
msgctxt "@info:status"
msgid "Only one G-code file can be loaded at a time. Skipped importing {0}"
msgstr "Vain yksi G-code-tiedosto voidaan ladata kerralla. Tiedoston {0} tuonti ohitettiin."

msgctxt "@action:button"
msgid "Open"
msgstr "Avaa"

msgctxt "@title:menu menubar:file"
msgid "Open &Recent"
msgstr "Avaa &viimeisin"

msgctxt "@item:inlistbox 'Open' is part of the name of this file format."
msgid "Open Compressed Triangle Mesh"
msgstr ""

msgctxt "@title:window"
msgid "Open File(s)"
msgstr "Avaa tiedosto(t)"

msgctxt "@title:menu menubar:file"
msgid "Open File(s)..."
msgstr ""

msgctxt "@title:window"
msgid "Open Project"
msgstr "Avaa projekti"

msgctxt "@info:title"
msgid "Open Project File"
msgstr ""

msgctxt "@action:label"
msgid "Open With"
msgstr ""

msgctxt "@action:button"
msgid "Open as project"
msgstr "Avaa projektina"

msgctxt "@title:window"
msgid "Open file(s)"
msgstr "Avaa tiedosto(t)"

msgctxt "@action:button"
msgid "Open project anyway"
msgstr ""

msgctxt "@title:window"
msgid "Open project file"
msgstr "Avaa projektitiedosto"

msgctxt "@label OpenGL version"
msgid "OpenGL"
msgstr ""

msgctxt "@label"
msgid "Opening and saving files"
msgstr "Tiedostojen avaaminen ja tallentaminen"

msgctxt "@header"
msgid "Optimized for Air Manager"
msgstr ""

msgctxt "@label"
msgid "Origin at center"
msgstr ""

msgid "Orthographic"
msgstr ""

msgctxt "@action:inmenu menubar:view"
msgid "Orthographic"
msgstr ""

msgctxt "@tooltip"
msgid "Other"
msgstr "Muu"

msgctxt "@label"
msgid "Other models overlapping with this model are modified."
msgstr ""

msgctxt "@label"
msgid "Other printers"
msgstr ""

msgctxt "@tooltip"
msgid "Outer Wall"
msgstr "Ulkoseinämä"

msgctxt "@label"
msgid "Overlaps with this model are not supported."
msgstr ""

msgctxt "@action:button"
msgid "Override"
msgstr ""

msgctxt "@label"
msgid "Override will use the specified settings with the existing printer configuration. This may result in a failed print."
msgstr ""

msgctxt "@label %1 is the number of settings it overrides."
msgid "Overrides %1 setting."
msgid_plural "Overrides %1 settings."
msgstr[0] ""
msgstr[1] ""

msgctxt "@title:menu menubar:toplevel"
msgid "P&references"
msgstr "L&isäasetukset"

msgctxt "@item:inlistbox"
msgid "PNG Image"
msgstr "PNG-kuva"

msgctxt "@header"
msgid "Package details"
msgstr ""

msgctxt "@label Description for development tool"
msgid "Packaging Python-applications"
msgstr ""

msgctxt "@info:status"
msgid "Parsing G-code"
msgstr "G-coden jäsennys"

msgctxt "@action:inmenu menubar:edit"
msgid "Paste from clipboard"
msgstr ""

msgctxt "@label"
msgid "Pause"
msgstr ""

msgctxt "@label:MonitorStatus"
msgid "Paused"
msgstr "Keskeytetty"

msgctxt "@label:status"
msgid "Paused"
msgstr ""

msgctxt "@label"
msgid "Pausing..."
msgstr ""

msgctxt "@label:status"
msgid "Pausing..."
msgstr ""

msgctxt "@label"
msgid "Per Model Settings"
msgstr "Mallikohtaiset asetukset"

msgctxt "name"
msgid "Per Model Settings Tool"
msgstr "Mallikohtaisten asetusten työkalu"

msgid "Perspective"
msgstr ""

msgctxt "@action:inmenu menubar:view"
msgid "Perspective"
msgstr ""

msgctxt "@action:label"
msgid "Placement"
msgstr ""

msgctxt "@info:title"
msgid "Placing Object"
msgstr "Sijoitetaan kappaletta"

msgctxt "@info:title"
msgid "Placing Objects"
msgstr ""

msgctxt "@label Type of platform"
msgid "Platform"
msgstr ""

msgctxt "@info"
msgid "Please connect your printer to the network."
msgstr ""

msgctxt "@text"
msgid "Please enter a valid IP address."
msgstr ""

msgctxt "@message"
msgid "Please give the required permissions when authorizing this application."
msgstr ""

msgctxt "@info"
msgid ""
"Please make sure your printer has a connection:\n"
"- Check if the printer is turned on.\n"
"- Check if the printer is connected to the network.\n"
"- Check if you are signed in to discover cloud-connected printers."
msgstr ""

msgctxt "@text"
msgid "Please name your printer"
msgstr ""

msgctxt "@warning:status"
msgid "Please prepare G-code before exporting."
msgstr ""

msgctxt "@info"
msgid "Please provide a name for this profile."
msgstr ""

msgctxt "@info"
msgid "Please provide a new name."
msgstr ""

msgctxt "@text"
msgid "Please read and agree with the plugin licence."
msgstr ""

msgctxt "@label:MonitorStatus"
msgid "Please remove the print"
msgstr "Poista tuloste"

msgctxt "@info:status"
msgid ""
"Please review settings and check if your models:\n"
"- Fit within the build volume\n"
"- Are assigned to an enabled extruder\n"
"- Are not all set as modifier meshes"
msgstr ""

msgctxt "@label"
msgid "Please select any upgrades made to this UltiMaker Original"
msgstr "Valitse tähän UltiMaker Original -laitteeseen tehdyt päivitykset"

msgctxt "@description"
msgid "Please sign in to get verified plugins and materials for UltiMaker Cura Enterprise"
msgstr ""

msgctxt "@action:button"
msgid "Please sync the material profiles with your printers before starting to print."
msgstr ""

msgctxt "@info"
msgid "Please update your printer's firmware to manage the queue remotely."
msgstr ""

msgctxt "@info:status"
msgid "Please wait until the current job has been sent."
msgstr ""

msgctxt "@title:window"
msgid "Plugin License Agreement"
msgstr "Lisäosan lisenssisopimus"

msgctxt "@button"
msgid "Plugin license agreement"
msgstr ""

msgctxt "@backuplist:label"
msgid "Plugins"
msgstr ""

msgctxt "@button"
msgid "Plugins"
msgstr ""

msgctxt "@label Description for application dependency"
msgid "Polygon clipping library"
msgstr "Monikulmion leikkauskirjasto"

msgctxt "@label Description for application component"
msgid "Polygon packing library, developed by Prusa Research"
msgstr ""

msgctxt "@item:inmenu"
msgid "Post Processing"
msgstr ""

msgctxt "name"
msgid "Post Processing"
msgstr "Jälkikäsittely"

msgctxt "@title:window"
msgid "Post Processing Plugin"
msgstr "Jälkikäsittelylisäosa"

msgctxt "@label"
msgid "Post Processing Scripts"
msgstr "Jälkikäsittelykomentosarjat"

msgctxt "@button"
msgid "Pre-heat"
msgstr "Esilämmitä"

msgctxt "@item:inmenu"
msgid "Prepare"
msgstr ""

msgctxt "name"
msgid "Prepare Stage"
msgstr ""

msgctxt "@label:MonitorStatus"
msgid "Preparing..."
msgstr "Valmistellaan..."

msgctxt "@label:status"
msgid "Preparing..."
msgstr ""

msgctxt "@label"
msgid "Preset printers"
msgstr ""

msgctxt "@button"
msgid "Preview"
msgstr ""

msgctxt "@item:inmenu"
msgid "Preview"
msgstr ""

msgctxt "name"
msgid "Preview Stage"
msgstr ""

msgctxt "@tooltip"
msgid "Prime Tower"
msgstr ""

msgctxt "@action:button"
msgid "Print"
msgstr "Tulosta"

msgctxt "@label"
msgid "Print Selected Model With:"
msgid_plural "Print Selected Models With:"
msgstr[0] "Tulosta valittu malli asetuksella:"
msgstr[1] "Tulosta valitut mallit asetuksella:"

msgctxt "@label %1 is filled in with the name of an extruder"
msgid "Print Selected Model with %1"
msgid_plural "Print Selected Models with %1"
msgstr[0] "Tulosta valittu malli asetuksella %1"
msgstr[1] "Tulosta valitut mallit asetuksella %1"

msgctxt "@label"
msgid "Print as support"
msgstr ""

msgctxt "@info:title"
msgid "Print error"
msgstr ""

msgctxt "@message"
msgid "Print in Progress"
msgstr ""

msgctxt "@info:status"
msgid "Print job queue is full. The printer can't accept a new job."
msgstr ""

msgctxt "@info:status"
msgid "Print job was successfully sent to the printer."
msgstr ""

msgctxt "@label"
msgid "Print jobs"
msgstr ""

msgctxt "@label:button"
msgid "Print jobs"
msgstr ""

msgctxt "@action:button Preceded by 'Ready to'."
msgid "Print over network"
msgstr "Tulosta verkon kautta"

msgctxt "@properties:tooltip"
msgid "Print over network"
msgstr "Tulosta verkon kautta"

msgctxt "@title:window"
msgid "Print over network"
msgstr "Tulosta verkon kautta"

msgctxt "@label"
msgid "Print settings"
msgstr "Tulostusasetukset"

msgctxt "@label shown when we load a Gcode file"
msgid "Print setup disabled. G-code file can not be modified."
msgstr ""

msgctxt "@action:button Preceded by 'Ready to'."
msgid "Print via USB"
msgstr "Tulosta USB:n kautta"

msgctxt "@info:tooltip"
msgid "Print via USB"
msgstr "Tulosta USB:n kautta"

msgctxt "@action:button"
msgid "Print via cloud"
msgstr ""

msgctxt "@properties:tooltip"
msgid "Print via cloud"
msgstr ""

msgctxt "@action:label"
msgid "Print with"
msgstr ""

msgctxt "@title:tab"
msgid "Printer"
msgstr "Tulostin"

msgctxt "@title:window"
msgid "Printer Address"
msgstr "Tulostimen osoite"

msgctxt "@action:label"
msgid "Printer Group"
msgstr ""

msgctxt "@title:label"
msgid "Printer Settings"
msgstr ""

msgctxt "@label"
msgid "Printer control"
msgstr ""

msgctxt "@label:MonitorStatus"
msgid "Printer does not accept commands"
msgstr "Tulostin ei hyväksy komentoja"

msgctxt "@label"
msgid "Printer name"
msgstr ""

msgctxt "@label"
msgid "Printer selection"
msgstr ""

msgctxt "@action:label"
msgid "Printer settings"
msgstr "Tulostimen asetukset"

msgctxt "@info:tooltip"
msgid "Printer settings will be updated to match the settings saved with the project."
msgstr ""

msgctxt "@title:tab"
msgid "Printers"
msgstr "Tulostimet"

msgctxt "info:status"
msgid "Printers added from Digital Factory:"
msgstr ""

msgctxt "@text Asking the user whether printers are missing in a list."
msgid "Printers missing?"
msgstr ""

msgctxt "@title:label"
msgid "Printhead Settings"
msgstr ""

msgctxt "@label:status"
msgid "Printing"
msgstr "Tulostetaan"

msgctxt "@label"
msgid "Printing Time"
msgstr "Tulostusaika"

msgctxt "@label:MonitorStatus"
msgid "Printing..."
msgstr "Tulostetaan..."

msgctxt "@label"
msgid "Privacy"
msgstr "Tietosuoja"

msgctxt "@button"
msgid "Processing"
msgstr ""

msgctxt "@info:status"
msgid "Processing Layers"
msgstr "Käsitellään kerroksia"

msgctxt "@label"
msgid "Profile"
msgstr ""

msgctxt "@title:column"
msgid "Profile"
msgstr "Profiili"

msgctxt "@label"
msgid "Profile author"
msgstr ""

msgctxt "@info:status"
msgid "Profile is missing a quality type."
msgstr "Profiilista puuttuu laatutyyppi."

msgctxt "@action:label"
msgid "Profile settings"
msgstr "Profiilin asetukset"

msgctxt "@title:column"
msgid "Profile settings"
msgstr "Profiilin asetukset"

#, python-brace-format
msgctxt "@info:status"
msgid "Profile {0} has an unknown file type or is corrupted."
msgstr "Profiililla {0} on tuntematon tiedostotyyppi tai se on vioittunut."

msgctxt "@backuplist:label"
msgid "Profiles"
msgstr ""

msgctxt "@label"
msgid "Profiles"
msgstr ""

msgctxt "@title:tab"
msgid "Profiles"
msgstr "Profiilit"

msgctxt "@label"
msgid "Profiles compatible with active printer:"
msgstr ""

msgctxt "@label Description for application dependency"
msgid "Programming language"
msgstr "Ohjelmointikieli"

#, python-brace-format
msgctxt "@info:status Don't translate the XML tags <filename> or <message>!"
msgid "Project file <filename>{0}</filename> contains an unknown machine type <message>{1}</message>. Cannot import the machine. Models will be imported instead."
msgstr ""

#, python-brace-format
msgctxt "@info:error Don't translate the XML tags <filename> or <message>!"
msgid "Project file <filename>{0}</filename> is corrupt: <message>{1}</message>."
msgstr ""

#, python-brace-format
msgctxt "@info:error Don't translate the XML tag <filename>!"
msgid "Project file <filename>{0}</filename> is made using profiles that are unknown to this version of UltiMaker Cura."
msgstr ""

#, python-brace-format
msgctxt "@info:error Don't translate the XML tags <filename> or <message>!"
msgid "Project file <filename>{0}</filename> is suddenly inaccessible: <message>{1}</message>."
msgstr ""

msgctxt "@label"
msgid "Properties"
msgstr "Ominaisuudet"

msgctxt "description"
msgid "Provides a machine actions for updating firmware."
msgstr ""

msgctxt "description"
msgid "Provides a monitor stage in Cura."
msgstr ""

msgctxt "description"
msgid "Provides a normal solid mesh view."
msgstr "Näyttää normaalin kiinteän verkkonäkymän."

msgctxt "description"
msgid "Provides a prepare stage in Cura."
msgstr ""

msgctxt "description"
msgid "Provides a preview stage in Cura."
msgstr ""

msgctxt "description"
msgid "Provides a way to change machine settings (such as build volume, nozzle size, etc.)."
msgstr ""

msgctxt "description"
msgid "Provides capabilities to read and write XML-based material profiles."
msgstr "Mahdollistaa XML-pohjaisten materiaaliprofiilien lukemisen ja kirjoittamisen."

msgctxt "description"
msgid "Provides machine actions for Ultimaker machines (such as bed leveling wizard, selecting upgrades, etc.)."
msgstr "UltiMaker-laitteiden toimintojen käyttö (esim. pöydän tasaaminen, päivitysten valinta yms.)"

msgctxt "description"
msgid "Provides removable drive hotplugging and writing support."
msgstr "Tukee irrotettavan aseman kytkemistä lennossa ja sille kirjoittamista."

msgctxt "description"
msgid "Provides support for exporting Cura profiles."
msgstr "Tukee Cura-profiilien vientiä."

msgctxt "description"
msgid "Provides support for importing Cura profiles."
msgstr "Tukee Cura-profiilien tuontia."

msgctxt "description"
msgid "Provides support for importing profiles from g-code files."
msgstr "Tukee profiilien tuontia GCode-tiedostoista."

msgctxt "description"
msgid "Provides support for importing profiles from legacy Cura versions."
msgstr "Tukee profiilien tuontia aikaisemmista Cura-versioista."

msgctxt "description"
msgid "Provides support for reading 3MF files."
msgstr "Tukee 3MF-tiedostojen lukemista."

msgctxt "description"
msgid "Provides support for reading AMF files."
msgstr ""

msgctxt "description"
msgid "Provides support for reading Ultimaker Format Packages."
msgstr ""

msgctxt "description"
msgid "Provides support for reading X3D files."
msgstr "Tukee X3D-tiedostojen lukemista."

msgctxt "description"
msgid "Provides support for reading model files."
msgstr ""

msgctxt "description"
msgid "Provides support for writing 3MF files."
msgstr "Tukee 3MF-tiedostojen kirjoittamista."

msgctxt "description"
msgid "Provides support for writing MakerBot Format Packages."
msgstr ""

msgctxt "description"
msgid "Provides support for writing Ultimaker Format Packages."
msgstr ""

msgctxt "description"
msgid "Provides the Per Model Settings."
msgstr "Mallikohtaisten asetusten muokkaus."

msgctxt "description"
msgid "Provides the X-Ray view."
msgstr "Näyttää kerrosnäkymän."

msgctxt "description"
msgid "Provides the link to the CuraEngine slicing backend."
msgstr "Linkki CuraEngine-viipalointiin taustalla."

msgctxt "description"
msgid "Provides the preview of sliced layerdata."
msgstr ""

msgctxt "@label"
msgid "PyQt version"
msgstr ""

msgctxt "@Label Description for application dependency"
msgid "Python Error tracking library"
msgstr ""

msgctxt "@label Description for application dependency"
msgid "Python bindings for Clipper"
msgstr ""

msgctxt "@label Description for application component"
msgid "Python bindings for libnest2d"
msgstr ""

msgctxt "@label"
msgid "Qt version"
msgstr ""

#, python-brace-format
msgctxt "@info:status"
msgid "Quality type '{0}' is not compatible with the current active machine definition '{1}'."
msgstr ""

msgctxt "@info:title"
msgid "Queue Full"
msgstr ""

msgctxt "@label"
msgid "Queued"
msgstr "Jonossa"

msgctxt "@info:button, %1 is the application name"
msgid "Quit %1"
msgstr ""

msgctxt "description"
msgid "Reads g-code from a compressed archive."
msgstr ""

msgctxt "@button"
msgid "Recommended"
msgstr ""

msgctxt "@title:tab"
msgid "Recommended"
msgstr "Suositeltu"

msgctxt "@label"
msgid "Recommended print settings"
msgstr ""

msgctxt "@info %1 is the name of a profile"
msgid "Recommended settings (for <b>%1</b>) were altered."
msgstr ""

msgctxt "@action:button"
msgid "Refresh"
msgstr "Päivitä"

msgctxt "@button"
msgid "Refresh"
msgstr ""

msgctxt "@label"
msgid "Refresh"
msgstr ""

msgctxt "@button"
msgid "Refresh List"
msgstr ""

msgctxt "@button"
msgid "Refreshing..."
msgstr ""

msgctxt "@label"
msgid "Release Notes"
msgstr ""

msgctxt "@action:inmenu menubar:file"
msgid "Reload All Models"
msgstr "Lataa kaikki mallit uudelleen"

msgctxt "@text:window"
msgid "Remember my choice"
msgstr "Muista valintani"

msgctxt "@item:intext"
msgid "Removable Drive"
msgstr "Siirrettävä asema"

msgctxt "name"
msgid "Removable Drive Output Device Plugin"
msgstr "Irrotettavan aseman tulostusvälineen laajennus"

msgctxt "@action:button"
msgid "Remove"
msgstr "Poista"

msgctxt "@action:button"
msgid "Remove printers"
msgstr ""

msgctxt "@title:window"
msgid "Remove printers?"
msgstr ""

msgctxt "@action:button"
msgid "Rename"
msgstr "Nimeä uudelleen"

msgctxt "@title:window"
msgid "Rename"
msgstr ""

msgctxt "@title:window"
msgid "Rename Profile"
msgstr "Nimeä profiili uudelleen"

msgctxt "@action:inmenu menubar:help"
msgid "Report a &Bug"
msgstr "Ilmoita &virheestä"

msgctxt "@label:button"
msgid "Report a bug"
msgstr ""

msgctxt "@message:button"
msgid "Report a bug"
msgstr ""

msgctxt "@message:description"
msgid "Report a bug on UltiMaker Cura's issue tracker."
msgstr ""

msgctxt "@label:status"
msgid "Requires configuration changes"
msgstr ""

msgctxt "@action:inmenu menubar:edit"
msgid "Reset All Model Positions"
msgstr "Määritä kaikkien mallien positiot uudelleen"

msgctxt "@action:inmenu menubar:edit"
msgid "Reset All Model Transformations"
msgstr "Määritä kaikkien mallien muutokset uudelleen"

msgctxt "@info"
msgid "Reset to defaults."
msgstr ""

msgctxt "@label"
msgid "Resolution"
msgstr ""

msgctxt "@button"
msgid "Restore"
msgstr ""

msgctxt "@dialog:title"
msgid "Restore Backup"
msgstr ""

msgctxt "@option:check"
msgid "Restore window position on start"
msgstr ""

msgctxt "@label"
msgid "Resume"
msgstr ""

msgctxt "@label"
msgid "Resuming..."
msgstr ""

msgctxt "@label:status"
msgid "Resuming..."
msgstr ""

msgctxt "@tooltip"
msgid "Retractions"
msgstr "Takaisinvedot"

msgctxt "@button"
msgid "Retry?"
msgstr ""

msgctxt "@action:inmenu menubar:view"
msgid "Right Side View"
msgstr ""

msgctxt "@info:tooltip"
msgid "Right View"
msgstr ""

msgctxt "@label Description for application dependency"
msgid "Root Certificates for validating SSL trustworthiness"
msgstr ""

msgctxt "@info:title"
msgid "Safely Remove Hardware"
msgstr "Poista laite turvallisesti"

msgctxt "@button"
msgid "Safety datasheet"
msgstr ""

msgctxt "@action:button"
msgid "Save"
msgstr "Tallenna"

msgctxt "@option"
msgid "Save Cura project"
msgstr ""

msgctxt "@option"
msgid "Save Cura project and print file"
msgstr ""

msgctxt "@title:window"
msgid "Save Custom Profile"
msgstr ""

msgctxt "@title:window"
msgid "Save Project"
msgstr "Tallenna projekti"

msgctxt "@title:menu menubar:file"
msgid "Save Project..."
msgstr ""

msgctxt "@action:button"
msgid "Save as new custom profile"
msgstr ""

msgctxt "@action:button"
msgid "Save changes"
msgstr ""

msgctxt "@button"
msgid "Save new profile"
msgstr ""

msgctxt "@text"
msgid "Save the .umm file on a USB stick."
msgstr ""

msgctxt "@action:button Preceded by 'Ready to'."
msgid "Save to Removable Drive"
msgstr "Tallenna siirrettävälle asemalle"

#, python-brace-format
msgctxt "@item:inlistbox"
msgid "Save to Removable Drive {0}"
msgstr "Tallenna siirrettävälle asemalle {0}"

#, python-brace-format
msgctxt "@info:status"
msgid "Saved to Removable Drive {0} as {1}"
msgstr "Tallennettu siirrettävälle asemalle {0} nimellä {1}"

msgctxt "@info:title"
msgid "Saving"
msgstr "Tallennetaan"

#, python-brace-format
msgctxt "@info:progress Don't translate the XML tags <filename>!"
msgid "Saving to Removable Drive <filename>{0}</filename>"
msgstr "Tallennetaan siirrettävälle asemalle <filename>{0}</filename>"

msgctxt "@option:check"
msgid "Scale extremely small models"
msgstr "Skaalaa erittäin pienet mallit"

msgctxt "@option:check"
msgid "Scale large models"
msgstr "Skaalaa suuret mallit"

msgctxt "@placeholder"
msgid "Search"
msgstr ""

msgctxt "@info"
msgid "Search in the browser"
msgstr ""

msgctxt "@label:textbox"
msgid "Search settings"
msgstr ""

msgctxt "@action:inmenu menubar:edit"
msgid "Select All Models"
msgstr "Valitse kaikki mallit"

msgctxt "@title:window"
msgid "Select Printer"
msgstr ""

msgctxt "@title:window"
msgid "Select Settings to Customize for this model"
msgstr "Valitse tätä mallia varten mukautettavat asetukset"

msgctxt "@text"
msgid "Select and install material profiles optimised for your UltiMaker 3D printers."
msgstr ""

msgctxt "@label"
msgid "Select configuration"
msgstr ""

msgctxt "@title:window"
msgid "Select custom firmware"
msgstr "Valitse mukautettu laiteohjelmisto"

msgctxt "@option:check"
msgid "Select models when loaded"
msgstr ""

msgctxt "@action:button"
msgid "Select settings"
msgstr "Valitse asetukset"

msgctxt "@action"
msgid "Select upgrades"
msgstr "Valitse päivitykset"

msgctxt "@label"
msgid "Select your printer from the list below:"
msgstr ""

msgctxt "@option:check"
msgid "Send (anonymous) print information"
msgstr "Lähetä (anonyymit) tulostustiedot"

msgctxt "@label"
msgid "Send G-code"
msgstr ""

msgctxt "@tooltip of G-code command input"
msgid "Send a custom G-code command to the connected printer. Press 'enter' to send the command."
msgstr ""

msgctxt "@action:button"
msgid "Send crash report to UltiMaker"
msgstr ""

msgctxt "@action:button"
msgid "Send report"
msgstr ""

msgctxt "@info:status"
msgid "Sending Print Job"
msgstr ""

msgctxt "@info:title"
msgid "Sending materials to printer"
msgstr ""

msgctxt "name"
msgid "Sentry Logger"
msgstr ""

msgctxt "@label Description for application dependency"
msgid "Serial communication library"
msgstr "Sarjatietoliikennekirjasto"

msgctxt "@action:inmenu"
msgid "Set as Active Extruder"
msgstr "Aseta aktiiviseksi suulakepuristimeksi"

msgctxt "@title:column"
msgid "Setting"
msgstr "Asetus"

msgctxt "@title:tab"
msgid "Setting Visibility"
msgstr "Näkyvyyden asettaminen"

msgctxt "@info:progress"
msgid "Setting up preferences..."
msgstr ""

msgctxt "@info:progress"
msgid "Setting up scene..."
msgstr "Asetetaan näkymää..."

msgctxt "@action:label"
msgid "Setting visibility"
msgstr "Asetusten näkyvyys"

msgctxt "@label"
msgid "Settings"
msgstr "Asetukset"

msgctxt "@title:tab"
msgid "Settings"
msgstr "Asetukset"

msgctxt "@info:message Followed by a list of settings."
msgid "Settings have been changed to match the current availability of extruders:"
msgstr ""

msgctxt "@info:title"
msgid "Settings updated"
msgstr ""

msgctxt "@text"
msgid "Share ideas and get help from 48,000+ users in the UltiMaker Community"
msgstr ""

msgctxt "@label"
msgid "Shell"
msgstr ""

msgctxt "@action:label"
msgid "Shell Thickness"
msgstr ""

msgctxt "@info:tooltip"
msgid "Should Cura check for updates when the program is started?"
msgstr "Pitäisikö Curan tarkistaa saatavilla olevat päivitykset, kun ohjelma käynnistetään?"

msgctxt "@info:tooltip"
msgid "Should Cura open at the location it was closed?"
msgstr ""

msgctxt "@info:tooltip"
msgid "Should a prefix based on the printer name be added to the print job name automatically?"
msgstr "Pitäisikö tulostustyön nimeen lisätä automaattisesti tulostimen nimeen perustuva etuliite?"

msgctxt "@info:tooltip"
msgid "Should a summary be shown when saving a project file?"
msgstr "Näytetäänkö yhteenveto, kun projektitiedosto tallennetaan?"

msgctxt "@info:tooltip"
msgid "Should an automatic check for new plugins be done every time Cura is started? It is highly recommended that you do not disable this!"
msgstr ""

msgctxt "@info:tooltip"
msgid "Should anonymous data about your print be sent to UltiMaker? Note, no models, IP addresses or other personally identifiable information is sent or stored."
msgstr "Pitäisikö anonyymejä tietoja tulosteesta lähettää UltiMakerille? Huomaa, että malleja, IP-osoitteita tai muita henkilökohtaisia tietoja ei lähetetä eikä tallenneta."

msgctxt "@info:tooltip"
msgid "Should layer be forced into compatibility mode?"
msgstr "Pakotetaanko kerros yhteensopivuustilaan?"

msgctxt "@info:tooltip"
msgid "Should models be scaled to the build volume if they are too large?"
msgstr "Pitäisikö mallit skaalata tulostustilavuuteen, jos ne ovat liian isoja?"

msgctxt "@info:tooltip"
msgid "Should models be selected after they are loaded?"
msgstr ""

msgctxt "@info:tooltip"
msgid "Should models on the platform be moved down to touch the build plate?"
msgstr "Pitäisikö tulostusalueella olevia malleja siirtää alas niin, että ne koskettavat tulostusalustaa?"

msgctxt "@info:tooltip"
msgid "Should models on the platform be moved so that they no longer intersect?"
msgstr "Pitäisikö alustalla olevia malleja siirtää niin, etteivät ne enää leikkaa toisiaan?"

msgctxt "@info:tooltip"
msgid "Should opening files from the desktop or external applications open in the same instance of Cura?"
msgstr ""

msgctxt "@info:tooltip"
msgid "Should the build plate be cleared before loading a new model in the single instance of Cura?"
msgstr ""

msgctxt "@info:tooltip"
msgid "Should the default zoom behavior of cura be inverted?"
msgstr "Pitääkö Curan oletusarvoinen zoom-toimintatapa muuttaa päinvastaiseksi?"

msgctxt "@info:tooltip"
msgid "Should zooming move in the direction of the mouse?"
msgstr "Tuleeko zoomauksen siirtyä hiiren suuntaan?"

msgctxt "@label"
msgid "Show 5 Detailed Layers On Top"
msgstr "Näytä 5 yksityiskohtaista kerrosta ylhäällä"

msgctxt "@action:inmenu menubar:help"
msgid "Show Configuration Folder"
msgstr "Näytä määrityskansio"

msgctxt "@button"
msgid "Show Custom"
msgstr ""

msgctxt "@action:inmenu menubar:help"
msgid "Show Online &Documentation"
msgstr "Näytä sähköinen &dokumentaatio"

msgctxt "@action:inmenu"
msgid "Show Online Troubleshooting"
msgstr ""

msgctxt "@label:checkbox"
msgid "Show all"
msgstr "Näytä kaikki"

msgctxt "@label"
msgid "Show all connected printers"
msgstr ""

msgctxt "@info:tooltip"
msgid "Show an icon and notifications in the system notification area."
msgstr ""

msgctxt "@info:tooltip"
msgid "Show caution message in g-code reader."
msgstr ""

msgctxt "@action:button"
msgid "Show configuration folder"
msgstr ""

msgctxt "@action:button"
msgid "Show detailed crash report"
msgstr ""

msgctxt "@option:check"
msgid "Show summary dialog when saving project"
msgstr "Näytä yhteenvetoikkuna, kun projekti tallennetaan"

msgctxt "@tooltip:button"
msgid "Show your support for Cura with a donation."
msgstr ""

msgctxt "@button"
msgid "Sign Out"
msgstr ""

msgctxt "@action:button"
msgid "Sign in"
msgstr ""

msgctxt "@button"
msgid "Sign in"
msgstr ""

msgctxt "@title:header"
msgid "Sign in"
msgstr ""

msgctxt "@info"
msgid "Sign in into UltiMaker Digital Factory"
msgstr ""

msgctxt "@button"
msgid "Sign in to Digital Factory"
msgstr ""

msgctxt "@label"
msgid "Sign in to the UltiMaker platform"
msgstr ""

msgctxt "name"
msgid "Simulation View"
msgstr ""

msgctxt "@tooltip"
msgid "Skin"
msgstr "Pintakalvo"

msgctxt "@action:button"
msgid "Skip"
msgstr ""

msgctxt "@button"
msgid "Skip"
msgstr ""

msgctxt "@tooltip"
msgid "Skirt"
msgstr "Helma"

msgctxt "@button"
msgid "Slice"
msgstr ""

msgctxt "@option:check"
msgid "Slice automatically"
msgstr "Viipaloi automaattisesti"

msgctxt "@info:tooltip"
msgid "Slice automatically when changing settings."
msgstr "Viipaloi automaattisesti, kun asetuksia muutetaan."

msgctxt "name"
msgid "Slice info"
msgstr "Viipalointitiedot"

msgctxt "@message:title"
msgid "Slicing failed"
msgstr ""

msgctxt "@message"
msgid "Slicing failed with an unexpected error. Please consider reporting a bug on our issue tracker."
msgstr ""

msgctxt "@label:PrintjobStatus"
msgid "Slicing..."
msgstr "Viipaloidaan..."

msgctxt "@action:label"
msgid "Smoothing"
msgstr "Tasoitus"

msgctxt "name"
msgid "Solid View"
msgstr "Kiinteä näkymä"

msgctxt "@item:inmenu"
msgid "Solid view"
msgstr "Kiinteä näkymä"

msgctxt "@label"
msgid ""
"Some hidden settings use values different from their normal calculated value.\n"
"\n"
"Click to make these settings visible."
msgstr ""
"Jotkin piilotetut asetukset käyttävät arvoja, jotka eroavat normaaleista lasketuista arvoista.\n"
"\n"
"Tee asetuksista näkyviä napsauttamalla."

msgctxt "@info:status"
msgid "Some of the packages used in the project file are currently not installed in Cura, this might produce undesirable print results. We highly recommend installing the all required packages from the Marketplace."
msgstr ""

msgctxt "@info:title"
msgid "Some required packages are not installed"
msgstr ""

msgctxt "@info %1 is the name of a profile"
msgid "Some setting-values defined in <b>%1</b> were overridden."
msgstr ""

msgctxt "@tooltip"
msgid ""
"Some setting/override values are different from the values stored in the profile.\n"
"\n"
"Click to open the profile manager."
msgstr ""
"Jotkut asetusten ja ohitusten arvot eroavat profiiliin tallennetuista arvoista.\n"
"\n"
"Avaa profiilin hallinta napsauttamalla."

msgctxt "@action:label"
msgid "Some settings from current profile were overwritten."
msgstr ""

msgctxt "@message"
msgid "Something unexpected happened when trying to log in, please try again."
msgstr ""

msgctxt "@title:header"
msgid "Something went wrong when sending the materials to the printers."
msgstr ""

msgctxt "@label"
msgid "Something went wrong..."
msgstr ""

msgctxt "@label:listbox"
msgid "Speed"
msgstr ""

msgctxt "@action:inmenu"
msgid "Sponsor Cura"
msgstr ""

msgctxt "@label:button"
msgid "Sponsor Cura"
msgstr ""

msgctxt "@option:radio"
msgid "Stable and Beta releases"
msgstr ""

msgctxt "@option:radio"
msgid "Stable releases only"
msgstr ""

msgctxt "@item:inlistbox"
msgid "Stanford Triangle Format"
msgstr ""

msgctxt "@button"
msgid "Start"
msgstr ""

msgctxt "@action:button"
msgid "Start Build Plate Leveling"
msgstr "Aloita alustan tasaaminen"

msgctxt "@title:label"
msgid "Start G-code"
msgstr ""

msgctxt "@label"
msgid "Start the slicing process"
msgstr ""

msgctxt "@label"
msgid "Starts"
msgstr ""

msgctxt "@text"
msgid "Streamline your workflow and customize your UltiMaker Cura experience with plugins contributed by our amazing community of users."
msgstr ""

msgctxt "@label"
msgid "Strength"
msgstr ""

msgctxt "description"
msgid "Submits anonymous slice info. Can be disabled through preferences."
msgstr "Lähettää anonyymiä viipalointitietoa. Voidaan lisäasetuksista kytkeä pois käytöstä."

msgctxt "@info:status Don't translate the XML tag <filename>!"
msgid "Successfully exported material to <filename>%1</filename>"
msgstr "Materiaalin vieminen onnistui kohteeseen <filename>%1</filename>"

msgctxt "@info:status Don't translate the XML tag <filename>!"
msgid "Successfully imported material <filename>%1</filename>"
msgstr "Materiaalin tuominen onnistui: <filename>%1</filename>"

#, python-brace-format
msgctxt "@info:status"
msgid "Successfully imported profile {0}."
msgstr ""

msgctxt "@action:title"
msgid "Summary - Cura Project"
msgstr "Yhteenveto – Cura-projekti"

msgctxt "@label"
msgid "Support"
msgstr ""

msgctxt "@tooltip"
msgid "Support"
msgstr "Tuki"

msgctxt "@label"
msgid "Support Blocker"
msgstr ""

msgctxt "name"
msgid "Support Eraser"
msgstr ""

msgctxt "@tooltip"
msgid "Support Infill"
msgstr "Tuen täyttö"

msgctxt "@tooltip"
msgid "Support Interface"
msgstr "Tukiliittymä"

msgctxt "@action:label"
msgid "Support Type"
msgstr ""

msgctxt "@label Description for application dependency"
msgid "Support library for faster math"
msgstr "Nopeamman laskennan tukikirjasto"

msgctxt "@label Description for application component"
msgid "Support library for file metadata and streaming"
msgstr ""

msgctxt "@label Description for application component"
msgid "Support library for handling 3MF files"
msgstr "Tukikirjasto 3MF-tiedostojen käsittelyyn"

msgctxt "@label Description for application dependency"
msgid "Support library for handling STL files"
msgstr "STL-tiedostojen käsittelyn tukikirjasto"

msgctxt "@label Description for application dependency"
msgid "Support library for handling triangular meshes"
msgstr ""

msgctxt "@label Description for application dependency"
msgid "Support library for scientific computing"
msgstr "Tieteellisen laskennan tukikirjasto"

msgctxt "@label Description for application dependency"
msgid "Support library for system keyring access"
msgstr ""

msgctxt "@action:button"
msgid "Sync"
msgstr ""

msgctxt "@button"
msgid "Sync"
msgstr ""

msgctxt "@title:header"
msgid "Sync material profiles via USB"
msgstr ""

msgctxt "@action:button"
msgid "Sync materials"
msgstr ""

msgctxt "@button"
msgid "Sync materials with USB"
msgstr ""

msgctxt "@title:header"
msgid "Sync materials with printers"
msgstr ""

msgctxt "@title:window"
msgid "Sync materials with printers"
msgstr ""

msgctxt "@action:button"
msgid "Sync with Printers"
msgstr ""

msgctxt "@button"
msgid "Syncing"
msgstr ""

msgctxt "@info:generic"
msgid "Syncing..."
msgstr ""

msgctxt "@title:groupbox"
msgid "System information"
msgstr ""

msgctxt "@button"
msgid "Technical datasheet"
msgstr ""

msgctxt "@info:tooltip"
msgid "The amount of smoothing to apply to the image."
msgstr "Kuvassa käytettävän tasoituksen määrä."

msgctxt "@text"
msgid "The annealing profile requires post-processing in an oven after the print is finished. This profile retains the dimensional accuracy of the printed part after annealing and improves strength, stiffness, and thermal resistance."
msgstr ""

msgctxt "@label"
msgid "The assigned printer, %1, requires the following configuration change:"
msgid_plural "The assigned printer, %1, requires the following configuration changes:"
msgstr[0] ""
msgstr[1] ""

msgctxt "@error:file_size"
msgid "The backup exceeds the maximum file size."
msgstr ""

msgctxt "@text"
msgid "The balanced profile is designed to strike a balance between productivity, surface quality, mechanical properties and dimensional accuracy."
msgstr "Tasapainotettu profiili on suunniteltu tasapainottamaan tuottavuutta, pinnanlaatua, mekaanisia ominaisuuksia ja dimensionaalista tarkkuutta."

msgctxt "@info:tooltip"
msgid "The base height from the build plate in millimeters."
msgstr "Pohjan korkeus alustasta millimetreinä."

msgctxt "@info:status"
msgid "The build volume height has been reduced due to the value of the \"Print Sequence\" setting to prevent the gantry from colliding with printed models."
msgstr "Tulostustilavuuden korkeutta on vähennetty tulostusjärjestysasetuksen vuoksi, jotta koroke ei osuisi tulostettuihin malleihin."

msgctxt "@status"
msgid "The cloud connection is currently unavailable. Please check your internet connection."
msgstr ""

msgctxt "@status"
msgid "The cloud connection is currently unavailable. Please sign in to connect to the cloud printer."
msgstr ""

msgctxt "@status"
msgid "The cloud printer is offline. Please check if the printer is turned on and connected to the internet."
msgstr ""

msgctxt "@tooltip"
msgid "The colour of the material in this extruder."
msgstr "Tämän suulakkeen materiaalin väri."

msgctxt "@tooltip"
msgid "The configuration of this extruder is not allowed, and prohibits slicing."
msgstr ""

msgctxt "@label"
msgid "The configurations are not available because the printer is disconnected."
msgstr ""

msgctxt "@tooltip"
msgid "The current temperature of the heated bed."
msgstr "Lämmitettävän pöydän nykyinen lämpötila."

msgctxt "@tooltip"
msgid "The current temperature of this hotend."
msgstr ""

msgctxt "@info:tooltip"
msgid "The depth in millimeters on the build plate"
msgstr "Syvyys millimetreinä alustalla"

msgctxt "@text"
msgid "The draft profile is designed to print initial prototypes and concept validation with the intent of significant print time reduction."
msgstr ""

msgctxt "@text"
msgid "The engineering profile is designed to print functional prototypes and end-use parts with the intent of better accuracy and for closer tolerances."
msgstr ""

msgctxt "@label"
msgid "The extruder train to use for printing the support. This is used in multi-extrusion."
msgstr ""

#, python-brace-format
msgctxt "@label Don't translate the XML tag <filename>!"
msgid "The file <filename>{0}</filename> already exists. Are you sure you want to overwrite it?"
msgstr "Tiedosto <filename>{0}</filename> on jo olemassa. Haluatko varmasti kirjoittaa sen päälle?"

msgctxt "@label"
msgid "The firmware shipping with new printers works, but new versions tend to have more features and improvements."
msgstr "Uusien tulostimien mukana toimitettava laiteohjelmisto toimii, mutta uusissa versioissa on yleensä enemmän toimintoja ja parannuksia."

msgctxt "@info:backup_failed"
msgid "The following error occurred while trying to restore a Cura backup:"
msgstr ""

msgctxt "@label"
msgid "The following packages can not be installed because of an incompatible Cura version:"
msgstr ""

msgctxt "@label"
msgid "The following packages will be added:"
msgstr ""

msgctxt "@label"
msgid "The following printers in your account have been added in Cura:"
msgstr ""

msgctxt "@title:header"
msgid "The following printers will receive the new material profiles:"
msgstr ""

msgctxt "@info:tooltip"
msgid "The following script is active:"
msgid_plural "The following scripts are active:"
msgstr[0] ""
msgstr[1] ""

msgctxt "@label"
msgid "The following settings define the strength of your part."
msgstr ""

msgctxt "@info:status"
msgid "The highlighted areas indicate either missing or extraneous surfaces. Fix your model and open it again into Cura."
msgstr ""

msgctxt "@tooltip"
msgid "The material in this extruder."
msgstr "Tämän suulakkeen materiaali."

msgctxt "@label:label Ultimaker Marketplace is a brand name, don't translate"
msgid "The material package associated with the Cura project could not be found on the Ultimaker Marketplace. Use the partial material profile definition stored in the Cura project file at your own risk."
msgstr ""

msgctxt "@info:tooltip"
msgid "The maximum distance of each pixel from \"Base.\""
msgstr "Kunkin pikselin suurin etäisyys \"Pohja\"-arvosta."

msgctxt "@label (%1 is a number)"
msgid "The new filament diameter is set to %1 mm, which is not compatible with the current extruder. Do you wish to continue?"
msgstr ""

msgctxt "@tooltip"
msgid "The nozzle inserted in this extruder."
msgstr "Tähän suulakkeeseen liitetty suutin."

msgctxt "@label"
msgid ""
"The pattern of the infill material of the print:\n"
"\n"
"For quick prints of non functional model choose line, zig zag or lightning infill.\n"
"\n"
"For functional part not subjected to a lot of stress we recommend grid or triangle or tri hexagon.\n"
"\n"
"For functional 3D prints which require high strength in multiple directions use cubic, cubic subdivision, quarter cubic, octet, and gyroid."
msgstr ""

msgctxt "@info:tooltip"
msgid "The percentage of light penetrating a print with a thickness of 1 millimeter. Lowering this value increases the contrast in dark regions and decreases the contrast in light regions of the image."
msgstr ""

msgctxt "@label:label Ultimaker Marketplace is a brand name, don't translate"
msgid "The plugin associated with the Cura project could not be found on the Ultimaker Marketplace. As the plugin may be required to slice the project it might not be possible to correctly slice the file."
msgstr ""

msgctxt "@info:title"
msgid "The print job was successfully submitted"
msgstr ""

msgctxt "@label"
msgid "The printer %1 is assigned, but the job contains an unknown material configuration."
msgstr ""

msgctxt "@label"
msgid "The printer at this address has not responded yet."
msgstr ""

msgctxt "@label"
msgid "The printer at this address has not yet responded."
msgstr "Tämän osoitteen tulostin ei ole vielä vastannut."

msgctxt "@info:status"
msgid "The printer is not connected."
msgstr "Tulostinta ei ole yhdistetty."

msgctxt "@label"
msgid "The printer(s) below cannot be connected because they are part of a group"
msgstr ""

msgctxt "@message"
msgid "The provided state is not correct."
msgstr ""

msgctxt "@text:window"
msgid "The release notes could not be opened."
msgstr ""

msgctxt "@text:error"
msgid "The response from Digital Factory appears to be corrupted."
msgstr ""

msgctxt "@text:error"
msgid "The response from Digital Factory is missing important information."
msgstr ""

msgctxt "@tooltip"
msgid "The target temperature of the heated bed. The bed will heat up or cool down towards this temperature. If this is 0, the bed heating is turned off."
msgstr "Lämmitettävän pöydän kohdelämpötila. Pöytä lämpenee tai viilenee kohti tätä lämpötilaa. Jos asetus on 0, pöydän lämmitys sammutetaan."

msgctxt "@tooltip"
msgid "The target temperature of the hotend. The hotend will heat up or cool down towards this temperature. If this is 0, the hotend heating is turned off."
msgstr "Kuuman pään kohdelämpötila. Kuuma pää lämpenee tai viilenee kohti tätä lämpötilaa. Jos asetus on 0, kuuman pään lämmitys sammutetaan."

msgctxt "@tooltip of temperature input"
msgid "The temperature to pre-heat the bed to."
msgstr "Lämmitettävän pöydän esilämmityslämpötila."

msgctxt "@tooltip of temperature input"
msgid "The temperature to pre-heat the hotend to."
msgstr ""

msgctxt "@text"
msgid "The visual profile is designed to print visual prototypes and models with the intent of high visual and surface quality."
msgstr ""

msgctxt "@info:tooltip"
msgid "The width in millimeters on the build plate"
msgstr ""

msgctxt "@label: Please keep the asterix, it's to indicate that a restart is needed."
msgid "Theme*:"
msgstr ""

msgctxt "@info:status"
msgid "There are no file formats available to write with!"
msgstr ""

msgctxt "@label"
msgid "There are no print jobs in the queue. Slice and send a job to add one."
msgstr ""

msgctxt "@tooltip"
msgid "There are no profiles matching the configuration of this extruder."
msgstr ""

msgctxt "@info:status"
msgid "There is no active printer yet."
msgstr ""

msgctxt "@label"
msgid "There is no printer found over your network."
msgstr ""

msgctxt "@error"
msgid "There is no workspace yet to write. Please add a printer first."
msgstr ""

msgctxt "@info:backup_status"
msgid "There was an error trying to restore your backup."
msgstr ""

msgctxt "@info:backup_status"
msgid "There was an error while creating your backup."
msgstr ""

msgctxt "@info:backup_status"
msgid "There was an error while uploading your backup."
msgstr ""

msgctxt "@label"
msgid "This configuration is not available because %1 is not recognized. Please visit %2 to download the correct material profile."
msgstr ""

msgctxt "@text:window"
msgid "This is a Cura project file. Would you like to open it as a project or import the models from it?"
msgstr "Tämä on Cura-projektitiedosto. Haluatko avata sen projektina vai tuoda siinä olevat mallit?"

msgctxt "@label"
msgid "This material is linked to %1 and shares some of its properties."
msgstr "Materiaali on linkitetty kohteeseen %1 ja niillä on joitain samoja ominaisuuksia."

msgctxt "@label"
msgid "This package will be installed after restarting."
msgstr ""

msgctxt "@label"
msgid "This printer cannot be added because it's an unknown printer or it's not the host of a group."
msgstr ""

msgctxt "info:status"
msgid "This printer is not linked to the Digital Factory:"
msgid_plural "These printers are not linked to the Digital Factory:"
msgstr[0] ""
msgstr[1] ""

msgctxt "@status"
msgid "This printer is not linked to your account. Please visit the Ultimaker Digital Factory to establish a connection."
msgstr ""

msgctxt "@label"
msgid "This printer is not set up to host a group of printers."
msgstr ""

msgctxt "@label"
msgid "This printer is the host for a group of %1 printers."
msgstr ""

#, python-brace-format
msgctxt "@info:status Don't translate the XML tags <filename>!"
msgid "This profile <filename>{0}</filename> contains incorrect data, could not import it."
msgstr ""

msgctxt "@action:label"
msgid "This profile uses the defaults specified by the printer, so it has no settings/overrides in the list below."
msgstr "Tässä profiilissa käytetään tulostimen oletusarvoja, joten siinä ei ole alla olevan listan asetuksia tai ohituksia."

msgctxt "@label"
msgid "This project contains materials or plugins that are currently not installed in Cura.<br/>Install the missing packages and reopen the project."
msgstr ""

msgctxt "@label"
msgid ""
"This setting has a value that is different from the profile.\n"
"\n"
"Click to restore the value of the profile."
msgstr ""
"Tämän asetuksen arvo eroaa profiilin arvosta.\n"
"\n"
"Palauta profiilin arvo napsauttamalla."

msgctxt "@item:tooltip"
msgid "This setting has been hidden by the active machine and will not be visible."
msgstr ""

msgctxt "@item:tooltip %1 is list of setting names"
msgid "This setting has been hidden by the value of %1. Change the value of that setting to make this setting visible."
msgid_plural "This setting has been hidden by the values of %1. Change the values of those settings to make this setting visible."
msgstr[0] ""
msgstr[1] ""

msgctxt "@label"
msgid "This setting is always shared between all extruders. Changing it here will change the value for all extruders."
msgstr ""

msgctxt "@label"
msgid ""
"This setting is normally calculated, but it currently has an absolute value set.\n"
"\n"
"Click to restore the calculated value."
msgstr ""
"Tämä asetus on normaalisti laskettu, mutta sillä on tällä hetkellä absoluuttinen arvo.\n"
"\n"
"Palauta laskettu arvo napsauttamalla."

msgctxt "@label"
msgid "This setting is not used because all the settings that it influences are overridden."
msgstr ""

msgctxt "@label"
msgid "This setting is resolved from conflicting extruder-specific values:"
msgstr ""

msgctxt "@info:warning"
msgid "This version is not intended for production use. If you encounter any issues, please report them on our GitHub page, mentioning the full version {self.getVersion()}"
msgstr ""

msgctxt "@label"
msgid "Time estimation"
msgstr ""

msgctxt "@message"
msgid "Timeout when authenticating with the account server."
msgstr ""

msgctxt "@text"
msgid "To automatically sync the material profiles with all your printers connected to Digital Factory you need to be signed in in Cura."
msgstr ""

#, python-brace-format
msgctxt "info:status"
msgid "To establish a connection, please visit the {website_link}"
msgstr ""

msgctxt "@label"
msgid "To make sure your prints will come out great, you can now adjust your buildplate. When you click 'Move to Next Position' the nozzle will move to the different positions that can be adjusted."
msgstr "Voit säätää alustaa, jotta tulosteista tulisi hyviä. Kun napsautat \"Siirry seuraavaan positioon\", suutin siirtyy eri positioihin, joita voidaan säätää."

msgctxt "@label"
msgid "To print directly to your printer over the network, please make sure your printer is connected to the network using a network cable or by connecting your printer to your WIFI network. If you don't connect Cura with your printer, you can still use a USB drive to transfer g-code files to your printer."
msgstr ""

#, python-brace-format
msgctxt "@message {printer_name} is replaced with the name of the printer"
msgid "To remove {printer_name} permanently, visit {digital_factory_link}"
msgstr ""

msgctxt "@action:inmenu"
msgid "Toggle Full Screen"
msgstr "Vaihda koko näyttöön"

msgctxt "@label"
msgid "Top / Bottom"
msgstr "Yläosa/alaosa"

msgctxt "@action:inmenu menubar:view"
msgid "Top View"
msgstr ""

msgctxt "@info:tooltip"
msgid "Top View"
msgstr ""

msgctxt "@label"
msgid "Total print time"
msgstr ""

msgctxt "@action:tooltip"
msgid "Track the print in Ultimaker Digital Factory"
msgstr ""

msgctxt "@item:inlistbox"
msgid "Translucency"
msgstr ""

msgctxt "@tooltip"
msgid "Travel"
msgstr "Siirtoliike"

msgctxt "@label"
msgid "Travels"
msgstr ""

msgctxt "@info:backup_failed"
msgid "Tried to restore a Cura backup that is higher than the current version."
msgstr ""

msgctxt "@info:backup_failed"
msgid "Tried to restore a Cura backup without having proper data or meta data."
msgstr ""

msgctxt "name"
msgid "Trimesh Reader"
msgstr ""

msgctxt "@button"
msgid "Troubleshooting"
msgstr ""

msgctxt "@label"
msgid "Troubleshooting"
msgstr ""

msgctxt "@button"
msgid "Try again"
msgstr ""

msgctxt "@action:label"
msgid "Type"
msgstr "Tyyppi"

msgctxt "@label"
msgid "Type"
msgstr "Tyyppi"

msgctxt "name"
msgid "UFP Reader"
msgstr ""

msgctxt "name"
msgid "UFP Writer"
msgstr ""

msgctxt "@item:inmenu"
msgid "USB printing"
msgstr "USB-tulostus"

msgctxt "name"
msgid "USB printing"
msgstr "USB-tulostus"

msgctxt "@button"
msgid "UltiMaker Account"
msgstr ""

msgctxt "@info"
msgid "UltiMaker Certified Material"
msgstr ""

msgctxt "@text:window"
msgid "UltiMaker Cura collects anonymous data in order to improve the print quality and user experience. Below is an example of all the data that is shared:"
msgstr ""

msgctxt "@item:inlistbox"
msgid "UltiMaker Format Package"
msgstr ""

msgctxt "name"
msgid "UltiMaker Network Connection"
msgstr "UltiMaker-verkkoyhteys"

msgctxt "@info"
msgid "UltiMaker Verified Package"
msgstr ""

msgctxt "@info"
msgid "UltiMaker Verified Plug-in"
msgstr ""

msgctxt "name"
msgid "UltiMaker machine actions"
msgstr ""

msgctxt "@button"
msgid "UltiMaker printer"
msgstr ""

msgctxt "@label:button"
msgid "UltiMaker support"
msgstr ""

msgctxt "info:name"
msgid "Ultimaker Digital Factory"
msgstr ""

msgctxt "name"
msgid "Ultimaker Digital Library"
msgstr ""

msgctxt "@info:status"
msgid "Unable to add the profile."
msgstr ""

msgctxt "@info:status"
msgid "Unable to find a location within the build volume for all objects"
msgstr "Kaikille kappaleille ei löydy paikkaa tulostustilavuudessa"

#, python-brace-format
msgctxt "@info:plugin_failed"
msgid "Unable to find local EnginePlugin server executable for: {self._plugin_id}"
msgstr ""

#, python-brace-format
msgctxt "@info:plugin_failed"
msgid ""
"Unable to kill running EnginePlugin: {self._plugin_id}\n"
"Access is denied."
msgstr ""

msgctxt "@info"
msgid "Unable to reach the UltiMaker account server."
msgstr ""

msgctxt "@text"
msgid "Unable to read example data file."
msgstr ""

msgctxt "@info:title"
msgid "Unable to slice"
msgstr "Viipalointi ei onnistu"

msgctxt "@label:PrintjobStatus"
msgid "Unable to slice"
msgstr ""

msgctxt "@info:status"
msgid "Unable to slice because the prime tower or prime position(s) are invalid."
msgstr "Viipalointi ei onnistu, koska esitäyttötorni tai esitäytön sijainti tai sijainnit eivät kelpaa."

#, python-format
msgctxt "@info:status"
msgid "Unable to slice because there are objects associated with disabled Extruder %s."
msgstr ""

#, python-brace-format
msgctxt "@info:status"
msgid "Unable to slice due to some per-model settings. The following settings have errors on one or more models: {error_labels}"
msgstr ""

msgctxt "@info:status"
msgid "Unable to slice with the current material as it is incompatible with the selected machine or configuration."
msgstr "Viipalointi ei onnistu nykyisellä materiaalilla, sillä se ei sovellu käytettäväksi valitun laitteen tai kokoonpanon kanssa."

#, python-brace-format
msgctxt "@info:status"
msgid "Unable to slice with the current settings. The following settings have errors: {0}"
msgstr "Viipalointi ei onnistu nykyisten asetuksien ollessa voimassa. Seuraavissa asetuksissa on virheitä: {0}"

msgctxt "@info"
msgid "Unable to start a new sign in process. Check if another sign in attempt is still active."
msgstr ""

msgctxt "@label:status"
msgid "Unavailable"
msgstr ""

msgctxt "@label"
msgid "Unavailable printer"
msgstr ""

msgctxt "@action:inmenu menubar:edit"
msgid "Ungroup Models"
msgstr "Poista mallien ryhmitys"

msgctxt "@button"
msgid "Uninstall"
msgstr ""

msgctxt "@title:column Unit of measurement"
msgid "Unit"
msgstr ""

msgctxt "@label Description for development tool"
msgid "Universal build system configuration"
msgstr ""

msgctxt "@label"
msgid "Unknown"
msgstr "Tuntematon"

msgctxt "@label unknown version of Cura"
msgid "Unknown"
msgstr ""

msgctxt "@label:property"
msgid "Unknown Author"
msgstr ""

msgctxt "@label:property"
msgid "Unknown Package"
msgstr ""

#, python-brace-format
msgctxt "@error:send"
msgid "Unknown error code when uploading print job: {0}"
msgstr ""

msgctxt "@text"
msgid "Unknown error."
msgstr ""

msgctxt "@label"
msgid "Unlink Material"
msgstr "Poista materiaalin linkitys"

msgctxt "@label:status"
msgid "Unreachable"
msgstr ""

msgctxt "@label"
msgid "Untitled"
msgstr ""

msgctxt "@text Print job name"
msgid "Untitled"
msgstr ""

msgctxt "@button"
msgid "Update"
msgstr ""

msgctxt "@action"
msgid "Update Firmware"
msgstr ""

msgctxt "@title"
msgid "Update Firmware"
msgstr ""

msgctxt "@action:ComboBox Update/override existing profile"
msgid "Update existing"
msgstr ""

msgctxt "@action:tooltip"
msgid "Update profile with current settings/overrides"
msgstr ""

msgctxt "@action:button"
msgid "Update profile."
msgstr ""

msgctxt "@info:title"
msgid "Update your printer"
msgstr ""

msgctxt "@label"
msgid "Updates"
msgstr ""

msgctxt "@label"
msgid "Updating firmware."
msgstr "Päivitetään laiteohjelmistoa."

msgctxt "@button"
msgid "Updating..."
msgstr ""

msgctxt "description"
msgid "Upgrades configurations from Cura 2.1 to Cura 2.2."
msgstr "Päivittää kokoonpanon versiosta Cura 2.1 versioon Cura 2.2."

msgctxt "description"
msgid "Upgrades configurations from Cura 2.2 to Cura 2.4."
msgstr "Päivittää kokoonpanon versiosta Cura 2.2 versioon Cura 2.4."

msgctxt "description"
msgid "Upgrades configurations from Cura 2.5 to Cura 2.6."
msgstr "Päivittää kokoonpanon versiosta Cura 2.5 versioon Cura 2.6."

msgctxt "description"
msgid "Upgrades configurations from Cura 2.6 to Cura 2.7."
msgstr "Päivittää kokoonpanon versiosta Cura 2.6 versioon Cura 2.7."

msgctxt "description"
msgid "Upgrades configurations from Cura 2.7 to Cura 3.0."
msgstr "Päivittää kokoonpanon versiosta Cura 2.7 versioon Cura 3.0."

msgctxt "description"
msgid "Upgrades configurations from Cura 3.0 to Cura 3.1."
msgstr "Päivittää kokoonpanon versiosta Cura 3.0 versioon Cura 3.1."

msgctxt "description"
msgid "Upgrades configurations from Cura 3.2 to Cura 3.3."
msgstr "Päivittää kokoonpanon versiosta Cura 3.2 versioon Cura 3.3."

msgctxt "description"
msgid "Upgrades configurations from Cura 3.3 to Cura 3.4."
msgstr "Päivittää kokoonpanon versiosta Cura 3.3 versioon Cura 3.4."

msgctxt "description"
msgid "Upgrades configurations from Cura 3.4 to Cura 3.5."
msgstr "Päivittää kokoonpanon versiosta Cura 3.4 versioon Cura 3.5."

msgctxt "description"
msgid "Upgrades configurations from Cura 3.5 to Cura 4.0."
msgstr "Päivittää kokoonpanon versiosta Cura 3.5 versioon Cura 4.0."

msgctxt "description"
msgid "Upgrades configurations from Cura 4.0 to Cura 4.1."
msgstr "Päivittää kokoonpanon versiosta Cura 4.0 versioon Cura 4.1."

msgctxt "description"
msgid "Upgrades configurations from Cura 4.1 to Cura 4.2."
msgstr "Päivittää kokoonpanon versiosta Cura 4.1 versioon Cura 4.2."

msgctxt "description"
msgid "Upgrades configurations from Cura 4.11 to Cura 4.12."
msgstr "Päivittää kokoonpanon versiosta Cura 4.11 versioon Cura 4.12."

msgctxt "description"
msgid "Upgrades configurations from Cura 4.13 to Cura 5.0."
msgstr "Päivittää kokoonpanon versiosta Cura 4.13 versioon Cura 5.0."

msgctxt "description"
msgid "Upgrades configurations from Cura 4.2 to Cura 4.3."
msgstr "Päivittää kokoonpanon versiosta Cura 4.2 versioon Cura 4.3."

msgctxt "description"
msgid "Upgrades configurations from Cura 4.3 to Cura 4.4."
msgstr "Päivittää kokoonpanon versiosta Cura 4.3 versioon Cura 4.4."

msgctxt "description"
msgid "Upgrades configurations from Cura 4.4 to Cura 4.5."
msgstr "Päivittää kokoonpanon versiosta Cura 4.4 versioon Cura 4.5."

msgctxt "description"
msgid "Upgrades configurations from Cura 4.5 to Cura 4.6."
msgstr "Päivittää kokoonpanon versiosta Cura 4.5 versioon Cura 4.6."

msgctxt "description"
msgid "Upgrades configurations from Cura 4.6.0 to Cura 4.6.2."
msgstr "Päivittää kokoonpanon versiosta Cura 4.6.0 versioon Cura 4.6.2."

msgctxt "description"
msgid "Upgrades configurations from Cura 4.6.2 to Cura 4.7."
msgstr "Päivittää kokoonpanon versiosta Cura 4.6.2 versioon Cura 4.7."

msgctxt "description"
msgid "Upgrades configurations from Cura 4.7 to Cura 4.8."
msgstr "Päivittää kokoonpanon versiosta Cura 4.7 versioon Cura 4.8."

msgctxt "description"
msgid "Upgrades configurations from Cura 4.8 to Cura 4.9."
msgstr "Päivittää kokoonpanon versiosta Cura 4.8 versioon Cura 4.9."

msgctxt "description"
msgid "Upgrades configurations from Cura 4.9 to Cura 4.10."
msgstr "Päivittää kokoonpanon versiosta Cura 4.9 versioon Cura 4.10."

msgctxt "description"
msgid "Upgrades configurations from Cura 5.2 to Cura 5.3."
msgstr "Päivittää kokoonpanon versiosta Cura 5.2 versioon Cura 5.3."

msgctxt "description"
msgid "Upgrades configurations from Cura 5.3 to Cura 5.4."
msgstr ""

msgctxt "description"
msgid "Upgrades configurations from Cura 5.4 to Cura 5.5."
msgstr ""

msgctxt "@action:button"
msgid "Upload custom Firmware"
msgstr "Lataa mukautettu laiteohjelmisto"

msgctxt "@info:status"
msgid "Uploading print job to printer."
msgstr ""

msgctxt "@info:backup_status"
msgid "Uploading your backup..."
msgstr ""

msgctxt "@option:check"
msgid "Use a single instance of Cura"
msgstr ""

msgctxt "@label"
msgid "Use glue for better adhesion with this material combination."
msgstr ""

msgctxt "@label"
msgid "User Agreement"
msgstr ""

msgctxt "@label Description for application dependency"
msgid "Utility functions, including an image loader"
msgstr ""

msgctxt "@label Description for application dependency"
msgid "Utility library, including Voronoi generation"
msgstr ""

msgctxt "name"
msgid "Version Upgrade 2.1 to 2.2"
msgstr "Päivitys versiosta 2.1 versioon 2.2"

msgctxt "name"
msgid "Version Upgrade 2.2 to 2.4"
msgstr "Päivitys versiosta 2.2 versioon 2.4"

msgctxt "name"
msgid "Version Upgrade 2.5 to 2.6"
msgstr "Päivitys versiosta 2.5 versioon 2.6"

msgctxt "name"
msgid "Version Upgrade 2.6 to 2.7"
msgstr "Päivitys versiosta 2.6 versioon 2.7"

msgctxt "name"
msgid "Version Upgrade 2.7 to 3.0"
msgstr "Päivitys versiosta 2.7 versioon 3.0"

msgctxt "name"
msgid "Version Upgrade 3.0 to 3.1"
msgstr "Päivitys versiosta 3.0 versioon 3.1"

msgctxt "name"
msgid "Version Upgrade 3.2 to 3.3"
msgstr "Päivitys versiosta 3.2 versioon 3.3"

msgctxt "name"
msgid "Version Upgrade 3.3 to 3.4"
msgstr "Päivitys versiosta 3.3 versioon 3.4"

msgctxt "name"
msgid "Version Upgrade 3.4 to 3.5"
msgstr "Päivitys versiosta 3.4 versioon 3.5"

msgctxt "name"
msgid "Version Upgrade 3.5 to 4.0"
msgstr "Päivitys versiosta 3.5 versioon 4.0"

msgctxt "name"
msgid "Version Upgrade 4.0 to 4.1"
msgstr "Päivitys versiosta 4.0 versioon 4.1"

msgctxt "name"
msgid "Version Upgrade 4.1 to 4.2"
msgstr "Päivitys versiosta 4.1 versioon 4.2"

msgctxt "name"
msgid "Version Upgrade 4.11 to 4.12"
msgstr "Päivitys versiosta 4.11 versioon 4.12"

msgctxt "name"
msgid "Version Upgrade 4.13 to 5.0"
msgstr "Päivitys versiosta 4.13 versioon 5.0"

msgctxt "name"
msgid "Version Upgrade 4.2 to 4.3"
msgstr "Päivitys versiosta 4.2 versioon 4.3"

msgctxt "name"
msgid "Version Upgrade 4.3 to 4.4"
msgstr "Päivitys versiosta 4.3 versioon 4.4"

msgctxt "name"
msgid "Version Upgrade 4.4 to 4.5"
msgstr "Päivitys versiosta 4.4 versioon 4.5"

msgctxt "name"
msgid "Version Upgrade 4.5 to 4.6"
msgstr "Päivitys versiosta 4.5 versioon 4.6"

msgctxt "name"
msgid "Version Upgrade 4.6.0 to 4.6.2"
msgstr "Päivitys versiosta 4.6.0 versioon 4.6.2"

msgctxt "name"
msgid "Version Upgrade 4.6.2 to 4.7"
msgstr "Päivitys versiosta 4.6.2 versioon 4.7"

msgctxt "name"
msgid "Version Upgrade 4.7 to 4.8"
msgstr "Päivitys versiosta 4.7 versioon 4.8"

msgctxt "name"
msgid "Version Upgrade 4.8 to 4.9"
msgstr "Päivitys versiosta 4.8 versioon 4.9"

msgctxt "name"
msgid "Version Upgrade 4.9 to 4.10"
msgstr "Päivitys versiosta 4.9 versioon 4.10"

msgctxt "name"
msgid "Version Upgrade 5.2 to 5.3"
msgstr "Päivitys versiosta 5.2 versioon 5.4"

msgctxt "name"
msgid "Version Upgrade 5.3 to 5.4"
msgstr ""

msgctxt "name"
msgid "Version Upgrade 5.4 to 5.5"
msgstr ""

msgctxt "@button"
msgid "View printers in Digital Factory"
msgstr ""

msgctxt "@label"
msgid "View type"
msgstr ""

msgctxt "@label link to technical assistance"
msgid "View user manuals online"
msgstr ""

msgctxt "@label"
msgid "Viewport behavior"
msgstr "Näyttöikkunan käyttäytyminen"

msgctxt "@action:inmenu"
msgid "Visible Settings"
msgstr ""

msgctxt "@button"
msgid "Visit plug-in website"
msgstr ""

msgctxt "@tooltip:button"
msgid "Visit the UltiMaker website."
msgstr ""

msgctxt "@label"
msgid "Visual"
msgstr ""

msgctxt "@label"
msgid "Waiting for"
msgstr ""

msgctxt "@label"
msgid "Waiting for Cloud response"
msgstr ""

msgctxt "@button"
msgid "Waiting for new printers"
msgstr ""

msgctxt "@button"
msgid "Want more?"
msgstr ""

msgctxt "@info:title"
msgid "Warning"
msgstr "Varoitus"

#, python-brace-format
msgctxt "@info:status"
msgid "Warning: The profile is not visible because its quality type '{0}' is not available for the current configuration. Switch to a material/nozzle combination that can use this quality type."
msgstr ""

msgctxt "@text:window"
msgid "We have found one or more G-Code files within the files you have selected. You can only open one G-Code file at a time. If you want to open a G-Code file, please just select only one."
msgstr "Löysimme vähintään yhden Gcode-tiedoston valitsemiesi tiedostojen joukosta. Voit avata vain yhden Gcode-tiedoston kerrallaan. Jos haluat avata Gcode-tiedoston, valitse vain yksi."

msgctxt "@text:window"
msgid "We have found one or more project file(s) within the files you have selected. You can open only one project file at a time. We suggest to only import models from those files. Would you like to proceed?"
msgstr "Löysimme vähintään yhden projektitiedoston valitsemiesi tiedostojen joukosta. Voit avata vain yhden projektitiedoston kerrallaan. Suosittelemme, että tuot vain malleja niistä tiedostoista. Haluatko jatkaa?"

msgctxt "@info"
msgid "Webcam feeds for cloud printers cannot be viewed from UltiMaker Cura. Click \"Manage printer\" to visit Ultimaker Digital Factory and view this webcam."
msgstr ""

msgctxt "@button"
msgid "Website"
msgstr ""

msgctxt "@label"
msgid "What printer would you like to setup?"
msgstr ""

msgctxt "@info:tooltip"
msgid "What type of camera rendering should be used?"
msgstr ""

msgctxt "@action:inmenu menubar:help"
msgid "What's New"
msgstr ""

msgctxt "@label"
msgid "What's New"
msgstr ""

msgctxt "@title:window"
msgid "What's New"
msgstr ""

msgctxt "@info:tooltip"
msgid "When checking for updates, check for both stable and for beta releases."
msgstr ""

msgctxt "@info:tooltip"
msgid "When checking for updates, only check for stable releases."
msgstr ""

msgctxt "@info:tooltip"
msgid "When you have made changes to a profile and switched to a different one, a dialog will be shown asking whether you want to keep your modifications or not, or you can choose a default behaviour and never show that dialog again."
msgstr "Kun olet tehnyt muutokset profiiliin ja vaihtanut toiseen, näytetään valintaikkuna, jossa kysytään, haluatko säilyttää vai hylätä muutokset. Tässä voit myös valita oletuskäytöksen, jolloin valintaikkunaa ei näytetä uudelleen."

msgctxt "@button"
msgid "Why do I need to sync material profiles?"
msgstr ""

msgctxt "@action:label"
msgid "Width (mm)"
msgstr "Leveys (mm)"

msgctxt "description"
msgid "Writes g-code to a compressed archive."
msgstr ""

msgctxt "description"
msgid "Writes g-code to a file."
msgstr ""

msgctxt "@label"
msgid "X (Width)"
msgstr "X (leveys)"

msgctxt "@label"
msgid "X max"
msgstr "X enint"

msgctxt "@label"
msgid "X min"
msgstr "X väh"

msgctxt "name"
msgid "X-Ray View"
msgstr "Kerrosnäkymä"

msgctxt "@item:inlistbox"
msgid "X-Ray view"
msgstr "Kerrosnäkymä"

msgctxt "@label"
msgid "X/Y"
msgstr ""

msgctxt "@item:inlistbox"
msgid "X3D File"
msgstr "X3D-tiedosto"

msgctxt "name"
msgid "X3D Reader"
msgstr "X3D-lukija"

msgctxt "@label"
msgid "Y (Depth)"
msgstr "Y (syvyys)"

msgctxt "@label"
msgid "Y max"
msgstr "Y enint"

msgctxt "@label"
msgid "Y min"
msgstr "Y väh"

msgctxt "@info"
msgid "Yes"
msgstr ""

msgctxt "@label"
msgid ""
"You are about to remove all printers from Cura. This action cannot be undone.\n"
"Are you sure you want to continue?"
msgstr ""

#, python-brace-format
msgctxt "@label"
msgid ""
"You are about to remove {0} printer from Cura. This action cannot be undone.\n"
"Are you sure you want to continue?"
msgid_plural ""
"You are about to remove {0} printers from Cura. This action cannot be undone.\n"
"Are you sure you want to continue?"
msgstr[0] ""
msgstr[1] ""

msgctxt "@info:status"
msgid "You are attempting to connect to a printer that is not running UltiMaker Connect. Please update the printer to the latest firmware."
msgstr ""

#, python-brace-format
msgctxt "@info:status"
msgid "You are attempting to connect to {0} but it is not the host of a group. You can visit the web page to configure it as a group host."
msgstr ""

msgctxt "@empty_state"
msgid "You don't have any backups currently. Use the 'Backup Now' button to create one."
msgstr ""

msgctxt "@text:window, %1 is a profile name"
msgid "You have customized some profile settings. Would you like to Keep these changed settings after switching profiles? Alternatively, you can discard the changes to load the defaults from '%1'."
msgstr ""

msgctxt "@label"
msgid "You need to accept the license to install the package"
msgstr ""

msgctxt "@info:generic"
msgid "You need to quit and restart {} before changes have effect."
msgstr ""

msgctxt "@dialog:info"
msgid "You will need to restart Cura before your backup is restored. Do you want to close Cura now?"
msgstr ""

msgctxt "@info:status"
msgid "You will receive a confirmation via email when the print job is approved"
msgstr ""

msgctxt "@info:backup_status"
msgid "Your backup has finished uploading."
msgstr ""

msgctxt "@action:label"
msgid "Your current settings match the selected profile."
msgstr "Nykyiset asetukset vastaavat valittua profiilia."

msgctxt "@info"
msgid "Your new printer will automatically appear in Cura"
msgstr ""

#, python-brace-format
msgctxt "@info:status"
msgid ""
"Your printer <b>{printer_name}</b> could be connected via cloud.\n"
" Manage your print queue and monitor your prints from anywhere connecting your printer to Digital Factory"
msgstr ""

msgctxt "@label"
msgid "Z"
msgstr ""

msgctxt "@label"
msgid "Z (Height)"
msgstr "Z (korkeus)"

msgctxt "@label Description for application dependency"
msgid "ZeroConf discovery library"
msgstr "ZeroConf-etsintäkirjasto"

msgctxt "@action:button"
msgid "Zoom toward mouse direction"
msgstr "Zoomaa hiiren suuntaan"

msgctxt "@info:tooltip"
msgid "Zooming towards the mouse is not supported in the orthographic perspective."
msgstr ""

msgctxt "@text Placeholder for the username if it has been deleted"
msgid "deleted user"
msgstr ""

msgctxt "@item:inlistbox"
msgid "glTF Binary"
msgstr ""

msgctxt "@item:inlistbox"
msgid "glTF Embedded JSON"
msgstr ""

msgctxt "@label"
msgid "max"
msgstr ""

msgctxt "@label"
msgid "min"
msgstr ""

msgctxt "@label"
msgid "mm"
msgstr "mm"

msgctxt "@info:status"
msgid "today"
msgstr ""

msgctxt "@info:status"
msgid "tomorrow"
msgstr ""

msgctxt "@label"
msgid "version: %1"
msgstr ""

#, python-brace-format
msgctxt "@message {printer_name} is replaced with the name of the printer"
msgid "{printer_name} will be removed until the next account sync."
msgstr ""

msgctxt "@info:generic"
msgid "{} plugins failed to download"
msgstr ""

#~ msgctxt "@label"
#~ msgid "Enable printing a brim or raft. This will add a flat area around or under your object which is easy to cut off afterwards."
#~ msgstr "Ota reunuksen tai pohjaristikon tulostus käyttöön. Tämä lisää kappaleen ympärille tai alle tasaisen alueen, joka on helppo leikata pois myöhemmin."
