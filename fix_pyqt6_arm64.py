#!/usr/bin/env python3
"""
Fix PyQt6 compatibility issues on Windows ARM64
修复Windows ARM64上的PyQt6兼容性问题
"""

import sys
import os
import subprocess
from pathlib import Path

def check_architecture():
    """检查系统架构"""
    import platform
    arch = platform.machine().lower()
    print(f"系统架构: {arch}")
    return 'arm' in arch or 'aarch64' in arch

def install_compatible_qt():
    """安装兼容的Qt版本"""
    print("=== 安装兼容的Qt版本 ===")
    
    # 获取虚拟环境的pip路径
    venv_path = Path("build_windows/generators/cura_venv")
    pip_exe = venv_path / "Scripts" / "pip.exe"
    python_exe = venv_path / "Scripts" / "python.exe"
    
    if not pip_exe.exists():
        print(f"❌ 找不到pip: {pip_exe}")
        return False
    
    try:
        # 卸载有问题的PyQt6
        print("卸载PyQt6...")
        subprocess.run([str(pip_exe), "uninstall", "PyQt6", "PyQt6-Qt6", "PyQt6-sip", "-y"], 
                      check=True, capture_output=True)
        
        # 安装PySide6（更好的ARM64兼容性）
        print("安装PySide6...")
        subprocess.run([str(pip_exe), "install", "PySide6>=6.5.0"], 
                      check=True, capture_output=True)
        
        # 测试PySide6导入
        print("测试PySide6导入...")
        result = subprocess.run([str(python_exe), "-c", 
                               "from PySide6.QtCore import Qt; from PySide6.QtWidgets import QApplication; print('PySide6 导入成功')"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ PySide6安装成功")
            return True
        else:
            print(f"❌ PySide6测试失败: {result.stderr}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def create_qt_compatibility_layer():
    """创建Qt兼容性层"""
    print("=== 创建Qt兼容性层 ===")
    
    # 创建PyQt6到PySide6的兼容性映射
    compat_code = '''"""
PyQt6 to PySide6 compatibility layer for Windows ARM64
Windows ARM64上PyQt6到PySide6的兼容性层
"""

import sys
import warnings

# 检查是否需要兼容性层
try:
    import PyQt6
    print("PyQt6 available, using PyQt6")
except ImportError:
    print("PyQt6 not available, using PySide6 compatibility layer")
    
    # 创建PyQt6模块别名
    import PySide6
    sys.modules['PyQt6'] = PySide6
    
    # 映射子模块
    from PySide6 import QtCore, QtWidgets, QtGui, QtNetwork, QtOpenGL, QtSvg, QtPrintSupport
    
    # 创建PyQt6子模块别名
    sys.modules['PyQt6.QtCore'] = QtCore
    sys.modules['PyQt6.QtWidgets'] = QtWidgets
    sys.modules['PyQt6.QtGui'] = QtGui
    sys.modules['PyQt6.QtNetwork'] = QtNetwork
    sys.modules['PyQt6.QtOpenGL'] = QtOpenGL
    sys.modules['PyQt6.QtSvg'] = QtSvg
    sys.modules['PyQt6.QtPrintSupport'] = QtPrintSupport
    
    # 信号连接兼容性
    if hasattr(QtCore, 'Signal'):
        QtCore.pyqtSignal = QtCore.Signal
    if hasattr(QtCore, 'Slot'):
        QtCore.pyqtSlot = QtCore.Slot
    
    print("✅ PyQt6 to PySide6 compatibility layer activated")
'''
    
    # 保存兼容性层文件
    compat_file = Path("cura/qt_compat.py")
    compat_file.write_text(compat_code, encoding='utf-8')
    print(f"✅ 兼容性层已创建: {compat_file}")
    
    return True

def patch_cura_imports():
    """修补Cura的导入语句"""
    print("=== 修补Cura导入语句 ===")
    
    # 在cura_app.py开头添加兼容性层导入
    cura_app_file = Path("cura_app.py")
    if cura_app_file.exists():
        content = cura_app_file.read_text(encoding='utf-8')
        
        # 检查是否已经添加了兼容性层
        if "qt_compat" not in content:
            # 在第一个import之前添加兼容性层导入
            lines = content.split('\n')
            insert_index = 0
            
            # 找到第一个import语句
            for i, line in enumerate(lines):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    insert_index = i
                    break
            
            # 插入兼容性层导入
            compat_import = "# PyQt6 compatibility layer for Windows ARM64\ntry:\n    from cura import qt_compat\nexcept ImportError:\n    pass\n"
            lines.insert(insert_index, compat_import)
            
            # 写回文件
            cura_app_file.write_text('\n'.join(lines), encoding='utf-8')
            print("✅ cura_app.py已修补")
        else:
            print("✅ cura_app.py已经包含兼容性层")
    
    return True

def test_cura_startup():
    """测试Cura启动"""
    print("=== 测试Cura启动 ===")
    
    venv_python = Path("build_windows/generators/cura_venv/Scripts/python.exe")
    
    try:
        # 测试基本导入
        result = subprocess.run([str(venv_python), "-c", 
                               "import sys; print('Python:', sys.version); import cura; print('Cura imported successfully')"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Cura基本导入测试通过")
            print(result.stdout)
            return True
        else:
            print(f"❌ Cura导入测试失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Cura导入测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

def main():
    """主函数"""
    print("PyQt6 Windows ARM64 兼容性修复工具")
    print("=" * 50)
    
    if not check_architecture():
        print("⚠️  当前系统不是ARM64架构，可能不需要此修复")
    
    steps = [
        install_compatible_qt(),
        create_qt_compatibility_layer(),
        patch_cura_imports(),
        test_cura_startup(),
    ]
    
    success_count = sum(steps)
    total_count = len(steps)
    
    print("\n" + "=" * 50)
    print(f"修复完成: {success_count}/{total_count} 步骤成功")
    
    if success_count == total_count:
        print("🎉 PyQt6兼容性问题已修复！")
        print("\n下一步:")
        print("1. 在PyCharm中重新加载项目")
        print("2. 使用PySide6运行Cura")
        print("3. 如果仍有问题，请检查控制台输出")
        return True
    else:
        print(f"❌ {total_count - success_count} 个步骤失败")
        print("请检查错误信息并手动修复")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n修复过程中出现错误: {e}")
        sys.exit(1)
