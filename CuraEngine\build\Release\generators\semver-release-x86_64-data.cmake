########### AGGREGATED COMPONENTS AND DEPENDENCIES FOR THE MULTI CONFIG #####################
#############################################################################################

set(neargye-semver_COMPONENT_NAMES "")
if(DEFINED neargye-semver_FIND_DEPENDENCY_NAMES)
  list(APPEND neargye-semver_FIND_DEPENDENCY_NAMES )
  list(REMOVE_DUPLICATES neargye-semver_FIND_DEPENDENCY_NAMES)
else()
  set(neargye-semver_FIND_DEPENDENCY_NAMES )
endif()

########### VARIABLES #######################################################################
#############################################################################################
set(neargye-semver_PACKAGE_FOLDER_RELEASE "C:/Users/<USER>/.conan2/p/nearg388d58da7a54c/p")
set(neargye-semver_BUILD_MODULES_PATHS_RELEASE )


set(neargye-semver_INCLUDE_DIRS_RELEASE "${neargye-semver_PACKAGE_FOLDER_RELEASE}/include")
set(neargye-semver_RES_DIRS_RELEASE )
set(neargye-semver_DEFINITIONS_RELEASE )
set(neargye-semver_SHARED_LINK_FLAGS_RELEASE )
set(neargye-semver_EXE_LINK_FLAGS_RELEASE )
set(neargye-semver_OBJECTS_RELEASE )
set(neargye-semver_COMPILE_DEFINITIONS_RELEASE )
set(neargye-semver_COMPILE_OPTIONS_C_RELEASE )
set(neargye-semver_COMPILE_OPTIONS_CXX_RELEASE )
set(neargye-semver_LIB_DIRS_RELEASE )
set(neargye-semver_BIN_DIRS_RELEASE )
set(neargye-semver_LIBRARY_TYPE_RELEASE UNKNOWN)
set(neargye-semver_IS_HOST_WINDOWS_RELEASE 1)
set(neargye-semver_LIBS_RELEASE )
set(neargye-semver_SYSTEM_LIBS_RELEASE )
set(neargye-semver_FRAMEWORK_DIRS_RELEASE )
set(neargye-semver_FRAMEWORKS_RELEASE )
set(neargye-semver_BUILD_DIRS_RELEASE )
set(neargye-semver_NO_SONAME_MODE_RELEASE FALSE)


# COMPOUND VARIABLES
set(neargye-semver_COMPILE_OPTIONS_RELEASE
    "$<$<COMPILE_LANGUAGE:CXX>:${neargye-semver_COMPILE_OPTIONS_CXX_RELEASE}>"
    "$<$<COMPILE_LANGUAGE:C>:${neargye-semver_COMPILE_OPTIONS_C_RELEASE}>")
set(neargye-semver_LINKER_FLAGS_RELEASE
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,SHARED_LIBRARY>:${neargye-semver_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,MODULE_LIBRARY>:${neargye-semver_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,EXECUTABLE>:${neargye-semver_EXE_LINK_FLAGS_RELEASE}>")


set(neargye-semver_COMPONENTS_RELEASE )