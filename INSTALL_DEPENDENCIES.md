# 依赖安装指南

在运行Cura开发环境设置脚本之前，请确保安装了正确版本的依赖软件。

## 必需依赖

### 1. Python 3.12+

#### Windows
1. 访问 [python.org](https://www.python.org/downloads/)
2. 下载Python 3.12或更高版本
3. 安装时勾选 "Add Python to PATH"
4. 验证安装：
   ```cmd
   python --version
   ```

#### macOS
使用Homebrew安装：
```bash
# 安装Homebrew（如果未安装）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装Python 3.12
brew install python@3.12

# 验证安装
python3 --version
```

#### Linux (Ubuntu/Debian)
```bash
# 更新包列表
sudo apt update

# 安装Python 3.12
sudo apt install python3.12 python3.12-venv python3.12-dev

# 验证安装
python3.12 --version
```

### 2. Git

#### Windows
1. 访问 [git-scm.com](https://git-scm.com/download/win)
2. 下载并安装Git for Windows
3. 验证安装：
   ```cmd
   git --version
   ```

#### macOS
```bash
# 使用Homebrew
brew install git

# 或使用Xcode命令行工具
xcode-select --install
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt install git

# CentOS/RHEL
sudo yum install git
```

### 3. 开发工具

#### Windows
- **Visual Studio 2022** (Community版本免费)
  - 访问 [visualstudio.microsoft.com](https://visualstudio.microsoft.com/)
  - 安装时选择 "C++ build tools" 工作负载
  - 或安装 "Build Tools for Visual Studio 2022"

#### macOS
- **Xcode 12+**
  ```bash
  # 从App Store安装Xcode，或安装命令行工具
  xcode-select --install
  ```

#### Linux
- **GCC 13+**
  ```bash
  # Ubuntu/Debian
  sudo apt install build-essential gcc-13 g++-13
  
  # 设置默认编译器
  sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-13 100
  sudo update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-13 100
  ```

### 4. CMake 3.23+

#### Windows
1. 访问 [cmake.org](https://cmake.org/download/)
2. 下载Windows安装包
3. 安装时选择 "Add CMake to system PATH"

#### macOS
```bash
brew install cmake
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt install cmake

# 如果版本太旧，从官方源安装
wget -O - https://apt.kitware.com/keys/kitware-archive-latest.asc 2>/dev/null | gpg --dearmor - | sudo tee /usr/share/keyrings/kitware-archive-keyring.gpg >/dev/null
echo 'deb [signed-by=/usr/share/keyrings/kitware-archive-keyring.gpg] https://apt.kitware.com/ubuntu/ focal main' | sudo tee /etc/apt/sources.list.d/kitware.list >/dev/null
sudo apt update
sudo apt install cmake
```

### 5. Ninja 1.10+

#### Windows
1. 访问 [ninja-build.org](https://ninja-build.org/)
2. 下载ninja.exe
3. 将其放在PATH中的目录

#### macOS
```bash
brew install ninja
```

#### Linux
```bash
sudo apt install ninja-build
```

## 验证安装

运行检查脚本验证所有依赖：

```bash
python check_requirements.py
```

如果所有检查都通过，您就可以运行设置脚本了：

### Windows
```powershell
.\setup_cura_dev_env.ps1
```

### macOS/Linux
```bash
./setup_cura_dev_env.sh
```

## 常见问题

### Python版本问题
- **问题**: `python --version` 显示旧版本
- **解决**: 确保新版本Python在PATH中，或使用 `python3.12` 命令

### 权限问题
- **Windows**: 以管理员身份运行PowerShell
- **macOS/Linux**: 使用 `sudo` 安装系统包

### 网络问题
- 确保网络连接正常
- 如果在企业网络中，可能需要配置代理

### Visual Studio问题
- 确保安装了C++构建工具
- 检查Windows SDK是否已安装

## 下一步

依赖安装完成后，请返回主README文件继续设置开发环境。
