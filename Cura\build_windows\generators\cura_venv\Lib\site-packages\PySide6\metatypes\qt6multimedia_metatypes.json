[{"classes": [{"className": "QVideoFrame", "enums": [{"isClass": false, "isFlag": false, "name": "MapMode", "values": ["NotMapped", "Read<PERSON>nly", "WriteOnly", "ReadWrite"]}], "gadget": true, "lineNumber": 26, "qualifiedClassName": "QVideoFrame"}], "inputFile": "qvideoframe.h", "outputRevision": 69}, {"classes": [{"className": "QVideoFrameFormat", "enums": [{"isClass": false, "isFlag": false, "name": "PixelFormat", "values": ["Format_Invalid", "Format_ARGB8888", "Format_ARGB8888_Premultiplied", "Format_XRGB8888", "Format_BGRA8888", "Format_BGRA8888_Premultiplied", "Format_BGRX8888", "Format_ABGR8888", "Format_XBGR8888", "Format_RGBA8888", "Format_RGBX8888", "Format_AYUV", "Format_AYUV_Premultiplied", "Format_YUV420P", "Format_YUV422P", "Format_YV12", "Format_UYVY", "Format_YUYV", "Format_NV12", "Format_NV21", "Format_IMC1", "Format_IMC2", "Format_IMC3", "Format_IMC4", "Format_Y8", "Format_Y16", "Format_P010", "Format_P016", "Format_SamplerExternalOES", "Format_Jpeg", "Format_SamplerRect", "Format_YUV420P10"]}], "gadget": true, "lineNumber": 27, "qualifiedClassName": "QVideoFrameFormat"}], "inputFile": "qvideoframeformat.h", "outputRevision": 69}, {"classes": [{"className": "QVideoFrameInput", "lineNumber": 18, "object": true, "qualifiedClassName": "QVideoFrameInput", "signals": [{"access": "public", "index": 0, "name": "readyToSendVideoFrame", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qvideoframeinput.h", "outputRevision": 69}, {"classes": [{"className": "QAudioBufferInput", "lineNumber": 17, "object": true, "qualifiedClassName": "QAudioBufferInput", "signals": [{"access": "public", "index": 0, "name": "readyToSendAudioBuffer", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudiobufferinput.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformVideoSource", "lineNumber": 31, "object": true, "qualifiedClassName": "QPlatformVideoSource", "signals": [{"access": "public", "arguments": [{"type": "QVideoFrame"}], "index": 0, "name": "newVideoFrame", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 1, "name": "activeChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "errorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformvideosource_p.h", "outputRevision": 69}, {"classes": [{"className": "QWindowsAudioSink", "lineNumber": 24, "object": true, "qualifiedClassName": "QWindowsAudioSink", "superClasses": [{"access": "public", "name": "QPlatformAudioSink"}]}], "inputFile": "qwindowsaudiosink_p.h", "outputRevision": 69}, {"classes": [{"className": "QWindowsAudioSource", "lineNumber": 25, "object": true, "qualifiedClassName": "QWindowsAudioSource", "superClasses": [{"access": "public", "name": "QPlatformAudioSource"}]}], "inputFile": "qwindowsaudiosource_p.h", "outputRevision": 69}, {"classes": [{"className": "QAudioBufferOutput", "lineNumber": 16, "object": true, "qualifiedClassName": "QAudioBufferOutput", "signals": [{"access": "public", "arguments": [{"name": "buffer", "type": "QAudioBuffer"}], "index": 0, "name": "audioBufferReceived", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudiobufferoutput.h", "outputRevision": 69}, {"classes": [{"className": "QAudioDecoder", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "ResourceError", "FormatError", "AccessDeniedError", "NotSupportedError"]}], "lineNumber": 13, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "isDecoding", "notify": "isDecodingChanged", "read": "isDecoding", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "error", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "bufferAvailable", "notify": "bufferAvailableChanged", "read": "bufferAvailable", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QAudioDecoder", "signals": [{"access": "public", "arguments": [{"type": "bool"}], "index": 0, "name": "bufferAvailableChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "bufferReady", "returnType": "void"}, {"access": "public", "index": 2, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 3, "name": "isDecodingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "format", "type": "QAudioFormat"}], "index": 4, "name": "formatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QAudioDecoder::<PERSON><PERSON><PERSON>"}], "index": 5, "name": "error", "returnType": "void"}, {"access": "public", "index": 6, "name": "sourceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "qint64"}], "index": 7, "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "duration", "type": "qint64"}], "index": 8, "name": "durationChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 9, "name": "start", "returnType": "void"}, {"access": "public", "index": 10, "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudiodecoder.h", "outputRevision": 69}, {"classes": [{"className": "QAudioDevice", "enums": [{"isClass": false, "isFlag": false, "name": "Mode", "values": ["<PERSON><PERSON>", "Input", "Output"]}], "gadget": true, "lineNumber": 24, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "id", "read": "id", "required": false, "scriptable": true, "stored": true, "type": "QByteArray", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "description", "read": "description", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "isDefault", "read": "isDefault", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "mode", "read": "mode", "required": false, "scriptable": true, "stored": true, "type": "Mode", "user": false}], "qualifiedClassName": "QAudioDevice"}], "inputFile": "qaudiodevice.h", "outputRevision": 69}, {"classes": [{"className": "QAudioInput", "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "device", "notify": "deviceChanged", "read": "device", "required": false, "scriptable": true, "stored": true, "type": "QAudioDevice", "user": false, "write": "setDevice"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "volume", "notify": "volumeChanged", "read": "volume", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setVolume"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "muted", "notify": "mutedChanged", "read": "isMuted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMuted"}], "qualifiedClassName": "QAudioInput", "signals": [{"access": "public", "index": 0, "name": "deviceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "volume", "type": "float"}], "index": 1, "name": "volumeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "muted", "type": "bool"}], "index": 2, "name": "mutedChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "device", "type": "QAudioDevice"}], "index": 3, "name": "setDevice", "returnType": "void"}, {"access": "public", "arguments": [{"name": "volume", "type": "float"}], "index": 4, "name": "setVolume", "returnType": "void"}, {"access": "public", "arguments": [{"name": "muted", "type": "bool"}], "index": 5, "name": "setMuted", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudioinput.h", "outputRevision": 69}, {"classes": [{"className": "QAudioOutput", "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "device", "notify": "deviceChanged", "read": "device", "required": false, "scriptable": true, "stored": true, "type": "QAudioDevice", "user": false, "write": "setDevice"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "volume", "notify": "volumeChanged", "read": "volume", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setVolume"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "muted", "notify": "mutedChanged", "read": "isMuted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMuted"}], "qualifiedClassName": "QAudioOutput", "signals": [{"access": "public", "index": 0, "name": "deviceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "volume", "type": "float"}], "index": 1, "name": "volumeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "muted", "type": "bool"}], "index": 2, "name": "mutedChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "device", "type": "QAudioDevice"}], "index": 3, "name": "setDevice", "returnType": "void"}, {"access": "public", "arguments": [{"name": "volume", "type": "float"}], "index": 4, "name": "setVolume", "returnType": "void"}, {"access": "public", "arguments": [{"name": "muted", "type": "bool"}], "index": 5, "name": "setMuted", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudiooutput.h", "outputRevision": 69}, {"classes": [{"className": "QAudioSink", "lineNumber": 23, "object": true, "qualifiedClassName": "QAudioSink", "signals": [{"access": "public", "arguments": [{"name": "state", "type": "QAudio::State"}], "index": 0, "name": "stateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudiosink.h", "outputRevision": 69}, {"classes": [{"className": "QAudioSource", "lineNumber": 21, "object": true, "qualifiedClassName": "QAudioSource", "signals": [{"access": "public", "arguments": [{"name": "state", "type": "QAudio::State"}], "index": 0, "name": "stateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudiosource.h", "outputRevision": 69}, {"classes": [{"className": "QAudioStateChangeNotifier", "lineNumber": 151, "object": true, "qualifiedClassName": "QAudioStateChangeNotifier", "signals": [{"access": "public", "arguments": [{"name": "error", "type": "QAudio::<PERSON><PERSON><PERSON>"}], "index": 0, "name": "errorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "state", "type": "QAudio::State"}], "index": 1, "name": "stateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qaudiosystem_p.h", "outputRevision": 69}, {"classes": [{"className": "QAutoResetEventWin32", "lineNumber": 25, "object": true, "qualifiedClassName": "QtPrivate::QAutoResetEventWin32", "signals": [{"access": "public", "index": 0, "name": "activated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qautoresetevent_win32_p.h", "outputRevision": 69}, {"classes": [{"className": "QCamera", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "CameraError"]}, {"isClass": false, "isFlag": false, "name": "FocusMode", "values": ["FocusModeAuto", "FocusModeAutoNear", "FocusModeAutoFar", "FocusModeHyperfocal", "FocusModeInfinity", "FocusModeManual"]}, {"isClass": false, "isFlag": false, "name": "FlashMode", "values": ["<PERSON><PERSON>ff", "FlashOn", "FlashAuto"]}, {"isClass": false, "isFlag": false, "name": "TorchMode", "values": ["<PERSON><PERSON><PERSON><PERSON>", "TorchOn", "TorchAuto"]}, {"isClass": false, "isFlag": false, "name": "ExposureMode", "values": ["ExposureAuto", "ExposureManual", "ExposurePortrait", "ExposureNight", "ExposureSports", "ExposureSnow", "ExposureBeach", "ExposureAction", "ExposureLandscape", "ExposureNightPortrait", "ExposureTheatre", "ExposureSunset", "ExposureSteadyPhoto", "ExposureFireworks", "ExposureParty", "ExposureCandlelight", "ExposureBarcode"]}, {"isClass": false, "isFlag": false, "name": "WhiteBalanceMode", "values": ["WhiteBalanceAuto", "WhiteBalanceManual", "WhiteBalanceSunlight", "WhiteBalanceCloudy", "WhiteBalanceShade", "WhiteBalanceTungsten", "WhiteBalanceFluorescent", "WhiteBalanceFlash", "WhiteBalanceSunset"]}, {"alias": "Feature", "isClass": true, "isFlag": true, "name": "Features", "values": ["ColorTemperature", "ExposureCompensation", "IsoSensitivity", "ManualExposureTime", "CustomFocusPoint", "FocusDistance"]}], "lineNumber": 25, "methods": [{"access": "public", "arguments": [{"name": "mode", "type": "FocusMode"}], "index": 42, "isConst": true, "name": "isFocusModeSupported", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "mode", "type": "FlashMode"}], "index": 43, "isConst": true, "name": "isFlashModeSupported", "returnType": "bool"}, {"access": "public", "index": 44, "isConst": true, "name": "isFlashReady", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "mode", "type": "TorchMode"}], "index": 45, "isConst": true, "name": "isTorchModeSupported", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "mode", "type": "ExposureMode"}], "index": 46, "isConst": true, "name": "isExposureModeSupported", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "mode", "type": "WhiteBalanceMode"}], "index": 47, "isConst": true, "name": "isWhiteBalanceModeSupported", "returnType": "bool"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "active", "notify": "activeChanged", "read": "isActive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setActive"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "cameraDevice", "notify": "cameraDeviceChanged", "read": "cameraDevice", "required": false, "scriptable": true, "stored": true, "type": "QCameraDevice", "user": false, "write": "setCameraDevice"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "Error", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "cameraFormat", "notify": "cameraFormatChanged", "read": "cameraFormat", "required": false, "scriptable": true, "stored": true, "type": "QCameraFormat", "user": false, "write": "setCameraFormat"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "focusMode", "notify": "focusModeChanged", "read": "focusMode", "required": false, "scriptable": true, "stored": true, "type": "FocusMode", "user": false, "write": "setFocusMode"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "focusPoint", "notify": "focusPointChanged", "read": "focusPoint", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "customFocusPoint", "notify": "customFocusPointChanged", "read": "customFocusPoint", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false, "write": "setCustomFocusPoint"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "focusDistance", "notify": "focusDistanceChanged", "read": "focusDistance", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setFocusDistance"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "minimumZoomFactor", "notify": "minimumZoomFactorChanged", "read": "minimumZoomFactor", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "maximumZoomFactor", "notify": "maximumZoomFactorChanged", "read": "maximumZoomFactor", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "zoomFactor", "notify": "zoomFactorChanged", "read": "zoomFactor", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setZoomFactor"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "exposureTime", "notify": "exposureTimeChanged", "read": "exposureTime", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "manualExposureTime", "notify": "manualExposureTimeChanged", "read": "manualExposureTime", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setManualExposureTime"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "isoSensitivity", "notify": "isoSensitivityChanged", "read": "isoSensitivity", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "manualIsoSensitivity", "notify": "manualIsoSensitivityChanged", "read": "manualIsoSensitivity", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setManualIsoSensitivity"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "exposureCompensation", "notify": "exposureCompensationChanged", "read": "exposureCompensation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setExposureCompensation"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "exposureMode", "notify": "exposureModeChanged", "read": "exposureMode", "required": false, "scriptable": true, "stored": true, "type": "QCamera::ExposureMode", "user": false, "write": "setExposureMode"}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "flashReady", "notify": "flashReady", "read": "isFlashReady", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "flashMode", "notify": "flashModeChanged", "read": "flashMode", "required": false, "scriptable": true, "stored": true, "type": "QCamera::FlashMode", "user": false, "write": "setFlashMode"}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "torchMode", "notify": "torchModeChanged", "read": "torchMode", "required": false, "scriptable": true, "stored": true, "type": "QCamera::TorchMode", "user": false, "write": "setTorchMode"}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "whiteBalanceMode", "notify": "whiteBalanceModeChanged", "read": "whiteBalanceMode", "required": false, "scriptable": true, "stored": true, "type": "WhiteBalanceMode", "user": false, "write": "setWhiteBalanceMode"}, {"constant": false, "designable": true, "final": false, "index": 22, "name": "colorTemperature", "notify": "colorTemperatureChanged", "read": "colorTemperature", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setColorTemperature"}, {"constant": false, "designable": true, "final": false, "index": 23, "name": "supportedFeatures", "notify": "supportedFeaturesChanged", "read": "supportedFeatures", "required": false, "scriptable": true, "stored": true, "type": "Features", "user": false}], "qualifiedClassName": "QCamera", "signals": [{"access": "public", "arguments": [{"type": "bool"}], "index": 0, "name": "activeChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "errorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QCamera::<PERSON><PERSON><PERSON>"}, {"name": "errorString", "type": "QString"}], "index": 2, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "index": 3, "name": "cameraDeviceChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "cameraFormatChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "supportedFeaturesChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "focusModeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "float"}], "index": 7, "name": "zoomFactorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "float"}], "index": 8, "name": "minimumZoomFactorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "float"}], "index": 9, "name": "maximumZoomFactorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "float"}], "index": 10, "name": "focusDistanceChanged", "returnType": "void"}, {"access": "public", "index": 11, "name": "focusPointChanged", "returnType": "void"}, {"access": "public", "index": 12, "name": "customFocusPointChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "bool"}], "index": 13, "name": "flashReady", "returnType": "void"}, {"access": "public", "index": 14, "name": "flashModeChanged", "returnType": "void"}, {"access": "public", "index": 15, "name": "torchModeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "speed", "type": "float"}], "index": 16, "name": "exposureTimeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "speed", "type": "float"}], "index": 17, "name": "manualExposureTimeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "int"}], "index": 18, "name": "isoSensitivityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "int"}], "index": 19, "name": "manualIsoSensitivityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "float"}], "index": 20, "name": "exposureCompensationChanged", "returnType": "void"}, {"access": "public", "index": 21, "name": "exposureModeChanged", "returnType": "void"}, {"access": "public", "index": 22, "isConst": true, "name": "whiteBalanceModeChanged", "returnType": "void"}, {"access": "public", "index": 23, "isConst": true, "name": "colorTemperatureChanged", "returnType": "void"}, {"access": "public", "index": 24, "name": "brightnessChanged", "returnType": "void"}, {"access": "public", "index": 25, "name": "contrastChanged", "returnType": "void"}, {"access": "public", "index": 26, "name": "saturationChanged", "returnType": "void"}, {"access": "public", "index": 27, "name": "h<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "active", "type": "bool"}], "index": 28, "name": "setActive", "returnType": "void"}, {"access": "public", "index": 29, "name": "start", "returnType": "void"}, {"access": "public", "index": 30, "name": "stop", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zoom", "type": "float"}, {"name": "rate", "type": "float"}], "index": 31, "name": "zoomTo", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "FlashMode"}], "index": 32, "name": "setFlashMode", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "TorchMode"}], "index": 33, "name": "setTorchMode", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "ExposureMode"}], "index": 34, "name": "setExposureMode", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ev", "type": "float"}], "index": 35, "name": "setExposureCompensation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "iso", "type": "int"}], "index": 36, "name": "setManualIsoSensitivity", "returnType": "void"}, {"access": "public", "index": 37, "name": "setAutoIsoSensitivity", "returnType": "void"}, {"access": "public", "arguments": [{"name": "seconds", "type": "float"}], "index": 38, "name": "setManualExposureTime", "returnType": "void"}, {"access": "public", "index": 39, "name": "setAutoExposureTime", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "WhiteBalanceMode"}], "index": 40, "name": "setWhiteBalanceMode", "returnType": "void"}, {"access": "public", "arguments": [{"name": "colorTemperature", "type": "int"}], "index": 41, "name": "setColorTemperature", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qcamera.h", "outputRevision": 69}, {"classes": [{"className": "QCameraFormat", "gadget": true, "lineNumber": 14, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "resolution", "read": "resolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "pixelFormat", "read": "pixelFormat", "required": false, "scriptable": true, "stored": true, "type": "QVideoFrameFormat::PixelFormat", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "minFrameRate", "read": "minFrameRate", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "maxFrameRate", "read": "maxFrameRate", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}], "qualifiedClassName": "QCameraFormat"}, {"className": "QCameraDevice", "enums": [{"isClass": false, "isFlag": false, "name": "Position", "values": ["UnspecifiedPosition", "BackFace", "FrontFace"]}], "gadget": true, "lineNumber": 45, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "id", "read": "id", "required": false, "scriptable": true, "stored": true, "type": "QByteArray", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "description", "read": "description", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "isDefault", "read": "isDefault", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "position", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "Position", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "videoFormats", "read": "videoFormats", "required": false, "scriptable": true, "stored": true, "type": "QList<QCameraFormat>", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "correctionAngle", "read": "correctionAngle", "required": false, "scriptable": true, "stored": true, "type": "QtVideo::Rotation", "user": false}], "qualifiedClassName": "QCameraDevice"}], "inputFile": "qcameradevice.h", "outputRevision": 69}, {"classes": [{"className": "QCapturableWindow", "gadget": true, "lineNumber": 19, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "description", "read": "description", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QCapturableWindow"}], "inputFile": "qcapturablewindow.h", "outputRevision": 69}, {"classes": [{"className": "QImageCapture", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "NotReadyError", "ResourceError", "OutOfSpaceError", "NotSupportedFeatureError", "FormatError"]}, {"isClass": false, "isFlag": false, "name": "Quality", "values": ["VeryLowQuality", "LowQuality", "NormalQuality", "HighQuality", "VeryHighQuality"]}, {"isClass": false, "isFlag": false, "name": "FileFormat", "values": ["UnspecifiedFormat", "JPEG", "PNG", "WebP", "Tiff", "LastFileFormat"]}], "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "readyForCapture", "notify": "readyForCaptureChanged", "read": "isReadyForCapture", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "metaData", "notify": "metaDataChanged", "read": "metaData", "required": false, "scriptable": true, "stored": true, "type": "QMediaMetaData", "user": false, "write": "setMetaData"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "Error", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "fileFormat", "notify": "fileFormatChanged", "read": "fileFormat", "required": false, "scriptable": true, "stored": true, "type": "FileFormat", "user": false, "write": "setFileFormat"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "quality", "notify": "qualityChanged", "read": "quality", "required": false, "scriptable": true, "stored": true, "type": "Quality", "user": false, "write": "setQuality"}], "qualifiedClassName": "QImageCapture", "signals": [{"access": "public", "index": 0, "name": "errorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}, {"name": "error", "type": "QImageCapture::<PERSON><PERSON><PERSON>"}, {"name": "errorString", "type": "QString"}], "index": 1, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ready", "type": "bool"}], "index": 2, "name": "readyForCaptureChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "metaDataChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "fileFormatChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "qualityChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "resolutionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}], "index": 7, "name": "imageExposed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}, {"name": "preview", "type": "QImage"}], "index": 8, "name": "imageCaptured", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}, {"name": "metaData", "type": "QMediaMetaData"}], "index": 9, "name": "imageMetadataAvailable", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}, {"name": "frame", "type": "QVideoFrame"}], "index": 10, "name": "imageAvailable", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}, {"name": "fileName", "type": "QString"}], "index": 11, "name": "imageSaved", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "location", "type": "QString"}], "index": 12, "name": "captureToFile", "returnType": "int"}, {"access": "public", "index": 13, "isCloned": true, "name": "captureToFile", "returnType": "int"}, {"access": "public", "index": 14, "name": "capture", "returnType": "int"}, {"access": "private", "arguments": [{"type": "int"}, {"type": "int"}, {"type": "QString"}], "index": 15, "name": "_q_error", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qimagecapture.h", "outputRevision": 69}, {"classes": [{"className": "QMediaCaptureSession", "lineNumber": 26, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "audioInput", "notify": "audioInputChanged", "read": "audioInput", "required": false, "scriptable": true, "stored": true, "type": "QAudioInput*", "user": false, "write": "setAudioInput"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "audioBufferInput", "notify": "audioBufferInputChanged", "read": "audioBufferInput", "required": false, "revision": 1544, "scriptable": true, "stored": true, "type": "QAudioBufferInput*", "user": false, "write": "setAudioBufferInput"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "audioOutput", "notify": "audioOutputChanged", "read": "audioOutput", "required": false, "scriptable": true, "stored": true, "type": "QAudioOutput*", "user": false, "write": "setAudioOutput"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "camera", "notify": "cameraChanged", "read": "camera", "required": false, "scriptable": true, "stored": true, "type": "QCamera*", "user": false, "write": "setCamera"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "screenCapture", "notify": "screenCaptureChanged", "read": "screenCapture", "required": false, "scriptable": true, "stored": true, "type": "QScreenCapture*", "user": false, "write": "setScreenCapture"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "windowCapture", "notify": "windowCaptureChanged", "read": "windowCapture", "required": false, "scriptable": true, "stored": true, "type": "QWindowCapture*", "user": false, "write": "setWindowCapture"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "videoFrameInput", "notify": "videoFrameInputChanged", "read": "videoFrameInput", "required": false, "revision": 1544, "scriptable": true, "stored": true, "type": "QVideoFrameInput*", "user": false, "write": "setVideoFrameInput"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "imageCapture", "notify": "imageCaptureChanged", "read": "imageCapture", "required": false, "scriptable": true, "stored": true, "type": "QImageCapture*", "user": false, "write": "setImageCapture"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "recorder", "notify": "<PERSON><PERSON><PERSON><PERSON>", "read": "recorder", "required": false, "scriptable": true, "stored": true, "type": "QMediaRecorder*", "user": false, "write": "setRecorder"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "videoOutput", "notify": "videoOutputChanged", "read": "videoOutput", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false, "write": "setVideoOutput"}], "qualifiedClassName": "QMediaCaptureSession", "signals": [{"access": "public", "index": 0, "name": "audioInputChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "audioBufferInputChanged", "returnType": "void", "revision": 1544}, {"access": "public", "index": 2, "name": "cameraChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "screenCaptureChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "windowCaptureChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "videoFrameInputChanged", "returnType": "void", "revision": 1544}, {"access": "public", "index": 6, "name": "imageCaptureChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 8, "name": "videoOutputChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "audioOutputChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmediacapturesession.h", "outputRevision": 69}, {"classes": [{"className": "QMediaDevices", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "audioInputs", "notify": "audioInputsChanged", "read": "audioInputs", "required": false, "scriptable": true, "stored": true, "type": "QList<QAudioDevice>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "audioOutputs", "notify": "audioOutputsChanged", "read": "audioOutputs", "required": false, "scriptable": true, "stored": true, "type": "QList<QAudioDevice>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "videoInputs", "notify": "videoInputsChanged", "read": "videoInputs", "required": false, "scriptable": true, "stored": true, "type": "QList<QCameraDevice>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "defaultAudioInput", "notify": "audioInputsChanged", "read": "defaultAudioInput", "required": false, "scriptable": true, "stored": true, "type": "QAudioDevice", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "defaultAudioOutput", "notify": "audioOutputsChanged", "read": "defaultAudioOutput", "required": false, "scriptable": true, "stored": true, "type": "QAudioDevice", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "defaultVideoInput", "notify": "videoInputsChanged", "read": "defaultVideoInput", "required": false, "scriptable": true, "stored": true, "type": "QCameraDevice", "user": false}], "qualifiedClassName": "QMediaDevices", "signals": [{"access": "public", "index": 0, "name": "audioInputsChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "audioOutputsChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "videoInputsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmediadevices.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "RegisterEnumClassesUnscoped", "value": "false"}], "className": "QMediaFormat", "enums": [{"isClass": false, "isFlag": false, "name": "FileFormat", "values": ["UnspecifiedFormat", "WMV", "AVI", "<PERSON><PERSON><PERSON>", "MPEG4", "<PERSON><PERSON>", "QuickTime", "WebM", "Mpeg4Audio", "AAC", "WMA", "MP3", "FLAC", "Wave", "LastFileFormat"]}, {"isClass": true, "isFlag": false, "name": "AudioCodec", "values": ["Unspecified", "MP3", "AAC", "AC3", "EAC3", "FLAC", "DolbyTrueHD", "Opus", "Vorbis", "Wave", "WMA", "ALAC", "LastAudioCodec"]}, {"isClass": true, "isFlag": false, "name": "VideoCodec", "values": ["Unspecified", "MPEG1", "MPEG2", "MPEG4", "H264", "H265", "VP8", "VP9", "AV1", "Theora", "WMV", "MotionJPEG", "LastVideoCodec"]}, {"isClass": false, "isFlag": false, "name": "ConversionMode", "values": ["Encode", "Decode"]}], "gadget": true, "lineNumber": 18, "methods": [{"access": "public", "arguments": [{"name": "mode", "type": "ConversionMode"}], "index": 0, "isConst": true, "name": "isSupported", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "m", "type": "ConversionMode"}], "index": 1, "name": "supportedFileFormats", "returnType": "QList<FileFormat>"}, {"access": "public", "arguments": [{"name": "m", "type": "ConversionMode"}], "index": 2, "name": "supportedVideoCodecs", "returnType": "QList<VideoCodec>"}, {"access": "public", "arguments": [{"name": "m", "type": "ConversionMode"}], "index": 3, "name": "supportedAudioCodecs", "returnType": "QList<AudioCodec>"}, {"access": "public", "arguments": [{"name": "fileFormat", "type": "FileFormat"}], "index": 4, "name": "fileFormatName", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "codec", "type": "AudioCodec"}], "index": 5, "name": "audioCodecName", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "codec", "type": "VideoCodec"}], "index": 6, "name": "videoCodecName", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "fileFormat", "type": "QMediaFormat::FileFormat"}], "index": 7, "name": "fileFormatDescription", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "codec", "type": "QMediaFormat::AudioCodec"}], "index": 8, "name": "audioCodecDescription", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "codec", "type": "QMediaFormat::VideoCodec"}], "index": 9, "name": "videoCodecDescription", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "fileFormat", "read": "fileFormat", "required": false, "scriptable": true, "stored": true, "type": "FileFormat", "user": false, "write": "setFileFormat"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "audioCodec", "read": "audioCodec", "required": false, "scriptable": true, "stored": true, "type": "AudioCodec", "user": false, "write": "setAudioCodec"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "videoCodec", "read": "videoCodec", "required": false, "scriptable": true, "stored": true, "type": "VideoCodec", "user": false, "write": "setVideoCodec"}], "qualifiedClassName": "QMediaFormat"}], "inputFile": "qmediaformat.h", "outputRevision": 69}, {"classes": [{"className": "QMediaMetaData", "enums": [{"isClass": false, "isFlag": false, "name": "Key", "values": ["Title", "Author", "Comment", "Description", "Genre", "Date", "Language", "Publisher", "Copyright", "Url", "Duration", "MediaType", "FileFormat", "AudioBitRate", "AudioCodec", "VideoBitRate", "VideoCodec", "VideoFrameRate", "AlbumTitle", "AlbumArtist", "ContributingArtist", "TrackNumber", "Composer", "LeadPerformer", "ThumbnailImage", "CoverArtImage", "Orientation", "Resolution", "Has<PERSON>dr<PERSON><PERSON>nt"]}], "gadget": true, "lineNumber": 22, "methods": [{"access": "public", "arguments": [{"name": "k", "type": "Key"}], "index": 0, "isConst": true, "name": "value", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "k", "type": "Key"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 1, "name": "insert", "returnType": "void"}, {"access": "public", "arguments": [{"name": "k", "type": "Key"}], "index": 2, "name": "remove", "returnType": "void"}, {"access": "public", "index": 3, "isConst": true, "name": "keys", "returnType": "QList<Key>"}, {"access": "public", "index": 4, "name": "clear", "returnType": "void"}, {"access": "public", "index": 5, "isConst": true, "name": "isEmpty", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "k", "type": "Key"}], "index": 6, "isConst": true, "name": "stringValue", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "k", "type": "Key"}], "index": 7, "name": "metaDataKeyToString", "returnType": "QString"}], "qualifiedClassName": "QMediaMetaData"}], "inputFile": "qmediametadata.h", "outputRevision": 69}, {"classes": [{"className": "QMediaPlayer", "enums": [{"isClass": false, "isFlag": false, "name": "PlaybackState", "values": ["StoppedState", "PlayingState", "PausedState"]}, {"isClass": false, "isFlag": false, "name": "MediaStatus", "values": ["NoMedia", "LoadingMedia", "LoadedMedia", "StalledMedia", "BufferingMedia", "BufferedMedia", "EndOfMedia", "InvalidMedia"]}, {"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "ResourceError", "FormatError", "NetworkError", "AccessDeniedError"]}, {"isClass": false, "isFlag": false, "name": "Loops", "values": ["Infinite", "Once"]}], "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "duration", "notify": "durationChanged", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "qint64", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "qint64", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "bufferProgress", "notify": "bufferProgressChanged", "read": "bufferProgress", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "hasAudio", "notify": "hasAudioChanged", "read": "hasAudio", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "hasVideo", "notify": "hasVideoChanged", "read": "hasVideo", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "seekable", "notify": "seekableChanged", "read": "isSeekable", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "playing", "notify": "playingChanged", "read": "isPlaying", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "playbackRate", "notify": "playbackRateChanged", "read": "playbackRate", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setPlaybackRate"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "loops", "notify": "loopsChanged", "read": "loops", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLoops"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "playbackState", "notify": "playbackStateChanged", "read": "playbackState", "required": false, "scriptable": true, "stored": true, "type": "PlaybackState", "user": false}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "mediaStatus", "notify": "mediaStatusChanged", "read": "mediaStatus", "required": false, "scriptable": true, "stored": true, "type": "MediaStatus", "user": false}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "metaData", "notify": "metaDataChanged", "read": "metaData", "required": false, "scriptable": true, "stored": true, "type": "QMediaMetaData", "user": false}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "Error", "user": false}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "videoOutput", "notify": "videoOutputChanged", "read": "videoOutput", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false, "write": "setVideoOutput"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "audioOutput", "notify": "audioOutputChanged", "read": "audioOutput", "required": false, "scriptable": true, "stored": true, "type": "QAudioOutput*", "user": false, "write": "setAudioOutput"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "audioBufferOutput", "notify": "audioBufferOutputChanged", "read": "audioBufferOutput", "required": false, "scriptable": true, "stored": true, "type": "QAudioBufferOutput*", "user": false, "write": "setAudioBufferOutput"}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "audioTracks", "notify": "tracksChanged", "read": "audioTracks", "required": false, "scriptable": true, "stored": true, "type": "QList<QMediaMetaData>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "videoTracks", "notify": "tracksChanged", "read": "videoTracks", "required": false, "scriptable": true, "stored": true, "type": "QList<QMediaMetaData>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "subtitleTracks", "notify": "tracksChanged", "read": "subtitleTracks", "required": false, "scriptable": true, "stored": true, "type": "QList<QMediaMetaData>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "activeAudioTrack", "notify": "activeTracksChanged", "read": "activeAudioTrack", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setActiveAudioTrack"}, {"constant": false, "designable": true, "final": false, "index": 22, "name": "activeVideoTrack", "notify": "activeTracksChanged", "read": "activeVideoTrack", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setActiveVideoTrack"}, {"constant": false, "designable": true, "final": false, "index": 23, "name": "activeSubtitleTrack", "notify": "activeTracksChanged", "read": "activeSubtitleTrack", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setActiveSubtitleTrack"}], "qualifiedClassName": "QMediaPlayer", "signals": [{"access": "public", "arguments": [{"name": "media", "type": "QUrl"}], "index": 0, "name": "sourceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "newState", "type": "QMediaPlayer::PlaybackState"}], "index": 1, "name": "playbackStateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "status", "type": "QMediaPlayer::MediaStatus"}], "index": 2, "name": "mediaStatusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "duration", "type": "qint64"}], "index": 3, "name": "durationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "qint64"}], "index": 4, "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "available", "type": "bool"}], "index": 5, "name": "hasAudioChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "videoAvailable", "type": "bool"}], "index": 6, "name": "hasVideoChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "progress", "type": "float"}], "index": 7, "name": "bufferProgressChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "seekable", "type": "bool"}], "index": 8, "name": "seekableChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "playing", "type": "bool"}], "index": 9, "name": "playingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rate", "type": "qreal"}], "index": 10, "name": "playbackRateChanged", "returnType": "void"}, {"access": "public", "index": 11, "name": "loopsChanged", "returnType": "void"}, {"access": "public", "index": 12, "name": "metaDataChanged", "returnType": "void"}, {"access": "public", "index": 13, "name": "videoOutputChanged", "returnType": "void"}, {"access": "public", "index": 14, "name": "audioOutputChanged", "returnType": "void"}, {"access": "public", "index": 15, "name": "audioBufferOutputChanged", "returnType": "void", "revision": 1544}, {"access": "public", "index": 16, "name": "tracksChanged", "returnType": "void"}, {"access": "public", "index": 17, "name": "activeTracksChanged", "returnType": "void"}, {"access": "public", "index": 18, "name": "errorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QMediaPlayer::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "index": 19, "name": "errorOccurred", "returnType": "void"}], "slots": [{"access": "public", "index": 20, "name": "play", "returnType": "void"}, {"access": "public", "index": 21, "name": "pause", "returnType": "void"}, {"access": "public", "index": 22, "name": "stop", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "qint64"}], "index": 23, "name": "setPosition", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rate", "type": "qreal"}], "index": 24, "name": "setPlaybackRate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "source", "type": "QUrl"}], "index": 25, "name": "setSource", "returnType": "void"}, {"access": "public", "arguments": [{"name": "device", "type": "QIODevice*"}, {"name": "sourceUrl", "type": "QUrl"}], "index": 26, "name": "setSourceDevice", "returnType": "void"}, {"access": "public", "arguments": [{"name": "device", "type": "QIODevice*"}], "index": 27, "isCloned": true, "name": "setSourceDevice", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmediaplayer.h", "outputRevision": 69}, {"classes": [{"className": "QMediaRecorder", "enums": [{"isClass": false, "isFlag": false, "name": "Quality", "values": ["VeryLowQuality", "LowQuality", "NormalQuality", "HighQuality", "VeryHighQuality"]}, {"isClass": false, "isFlag": false, "name": "EncodingMode", "values": ["ConstantQualityEncoding", "ConstantBitRateEncoding", "AverageBitRateEncoding", "TwoPassEncoding"]}, {"isClass": false, "isFlag": false, "name": "RecorderState", "values": ["StoppedState", "RecordingState", "PausedState"]}, {"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "ResourceError", "FormatError", "OutOfSpaceError", "LocationNotWritable"]}], "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "recorderState", "notify": "recorderStateChanged", "read": "recorderState", "required": false, "scriptable": true, "stored": true, "type": "QMediaRecorder::RecorderState", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "duration", "notify": "durationChanged", "read": "duration", "required": false, "scriptable": true, "stored": true, "type": "qint64", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "outputLocation", "read": "outputLocation", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setOutputLocation"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "actualLocation", "notify": "actualLocationChanged", "read": "actualLocation", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "metaData", "notify": "metaDataChanged", "read": "metaData", "required": false, "scriptable": true, "stored": true, "type": "QMediaMetaData", "user": false, "write": "setMetaData"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "QMediaRecorder::<PERSON><PERSON><PERSON>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "mediaFormat", "notify": "mediaFormatChanged", "read": "mediaFormat", "required": false, "scriptable": true, "stored": true, "type": "QMediaFormat", "user": false, "write": "setMediaFormat"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "quality", "notify": "qualityChanged", "read": "quality", "required": false, "scriptable": true, "stored": true, "type": "Quality", "user": false, "write": "setQuality"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "encodingMode", "notify": "encodingModeChanged", "read": "encodingMode", "required": false, "scriptable": true, "stored": true, "type": "QMediaRecorder::EncodingMode", "user": false, "write": "setEncodingMode"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "videoResolution", "notify": "videoResolutionChanged", "read": "videoResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setVideoResolution"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "videoFrameRate", "notify": "videoFrameRateChanged", "read": "videoFrameRate", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setVideoFrameRate"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "videoBitRate", "notify": "videoBitRateChanged", "read": "videoBitRate", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setVideoBitRate"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "audioBitRate", "notify": "audioBitRateChanged", "read": "audioBitRate", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setAudioBitRate"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "audioChannelCount", "notify": "audioChannelCountChanged", "read": "audioChannelCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setAudioChannelCount"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "audioSampleRate", "notify": "audioSampleRateChanged", "read": "audioSampleRate", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setAudioSampleRate"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "autoStop", "notify": "autoStopChanged", "read": "autoStop", "required": false, "revision": 1544, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoStop"}], "qualifiedClassName": "QMediaRecorder", "signals": [{"access": "public", "arguments": [{"name": "state", "type": "RecorderState"}], "index": 0, "name": "recorderStateChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "duration", "type": "qint64"}], "index": 1, "name": "durationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "location", "type": "QUrl"}], "index": 2, "name": "actualLocationChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "encoderSettingsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "Error"}, {"name": "errorString", "type": "QString"}], "index": 4, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "index": 5, "name": "errorChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "metaDataChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "mediaFormatChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "encodingModeChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "qualityChanged", "returnType": "void"}, {"access": "public", "index": 10, "name": "videoResolutionChanged", "returnType": "void"}, {"access": "public", "index": 11, "name": "videoFrameRateChanged", "returnType": "void"}, {"access": "public", "index": 12, "name": "videoBitRateChanged", "returnType": "void"}, {"access": "public", "index": 13, "name": "audioBitRateChanged", "returnType": "void"}, {"access": "public", "index": 14, "name": "audioChannelCountChanged", "returnType": "void"}, {"access": "public", "index": 15, "name": "audioSampleRateChanged", "returnType": "void"}, {"access": "public", "index": 16, "name": "autoStopChanged", "returnType": "void", "revision": 1544}], "slots": [{"access": "public", "index": 17, "name": "record", "returnType": "void"}, {"access": "public", "index": 18, "name": "pause", "returnType": "void"}, {"access": "public", "index": 19, "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qmediarecorder.h", "outputRevision": 69}, {"classes": [{"className": "QAudioBufferSource", "lineNumber": 25, "object": true, "qualifiedClassName": "QAudioBufferSource", "signals": [{"access": "public", "arguments": [{"name": "buffer", "type": "QAudioBuffer"}], "index": 0, "name": "newAudioBuffer", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QPlatformAudioBufferInput", "lineNumber": 32, "object": true, "qualifiedClassName": "QPlatformAudioBufferInput", "signals": [{"access": "public", "index": 0, "name": "encoderUpdated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAudioBufferSource"}]}], "inputFile": "qplatformaudiobufferinput_p.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformAudioDecoder", "lineNumber": 27, "object": true, "qualifiedClassName": "QPlatformAudioDecoder", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformaudiodecoder_p.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformAudioDevices", "lineNumber": 31, "object": true, "qualifiedClassName": "QPlatformAudioDevices", "signals": [{"access": "public", "arguments": [{"type": "PrivateTag"}], "index": 0, "name": "audioInputsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "PrivateTag"}], "index": 1, "name": "audioOutputsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformaudiodevices_p.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformCamera", "lineNumber": 24, "object": true, "qualifiedClassName": "QPlatformCamera", "signals": [{"access": "public", "arguments": [{"name": "error", "type": "QCamera::<PERSON><PERSON><PERSON>"}, {"name": "errorString", "type": "QString"}], "index": 0, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPlatformVideoSource"}]}], "inputFile": "qplatformcamera_p.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformImageCapture", "lineNumber": 54, "object": true, "qualifiedClassName": "QPlatformImageCapture", "signals": [{"access": "public", "arguments": [{"name": "ready", "type": "bool"}], "index": 0, "name": "readyForCaptureChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "requestId", "type": "int"}], "index": 1, "name": "imageExposed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "requestId", "type": "int"}, {"name": "preview", "type": "QImage"}], "index": 2, "name": "imageCaptured", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}, {"type": "QMediaMetaData"}], "index": 3, "name": "imageMetadataAvailable", "returnType": "void"}, {"access": "public", "arguments": [{"name": "requestId", "type": "int"}, {"name": "buffer", "type": "QVideoFrame"}], "index": 4, "name": "imageAvailable", "returnType": "void"}, {"access": "public", "arguments": [{"name": "requestId", "type": "int"}, {"name": "fileName", "type": "QString"}], "index": 5, "name": "imageSaved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "id", "type": "int"}, {"name": "error", "type": "int"}, {"name": "errorString", "type": "QString"}], "index": 6, "name": "error", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformimagecapture_p.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformMediaCaptureSession", "lineNumber": 35, "object": true, "qualifiedClassName": "QPlatformMediaCaptureSession", "signals": [{"access": "public", "index": 0, "name": "cameraChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "screenCaptureChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "windowCaptureChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "videoFrameInputChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "imageCaptureChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "encoderChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformmediacapture_p.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformMediaIntegration", "lineNumber": 66, "object": true, "qualifiedClassName": "QPlatformMediaIntegration", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformmediaintegration_p.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformMediaPlugin", "lineNumber": 29, "object": true, "qualifiedClassName": "QPlatformMediaPlugin", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformmediaplugin_p.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformSurfaceCapture", "lineNumber": 30, "object": true, "qualifiedClassName": "QPlatformSurfaceCapture", "signals": [{"access": "public", "arguments": [{"type": "WindowSource"}], "index": 0, "name": "sourceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "ScreenSource"}], "index": 1, "name": "sourceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "Error"}, {"name": "errorString", "type": "QString"}], "index": 2, "name": "errorOccurred", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "error", "type": "Error"}, {"name": "errorString", "type": "QString"}], "index": 3, "name": "updateError", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPlatformVideoSource"}]}], "inputFile": "qplatformsurfacecapture_p.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformVideoDevices", "lineNumber": 33, "object": true, "qualifiedClassName": "QPlatformVideoDevices", "signals": [{"access": "public", "arguments": [{"type": "PrivateTag"}], "index": 0, "name": "videoInputsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformvideodevices_p.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformVideoFrameInput", "lineNumber": 26, "object": true, "qualifiedClassName": "QPlatformVideoFrameInput", "signals": [{"access": "public", "index": 0, "name": "encoderUpdated", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QPlatformVideoSource"}]}], "inputFile": "qplatformvideoframeinput_p.h", "outputRevision": 69}, {"classes": [{"className": "QPlatformVideoSink", "lineNumber": 34, "object": true, "qualifiedClassName": "QPlatformVideoSink", "signals": [{"access": "public", "index": 0, "name": "rhi<PERSON><PERSON><PERSON>", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplatformvideosink_p.h", "outputRevision": 69}, {"classes": [{"className": "QSample", "lineNumber": 35, "object": true, "qualifiedClassName": "QSample", "signals": [{"access": "public", "arguments": [{"name": "self", "type": "QPointer<QSample>"}], "index": 0, "name": "error", "returnType": "void"}, {"access": "public", "arguments": [{"name": "self", "type": "QPointer<QSample>"}], "index": 1, "name": "ready", "returnType": "void"}], "slots": [{"access": "private", "index": 2, "name": "load", "returnType": "void"}, {"access": "private", "arguments": [{"name": "errorCode", "type": "int"}], "index": 3, "name": "handleLoadingError", "returnType": "void"}, {"access": "private", "index": 4, "isCloned": true, "name": "handleLoadingError", "returnType": "void"}, {"access": "private", "index": 5, "name": "decoderError", "returnType": "void"}, {"access": "private", "index": 6, "name": "readSample", "returnType": "void"}, {"access": "private", "index": 7, "name": "decoderReady", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QSampleCache", "lineNumber": 89, "object": true, "qualifiedClassName": "QSampleCache", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qsamplecache_p.h", "outputRevision": 69}, {"classes": [{"className": "QScreenCapture", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "InternalError", "CapturingNotSupported", "CaptureFailed", "NotFound"]}], "lineNumber": 20, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "active", "notify": "activeChanged", "read": "isActive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setActive"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "screen", "notify": "screenChanged", "read": "screen", "required": false, "scriptable": true, "stored": true, "type": "QScreen*", "user": false, "write": "setScreen"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "Error", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}], "qualifiedClassName": "QScreenCapture", "signals": [{"access": "public", "arguments": [{"type": "bool"}], "index": 0, "name": "activeChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "errorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QScreen*"}], "index": 2, "name": "screenChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QScreenCapture::<PERSON><PERSON><PERSON>"}, {"name": "errorString", "type": "QString"}], "index": 3, "name": "errorOccurred", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "active", "type": "bool"}], "index": 4, "name": "setActive", "returnType": "void"}, {"access": "public", "index": 5, "name": "start", "returnType": "void"}, {"access": "public", "index": 6, "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qscreencapture.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "DefaultMethod", "value": "play()"}], "className": "QSoundEffect", "enums": [{"isClass": false, "isFlag": false, "name": "Loop", "values": ["Infinite"]}, {"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Loading", "Ready", "Error"]}], "lineNumber": 20, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "loops", "notify": "loopCountChanged", "read": "loopCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLoopCount"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "loopsRemaining", "notify": "loopsRemainingChanged", "read": "loopsRemaining", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "volume", "notify": "volumeChanged", "read": "volume", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setVolume"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "muted", "notify": "mutedChanged", "read": "isMuted", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMuted"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "playing", "notify": "playingChanged", "read": "isPlaying", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "audioDevice", "notify": "audioDeviceChanged", "read": "audioDevice", "required": false, "scriptable": true, "stored": true, "type": "QAudioDevice", "user": false, "write": "setAudioDevice"}], "qualifiedClassName": "QSoundEffect", "signals": [{"access": "public", "index": 0, "name": "sourceChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "loopCountChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "loopsRemainingChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "volumeChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "mutedChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "loadedChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "playingChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "statusChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "audioDeviceChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 9, "name": "play", "returnType": "void"}, {"access": "public", "index": 10, "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qsoundeffect.h", "outputRevision": 69}, {"classes": [{"className": "QtVideo", "enums": [{"isClass": true, "isFlag": false, "name": "Rotation", "values": ["None", "Clockwise90", "Clockwise180", "Clockwise270"]}], "lineNumber": 14, "namespace": true, "qualifiedClassName": "QtVideo"}], "inputFile": "qtvideo.h", "outputRevision": 69}, {"classes": [{"className": "QVideoOutputOrientationHandler", "lineNumber": 25, "object": true, "qualifiedClassName": "QVideoOutputOrientationHandler", "signals": [{"access": "public", "arguments": [{"name": "angle", "type": "int"}], "index": 0, "name": "orientationChanged", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "orientation", "type": "Qt::ScreenOrientation"}], "index": 1, "name": "screenOrientationChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qvideooutputorientationhandler_p.h", "outputRevision": 69}, {"classes": [{"className": "QVideoSink", "lineNumber": 21, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "subtitleText", "notify": "subtitleTextChanged", "read": "subtitleText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setSubtitleText"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "videoSize", "notify": "videoSizeChanged", "read": "videoSize", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false}], "qualifiedClassName": "QVideoSink", "signals": [{"access": "public", "arguments": [{"name": "frame", "type": "QVideoFrame"}], "index": 0, "isConst": true, "name": "videoFrameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "subtitleText", "type": "QString"}], "index": 1, "isConst": true, "name": "subtitleTextChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "videoSizeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qvideosink.h", "outputRevision": 69}, {"classes": [{"className": "QVideoWindow", "lineNumber": 91, "methods": [{"access": "public", "index": 3, "isConst": true, "name": "videoSink", "returnType": "QVideoSink*"}], "object": true, "qualifiedClassName": "QVideoWindow", "signals": [{"access": "public", "arguments": [{"name": "mode", "type": "Qt::AspectRatioMode"}], "index": 0, "name": "aspectRatioModeChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "mode", "type": "Qt::AspectRatioMode"}], "index": 1, "name": "setAspectRatioMode", "returnType": "void"}, {"access": "private", "arguments": [{"name": "frame", "type": "QVideoFrame"}], "index": 2, "name": "setVideoFrame", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWindow"}]}], "inputFile": "qvideowindow_p.h", "outputRevision": 69}, {"classes": [{"className": "QWaveDecoder", "lineNumber": 16, "object": true, "qualifiedClassName": "QWaveDecoder", "signals": [{"access": "public", "index": 0, "name": "formatKnown", "returnType": "void"}, {"access": "public", "index": 1, "name": "parsingError", "returnType": "void"}], "slots": [{"access": "private", "index": 2, "name": "handleData", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QIODevice"}]}], "inputFile": "qwavedecoder.h", "outputRevision": 69}, {"classes": [{"className": "QWindowCapture", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "InternalError", "CapturingNotSupported", "CaptureFailed", "NotFound"]}], "lineNumber": 16, "methods": [{"access": "public", "index": 7, "name": "capturableWindows", "returnType": "QList<QCapturableWindow>"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "active", "notify": "activeChanged", "read": "isActive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setActive"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "window", "notify": "windowChanged", "read": "window", "required": false, "scriptable": true, "stored": true, "type": "QCapturableWindow", "user": false, "write": "setWindow"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "Error", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}], "qualifiedClassName": "QWindowCapture", "signals": [{"access": "public", "arguments": [{"type": "bool"}], "index": 0, "name": "activeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "window", "type": "QCapturableWindow"}], "index": 1, "name": "windowChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "errorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QWindowCapture::<PERSON><PERSON><PERSON>"}, {"name": "errorString", "type": "QString"}], "index": 3, "name": "errorOccurred", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "active", "type": "bool"}], "index": 4, "name": "setActive", "returnType": "void"}, {"access": "public", "index": 5, "name": "start", "returnType": "void"}, {"access": "public", "index": 6, "name": "stop", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qwindowcapture.h", "outputRevision": 69}, {"classes": [{"className": "SleepTimeoutMonitor", "lineNumber": 132, "object": true, "qualifiedClassName": "QtMultimediaPrivate::SleepTimeoutMonitor", "signals": [{"access": "public", "arguments": [{"type": "std::chrono::seconds"}], "index": 0, "name": "sleepTimeoutChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qwindows_wasapi_warmup_client.cpp", "outputRevision": 69}]