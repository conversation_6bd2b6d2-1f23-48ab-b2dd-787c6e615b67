# PyCharm 开发环境配置指南

## 🎉 环境搭建完成！

您的Cura和Uranium并行开发环境已经成功搭建完成！以下是配置PyCharm的详细步骤。

## 已完成的配置

✅ **Python 3.12** - 已安装并配置  
✅ **Conan 2.7.0** - 已安装并配置  
✅ **Cura依赖** - 已通过conan install安装  
✅ **Uranium可编辑模式** - 已设置为editable模式  
✅ **系统后缀** - 使用`_windows`后缀避免跨平台冲突  
✅ **CuraEngine排除** - 已从构建中排除  
✅ **PyCharm运行配置** - 已自动生成  

## PyCharm配置步骤

### 1. 打开项目

1. 启动PyCharm
2. 选择 **File > Open**
3. 选择 `C:\Mac\Home\Desktop\CuraProject\Cura` 文件夹
4. 点击 **OK**

### 2. 添加Uranium项目

1. 在PyCharm中，选择 **File > Open**
2. 选择 `C:\Mac\Home\Desktop\CuraProject\Uranium` 文件夹
3. 选择 **Attach to current project**（附加到当前项目）
4. 点击 **OK**

### 3. 配置Python解释器

1. 打开 **File > Settings** (或 **Ctrl+Alt+S**)
2. 导航到 **Project > Python Interpreter**
3. 点击齿轮图标 ⚙️ > **Add...**
4. 选择 **Existing environment**
5. 浏览到解释器路径：
   ```
   C:\Mac\Home\Desktop\CuraProject\Cura\build_windows\generators\cura_venv\Scripts\python.exe
   ```
6. 点击 **OK**

### 4. 配置项目结构

1. 打开 **File > Settings**
2. 导航到 **Project > Project Structure**
3. 确保以下目录被标记为 **Sources Root**：
   - `Cura/cura`
   - `Cura/plugins`
   - `Uranium/UM`
   - `Uranium/plugins`

### 5. 使用运行配置

PyCharm运行配置已自动生成在 `.run` 文件夹中：

- **cura.run.xml** - 启动Cura应用程序
- **cura_external_engine.run.xml** - 使用外部引擎启动Cura
- **pytest配置** - 各种测试配置

要使用这些配置：
1. 在PyCharm顶部工具栏找到运行配置下拉菜单
2. 选择 **cura** 配置
3. 点击绿色运行按钮 ▶️

### 6. 验证配置

运行以下测试来验证环境：

1. **测试Cura启动**：
   - 使用 `cura` 运行配置启动Cura
   - 应该能看到Cura界面

2. **测试Uranium并行开发**：
   - 在Uranium项目中修改一个文件
   - 重启Cura，修改应该立即生效

## 开发工作流

### 日常开发

1. **启动PyCharm**：打开Cura项目
2. **修改代码**：
   - Cura代码：直接在 `Cura/cura` 目录中修改
   - Uranium代码：直接在 `Uranium/UM` 目录中修改
3. **运行测试**：使用pytest运行配置
4. **调试**：使用PyCharm调试器

### 并行开发验证

要验证Uranium并行开发是否工作：

1. 打开 `Uranium/UM/Application.py`
2. 在某个函数中添加一个print语句
3. 运行Cura
4. 检查控制台输出是否包含您的print语句

## 项目结构

```
CuraProject/
├── Cura/                           # 主Cura项目
│   ├── build_windows/              # Windows构建文件
│   │   └── generators/
│   │       └── cura_venv/          # Cura虚拟环境
│   ├── .run/                       # PyCharm运行配置
│   ├── cura/                       # Cura源代码
│   └── cura_app.py                 # Cura入口点
├── Uranium/                        # Uranium项目（可编辑模式）
│   └── UM/                         # Uranium源代码
└── CuraEngine/                     # 独立开发（已排除）
```

## 故障排除

### 常见问题

1. **Python解释器错误**：
   - 确保选择了正确的虚拟环境Python
   - 路径：`build_windows\generators\cura_venv\Scripts\python.exe`

2. **模块导入错误**：
   - 检查项目结构配置
   - 确保源代码目录已正确标记

3. **Cura启动失败**：
   - 检查控制台错误信息
   - 确保所有依赖已正确安装

### 重新构建环境

如果需要重新构建环境：

```powershell
# 删除构建文件夹
Remove-Item -Recurse -Force Cura\build_windows

# 重新运行conan install
cd Cura
conan install . --build=missing --update -g VirtualPythonEnv -g PyCharmRunEnv -c user.generator.virtual_python_env:dev_tools=True
```

## 下一步

🎯 **开始开发**：
- 熟悉Cura和Uranium代码结构
- 运行现有测试确保环境正常
- 开始您的功能开发

🔧 **高级配置**：
- 配置代码格式化工具
- 设置Git集成
- 配置调试断点

## 支持

如果遇到问题：
1. 检查PyCharm控制台错误信息
2. 验证Python解释器配置
3. 确认虚拟环境路径正确
4. 重新运行conan install如果依赖有问题

**恭喜！您的Cura和Uranium并行开发环境已经完全配置好了！** 🚀
