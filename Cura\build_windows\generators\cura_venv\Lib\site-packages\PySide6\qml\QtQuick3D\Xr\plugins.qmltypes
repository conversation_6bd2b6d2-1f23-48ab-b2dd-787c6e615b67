import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquick3dxrabstracthapticeffect_p.h"
        name: "QQuick3DXrAbstractHapticEffect"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D.Xr/XrHapticEffect 6.9"]
        isCreatable: false
        exportMetaObjectRevisions: [1545]
    }
    Component {
        file: "private/qquick3dxrcamera_p.h"
        name: "QQuick3DXrCamera"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D.Xr/XrCamera 6.8"]
        exportMetaObjectRevisions: [1544]
        Property {
            name: "clipNear"
            type: "float"
            read: "clipNear"
            write: "setClipNear"
            notify: "clipNearChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "clipFar"
            type: "float"
            read: "clipFar"
            write: "setClipFar"
            notify: "clipFarChanged"
            index: 1
            isFinal: true
        }
        Signal {
            name: "clipNearChanged"
            Parameter { name: "clipNear"; type: "float" }
        }
        Signal {
            name: "clipFarChanged"
            Parameter { name: "clipFar"; type: "float" }
        }
        Method {
            name: "setClipNear"
            Parameter { name: "clipNear"; type: "float" }
        }
        Method {
            name: "setClipFar"
            Parameter { name: "clipFar"; type: "float" }
        }
    }
    Component {
        file: "private/qquick3dxrcontroller_p.h"
        name: "QQuick3DXrController"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D.Xr/XrController 6.8"]
        exportMetaObjectRevisions: [1544]
        Enum {
            name: "Controller"
            values: [
                "ControllerLeft",
                "ControllerRight",
                "ControllerNone",
                "LeftController",
                "RightController",
                "UnknownController"
            ]
        }
        Enum {
            name: "HandPoseSpace"
            isScoped: true
            values: ["GripPose", "AimPose"]
        }
        Property {
            name: "controller"
            type: "Controller"
            read: "controller"
            write: "setController"
            notify: "controllerChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "isActive"
            type: "bool"
            read: "isActive"
            notify: "isActiveChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "poseSpace"
            type: "HandPoseSpace"
            read: "poseSpace"
            write: "setPoseSpace"
            notify: "poseSpaceChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "pokePosition"
            type: "QVector3D"
            read: "pokePosition"
            notify: "pokePositionChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "jointPositions"
            type: "QVector3D"
            isList: true
            read: "jointPositions"
            notify: "jointPositionsChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "jointRotations"
            type: "QQuaternion"
            isList: true
            read: "jointRotations"
            notify: "jointRotationsChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Signal { name: "controllerChanged" }
        Signal { name: "actionMapperChanged" }
        Signal { name: "poseSpaceChanged" }
        Signal { name: "pokePositionChanged" }
        Signal { name: "jointPositionsChanged" }
        Signal { name: "jointRotationsChanged" }
        Signal { name: "jointDataUpdated" }
        Signal { name: "isActiveChanged" }
    }
    Component {
        file: "private/qquick3dxrhandmodel_p.h"
        name: "QQuick3DXrHandModel"
        accessSemantics: "reference"
        prototype: "QQuick3DModel"
        exports: ["QtQuick3D.Xr/XrHandModel 6.8"]
        exportMetaObjectRevisions: [1544]
        Enum {
            name: "Hand"
            type: "quint8"
            values: ["LeftHand", "RightHand", "Unknown"]
        }
        Property {
            name: "hand"
            type: "Hand"
            read: "hand"
            write: "setHand"
            notify: "handChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "handChanged" }
        Signal { name: "handTrackerChanged" }
        Method { name: "updatePose" }
    }
    Component {
        file: "private/qquick3dxractionmapper_p.h"
        name: "QQuick3DXrHapticFeedback"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtQuick3D.Xr/XrHapticFeedback 6.9"]
        exportMetaObjectRevisions: [1545]
        Enum {
            name: "Controller"
            isScoped: true
            type: "quint8"
            values: ["LeftController", "RightController", "UnknownController"]
        }
        Enum {
            name: "Condition"
            isScoped: true
            type: "quint8"
            values: ["RisingEdge", "TrailingEdge"]
        }
        Property {
            name: "controller"
            type: "Controller"
            read: "controller"
            write: "setController"
            notify: "controllerChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "hapticEffect"
            type: "QQuick3DXrAbstractHapticEffect"
            isPointer: true
            read: "hapticEffect"
            write: "setHapticEffect"
            notify: "hapticEffectChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "trigger"
            type: "bool"
            read: "trigger"
            write: "setTrigger"
            notify: "triggerChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "condition"
            type: "Condition"
            read: "condition"
            write: "setCondition"
            notify: "conditionChanged"
            index: 3
            isFinal: true
        }
        Signal { name: "controllerChanged" }
        Signal { name: "hapticEffectChanged" }
        Signal { name: "triggerChanged" }
        Signal { name: "conditionChanged" }
        Method { name: "start" }
        Method { name: "stop" }
    }
    Component {
        file: "private/qquick3dxractionmapper_p.h"
        name: "QQuick3DXrInputAction"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQuick3D.Xr/XrInputAction 6.8",
            "QtQuick3D.Xr/XrInputAction 6.9"
        ]
        exportMetaObjectRevisions: [1544, 1545]
        Enum {
            name: "Hand"
            type: "quint8"
            values: ["LeftHand", "RightHand", "Unknown"]
        }
        Enum {
            name: "Action"
            type: "short"
            values: [
                "CustomAction",
                "Button1Pressed",
                "Button1Touched",
                "Button2Pressed",
                "Button2Touched",
                "ButtonMenuPressed",
                "ButtonMenuTouched",
                "ButtonSystemPressed",
                "ButtonSystemTouched",
                "SqueezeValue",
                "SqueezeForce",
                "SqueezePressed",
                "TriggerValue",
                "TriggerPressed",
                "TriggerTouched",
                "ThumbstickX",
                "ThumbstickY",
                "ThumbstickPressed",
                "ThumbstickTouched",
                "ThumbrestTouched",
                "TrackpadX",
                "TrackpadY",
                "TrackpadForce",
                "TrackpadTouched",
                "TrackpadPressed",
                "IndexFingerPinch",
                "MiddleFingerPinch",
                "RingFingerPinch",
                "LittleFingerPinch",
                "HandTrackingMenuPress",
                "NumHandActions",
                "NumActions"
            ]
        }
        Property {
            name: "value"
            type: "float"
            read: "value"
            notify: "valueChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "pressed"
            notify: "pressedChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "actionName"
            type: "QString"
            read: "actionName"
            write: "setActionName"
            notify: "actionNameChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "actionId"
            type: "Action"
            isList: true
            read: "actionId"
            write: "setActionId"
            notify: "actionIdChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "enabled"
            revision: 1545
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "hand"
            type: "Hand"
            read: "hand"
            write: "setHand"
            notify: "handChanged"
            index: 5
            isFinal: true
        }
        Signal { name: "valueChanged" }
        Signal { name: "pressedChanged" }
        Signal { name: "triggered" }
        Signal { name: "actionNameChanged" }
        Signal { name: "actionIdChanged" }
        Signal { name: "handChanged" }
        Signal { name: "enabledChanged" }
    }
    Component {
        file: "private/qquick3dxritem_p.h"
        name: "QQuick3DXrItem"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D.Xr/XrItem 6.8"]
        exportMetaObjectRevisions: [1544]
        Property {
            name: "contentItem"
            type: "QQuickItem"
            isPointer: true
            read: "contentItem"
            write: "setContentItem"
            notify: "contentItemChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "pixelsPerUnit"
            type: "double"
            read: "pixelsPerUnit"
            write: "setPixelsPerUnit"
            notify: "pixelsPerUnitChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "manualPixelsPerUnit"
            type: "bool"
            read: "manualPixelsPerUnit"
            write: "setManualPixelsPerUnit"
            notify: "manualPixelsPerUnitChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "automaticHeight"
            type: "bool"
            read: "automaticHeight"
            write: "setAutomaticHeight"
            notify: "automaticHeightChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "automaticWidth"
            type: "bool"
            read: "automaticWidth"
            write: "setAutomaticWidth"
            notify: "automaticWidthChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "width"
            type: "double"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "height"
            type: "double"
            read: "height"
            write: "setHeight"
            notify: "heightChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 7
            isFinal: true
        }
        Signal { name: "contentItemChanged" }
        Signal { name: "pixelsPerUnitChanged" }
        Signal { name: "flagsChanged" }
        Signal { name: "manualPixelsPerUnitChanged" }
        Signal { name: "widthChanged" }
        Signal { name: "heightChanged" }
        Signal { name: "colorChanged" }
        Signal { name: "automaticHeightChanged" }
        Signal { name: "automaticWidthChanged" }
    }
    Component {
        file: "private/qquick3dxrorigin_p.h"
        name: "QQuick3DXrOrigin"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D.Xr/XrOrigin 6.8"]
        exportMetaObjectRevisions: [1544]
        Property {
            name: "camera"
            type: "QQuick3DXrCamera"
            isPointer: true
            read: "camera"
            write: "setCamera"
            notify: "cameraChanged"
            index: 0
        }
        Signal { name: "cameraChanged" }
    }
    Component {
        file: "private/qquick3dxrruntimeinfo_p.h"
        name: "QQuick3DXrRuntimeInfo"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D.Xr/XrRuntimeInfo 6.8"]
        isCreatable: false
        exportMetaObjectRevisions: [1544]
        Property {
            name: "enabledExtensions"
            type: "QStringList"
            read: "enabledExtensions"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "runtimeName"
            type: "QString"
            read: "runtimeName"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "runtimeVersion"
            type: "QString"
            read: "runtimeVersion"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "graphicsApiName"
            type: "QString"
            read: "graphicsApiName"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qquick3dxrabstracthapticeffect_p.h"
        name: "QQuick3DXrSimpleHapticEffect"
        accessSemantics: "reference"
        prototype: "QQuick3DXrAbstractHapticEffect"
        exports: ["QtQuick3D.Xr/XrSimpleHapticEffect 6.9"]
        exportMetaObjectRevisions: [1545]
        Property {
            name: "amplitude"
            type: "float"
            read: "amplitude"
            write: "setAmplitude"
            notify: "amplitudeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "duration"
            type: "float"
            read: "duration"
            write: "setDuration"
            notify: "durationChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "frequency"
            type: "float"
            read: "frequency"
            write: "setFrequency"
            notify: "frequencyChanged"
            index: 2
            isFinal: true
        }
        Signal { name: "amplitudeChanged" }
        Signal { name: "durationChanged" }
        Signal { name: "frequencyChanged" }
    }
    Component {
        file: "private/qquick3dxrspatialanchor_p.h"
        name: "QQuick3DXrSpatialAnchor"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D.Xr/XrSpatialAnchor 6.8"]
        isCreatable: false
        exportMetaObjectRevisions: [1544]
        Enum {
            name: "Classification"
            isScoped: true
            values: [
                "Unknown",
                "Wall",
                "Ceiling",
                "Floor",
                "Table",
                "Seat",
                "Window",
                "Door",
                "Other"
            ]
        }
        Property {
            name: "has2DBounds"
            type: "bool"
            read: "has2DBounds"
            notify: "has2DBoundsChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "has3DBounds"
            type: "bool"
            read: "has3DBounds"
            notify: "has3DBoundsChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "offset2D"
            type: "QVector2D"
            read: "offset2D"
            notify: "offset2DChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "extent2D"
            type: "QVector2D"
            read: "extent2D"
            notify: "extent2DChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "offset3D"
            type: "QVector3D"
            read: "offset3D"
            notify: "offset3DChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "extent3D"
            type: "QVector3D"
            read: "extent3D"
            notify: "extent3DChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "position"
            type: "QVector3D"
            read: "position"
            notify: "positionChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "rotation"
            type: "QQuaternion"
            read: "rotation"
            notify: "rotationChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "classification"
            type: "Classification"
            read: "classification"
            notify: "classificationChanged"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "classificationString"
            type: "QString"
            read: "classificationString"
            notify: "classificationStringChanged"
            index: 9
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "identifier"
            type: "QString"
            read: "identifier"
            index: 10
            isReadonly: true
            isPropertyConstant: true
        }
        Signal { name: "offset3DChanged" }
        Signal { name: "extent3DChanged" }
        Signal { name: "positionChanged" }
        Signal { name: "rotationChanged" }
        Signal { name: "classificationChanged" }
        Signal { name: "classificationStringChanged" }
        Signal { name: "has2DBoundsChanged" }
        Signal { name: "has3DBoundsChanged" }
        Signal { name: "offset2DChanged" }
        Signal { name: "extent2DChanged" }
    }
    Component {
        file: "private/qquick3dxrspatialanchorlistmodel_p.h"
        name: "QQuick3DXrSpatialAnchorListModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        exports: ["QtQuick3D.Xr/XrSpatialAnchorListModel 6.8"]
        exportMetaObjectRevisions: [1544]
        Enum {
            name: "FilterMode"
            isScoped: true
            values: ["All", "Classification", "Identifier"]
        }
        Enum {
            name: "ClassificationFlag"
            type: "uint"
            values: [
                "Wall",
                "Ceiling",
                "Floor",
                "Table",
                "Seat",
                "Window",
                "Door",
                "Other"
            ]
        }
        Enum {
            name: "ClassificationFlags"
            alias: "ClassificationFlag"
            isFlag: true
            type: "uint"
            values: [
                "Wall",
                "Ceiling",
                "Floor",
                "Table",
                "Seat",
                "Window",
                "Door",
                "Other"
            ]
        }
        Property {
            name: "filterMode"
            type: "FilterMode"
            read: "filterMode"
            write: "setFilterMode"
            notify: "filterModeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "classificationFilter"
            type: "ClassificationFlags"
            read: "classificationFilter"
            write: "setClassificationFilter"
            notify: "classificationFilterChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "classificationStringFilter"
            type: "QStringList"
            read: "classificationStringFilter"
            write: "setClassificationStringFilter"
            notify: "classificationStringFilterChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "identifierFilter"
            type: "QStringList"
            read: "identifierFilter"
            write: "setIdentifierFilter"
            notify: "identifierFilterChanged"
            index: 3
            isFinal: true
        }
        Signal { name: "filterModeChanged" }
        Signal { name: "identifierFilterChanged" }
        Signal { name: "classificationFilterChanged" }
        Signal { name: "classificationStringFilterChanged" }
        Method {
            name: "handleAnchorAdded"
            Parameter { name: "anchor"; type: "QQuick3DXrSpatialAnchor"; isPointer: true }
        }
        Method {
            name: "handleAnchorRemoved"
            Parameter { name: "uuid"; type: "QUuid" }
        }
        Method {
            name: "handleAnchorUpdated"
            Parameter { name: "anchor"; type: "QQuick3DXrSpatialAnchor"; isPointer: true }
        }
        Method { name: "requestSceneCapture" }
        Method { name: "queryAnchors" }
    }
    Component {
        file: "private/qquick3dxrview_p.h"
        name: "QQuick3DXrView"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D.Xr/XrView 6.8"]
        exportMetaObjectRevisions: [1544]
        Enum {
            name: "FoveationLevel"
            values: [
                "NoFoveation",
                "LowFoveation",
                "MediumFoveation",
                "HighFoveation"
            ]
        }
        Enum {
            name: "ReferenceSpace"
            isScoped: true
            values: [
                "ReferenceSpaceUnknown",
                "ReferenceSpaceLocal",
                "ReferenceSpaceStage",
                "ReferenceSpaceLocalFloor"
            ]
        }
        Property {
            name: "xrOrigin"
            type: "QQuick3DXrOrigin"
            isPointer: true
            read: "xrOrigin"
            write: "setXROrigin"
            notify: "xrOriginChanged"
            index: 0
        }
        Property {
            name: "environment"
            type: "QQuick3DSceneEnvironment"
            isPointer: true
            read: "environment"
            write: "setEnvironment"
            notify: "environmentChanged"
            index: 1
        }
        Property {
            name: "passthroughSupported"
            type: "bool"
            read: "passthroughSupported"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "passthroughEnabled"
            type: "bool"
            read: "passthroughEnabled"
            write: "setPassthroughEnabled"
            notify: "passthroughEnabledChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "runtimeInfo"
            type: "QQuick3DXrRuntimeInfo"
            isPointer: true
            read: "runtimeInfo"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "quitOnSessionEnd"
            type: "bool"
            read: "isQuitOnSessionEndEnabled"
            write: "setQuitOnSessionEnd"
            notify: "quitOnSessionEndChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "renderStats"
            type: "QQuick3DRenderStats"
            isPointer: true
            read: "renderStats"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "fixedFoveation"
            type: "FoveationLevel"
            read: "fixedFoveation"
            write: "setFixedFoveation"
            notify: "fixedFoveationChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "referenceSpace"
            type: "ReferenceSpace"
            read: "referenceSpace"
            write: "setReferenceSpace"
            notify: "referenceSpaceChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "depthSubmissionEnabled"
            type: "bool"
            read: "depthSubmissionEnabled"
            write: "setDepthSubmissionEnabled"
            notify: "depthSubmissionEnabledChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "multiViewRenderingSupported"
            type: "bool"
            read: "isMultiViewRenderingSupported"
            index: 10
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "multiViewRenderingEnabled"
            type: "bool"
            read: "multiViewRenderingEnabled"
            notify: "multiViewRenderingEnabledChanged"
            index: 11
            isReadonly: true
            isFinal: true
        }
        Signal {
            name: "initializeFailed"
            Parameter { name: "errorString"; type: "QString" }
        }
        Signal { name: "sessionEnded" }
        Signal { name: "xrOriginChanged" }
        Signal {
            name: "environmentChanged"
            Parameter { name: "environment"; type: "QQuick3DSceneEnvironment"; isPointer: true }
        }
        Signal { name: "passthroughEnabledChanged" }
        Signal { name: "quitOnSessionEndChanged" }
        Signal { name: "fixedFoveationChanged" }
        Signal { name: "frameReady" }
        Signal { name: "referenceSpaceChanged" }
        Signal { name: "depthSubmissionEnabledChanged" }
        Signal { name: "multiViewRenderingEnabledChanged" }
        Method {
            name: "setEnvironment"
            Parameter { name: "environment"; type: "QQuick3DSceneEnvironment"; isPointer: true }
        }
        Method {
            name: "setPassthroughEnabled"
            Parameter { name: "enable"; type: "bool" }
        }
        Method {
            name: "setQuitOnSessionEnd"
            Parameter { name: "enable"; type: "bool" }
        }
        Method {
            name: "setDepthSubmissionEnabled"
            Parameter { name: "enable"; type: "bool" }
        }
        Method {
            name: "setXROrigin"
            Parameter { name: "newXrOrigin"; type: "QQuick3DXrOrigin"; isPointer: true }
        }
        Method { name: "updateViewportGeometry" }
        Method { name: "handleSessionEnded" }
        Method { name: "handleClearColorChanged" }
        Method { name: "handleAAChanged" }
        Method { name: "init"; type: "bool" }
        Method {
            name: "rayPick"
            type: "QQuick3DPickResult"
            isMethodConstant: true
            Parameter { name: "origin"; type: "QVector3D" }
            Parameter { name: "direction"; type: "QVector3D" }
        }
        Method {
            name: "rayPickAll"
            type: "QQuick3DPickResult"
            isList: true
            isMethodConstant: true
            Parameter { name: "origin"; type: "QVector3D" }
            Parameter { name: "direction"; type: "QVector3D" }
        }
        Method {
            name: "setTouchpoint"
            Parameter { name: "target"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "position"; type: "QPointF" }
            Parameter { name: "pointId"; type: "int" }
            Parameter { name: "active"; type: "bool" }
        }
        Method {
            name: "processTouch"
            type: "QVector3D"
            Parameter { name: "pos"; type: "QVector3D" }
            Parameter { name: "pointId"; type: "int" }
        }
        Method {
            name: "touchpointState"
            type: "QVariantMap"
            isMethodConstant: true
            Parameter { name: "pointId"; type: "int" }
        }
    }
    Component {
        file: "private/qquick3dxrvirtualmouse_p.h"
        name: "QQuick3DXrVirtualMouse"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D.Xr/XrVirtualMouse 6.8"]
        exportMetaObjectRevisions: [1544]
        Property {
            name: "rightMouseButton"
            type: "bool"
            read: "rightMouseButton"
            write: "setRightMouseButton"
            notify: "rightMouseButtonChanged"
            index: 0
        }
        Property {
            name: "leftMouseButton"
            type: "bool"
            read: "leftMouseButton"
            write: "setLeftMouseButton"
            notify: "leftMouseButtonChanged"
            index: 1
        }
        Property {
            name: "middleMouseButton"
            type: "bool"
            read: "middleMouseButton"
            write: "setMiddleMouseButton"
            notify: "middleMouseButtonChanged"
            index: 2
        }
        Property {
            name: "scrollWheelX"
            type: "float"
            read: "scrollWheelX"
            write: "setScrollWheelX"
            notify: "scrollWheelXChanged"
            index: 3
        }
        Property {
            name: "scrollWheelY"
            type: "float"
            read: "scrollWheelY"
            write: "setScrollWheelY"
            notify: "scrollWheelYChanged"
            index: 4
        }
        Property {
            name: "scrollTimerInterval"
            type: "int"
            read: "scrollTimerInterval"
            write: "setScrollTimerInterval"
            notify: "scrollTimerIntervalChanged"
            index: 5
        }
        Property {
            name: "scrollPixelDelta"
            type: "int"
            read: "scrollPixelDelta"
            write: "setScrollPixelDelta"
            notify: "scrollPixelDeltaChanged"
            index: 6
        }
        Property {
            name: "source"
            type: "QQuick3DNode"
            isPointer: true
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 7
        }
        Property {
            name: "view"
            type: "QQuick3DXrView"
            isPointer: true
            read: "view"
            write: "setView"
            notify: "viewChanged"
            index: 8
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "enabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 9
        }
        Signal {
            name: "rightMouseButtonChanged"
            Parameter { name: "rightMouseButton"; type: "bool" }
        }
        Signal {
            name: "leftMouseButtonChanged"
            Parameter { name: "leftMouseButton"; type: "bool" }
        }
        Signal {
            name: "middleMouseButtonChanged"
            Parameter { name: "middleMouseButton"; type: "bool" }
        }
        Signal {
            name: "scrollWheelXChanged"
            Parameter { name: "scrollWheelX"; type: "float" }
        }
        Signal {
            name: "scrollWheelYChanged"
            Parameter { name: "scrollWheelY"; type: "float" }
        }
        Signal {
            name: "scrollTimerIntervalChanged"
            Parameter { name: "scrollTimerInterval"; type: "int" }
        }
        Signal {
            name: "scrollPixelDeltaChanged"
            Parameter { name: "scrollPixelDelta"; type: "int" }
        }
        Signal {
            name: "sourceChanged"
            Parameter { name: "source"; type: "QQuick3DNode"; isPointer: true }
        }
        Signal {
            name: "viewChanged"
            Parameter { name: "view"; type: "QQuick3DXrView"; isPointer: true }
        }
        Signal {
            name: "enabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setRightMouseButton"
            Parameter { name: "rightMouseButton"; type: "bool" }
        }
        Method {
            name: "setLeftMouseButton"
            Parameter { name: "leftMouseButton"; type: "bool" }
        }
        Method {
            name: "setMiddleMouseButton"
            Parameter { name: "middleMouseButton"; type: "bool" }
        }
        Method {
            name: "setScrollWheelX"
            Parameter { name: "scrollWheelX"; type: "float" }
        }
        Method {
            name: "setScrollWheelY"
            Parameter { name: "scrollWheelY"; type: "float" }
        }
        Method {
            name: "setScrollTimerInterval"
            Parameter { name: "scrollTimerInterval"; type: "int" }
        }
        Method {
            name: "setScrollPixelDelta"
            Parameter { name: "scrollPixelDelta"; type: "int" }
        }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QQuick3DNode"; isPointer: true }
        }
        Method {
            name: "setView"
            Parameter { name: "view"; type: "QQuick3DXrView"; isPointer: true }
        }
        Method {
            name: "setEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method { name: "moveEvent" }
        Method { name: "generateWheelEvent" }
    }
}
