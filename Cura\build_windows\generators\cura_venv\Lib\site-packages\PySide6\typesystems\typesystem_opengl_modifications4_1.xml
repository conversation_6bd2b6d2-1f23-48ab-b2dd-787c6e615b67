<!--
// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<modify-function signature="^gl(DepthRange|Scissor|Viewport)Arrayv\(.*$">
    <modify-argument index="3"><array/></modify-argument>
</modify-function>
<modify-function signature="glGetDoublei_v(GLenum,GLuint,GLdouble*)">
    <modify-argument index="return" pyi-type="Union[float,numpy.ndarray]">
        <replace-type modified-type="PyObject"/>
    </modify-argument>
    <modify-argument index="3">
        <remove-argument/>
    </modify-argument>
    <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qopenglextrafunctions-glgetdoublei-v"/>
</modify-function>
<modify-function signature="glGetFloati_v(GLenum,GLuint,GLfloat*)">
    <modify-argument index="return" pyi-type="Union[float,numpy.ndarray]">
        <replace-type modified-type="PyObject"/>
    </modify-argument>
    <modify-argument index="3">
        <remove-argument/>
    </modify-argument>
    <inject-code class="target" position="beginning" file="../glue/qtgui.cpp" snippet="qopenglextrafunctions-glgetfloati-v"/>
</modify-function>
<modify-function signature="^glProgramUniform\du?[dfi]v\(.*$">
    <modify-argument index="4"><array/></modify-argument>
</modify-function>
<modify-function signature="^glProgramUniformMatrix\d[df]v\(.*$">
    <modify-argument index="5"><array/></modify-argument>
</modify-function>
<modify-function signature="^glProgramUniformMatrix\dx\d[df]v\(.*$">
    <modify-argument index="5"><array/></modify-argument>
</modify-function>
<modify-function signature="^gl(Scissor|Viewport)Indexedf?v\(.*$">
    <modify-argument index="2"><array/></modify-argument>
</modify-function>
<modify-function signature="^glShaderBinary\(GLsizei,const GLuint\*,.*$">
    <modify-argument index="2"><array/></modify-argument>
</modify-function>
