########### AGGREGATED COMPONENTS AND DEPENDENCIES FOR THE MULTI CONFIG #####################
#############################################################################################

set(scripta_COMPONENT_NAMES "")
if(DEFINED scripta_FIND_DEPENDENCY_NAMES)
  list(APPEND scripta_FIND_DEPENDENCY_NAMES )
  list(REMOVE_DUPLICATES scripta_FIND_DEPENDENCY_NAMES)
else()
  set(scripta_FIND_DEPENDENCY_NAMES )
endif()

########### VARIABLES #######################################################################
#############################################################################################
set(scripta_PACKAGE_FOLDER_RELEASE "C:/Users/<USER>/.conan2/p/scrip4cfd014ad4660/p")
set(scripta_BUILD_MODULES_PATHS_RELEASE )


set(scripta_INCLUDE_DIRS_RELEASE "${scripta_PACKAGE_FOLDER_RELEASE}/include")
set(scripta_RES_DIRS_RELEASE )
set(scripta_DEFINITIONS_RELEASE )
set(scripta_SHARED_LINK_FLAGS_RELEASE )
set(scripta_EXE_LINK_FLAGS_RELEASE )
set(scripta_OBJECTS_RELEASE )
set(scripta_COMPILE_DEFINITIONS_RELEASE )
set(scripta_COMPILE_OPTIONS_C_RELEASE )
set(scripta_COMPILE_OPTIONS_CXX_RELEASE )
set(scripta_LIB_DIRS_RELEASE )
set(scripta_BIN_DIRS_RELEASE )
set(scripta_LIBRARY_TYPE_RELEASE UNKNOWN)
set(scripta_IS_HOST_WINDOWS_RELEASE 1)
set(scripta_LIBS_RELEASE )
set(scripta_SYSTEM_LIBS_RELEASE )
set(scripta_FRAMEWORK_DIRS_RELEASE )
set(scripta_FRAMEWORKS_RELEASE )
set(scripta_BUILD_DIRS_RELEASE )
set(scripta_NO_SONAME_MODE_RELEASE FALSE)


# COMPOUND VARIABLES
set(scripta_COMPILE_OPTIONS_RELEASE
    "$<$<COMPILE_LANGUAGE:CXX>:${scripta_COMPILE_OPTIONS_CXX_RELEASE}>"
    "$<$<COMPILE_LANGUAGE:C>:${scripta_COMPILE_OPTIONS_C_RELEASE}>")
set(scripta_LINKER_FLAGS_RELEASE
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,SHARED_LIBRARY>:${scripta_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,MODULE_LIBRARY>:${scripta_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,EXECUTABLE>:${scripta_EXE_LINK_FLAGS_RELEASE}>")


set(scripta_COMPONENTS_RELEASE )