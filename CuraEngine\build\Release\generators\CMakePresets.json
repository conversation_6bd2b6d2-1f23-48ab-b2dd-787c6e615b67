{"version": 3, "vendor": {"conan": {}}, "cmakeMinimumRequired": {"major": 3, "minor": 15, "patch": 0}, "configurePresets": [{"name": "conan-release", "displayName": "'conan-release' config", "description": "'conan-release' configure using 'Ninja' generator", "generator": "Ninja", "cacheVariables": {"CMAKE_POLICY_DEFAULT_CMP0091": "NEW", "BUILD_TESTING": "OFF", "CMAKE_BUILD_TYPE": "Release"}, "toolset": {"value": "v143", "strategy": "external"}, "architecture": {"value": "x64", "strategy": "external"}, "toolchainFile": "generators\\conan_toolchain.cmake", "binaryDir": "C:\\Mac\\Home\\Desktop\\CuraProject\\CuraEngine\\build\\Release"}], "buildPresets": [{"name": "conan-release", "configurePreset": "conan-release", "jobs": 6}], "testPresets": [{"name": "conan-release", "configurePreset": "conan-release", "environment": {"PATH": "C:\\Users\\<USER>\\.conan2\\p\\b\\arcus03cb8757a37eb\\p\\bin;C:\\Users\\<USER>\\.conan2\\p\\b\\clippf6b8d4243e255\\p\\bin;$penv{PATH}", "GRPC_DEFAULT_SSL_ROOTS_FILE_PATH": "C:\\Users\\<USER>\\.conan2\\p\\b\\grpcc342d5e50a306\\p\\res\\grpc\\roots.pem", "OPENSSL_MODULES": "C:\\Users\\<USER>\\.conan2\\p\\opensa8084e5231736\\p\\lib\\ossl-modules"}}]}