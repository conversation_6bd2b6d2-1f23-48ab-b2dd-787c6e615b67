########## MACROS ###########################################################################
#############################################################################################

# Requires CMake > 3.15
if(${CMAKE_VERSION} VERSION_LESS "3.15")
    message(FATAL_ERROR "The 'CMakeDeps' generator only works with CMake >= 3.15")
endif()

if(mapbox-geometry_FIND_QUIETLY)
    set(mapbox-geometry_MESSAGE_MODE VERBOSE)
else()
    set(mapbox-geometry_MESSAGE_MODE STATUS)
endif()

include(${CMAKE_CURRENT_LIST_DIR}/cmakedeps_macros.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/mapbox-geometryTargets.cmake)
include(CMakeFindDependencyMacro)

check_build_type_defined()

foreach(_DEPENDENCY ${mapbox-geometry_FIND_DEPENDENCY_NAMES} )
    # Check that we have not already called a find_package with the transitive dependency
    if(NOT ${_DEPENDENCY}_FOUND)
        find_dependency(${_DEPENDENCY} REQUIRED ${${_DEPENDENCY}_FIND_MODE})
    endif()
endforeach()

set(mapbox-geometry_VERSION_STRING "2.0.3")
set(mapbox-geometry_INCLUDE_DIRS ${mapbox-geometry_INCLUDE_DIRS_RELEASE} )
set(mapbox-geometry_INCLUDE_DIR ${mapbox-geometry_INCLUDE_DIRS_RELEASE} )
set(mapbox-geometry_LIBRARIES ${mapbox-geometry_LIBRARIES_RELEASE} )
set(mapbox-geometry_DEFINITIONS ${mapbox-geometry_DEFINITIONS_RELEASE} )


# Only the last installed configuration BUILD_MODULES are included to avoid the collision
foreach(_BUILD_MODULE ${mapbox-geometry_BUILD_MODULES_PATHS_RELEASE} )
    message(${mapbox-geometry_MESSAGE_MODE} "Conan: Including build module from '${_BUILD_MODULE}'")
    include(${_BUILD_MODULE})
endforeach()


