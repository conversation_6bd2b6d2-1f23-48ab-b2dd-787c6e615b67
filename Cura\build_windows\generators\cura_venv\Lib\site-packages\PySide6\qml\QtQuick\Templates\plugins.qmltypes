import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickdialog_p.h"
        name: "QColorDialogOptions"
        accessSemantics: "none"
        Enum {
            name: "ColorDialogOptions"
            alias: "ColorDialogOption"
            isFlag: true
            values: [
                "ShowAlphaChannel",
                "NoButtons",
                "DontUseNativeDialog",
                "NoEyeDropperButton"
            ]
        }
    }
    Component {
        file: "private/qquickdialog_p.h"
        name: "QFileDialogOptions"
        accessSemantics: "none"
        Enum {
            name: "ViewMode"
            values: ["Detail", "List"]
        }
        Enum {
            name: "FileMode"
            values: [
                "AnyFile",
                "ExistingFile",
                "Directory",
                "ExistingFiles",
                "DirectoryOnly"
            ]
        }
        Enum {
            name: "AcceptMode"
            values: ["AcceptOpen", "AcceptSave"]
        }
        Enum {
            name: "DialogLabel"
            values: [
                "LookIn",
                "FileName",
                "FileType",
                "Accept",
                "Reject",
                "DialogLabelCount"
            ]
        }
        Enum {
            name: "FileDialogOptions"
            alias: "FileDialogOption"
            isFlag: true
            values: [
                "ShowDirsOnly",
                "DontResolveSymlinks",
                "DontConfirmOverwrite",
                "DontUseNativeDialog",
                "ReadOnly",
                "HideNameFilterDetails",
                "DontUseCustomDirectoryIcons"
            ]
        }
    }
    Component {
        file: "private/qquickdialog_p.h"
        name: "QFontDialogOptions"
        accessSemantics: "none"
        Enum {
            name: "FontDialogOptions"
            alias: "FontDialogOption"
            isFlag: true
            values: [
                "NoButtons",
                "DontUseNativeDialog",
                "ScalableFonts",
                "NonScalableFonts",
                "MonospacedFonts",
                "ProportionalFonts"
            ]
        }
    }
    Component {
        file: "qpa/qplatformdialoghelper.h"
        name: "QPlatformDialogHelper"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "StandardButtons"
            alias: "StandardButton"
            isFlag: true
            values: [
                "NoButton",
                "Ok",
                "Save",
                "SaveAll",
                "Open",
                "Yes",
                "YesToAll",
                "No",
                "NoToAll",
                "Abort",
                "Retry",
                "Ignore",
                "Close",
                "Cancel",
                "Discard",
                "Help",
                "Apply",
                "Reset",
                "RestoreDefaults",
                "FirstButton",
                "LastButton",
                "LowestBit",
                "HighestBit"
            ]
        }
        Enum {
            name: "ButtonRole"
            values: [
                "InvalidRole",
                "AcceptRole",
                "RejectRole",
                "DestructiveRole",
                "ActionRole",
                "HelpRole",
                "YesRole",
                "NoRole",
                "ResetRole",
                "ApplyRole",
                "NRoles",
                "RoleMask",
                "AlternateRole",
                "Stretch",
                "Reverse",
                "EOL"
            ]
        }
        Enum {
            name: "ButtonLayout"
            values: [
                "UnknownLayout",
                "WinLayout",
                "MacLayout",
                "KdeLayout",
                "GnomeLayout",
                "AndroidLayout"
            ]
        }
        Signal { name: "accept" }
        Signal { name: "reject" }
    }
    Component {
        file: "private/qquickabstractbutton_p.h"
        name: "QQuickAbstractButton"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        deferredNames: ["background", "contentItem", "indicator"]
        exports: [
            "QtQuick.Templates/AbstractButton 2.0",
            "QtQuick.Templates/AbstractButton 2.1",
            "QtQuick.Templates/AbstractButton 2.2",
            "QtQuick.Templates/AbstractButton 2.3",
            "QtQuick.Templates/AbstractButton 2.4",
            "QtQuick.Templates/AbstractButton 2.5",
            "QtQuick.Templates/AbstractButton 2.7",
            "QtQuick.Templates/AbstractButton 2.11",
            "QtQuick.Templates/AbstractButton 6.0",
            "QtQuick.Templates/AbstractButton 6.3",
            "QtQuick.Templates/AbstractButton 6.7",
            "QtQuick.Templates/AbstractButton 6.8"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
        Enum {
            name: "Display"
            values: [
                "IconOnly",
                "TextOnly",
                "TextBesideIcon",
                "TextUnderIcon"
            ]
        }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            reset: "resetText"
            notify: "textChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "down"
            type: "bool"
            read: "isDown"
            write: "setDown"
            reset: "resetDown"
            notify: "downChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "isPressed"
            notify: "pressedChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "checked"
            type: "bool"
            read: "isChecked"
            write: "setChecked"
            notify: "checkedChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "checkable"
            type: "bool"
            read: "isCheckable"
            write: "setCheckable"
            notify: "checkableChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "autoExclusive"
            type: "bool"
            read: "autoExclusive"
            write: "setAutoExclusive"
            notify: "autoExclusiveChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "autoRepeat"
            type: "bool"
            read: "autoRepeat"
            write: "setAutoRepeat"
            notify: "autoRepeatChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "indicator"
            type: "QQuickItem"
            isPointer: true
            read: "indicator"
            write: "setIndicator"
            notify: "indicatorChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "icon"
            revision: 515
            type: "QQuickIcon"
            read: "icon"
            write: "setIcon"
            notify: "iconChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "display"
            revision: 515
            type: "Display"
            read: "display"
            write: "setDisplay"
            notify: "displayChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "action"
            revision: 515
            type: "QQuickAction"
            isPointer: true
            read: "action"
            write: "setAction"
            notify: "actionChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "autoRepeatDelay"
            revision: 516
            type: "int"
            read: "autoRepeatDelay"
            write: "setAutoRepeatDelay"
            notify: "autoRepeatDelayChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "autoRepeatInterval"
            revision: 516
            type: "int"
            read: "autoRepeatInterval"
            write: "setAutoRepeatInterval"
            notify: "autoRepeatIntervalChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "pressX"
            revision: 516
            type: "double"
            read: "pressX"
            notify: "pressXChanged"
            index: 13
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "pressY"
            revision: 516
            type: "double"
            read: "pressY"
            notify: "pressYChanged"
            index: 14
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitIndicatorWidth"
            revision: 517
            type: "double"
            read: "implicitIndicatorWidth"
            notify: "implicitIndicatorWidthChanged"
            index: 15
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitIndicatorHeight"
            revision: 517
            type: "double"
            read: "implicitIndicatorHeight"
            notify: "implicitIndicatorHeightChanged"
            index: 16
            isReadonly: true
            isFinal: true
        }
        Signal { name: "pressed" }
        Signal { name: "released" }
        Signal { name: "canceled" }
        Signal { name: "clicked" }
        Signal { name: "pressAndHold" }
        Signal { name: "doubleClicked" }
        Signal { name: "textChanged" }
        Signal { name: "downChanged" }
        Signal { name: "pressedChanged" }
        Signal { name: "checkedChanged" }
        Signal { name: "checkableChanged" }
        Signal { name: "autoExclusiveChanged" }
        Signal { name: "autoRepeatChanged" }
        Signal { name: "indicatorChanged" }
        Signal { name: "toggled"; revision: 514 }
        Signal { name: "iconChanged"; revision: 515 }
        Signal { name: "displayChanged"; revision: 515 }
        Signal { name: "actionChanged"; revision: 515 }
        Signal { name: "autoRepeatDelayChanged"; revision: 516 }
        Signal { name: "autoRepeatIntervalChanged"; revision: 516 }
        Signal { name: "pressXChanged"; revision: 516 }
        Signal { name: "pressYChanged"; revision: 516 }
        Signal { name: "implicitIndicatorWidthChanged"; revision: 517 }
        Signal { name: "implicitIndicatorHeightChanged"; revision: 517 }
        Method { name: "toggle" }
        Method { name: "click"; revision: 1544 }
        Method { name: "animateClick"; revision: 1544 }
        Method { name: "accessiblePressAction" }
    }
    Component {
        file: "private/qquickaction_p.h"
        name: "QQuickAction"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.Templates/Action 2.3",
            "QtQuick.Templates/Action 6.0"
        ]
        exportMetaObjectRevisions: [515, 1536]
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "icon"
            type: "QQuickIcon"
            read: "icon"
            write: "setIcon"
            notify: "iconChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            reset: "resetEnabled"
            notify: "enabledChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "checked"
            type: "bool"
            read: "isChecked"
            write: "setChecked"
            notify: "checkedChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "checkable"
            type: "bool"
            read: "isCheckable"
            write: "setCheckable"
            notify: "checkableChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "shortcut"
            type: "QVariant"
            read: "shortcut"
            write: "setShortcut"
            notify: "shortcutChanged"
            index: 5
            privateClass: "QQuickActionPrivate"
            isFinal: true
        }
        Signal {
            name: "textChanged"
            Parameter { name: "text"; type: "QString" }
        }
        Signal {
            name: "iconChanged"
            Parameter { name: "icon"; type: "QQuickIcon" }
        }
        Signal {
            name: "enabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "checkedChanged"
            Parameter { name: "checked"; type: "bool" }
        }
        Signal {
            name: "checkableChanged"
            Parameter { name: "checkable"; type: "bool" }
        }
        Signal {
            name: "shortcutChanged"
            Parameter { name: "shortcut"; type: "QKeySequence" }
        }
        Signal {
            name: "toggled"
            Parameter { name: "source"; type: "QObject"; isPointer: true }
        }
        Signal { name: "toggled"; isCloned: true }
        Signal {
            name: "triggered"
            Parameter { name: "source"; type: "QObject"; isPointer: true }
        }
        Signal { name: "triggered"; isCloned: true }
        Method {
            name: "toggle"
            Parameter { name: "source"; type: "QObject"; isPointer: true }
        }
        Method { name: "toggle"; isCloned: true }
        Method {
            name: "trigger"
            Parameter { name: "source"; type: "QObject"; isPointer: true }
        }
        Method { name: "trigger"; isCloned: true }
    }
    Component {
        file: "private/qquickactiongroup_p.h"
        name: "QQuickActionGroup"
        accessSemantics: "reference"
        defaultProperty: "actions"
        prototype: "QObject"
        exports: [
            "QtQuick.Templates/ActionGroup 2.3",
            "QtQuick.Templates/ActionGroup 6.0"
        ]
        exportMetaObjectRevisions: [515, 1536]
        attachedType: "QQuickActionGroupAttached"
        Property {
            name: "checkedAction"
            type: "QQuickAction"
            isPointer: true
            read: "checkedAction"
            write: "setCheckedAction"
            notify: "checkedActionChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "actions"
            type: "QQuickAction"
            isList: true
            read: "actions"
            notify: "actionsChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "exclusive"
            type: "bool"
            read: "isExclusive"
            write: "setExclusive"
            notify: "exclusiveChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 3
            isFinal: true
        }
        Signal { name: "checkedActionChanged" }
        Signal { name: "actionsChanged" }
        Signal { name: "exclusiveChanged" }
        Signal { name: "enabledChanged" }
        Signal {
            name: "triggered"
            Parameter { name: "action"; type: "QQuickAction"; isPointer: true }
        }
        Method {
            name: "addAction"
            Parameter { name: "action"; type: "QQuickAction"; isPointer: true }
        }
        Method {
            name: "removeAction"
            Parameter { name: "action"; type: "QQuickAction"; isPointer: true }
        }
        Method { name: "_q_updateCurrent" }
    }
    Component {
        file: "private/qquickactiongroup_p.h"
        name: "QQuickActionGroupAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "group"
            type: "QQuickActionGroup"
            isPointer: true
            read: "group"
            write: "setGroup"
            notify: "groupChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "groupChanged" }
    }
    Component {
        file: "private/qquickapplicationwindow_p.h"
        name: "QQuickApplicationWindow"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickWindowQmlImpl"
        deferredNames: ["background"]
        exports: [
            "QtQuick.Templates/ApplicationWindow 2.0",
            "QtQuick.Templates/ApplicationWindow 2.1",
            "QtQuick.Templates/ApplicationWindow 2.2",
            "QtQuick.Templates/ApplicationWindow 2.3",
            "QtQuick.Templates/ApplicationWindow 2.13",
            "QtQuick.Templates/ApplicationWindow 2.14",
            "QtQuick.Templates/ApplicationWindow 6.0",
            "QtQuick.Templates/ApplicationWindow 6.2",
            "QtQuick.Templates/ApplicationWindow 6.7",
            "QtQuick.Templates/ApplicationWindow 6.9"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            525,
            526,
            1536,
            1538,
            1543,
            1545
        ]
        attachedType: "QQuickApplicationWindowAttached"
        Property {
            name: "background"
            type: "QQuickItem"
            isPointer: true
            read: "background"
            write: "setBackground"
            notify: "backgroundChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "contentItem"
            type: "QQuickItem"
            isPointer: true
            read: "contentItem"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "contentData"
            type: "QObject"
            isList: true
            read: "contentData"
            index: 2
            privateClass: "QQuickApplicationWindowPrivate"
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "activeFocusControl"
            type: "QQuickItem"
            isPointer: true
            read: "activeFocusControl"
            notify: "activeFocusControlChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "header"
            type: "QQuickItem"
            isPointer: true
            read: "header"
            write: "setHeader"
            notify: "headerChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "footer"
            type: "QQuickItem"
            isPointer: true
            read: "footer"
            write: "setFooter"
            notify: "footerChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            reset: "resetFont"
            notify: "fontChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "locale"
            type: "QLocale"
            read: "locale"
            write: "setLocale"
            reset: "resetLocale"
            notify: "localeChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "menuBar"
            revision: 515
            type: "QQuickItem"
            isPointer: true
            read: "menuBar"
            write: "setMenuBar"
            notify: "menuBarChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "palette"
            revision: 515
            type: "QQuickPalette"
            isPointer: true
            read: "palette"
            write: "setPalette"
            reset: "resetPalette"
            notify: "paletteChanged"
            index: 9
            privateClass: "QQuickApplicationWindowPrivate"
        }
        Property {
            name: "topPadding"
            revision: 1545
            type: "double"
            read: "topPadding"
            write: "setTopPadding"
            reset: "resetTopPadding"
            notify: "topPaddingChanged"
            index: 10
            privateClass: "QQuickApplicationWindowPrivate->control"
            isFinal: true
        }
        Property {
            name: "leftPadding"
            revision: 1545
            type: "double"
            read: "leftPadding"
            write: "setLeftPadding"
            reset: "resetLeftPadding"
            notify: "leftPaddingChanged"
            index: 11
            privateClass: "QQuickApplicationWindowPrivate->control"
            isFinal: true
        }
        Property {
            name: "rightPadding"
            revision: 1545
            type: "double"
            read: "rightPadding"
            write: "setRightPadding"
            reset: "resetRightPadding"
            notify: "rightPaddingChanged"
            index: 12
            privateClass: "QQuickApplicationWindowPrivate->control"
            isFinal: true
        }
        Property {
            name: "bottomPadding"
            revision: 1545
            type: "double"
            read: "bottomPadding"
            write: "setBottomPadding"
            reset: "resetBottomPadding"
            notify: "bottomPaddingChanged"
            index: 13
            privateClass: "QQuickApplicationWindowPrivate->control"
            isFinal: true
        }
        Signal { name: "backgroundChanged" }
        Signal { name: "activeFocusControlChanged" }
        Signal { name: "headerChanged" }
        Signal { name: "footerChanged" }
        Signal { name: "fontChanged" }
        Signal { name: "localeChanged" }
        Signal { name: "menuBarChanged"; revision: 515 }
        Signal { name: "topPaddingChanged"; revision: 1545 }
        Signal { name: "leftPaddingChanged"; revision: 1545 }
        Signal { name: "rightPaddingChanged"; revision: 1545 }
        Signal { name: "bottomPaddingChanged"; revision: 1545 }
        Method { name: "_q_updateActiveFocus" }
    }
    Component {
        file: "private/qquickapplicationwindow_p.h"
        name: "QQuickApplicationWindowAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "window"
            type: "QQuickApplicationWindow"
            isPointer: true
            read: "window"
            notify: "windowChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "contentItem"
            type: "QQuickItem"
            isPointer: true
            read: "contentItem"
            notify: "contentItemChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "activeFocusControl"
            type: "QQuickItem"
            isPointer: true
            read: "activeFocusControl"
            notify: "activeFocusControlChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "header"
            type: "QQuickItem"
            isPointer: true
            read: "header"
            notify: "headerChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "footer"
            type: "QQuickItem"
            isPointer: true
            read: "footer"
            notify: "footerChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "menuBar"
            type: "QQuickItem"
            isPointer: true
            read: "menuBar"
            notify: "menuBarChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Signal { name: "windowChanged" }
        Signal { name: "contentItemChanged" }
        Signal { name: "activeFocusControlChanged" }
        Signal { name: "headerChanged" }
        Signal { name: "footerChanged" }
        Signal { name: "menuBarChanged" }
    }
    Component {
        file: "private/qquickbusyindicator_p.h"
        name: "QQuickBusyIndicator"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/BusyIndicator 2.0",
            "QtQuick.Templates/BusyIndicator 2.1",
            "QtQuick.Templates/BusyIndicator 2.4",
            "QtQuick.Templates/BusyIndicator 2.5",
            "QtQuick.Templates/BusyIndicator 2.7",
            "QtQuick.Templates/BusyIndicator 2.11",
            "QtQuick.Templates/BusyIndicator 6.0",
            "QtQuick.Templates/BusyIndicator 6.3",
            "QtQuick.Templates/BusyIndicator 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        Property {
            name: "running"
            type: "bool"
            read: "isRunning"
            write: "setRunning"
            notify: "runningChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "runningChanged" }
    }
    Component {
        file: "private/qquickbutton_p.h"
        name: "QQuickButton"
        accessSemantics: "reference"
        prototype: "QQuickAbstractButton"
        exports: [
            "QtQuick.Templates/Button 2.0",
            "QtQuick.Templates/Button 2.1",
            "QtQuick.Templates/Button 2.2",
            "QtQuick.Templates/Button 2.3",
            "QtQuick.Templates/Button 2.4",
            "QtQuick.Templates/Button 2.5",
            "QtQuick.Templates/Button 2.7",
            "QtQuick.Templates/Button 2.11",
            "QtQuick.Templates/Button 6.0",
            "QtQuick.Templates/Button 6.3",
            "QtQuick.Templates/Button 6.7",
            "QtQuick.Templates/Button 6.8"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
        Property {
            name: "highlighted"
            type: "bool"
            read: "isHighlighted"
            write: "setHighlighted"
            notify: "highlightedChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "flat"
            type: "bool"
            read: "isFlat"
            write: "setFlat"
            notify: "flatChanged"
            index: 1
            isFinal: true
        }
        Signal { name: "highlightedChanged" }
        Signal { name: "flatChanged" }
    }
    Component {
        file: "private/qquickbuttongroup_p.h"
        name: "QQuickButtonGroup"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQuick.Templates/ButtonGroup 2.0",
            "QtQuick.Templates/ButtonGroup 2.1",
            "QtQuick.Templates/ButtonGroup 2.3",
            "QtQuick.Templates/ButtonGroup 2.4",
            "QtQuick.Templates/ButtonGroup 6.0"
        ]
        exportMetaObjectRevisions: [512, 513, 515, 516, 1536]
        attachedType: "QQuickButtonGroupAttached"
        Property {
            name: "checkedButton"
            type: "QQuickAbstractButton"
            isPointer: true
            read: "checkedButton"
            write: "setCheckedButton"
            notify: "checkedButtonChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "buttons"
            type: "QQuickAbstractButton"
            isList: true
            read: "buttons"
            notify: "buttonsChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "exclusive"
            revision: 515
            type: "bool"
            read: "isExclusive"
            write: "setExclusive"
            notify: "exclusiveChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "checkState"
            revision: 516
            type: "Qt::CheckState"
            read: "checkState"
            write: "setCheckState"
            notify: "checkStateChanged"
            index: 3
            isFinal: true
        }
        Signal { name: "checkedButtonChanged" }
        Signal { name: "buttonsChanged" }
        Signal {
            name: "clicked"
            revision: 513
            Parameter { name: "button"; type: "QQuickAbstractButton"; isPointer: true }
        }
        Signal { name: "exclusiveChanged"; revision: 515 }
        Signal { name: "checkStateChanged"; revision: 516 }
        Method {
            name: "addButton"
            Parameter { name: "button"; type: "QQuickAbstractButton"; isPointer: true }
        }
        Method {
            name: "removeButton"
            Parameter { name: "button"; type: "QQuickAbstractButton"; isPointer: true }
        }
        Method { name: "_q_updateCurrent" }
    }
    Component {
        file: "private/qquickbuttongroup_p.h"
        name: "QQuickButtonGroupAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "group"
            type: "QQuickButtonGroup"
            isPointer: true
            read: "group"
            write: "setGroup"
            notify: "groupChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "groupChanged" }
    }
    Component {
        file: "private/qquickcalendar_p.h"
        name: "QQuickCalendar"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick.Templates/Calendar 6.3"]
        exportMetaObjectRevisions: [1539]
        Enum {
            name: "Month"
            values: [
                "January",
                "February",
                "March",
                "April",
                "May",
                "June",
                "July",
                "August",
                "September",
                "October",
                "November",
                "December"
            ]
        }
    }
    Component {
        file: "private/qquickcalendarmodel_p.h"
        name: "QQuickCalendarModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtQuick.Templates/CalendarModel 6.3",
            "QtQuick.Templates/CalendarModel 6.4"
        ]
        exportMetaObjectRevisions: [1539, 1540]
        Property {
            name: "from"
            type: "QDate"
            read: "from"
            write: "setFrom"
            notify: "fromChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "to"
            type: "QDate"
            read: "to"
            write: "setTo"
            notify: "toChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "count"
            type: "int"
            read: "rowCount"
            notify: "countChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "fromChanged" }
        Signal { name: "toChanged" }
        Signal { name: "countChanged" }
        Method {
            name: "monthAt"
            type: "int"
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "yearAt"
            type: "int"
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "indexOf"
            type: "int"
            isMethodConstant: true
            Parameter { name: "date"; type: "QDate" }
        }
        Method {
            name: "indexOf"
            type: "int"
            isMethodConstant: true
            Parameter { name: "year"; type: "int" }
            Parameter { name: "month"; type: "int" }
        }
    }
    Component {
        file: "private/qquickcheckbox_p.h"
        name: "QQuickCheckBox"
        accessSemantics: "reference"
        prototype: "QQuickAbstractButton"
        exports: [
            "QtQuick.Templates/CheckBox 2.0",
            "QtQuick.Templates/CheckBox 2.1",
            "QtQuick.Templates/CheckBox 2.2",
            "QtQuick.Templates/CheckBox 2.3",
            "QtQuick.Templates/CheckBox 2.4",
            "QtQuick.Templates/CheckBox 2.5",
            "QtQuick.Templates/CheckBox 2.7",
            "QtQuick.Templates/CheckBox 2.11",
            "QtQuick.Templates/CheckBox 6.0",
            "QtQuick.Templates/CheckBox 6.3",
            "QtQuick.Templates/CheckBox 6.7",
            "QtQuick.Templates/CheckBox 6.8"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
        Property {
            name: "tristate"
            type: "bool"
            read: "isTristate"
            write: "setTristate"
            notify: "tristateChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "checkState"
            type: "Qt::CheckState"
            read: "checkState"
            write: "setCheckState"
            notify: "checkStateChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "nextCheckState"
            revision: 516
            type: "QJSValue"
            read: "getNextCheckState"
            write: "setNextCheckState"
            notify: "nextCheckStateChanged"
            index: 2
            isFinal: true
        }
        Signal { name: "tristateChanged" }
        Signal { name: "checkStateChanged" }
        Signal { name: "nextCheckStateChanged"; revision: 516 }
    }
    Component {
        file: "private/qquickcheckdelegate_p.h"
        name: "QQuickCheckDelegate"
        accessSemantics: "reference"
        prototype: "QQuickItemDelegate"
        exports: [
            "QtQuick.Templates/CheckDelegate 2.0",
            "QtQuick.Templates/CheckDelegate 2.1",
            "QtQuick.Templates/CheckDelegate 2.2",
            "QtQuick.Templates/CheckDelegate 2.3",
            "QtQuick.Templates/CheckDelegate 2.4",
            "QtQuick.Templates/CheckDelegate 2.5",
            "QtQuick.Templates/CheckDelegate 2.7",
            "QtQuick.Templates/CheckDelegate 2.11",
            "QtQuick.Templates/CheckDelegate 6.0",
            "QtQuick.Templates/CheckDelegate 6.3",
            "QtQuick.Templates/CheckDelegate 6.7",
            "QtQuick.Templates/CheckDelegate 6.8"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
        Property {
            name: "tristate"
            type: "bool"
            read: "isTristate"
            write: "setTristate"
            notify: "tristateChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "checkState"
            type: "Qt::CheckState"
            read: "checkState"
            write: "setCheckState"
            notify: "checkStateChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "nextCheckState"
            revision: 516
            type: "QJSValue"
            write: "setNextCheckState"
            notify: "nextCheckStateChanged"
            index: 2
            privateClass: "QQuickCheckDelegatePrivate"
            isFinal: true
        }
        Signal { name: "tristateChanged" }
        Signal { name: "checkStateChanged" }
        Signal { name: "nextCheckStateChanged"; revision: 516 }
    }
    Component {
        file: "private/qquickcombobox_p.h"
        name: "QQuickComboBox"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        deferredNames: ["background", "contentItem", "indicator", "popup"]
        exports: [
            "QtQuick.Templates/ComboBox 2.0",
            "QtQuick.Templates/ComboBox 2.1",
            "QtQuick.Templates/ComboBox 2.2",
            "QtQuick.Templates/ComboBox 2.4",
            "QtQuick.Templates/ComboBox 2.5",
            "QtQuick.Templates/ComboBox 2.7",
            "QtQuick.Templates/ComboBox 2.11",
            "QtQuick.Templates/ComboBox 2.14",
            "QtQuick.Templates/ComboBox 2.15",
            "QtQuick.Templates/ComboBox 6.0",
            "QtQuick.Templates/ComboBox 6.3",
            "QtQuick.Templates/ComboBox 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            516,
            517,
            519,
            523,
            526,
            527,
            1536,
            1539,
            1543
        ]
        Enum {
            name: "ImplicitContentWidthPolicy"
            values: [
                "ContentItemImplicitWidth",
                "WidestText",
                "WidestTextWhenCompleted"
            ]
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "model"
            type: "QVariant"
            read: "model"
            write: "setModel"
            notify: "modelChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "delegateModel"
            type: "QQmlInstanceModel"
            isPointer: true
            read: "delegateModel"
            notify: "delegateModelChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "isPressed"
            notify: "pressedChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "highlightedIndex"
            type: "int"
            read: "highlightedIndex"
            notify: "highlightedIndexChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "currentIndex"
            type: "int"
            read: "currentIndex"
            write: "setCurrentIndex"
            notify: "currentIndexChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "currentText"
            type: "QString"
            read: "currentText"
            notify: "currentTextChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "displayText"
            type: "QString"
            read: "displayText"
            write: "setDisplayText"
            reset: "resetDisplayText"
            notify: "displayTextChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "textRole"
            type: "QString"
            read: "textRole"
            write: "setTextRole"
            notify: "textRoleChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "indicator"
            type: "QQuickItem"
            isPointer: true
            read: "indicator"
            write: "setIndicator"
            notify: "indicatorChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "popup"
            type: "QQuickPopup"
            isPointer: true
            read: "popup"
            write: "setPopup"
            notify: "popupChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "flat"
            revision: 513
            type: "bool"
            read: "isFlat"
            write: "setFlat"
            notify: "flatChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "down"
            revision: 514
            type: "bool"
            read: "isDown"
            write: "setDown"
            reset: "resetDown"
            notify: "downChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "editable"
            revision: 514
            type: "bool"
            read: "isEditable"
            write: "setEditable"
            notify: "editableChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "editText"
            revision: 514
            type: "QString"
            read: "editText"
            write: "setEditText"
            reset: "resetEditText"
            notify: "editTextChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "validator"
            revision: 514
            type: "QValidator"
            isPointer: true
            read: "validator"
            write: "setValidator"
            notify: "validatorChanged"
            index: 16
            isFinal: true
        }
        Property {
            name: "inputMethodHints"
            revision: 514
            type: "Qt::InputMethodHints"
            read: "inputMethodHints"
            write: "setInputMethodHints"
            notify: "inputMethodHintsChanged"
            index: 17
            isFinal: true
        }
        Property {
            name: "inputMethodComposing"
            revision: 514
            type: "bool"
            read: "isInputMethodComposing"
            notify: "inputMethodComposingChanged"
            index: 18
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "acceptableInput"
            revision: 514
            type: "bool"
            read: "hasAcceptableInput"
            notify: "acceptableInputChanged"
            index: 19
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitIndicatorWidth"
            revision: 517
            type: "double"
            read: "implicitIndicatorWidth"
            notify: "implicitIndicatorWidthChanged"
            index: 20
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitIndicatorHeight"
            revision: 517
            type: "double"
            read: "implicitIndicatorHeight"
            notify: "implicitIndicatorHeightChanged"
            index: 21
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "currentValue"
            revision: 526
            type: "QVariant"
            read: "currentValue"
            notify: "currentValueChanged"
            index: 22
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "valueRole"
            revision: 526
            type: "QString"
            read: "valueRole"
            write: "setValueRole"
            notify: "valueRoleChanged"
            index: 23
            isFinal: true
        }
        Property {
            name: "selectTextByMouse"
            revision: 527
            type: "bool"
            read: "selectTextByMouse"
            write: "setSelectTextByMouse"
            notify: "selectTextByMouseChanged"
            index: 24
            isFinal: true
        }
        Property {
            name: "implicitContentWidthPolicy"
            revision: 1536
            type: "ImplicitContentWidthPolicy"
            read: "implicitContentWidthPolicy"
            write: "setImplicitContentWidthPolicy"
            notify: "implicitContentWidthPolicyChanged"
            index: 25
            isFinal: true
        }
        Signal {
            name: "activated"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "highlighted"
            Parameter { name: "index"; type: "int" }
        }
        Signal { name: "countChanged" }
        Signal { name: "modelChanged" }
        Signal { name: "delegateModelChanged" }
        Signal { name: "pressedChanged" }
        Signal { name: "highlightedIndexChanged" }
        Signal { name: "currentIndexChanged" }
        Signal { name: "currentTextChanged" }
        Signal { name: "displayTextChanged" }
        Signal { name: "textRoleChanged" }
        Signal { name: "delegateChanged" }
        Signal { name: "indicatorChanged" }
        Signal { name: "popupChanged" }
        Signal { name: "flatChanged"; revision: 513 }
        Signal { name: "accepted"; revision: 514 }
        Signal { name: "downChanged"; revision: 514 }
        Signal { name: "editableChanged"; revision: 514 }
        Signal { name: "editTextChanged"; revision: 514 }
        Signal { name: "validatorChanged"; revision: 514 }
        Signal { name: "inputMethodHintsChanged"; revision: 514 }
        Signal { name: "inputMethodComposingChanged"; revision: 514 }
        Signal { name: "acceptableInputChanged"; revision: 514 }
        Signal { name: "implicitIndicatorWidthChanged"; revision: 517 }
        Signal { name: "implicitIndicatorHeightChanged"; revision: 517 }
        Signal { name: "valueRoleChanged"; revision: 526 }
        Signal { name: "currentValueChanged"; revision: 526 }
        Signal { name: "selectTextByMouseChanged"; revision: 527 }
        Signal { name: "implicitContentWidthPolicyChanged"; revision: 1536 }
        Method { name: "incrementCurrentIndex" }
        Method { name: "decrementCurrentIndex" }
        Method { name: "selectAll"; revision: 514 }
        Method {
            name: "textAt"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "find"
            type: "int"
            isMethodConstant: true
            Parameter { name: "text"; type: "QString" }
            Parameter { name: "flags"; type: "Qt::MatchFlags" }
        }
        Method {
            name: "find"
            type: "int"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "text"; type: "QString" }
        }
        Method {
            name: "valueAt"
            revision: 526
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "indexOfValue"
            revision: 526
            type: "int"
            isMethodConstant: true
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component {
        file: "private/qquickcontainer_p.h"
        name: "QQuickContainer"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/Container 2.0",
            "QtQuick.Templates/Container 2.1",
            "QtQuick.Templates/Container 2.3",
            "QtQuick.Templates/Container 2.4",
            "QtQuick.Templates/Container 2.5",
            "QtQuick.Templates/Container 2.7",
            "QtQuick.Templates/Container 2.11",
            "QtQuick.Templates/Container 6.0",
            "QtQuick.Templates/Container 6.3",
            "QtQuick.Templates/Container 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "contentModel"
            type: "QVariant"
            read: "contentModel"
            index: 1
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "contentData"
            type: "QObject"
            isList: true
            read: "contentData"
            index: 2
            isReadonly: true
        }
        Property {
            name: "contentChildren"
            type: "QQuickItem"
            isList: true
            read: "contentChildren"
            notify: "contentChildrenChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "currentIndex"
            type: "int"
            read: "currentIndex"
            write: "setCurrentIndex"
            notify: "currentIndexChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "currentItem"
            type: "QQuickItem"
            isPointer: true
            read: "currentItem"
            notify: "currentItemChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "contentWidth"
            revision: 517
            type: "double"
            read: "contentWidth"
            write: "setContentWidth"
            reset: "resetContentWidth"
            notify: "contentWidthChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "contentHeight"
            revision: 517
            type: "double"
            read: "contentHeight"
            write: "setContentHeight"
            reset: "resetContentHeight"
            notify: "contentHeightChanged"
            index: 7
            isFinal: true
        }
        Signal { name: "countChanged" }
        Signal { name: "contentChildrenChanged" }
        Signal { name: "currentIndexChanged" }
        Signal { name: "currentItemChanged" }
        Signal { name: "contentWidthChanged"; revision: 517 }
        Signal { name: "contentHeightChanged"; revision: 517 }
        Method {
            name: "setCurrentIndex"
            Parameter { name: "index"; type: "int" }
        }
        Method { name: "incrementCurrentIndex"; revision: 513 }
        Method { name: "decrementCurrentIndex"; revision: 513 }
        Method { name: "_q_currentIndexChanged" }
        Method {
            name: "itemAt"
            type: "QQuickItem"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "addItem"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "insertItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "moveItem"
            Parameter { name: "from"; type: "int" }
            Parameter { name: "to"; type: "int" }
        }
        Method {
            name: "removeItem"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "takeItem"
            revision: 515
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/qquickcontextmenu_p.h"
        name: "QQuickContextMenu"
        accessSemantics: "reference"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        deferredNames: ["menu"]
        exports: ["QtQuick.Templates/ContextMenu 6.9"]
        isCreatable: false
        exportMetaObjectRevisions: [1545]
        attachedType: "QQuickContextMenu"
        Property {
            name: "menu"
            type: "QQuickMenu"
            isPointer: true
            read: "menu"
            write: "setMenu"
            notify: "menuChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "menuChanged" }
        Signal {
            name: "requested"
            Parameter { name: "position"; type: "QPointF" }
        }
    }
    Component {
        file: "private/qquickcontrol_p.h"
        name: "QQuickControl"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        deferredNames: ["background", "contentItem"]
        exports: [
            "QtQuick.Templates/Control 2.0",
            "QtQuick.Templates/Control 2.1",
            "QtQuick.Templates/Control 2.4",
            "QtQuick.Templates/Control 2.5",
            "QtQuick.Templates/Control 2.7",
            "QtQuick.Templates/Control 2.11",
            "QtQuick.Templates/Control 6.0",
            "QtQuick.Templates/Control 6.3",
            "QtQuick.Templates/Control 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            reset: "resetFont"
            notify: "fontChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "availableWidth"
            type: "double"
            read: "availableWidth"
            notify: "availableWidthChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "availableHeight"
            type: "double"
            read: "availableHeight"
            notify: "availableHeightChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "padding"
            type: "double"
            read: "padding"
            write: "setPadding"
            reset: "resetPadding"
            notify: "paddingChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "topPadding"
            type: "double"
            read: "topPadding"
            write: "setTopPadding"
            reset: "resetTopPadding"
            notify: "topPaddingChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "leftPadding"
            type: "double"
            read: "leftPadding"
            write: "setLeftPadding"
            reset: "resetLeftPadding"
            notify: "leftPaddingChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "rightPadding"
            type: "double"
            read: "rightPadding"
            write: "setRightPadding"
            reset: "resetRightPadding"
            notify: "rightPaddingChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "bottomPadding"
            type: "double"
            read: "bottomPadding"
            write: "setBottomPadding"
            reset: "resetBottomPadding"
            notify: "bottomPaddingChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "spacing"
            type: "double"
            read: "spacing"
            write: "setSpacing"
            reset: "resetSpacing"
            notify: "spacingChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "locale"
            type: "QLocale"
            read: "locale"
            write: "setLocale"
            reset: "resetLocale"
            notify: "localeChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "mirrored"
            type: "bool"
            read: "isMirrored"
            notify: "mirroredChanged"
            index: 10
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "focusPolicy"
            type: "Qt::FocusPolicy"
            read: "focusPolicy"
            write: "setFocusPolicy"
            notify: "focusPolicyChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "focusReason"
            type: "Qt::FocusReason"
            read: "focusReason"
            write: "setFocusReason"
            notify: "focusReasonChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "visualFocus"
            type: "bool"
            read: "hasVisualFocus"
            notify: "visualFocusChanged"
            index: 13
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "hovered"
            type: "bool"
            read: "isHovered"
            notify: "hoveredChanged"
            index: 14
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "hoverEnabled"
            type: "bool"
            read: "isHoverEnabled"
            write: "setHoverEnabled"
            reset: "resetHoverEnabled"
            notify: "hoverEnabledChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "wheelEnabled"
            type: "bool"
            read: "isWheelEnabled"
            write: "setWheelEnabled"
            notify: "wheelEnabledChanged"
            index: 16
            isFinal: true
        }
        Property {
            name: "background"
            type: "QQuickItem"
            isPointer: true
            read: "background"
            write: "setBackground"
            notify: "backgroundChanged"
            index: 17
            isFinal: true
        }
        Property {
            name: "contentItem"
            type: "QQuickItem"
            isPointer: true
            read: "contentItem"
            write: "setContentItem"
            notify: "contentItemChanged"
            index: 18
            isFinal: true
        }
        Property {
            name: "baselineOffset"
            type: "double"
            read: "baselineOffset"
            write: "setBaselineOffset"
            reset: "resetBaselineOffset"
            notify: "baselineOffsetChanged"
            index: 19
            isFinal: true
        }
        Property {
            name: "horizontalPadding"
            revision: 517
            type: "double"
            read: "horizontalPadding"
            write: "setHorizontalPadding"
            reset: "resetHorizontalPadding"
            notify: "horizontalPaddingChanged"
            index: 20
            isFinal: true
        }
        Property {
            name: "verticalPadding"
            revision: 517
            type: "double"
            read: "verticalPadding"
            write: "setVerticalPadding"
            reset: "resetVerticalPadding"
            notify: "verticalPaddingChanged"
            index: 21
            isFinal: true
        }
        Property {
            name: "implicitContentWidth"
            revision: 517
            type: "double"
            read: "implicitContentWidth"
            notify: "implicitContentWidthChanged"
            index: 22
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitContentHeight"
            revision: 517
            type: "double"
            read: "implicitContentHeight"
            notify: "implicitContentHeightChanged"
            index: 23
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitBackgroundWidth"
            revision: 517
            type: "double"
            read: "implicitBackgroundWidth"
            notify: "implicitBackgroundWidthChanged"
            index: 24
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitBackgroundHeight"
            revision: 517
            type: "double"
            read: "implicitBackgroundHeight"
            notify: "implicitBackgroundHeightChanged"
            index: 25
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "topInset"
            revision: 517
            type: "double"
            read: "topInset"
            write: "setTopInset"
            reset: "resetTopInset"
            notify: "topInsetChanged"
            index: 26
            isFinal: true
        }
        Property {
            name: "leftInset"
            revision: 517
            type: "double"
            read: "leftInset"
            write: "setLeftInset"
            reset: "resetLeftInset"
            notify: "leftInsetChanged"
            index: 27
            isFinal: true
        }
        Property {
            name: "rightInset"
            revision: 517
            type: "double"
            read: "rightInset"
            write: "setRightInset"
            reset: "resetRightInset"
            notify: "rightInsetChanged"
            index: 28
            isFinal: true
        }
        Property {
            name: "bottomInset"
            revision: 517
            type: "double"
            read: "bottomInset"
            write: "setBottomInset"
            reset: "resetBottomInset"
            notify: "bottomInsetChanged"
            index: 29
            isFinal: true
        }
        Signal { name: "fontChanged" }
        Signal { name: "availableWidthChanged" }
        Signal { name: "availableHeightChanged" }
        Signal { name: "paddingChanged" }
        Signal { name: "topPaddingChanged" }
        Signal { name: "leftPaddingChanged" }
        Signal { name: "rightPaddingChanged" }
        Signal { name: "bottomPaddingChanged" }
        Signal { name: "spacingChanged" }
        Signal { name: "localeChanged" }
        Signal { name: "focusReasonChanged" }
        Signal { name: "mirroredChanged" }
        Signal { name: "visualFocusChanged" }
        Signal { name: "hoveredChanged" }
        Signal { name: "hoverEnabledChanged" }
        Signal { name: "wheelEnabledChanged" }
        Signal { name: "backgroundChanged" }
        Signal { name: "contentItemChanged" }
        Signal { name: "baselineOffsetChanged" }
        Signal { name: "horizontalPaddingChanged"; revision: 517 }
        Signal { name: "verticalPaddingChanged"; revision: 517 }
        Signal { name: "implicitContentWidthChanged"; revision: 517 }
        Signal { name: "implicitContentHeightChanged"; revision: 517 }
        Signal { name: "implicitBackgroundWidthChanged"; revision: 517 }
        Signal { name: "implicitBackgroundHeightChanged"; revision: 517 }
        Signal { name: "topInsetChanged"; revision: 517 }
        Signal { name: "leftInsetChanged"; revision: 517 }
        Signal { name: "rightInsetChanged"; revision: 517 }
        Signal { name: "bottomInsetChanged"; revision: 517 }
    }
    Component {
        file: "private/qquickdayofweekrow_p.h"
        name: "QQuickDayOfWeekRow"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/AbstractDayOfWeekRow 6.3",
            "QtQuick.Templates/AbstractDayOfWeekRow 6.7"
        ]
        exportMetaObjectRevisions: [1539, 1543]
        Property {
            name: "source"
            type: "QVariant"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 1
            isFinal: true
        }
        Signal { name: "sourceChanged" }
        Signal { name: "delegateChanged" }
    }
    Component {
        file: "private/qquickdelaybutton_p.h"
        name: "QQuickDelayButton"
        accessSemantics: "reference"
        prototype: "QQuickAbstractButton"
        exports: [
            "QtQuick.Templates/DelayButton 2.2",
            "QtQuick.Templates/DelayButton 2.3",
            "QtQuick.Templates/DelayButton 2.4",
            "QtQuick.Templates/DelayButton 2.5",
            "QtQuick.Templates/DelayButton 2.7",
            "QtQuick.Templates/DelayButton 2.11",
            "QtQuick.Templates/DelayButton 6.0",
            "QtQuick.Templates/DelayButton 6.3",
            "QtQuick.Templates/DelayButton 6.7",
            "QtQuick.Templates/DelayButton 6.8"
        ]
        exportMetaObjectRevisions: [
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
        Property {
            name: "delay"
            type: "int"
            read: "delay"
            write: "setDelay"
            notify: "delayChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "progress"
            type: "double"
            read: "progress"
            write: "setProgress"
            notify: "progressChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "transition"
            type: "QQuickTransition"
            isPointer: true
            read: "transition"
            write: "setTransition"
            notify: "transitionChanged"
            index: 2
            isFinal: true
        }
        Signal { name: "activated" }
        Signal { name: "delayChanged" }
        Signal { name: "progressChanged" }
        Signal { name: "transitionChanged" }
    }
    Component {
        file: "private/qquickdial_p.h"
        name: "QQuickDial"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        deferredNames: ["background", "handle"]
        exports: [
            "QtQuick.Templates/Dial 2.0",
            "QtQuick.Templates/Dial 2.1",
            "QtQuick.Templates/Dial 2.2",
            "QtQuick.Templates/Dial 2.4",
            "QtQuick.Templates/Dial 2.5",
            "QtQuick.Templates/Dial 2.7",
            "QtQuick.Templates/Dial 2.11",
            "QtQuick.Templates/Dial 6.0",
            "QtQuick.Templates/Dial 6.3",
            "QtQuick.Templates/Dial 6.6",
            "QtQuick.Templates/Dial 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1542,
            1543
        ]
        Enum {
            name: "SnapMode"
            values: ["NoSnap", "SnapAlways", "SnapOnRelease"]
        }
        Enum {
            name: "InputMode"
            values: ["Circular", "Horizontal", "Vertical"]
        }
        Enum {
            name: "WrapDirection"
            values: ["Clockwise", "CounterClockwise"]
        }
        Property {
            name: "from"
            type: "double"
            read: "from"
            write: "setFrom"
            notify: "fromChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "to"
            type: "double"
            read: "to"
            write: "setTo"
            notify: "toChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "value"
            type: "double"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "position"
            type: "double"
            read: "position"
            notify: "positionChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "angle"
            type: "double"
            read: "angle"
            notify: "angleChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "startAngle"
            revision: 1542
            type: "double"
            read: "startAngle"
            write: "setStartAngle"
            notify: "startAngleChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "endAngle"
            revision: 1542
            type: "double"
            read: "endAngle"
            write: "setEndAngle"
            notify: "endAngleChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "stepSize"
            type: "double"
            read: "stepSize"
            write: "setStepSize"
            notify: "stepSizeChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "snapMode"
            type: "SnapMode"
            read: "snapMode"
            write: "setSnapMode"
            notify: "snapModeChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "wrap"
            type: "bool"
            read: "wrap"
            write: "setWrap"
            notify: "wrapChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "isPressed"
            notify: "pressedChanged"
            index: 10
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "handle"
            type: "QQuickItem"
            isPointer: true
            read: "handle"
            write: "setHandle"
            notify: "handleChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "live"
            revision: 514
            type: "bool"
            read: "live"
            write: "setLive"
            notify: "liveChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "inputMode"
            revision: 517
            type: "InputMode"
            read: "inputMode"
            write: "setInputMode"
            notify: "inputModeChanged"
            index: 13
            isFinal: true
        }
        Signal { name: "fromChanged" }
        Signal { name: "toChanged" }
        Signal { name: "valueChanged" }
        Signal { name: "positionChanged" }
        Signal { name: "angleChanged" }
        Signal { name: "stepSizeChanged" }
        Signal { name: "snapModeChanged" }
        Signal { name: "wrapChanged" }
        Signal { name: "pressedChanged" }
        Signal { name: "handleChanged" }
        Signal { name: "moved"; revision: 514 }
        Signal { name: "liveChanged"; revision: 514 }
        Signal { name: "inputModeChanged"; revision: 517 }
        Signal { name: "startAngleChanged"; revision: 1542 }
        Signal { name: "endAngleChanged"; revision: 1542 }
        Signal {
            name: "wrapped"
            revision: 1542
            Parameter { type: "WrapDirection" }
        }
        Method { name: "increase" }
        Method { name: "decrease" }
    }
    Component {
        file: "private/qquickdialog_p.h"
        name: "QQuickDialog"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickPopup"
        extension: "QPlatformDialogHelper"
        extensionIsNamespace: true
        exports: [
            "QtQuick.Templates/Dialog 2.1",
            "QtQuick.Templates/Dialog 2.3",
            "QtQuick.Templates/Dialog 2.5",
            "QtQuick.Templates/Dialog 6.0",
            "QtQuick.Templates/Dialog 6.8"
        ]
        exportMetaObjectRevisions: [513, 515, 517, 1536, 1544]
        Enum {
            name: "StandardCode"
            values: ["Rejected", "Accepted"]
        }
        Property {
            name: "title"
            type: "QString"
            read: "title"
            write: "setTitle"
            notify: "titleChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "header"
            type: "QQuickItem"
            isPointer: true
            read: "header"
            write: "setHeader"
            notify: "headerChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "footer"
            type: "QQuickItem"
            isPointer: true
            read: "footer"
            write: "setFooter"
            notify: "footerChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "standardButtons"
            type: "QPlatformDialogHelper::StandardButtons"
            read: "standardButtons"
            write: "setStandardButtons"
            notify: "standardButtonsChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "result"
            revision: 515
            type: "int"
            read: "result"
            write: "setResult"
            notify: "resultChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "implicitHeaderWidth"
            revision: 517
            type: "double"
            read: "implicitHeaderWidth"
            notify: "implicitHeaderWidthChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitHeaderHeight"
            revision: 517
            type: "double"
            read: "implicitHeaderHeight"
            notify: "implicitHeaderHeightChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitFooterWidth"
            revision: 517
            type: "double"
            read: "implicitFooterWidth"
            notify: "implicitFooterWidthChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitFooterHeight"
            revision: 517
            type: "double"
            read: "implicitFooterHeight"
            notify: "implicitFooterHeightChanged"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Signal { name: "accepted" }
        Signal { name: "rejected" }
        Signal { name: "titleChanged" }
        Signal { name: "headerChanged" }
        Signal { name: "footerChanged" }
        Signal { name: "standardButtonsChanged" }
        Signal { name: "applied"; revision: 515 }
        Signal { name: "reset"; revision: 515 }
        Signal { name: "discarded"; revision: 515 }
        Signal { name: "helpRequested"; revision: 515 }
        Signal { name: "resultChanged"; revision: 515 }
        Signal { name: "implicitHeaderWidthChanged" }
        Signal { name: "implicitHeaderHeightChanged" }
        Signal { name: "implicitFooterWidthChanged" }
        Signal { name: "implicitFooterHeightChanged" }
        Method { name: "accept" }
        Method { name: "reject" }
        Method {
            name: "done"
            Parameter { name: "result"; type: "int" }
        }
        Method {
            name: "standardButton"
            revision: 515
            type: "QQuickAbstractButton"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "button"; type: "QPlatformDialogHelper::StandardButton" }
        }
    }
    Component {
        file: "private/qquickdialogbuttonbox_p.h"
        name: "QQuickDialogButtonBox"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickContainer"
        extension: "QPlatformDialogHelper"
        extensionIsNamespace: true
        exports: [
            "QtQuick.Templates/DialogButtonBox 2.1",
            "QtQuick.Templates/DialogButtonBox 2.3",
            "QtQuick.Templates/DialogButtonBox 2.4",
            "QtQuick.Templates/DialogButtonBox 2.5",
            "QtQuick.Templates/DialogButtonBox 2.7",
            "QtQuick.Templates/DialogButtonBox 2.11",
            "QtQuick.Templates/DialogButtonBox 6.0",
            "QtQuick.Templates/DialogButtonBox 6.3",
            "QtQuick.Templates/DialogButtonBox 6.7"
        ]
        exportMetaObjectRevisions: [
            513,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        attachedType: "QQuickDialogButtonBoxAttached"
        Enum {
            name: "Position"
            values: ["Header", "Footer"]
        }
        Property {
            name: "position"
            type: "Position"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "alignment"
            type: "Qt::Alignment"
            read: "alignment"
            write: "setAlignment"
            reset: "resetAlignment"
            notify: "alignmentChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "standardButtons"
            type: "QPlatformDialogHelper::StandardButtons"
            read: "standardButtons"
            write: "setStandardButtons"
            notify: "standardButtonsChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "buttonLayout"
            revision: 517
            type: "QPlatformDialogHelper::ButtonLayout"
            read: "buttonLayout"
            write: "setButtonLayout"
            reset: "resetButtonLayout"
            notify: "buttonLayoutChanged"
            index: 4
            isFinal: true
        }
        Signal { name: "accepted" }
        Signal { name: "rejected" }
        Signal { name: "helpRequested" }
        Signal {
            name: "clicked"
            Parameter { name: "button"; type: "QQuickAbstractButton"; isPointer: true }
        }
        Signal { name: "positionChanged" }
        Signal { name: "alignmentChanged" }
        Signal { name: "standardButtonsChanged" }
        Signal { name: "delegateChanged" }
        Signal { name: "applied"; revision: 515 }
        Signal { name: "reset"; revision: 515 }
        Signal { name: "discarded"; revision: 515 }
        Signal { name: "buttonLayoutChanged"; revision: 517 }
        Method {
            name: "standardButton"
            type: "QQuickAbstractButton"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "button"; type: "QPlatformDialogHelper::StandardButton" }
        }
    }
    Component {
        file: "private/qquickdialogbuttonbox_p.h"
        name: "QQuickDialogButtonBoxAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "buttonBox"
            type: "QQuickDialogButtonBox"
            isPointer: true
            read: "buttonBox"
            notify: "buttonBoxChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "buttonRole"
            type: "QPlatformDialogHelper::ButtonRole"
            read: "buttonRole"
            write: "setButtonRole"
            notify: "buttonRoleChanged"
            index: 1
            isFinal: true
        }
        Signal { name: "buttonBoxChanged" }
        Signal { name: "buttonRoleChanged" }
    }
    Component {
        file: "private/qquickdrawer_p.h"
        name: "QQuickDrawer"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickPopup"
        exports: [
            "QtQuick.Templates/Drawer 2.0",
            "QtQuick.Templates/Drawer 2.1",
            "QtQuick.Templates/Drawer 2.2",
            "QtQuick.Templates/Drawer 2.3",
            "QtQuick.Templates/Drawer 2.5",
            "QtQuick.Templates/Drawer 6.0",
            "QtQuick.Templates/Drawer 6.8"
        ]
        exportMetaObjectRevisions: [512, 513, 514, 515, 517, 1536, 1544]
        Property {
            name: "edge"
            type: "Qt::Edge"
            read: "edge"
            write: "setEdge"
            notify: "edgeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "position"
            type: "double"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "dragMargin"
            type: "double"
            read: "dragMargin"
            write: "setDragMargin"
            reset: "resetDragMargin"
            notify: "dragMarginChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "interactive"
            revision: 514
            type: "bool"
            read: "isInteractive"
            write: "setInteractive"
            notify: "interactiveChanged"
            index: 3
            isFinal: true
        }
        Signal { name: "edgeChanged" }
        Signal { name: "positionChanged" }
        Signal { name: "dragMarginChanged" }
        Signal { name: "interactiveChanged"; revision: 514 }
    }
    Component {
        file: "private/qquickframe_p.h"
        name: "QQuickFrame"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickPane"
        exports: [
            "QtQuick.Templates/Frame 2.0",
            "QtQuick.Templates/Frame 2.1",
            "QtQuick.Templates/Frame 2.4",
            "QtQuick.Templates/Frame 2.5",
            "QtQuick.Templates/Frame 2.7",
            "QtQuick.Templates/Frame 2.11",
            "QtQuick.Templates/Frame 6.0",
            "QtQuick.Templates/Frame 6.3",
            "QtQuick.Templates/Frame 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
    }
    Component {
        file: "private/qquickgroupbox_p.h"
        name: "QQuickGroupBox"
        accessSemantics: "reference"
        prototype: "QQuickFrame"
        deferredNames: ["background", "contentItem", "label"]
        exports: [
            "QtQuick.Templates/GroupBox 2.0",
            "QtQuick.Templates/GroupBox 2.1",
            "QtQuick.Templates/GroupBox 2.4",
            "QtQuick.Templates/GroupBox 2.5",
            "QtQuick.Templates/GroupBox 2.7",
            "QtQuick.Templates/GroupBox 2.11",
            "QtQuick.Templates/GroupBox 6.0",
            "QtQuick.Templates/GroupBox 6.3",
            "QtQuick.Templates/GroupBox 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        Property {
            name: "title"
            type: "QString"
            read: "title"
            write: "setTitle"
            notify: "titleChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "label"
            type: "QQuickItem"
            isPointer: true
            read: "label"
            write: "setLabel"
            notify: "labelChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "implicitLabelWidth"
            revision: 517
            type: "double"
            read: "implicitLabelWidth"
            notify: "implicitLabelWidthChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitLabelHeight"
            revision: 517
            type: "double"
            read: "implicitLabelHeight"
            notify: "implicitLabelHeightChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Signal { name: "titleChanged" }
        Signal { name: "labelChanged" }
        Signal { name: "implicitLabelWidthChanged"; revision: 517 }
        Signal { name: "implicitLabelHeightChanged"; revision: 517 }
    }
    Component {
        file: "private/qquickheaderview_p.h"
        name: "QQuickHeaderViewBase"
        accessSemantics: "reference"
        prototype: "QQuickTableView"
        Property {
            name: "textRole"
            type: "QString"
            read: "textRole"
            write: "setTextRole"
            notify: "textRoleChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "textRoleChanged" }
    }
    Component {
        file: "private/qquickheaderview_p.h"
        name: "QQuickHorizontalHeaderView"
        accessSemantics: "reference"
        prototype: "QQuickHeaderViewBase"
        exports: [
            "QtQuick.Templates/HorizontalHeaderView 2.15",
            "QtQuick.Templates/HorizontalHeaderView 6.0",
            "QtQuick.Templates/HorizontalHeaderView 6.2",
            "QtQuick.Templates/HorizontalHeaderView 6.3",
            "QtQuick.Templates/HorizontalHeaderView 6.4",
            "QtQuick.Templates/HorizontalHeaderView 6.5",
            "QtQuick.Templates/HorizontalHeaderView 6.6",
            "QtQuick.Templates/HorizontalHeaderView 6.7",
            "QtQuick.Templates/HorizontalHeaderView 6.8",
            "QtQuick.Templates/HorizontalHeaderView 6.9"
        ]
        exportMetaObjectRevisions: [
            527,
            1536,
            1538,
            1539,
            1540,
            1541,
            1542,
            1543,
            1544,
            1545
        ]
        Property {
            name: "movableColumns"
            revision: 1544
            type: "bool"
            read: "movableColumns"
            write: "setMovableColumns"
            notify: "movableColumnsChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "movableColumnsChanged"; revision: 1544 }
    }
    Component {
        file: "private/qquickicon_p.h"
        name: "QQuickIcon"
        accessSemantics: "value"
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            reset: "resetName"
            index: 0
            isFinal: true
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            reset: "resetSource"
            index: 1
            isFinal: true
        }
        Property {
            name: "width"
            type: "int"
            read: "width"
            write: "setWidth"
            reset: "resetWidth"
            index: 2
            isFinal: true
        }
        Property {
            name: "height"
            type: "int"
            read: "height"
            write: "setHeight"
            reset: "resetHeight"
            index: 3
            isFinal: true
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            reset: "resetColor"
            index: 4
            isFinal: true
        }
        Property {
            name: "cache"
            type: "bool"
            read: "cache"
            write: "setCache"
            reset: "resetCache"
            index: 5
            isFinal: true
        }
    }
    Component {
        file: "private/qquickindicatorbutton_p.h"
        name: "QQuickIndicatorButton"
        accessSemantics: "reference"
        prototype: "QObject"
        deferredNames: ["indicator"]
        Property {
            name: "pressed"
            type: "bool"
            read: "isPressed"
            write: "setPressed"
            notify: "pressedChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "indicator"
            type: "QQuickItem"
            isPointer: true
            read: "indicator"
            write: "setIndicator"
            notify: "indicatorChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "hovered"
            revision: 513
            type: "bool"
            read: "isHovered"
            write: "setHovered"
            notify: "hoveredChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "implicitIndicatorWidth"
            revision: 517
            type: "double"
            read: "implicitIndicatorWidth"
            notify: "implicitIndicatorWidthChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitIndicatorHeight"
            revision: 517
            type: "double"
            read: "implicitIndicatorHeight"
            notify: "implicitIndicatorHeightChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Signal { name: "pressedChanged" }
        Signal { name: "indicatorChanged" }
        Signal { name: "hoveredChanged"; revision: 513 }
        Signal { name: "implicitIndicatorWidthChanged"; revision: 517 }
        Signal { name: "implicitIndicatorHeightChanged"; revision: 517 }
    }
    Component {
        file: "private/qquickitemdelegate_p.h"
        name: "QQuickItemDelegate"
        accessSemantics: "reference"
        prototype: "QQuickAbstractButton"
        exports: [
            "QtQuick.Templates/ItemDelegate 2.0",
            "QtQuick.Templates/ItemDelegate 2.1",
            "QtQuick.Templates/ItemDelegate 2.2",
            "QtQuick.Templates/ItemDelegate 2.3",
            "QtQuick.Templates/ItemDelegate 2.4",
            "QtQuick.Templates/ItemDelegate 2.5",
            "QtQuick.Templates/ItemDelegate 2.7",
            "QtQuick.Templates/ItemDelegate 2.11",
            "QtQuick.Templates/ItemDelegate 6.0",
            "QtQuick.Templates/ItemDelegate 6.3",
            "QtQuick.Templates/ItemDelegate 6.7",
            "QtQuick.Templates/ItemDelegate 6.8"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
        Property {
            name: "highlighted"
            type: "bool"
            read: "isHighlighted"
            write: "setHighlighted"
            notify: "highlightedChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "highlightedChanged" }
    }
    Component {
        file: "private/qquicklabel_p.h"
        name: "QQuickLabel"
        accessSemantics: "reference"
        prototype: "QQuickText"
        deferredNames: ["background"]
        exports: [
            "QtQuick.Templates/Label 2.0",
            "QtQuick.Templates/Label 2.1",
            "QtQuick.Templates/Label 2.2",
            "QtQuick.Templates/Label 2.3",
            "QtQuick.Templates/Label 2.4",
            "QtQuick.Templates/Label 2.5",
            "QtQuick.Templates/Label 2.6",
            "QtQuick.Templates/Label 2.7",
            "QtQuick.Templates/Label 2.9",
            "QtQuick.Templates/Label 2.10",
            "QtQuick.Templates/Label 2.11",
            "QtQuick.Templates/Label 6.0",
            "QtQuick.Templates/Label 6.2",
            "QtQuick.Templates/Label 6.3",
            "QtQuick.Templates/Label 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            518,
            519,
            521,
            522,
            523,
            1536,
            1538,
            1539,
            1543
        ]
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 0
        }
        Property {
            name: "background"
            type: "QQuickItem"
            isPointer: true
            read: "background"
            write: "setBackground"
            notify: "backgroundChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "implicitBackgroundWidth"
            revision: 517
            type: "double"
            read: "implicitBackgroundWidth"
            notify: "implicitBackgroundWidthChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitBackgroundHeight"
            revision: 517
            type: "double"
            read: "implicitBackgroundHeight"
            notify: "implicitBackgroundHeightChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "topInset"
            revision: 517
            type: "double"
            read: "topInset"
            write: "setTopInset"
            reset: "resetTopInset"
            notify: "topInsetChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "leftInset"
            revision: 517
            type: "double"
            read: "leftInset"
            write: "setLeftInset"
            reset: "resetLeftInset"
            notify: "leftInsetChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "rightInset"
            revision: 517
            type: "double"
            read: "rightInset"
            write: "setRightInset"
            reset: "resetRightInset"
            notify: "rightInsetChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "bottomInset"
            revision: 517
            type: "double"
            read: "bottomInset"
            write: "setBottomInset"
            reset: "resetBottomInset"
            notify: "bottomInsetChanged"
            index: 7
            isFinal: true
        }
        Signal { name: "fontChanged" }
        Signal { name: "backgroundChanged" }
        Signal { name: "implicitBackgroundWidthChanged"; revision: 517 }
        Signal { name: "implicitBackgroundHeightChanged"; revision: 517 }
        Signal { name: "topInsetChanged"; revision: 517 }
        Signal { name: "leftInsetChanged"; revision: 517 }
        Signal { name: "rightInsetChanged"; revision: 517 }
        Signal { name: "bottomInsetChanged"; revision: 517 }
    }
    Component {
        file: "private/qquickmenu_p.h"
        name: "QQuickMenu"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickPopup"
        exports: [
            "QtQuick.Templates/Menu 2.0",
            "QtQuick.Templates/Menu 2.1",
            "QtQuick.Templates/Menu 2.3",
            "QtQuick.Templates/Menu 2.5",
            "QtQuick.Templates/Menu 6.0",
            "QtQuick.Templates/Menu 6.5",
            "QtQuick.Templates/Menu 6.8"
        ]
        exportMetaObjectRevisions: [512, 513, 515, 517, 1536, 1541, 1544]
        Property {
            name: "contentModel"
            type: "QVariant"
            read: "contentModel"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "contentData"
            type: "QObject"
            isList: true
            read: "contentData"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "title"
            type: "QString"
            read: "title"
            write: "setTitle"
            notify: "titleChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "count"
            revision: 515
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "cascade"
            revision: 515
            type: "bool"
            read: "cascade"
            write: "setCascade"
            reset: "resetCascade"
            notify: "cascadeChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "overlap"
            revision: 515
            type: "double"
            read: "overlap"
            write: "setOverlap"
            notify: "overlapChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "delegate"
            revision: 515
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "currentIndex"
            revision: 515
            type: "int"
            read: "currentIndex"
            write: "setCurrentIndex"
            notify: "currentIndexChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "icon"
            revision: 1541
            type: "QQuickIcon"
            read: "icon"
            write: "setIcon"
            notify: "iconChanged"
            index: 8
            isFinal: true
        }
        Signal {
            name: "titleChanged"
            Parameter { name: "title"; type: "QString" }
        }
        Signal { name: "countChanged"; revision: 515 }
        Signal {
            name: "cascadeChanged"
            revision: 515
            Parameter { name: "cascade"; type: "bool" }
        }
        Signal { name: "overlapChanged"; revision: 515 }
        Signal { name: "delegateChanged"; revision: 515 }
        Signal { name: "currentIndexChanged"; revision: 515 }
        Signal {
            name: "iconChanged"
            revision: 1541
            Parameter { name: "icon"; type: "QQuickIcon" }
        }
        Method {
            name: "itemAt"
            type: "QQuickItem"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "addItem"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "insertItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "moveItem"
            Parameter { name: "from"; type: "int" }
            Parameter { name: "to"; type: "int" }
        }
        Method {
            name: "removeItem"
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "takeItem"
            revision: 515
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "menuAt"
            revision: 515
            type: "QQuickMenu"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "addMenu"
            revision: 515
            Parameter { name: "menu"; type: "QQuickMenu"; isPointer: true }
        }
        Method {
            name: "insertMenu"
            revision: 515
            Parameter { name: "index"; type: "int" }
            Parameter { name: "menu"; type: "QQuickMenu"; isPointer: true }
        }
        Method {
            name: "removeMenu"
            revision: 515
            Parameter { name: "menu"; type: "QQuickMenu"; isPointer: true }
        }
        Method {
            name: "takeMenu"
            revision: 515
            type: "QQuickMenu"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "actionAt"
            revision: 515
            type: "QQuickAction"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "addAction"
            revision: 515
            Parameter { name: "action"; type: "QQuickAction"; isPointer: true }
        }
        Method {
            name: "insertAction"
            revision: 515
            Parameter { name: "index"; type: "int" }
            Parameter { name: "action"; type: "QQuickAction"; isPointer: true }
        }
        Method {
            name: "removeAction"
            revision: 515
            Parameter { name: "action"; type: "QQuickAction"; isPointer: true }
        }
        Method {
            name: "takeAction"
            revision: 515
            type: "QQuickAction"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "popup"
            revision: 515
            Parameter { name: "parent"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "menuItem"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "popup"
            revision: 515
            isCloned: true
            Parameter { name: "parent"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "popup"
            revision: 515
            Parameter { name: "parent"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "position"; type: "QPointF" }
            Parameter { name: "menuItem"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "popup"
            revision: 515
            isCloned: true
            Parameter { name: "parent"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "position"; type: "QPointF" }
        }
        Method {
            name: "popup"
            revision: 515
            Parameter { name: "parent"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "menuItem"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "popup"
            revision: 515
            Parameter { name: "parent"; type: "QQuickItem"; isPointer: true }
        }
        Method { name: "popup"; revision: 515; isCloned: true }
        Method {
            name: "popup"
            revision: 515
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
            Parameter { name: "menuItem"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "popup"
            revision: 515
            isCloned: true
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "popup"
            revision: 515
            Parameter { name: "position"; type: "QPointF" }
            Parameter { name: "menuItem"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "popup"
            revision: 515
            isCloned: true
            Parameter { name: "position"; type: "QPointF" }
        }
        Method { name: "dismiss"; revision: 515 }
    }
    Component {
        file: "private/qquickmenubar_p.h"
        name: "QQuickMenuBar"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickContainer"
        exports: [
            "QtQuick.Templates/MenuBar 2.3",
            "QtQuick.Templates/MenuBar 2.4",
            "QtQuick.Templates/MenuBar 2.5",
            "QtQuick.Templates/MenuBar 2.7",
            "QtQuick.Templates/MenuBar 2.11",
            "QtQuick.Templates/MenuBar 6.0",
            "QtQuick.Templates/MenuBar 6.3",
            "QtQuick.Templates/MenuBar 6.7"
        ]
        exportMetaObjectRevisions: [515, 516, 517, 519, 523, 1536, 1539, 1543]
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "menus"
            type: "QQuickMenu"
            isList: true
            read: "menus"
            notify: "menusChanged"
            index: 1
            privateClass: "QQuickMenuBarPrivate"
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "contentData"
            type: "QObject"
            isList: true
            read: "contentData"
            index: 2
            privateClass: "QQuickMenuBarPrivate"
            isReadonly: true
            isFinal: true
        }
        Signal { name: "delegateChanged" }
        Signal { name: "menusChanged" }
        Method {
            name: "menuAt"
            type: "QQuickMenu"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "addMenu"
            Parameter { name: "menu"; type: "QQuickMenu"; isPointer: true }
        }
        Method {
            name: "insertMenu"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "menu"; type: "QQuickMenu"; isPointer: true }
        }
        Method {
            name: "removeMenu"
            Parameter { name: "menu"; type: "QQuickMenu"; isPointer: true }
        }
        Method {
            name: "takeMenu"
            type: "QQuickMenu"
            isPointer: true
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/qquickmenubaritem_p.h"
        name: "QQuickMenuBarItem"
        accessSemantics: "reference"
        prototype: "QQuickAbstractButton"
        exports: [
            "QtQuick.Templates/MenuBarItem 2.3",
            "QtQuick.Templates/MenuBarItem 2.4",
            "QtQuick.Templates/MenuBarItem 2.5",
            "QtQuick.Templates/MenuBarItem 2.7",
            "QtQuick.Templates/MenuBarItem 2.11",
            "QtQuick.Templates/MenuBarItem 6.0",
            "QtQuick.Templates/MenuBarItem 6.3",
            "QtQuick.Templates/MenuBarItem 6.7",
            "QtQuick.Templates/MenuBarItem 6.8"
        ]
        exportMetaObjectRevisions: [
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
        Property {
            name: "menuBar"
            type: "QQuickMenuBar"
            isPointer: true
            read: "menuBar"
            notify: "menuBarChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "menu"
            type: "QQuickMenu"
            isPointer: true
            read: "menu"
            write: "setMenu"
            notify: "menuChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "highlighted"
            type: "bool"
            read: "isHighlighted"
            write: "setHighlighted"
            notify: "highlightedChanged"
            index: 2
            isFinal: true
        }
        Signal { name: "triggered" }
        Signal { name: "menuBarChanged" }
        Signal { name: "menuChanged" }
        Signal { name: "highlightedChanged" }
    }
    Component {
        file: "private/qquickmenuitem_p.h"
        name: "QQuickMenuItem"
        accessSemantics: "reference"
        prototype: "QQuickAbstractButton"
        deferredNames: ["arrow", "background", "contentItem", "indicator"]
        exports: [
            "QtQuick.Templates/MenuItem 2.0",
            "QtQuick.Templates/MenuItem 2.1",
            "QtQuick.Templates/MenuItem 2.2",
            "QtQuick.Templates/MenuItem 2.3",
            "QtQuick.Templates/MenuItem 2.4",
            "QtQuick.Templates/MenuItem 2.5",
            "QtQuick.Templates/MenuItem 2.7",
            "QtQuick.Templates/MenuItem 2.11",
            "QtQuick.Templates/MenuItem 6.0",
            "QtQuick.Templates/MenuItem 6.3",
            "QtQuick.Templates/MenuItem 6.7",
            "QtQuick.Templates/MenuItem 6.8"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
        Property {
            name: "highlighted"
            type: "bool"
            read: "isHighlighted"
            write: "setHighlighted"
            notify: "highlightedChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "arrow"
            revision: 515
            type: "QQuickItem"
            isPointer: true
            read: "arrow"
            write: "setArrow"
            notify: "arrowChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "menu"
            revision: 515
            type: "QQuickMenu"
            isPointer: true
            read: "menu"
            notify: "menuChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "subMenu"
            revision: 515
            type: "QQuickMenu"
            isPointer: true
            read: "subMenu"
            notify: "subMenuChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitTextPadding"
            revision: 1544
            type: "double"
            read: "implicitTextPadding"
            write: "setImplicitTextPadding"
            notify: "implicitTextPaddingChanged"
            index: 4
        }
        Property {
            name: "textPadding"
            revision: 1544
            type: "double"
            read: "textPadding"
            notify: "textPaddingChanged"
            index: 5
            isReadonly: true
        }
        Signal { name: "triggered" }
        Signal { name: "highlightedChanged" }
        Signal { name: "arrowChanged"; revision: 515 }
        Signal { name: "menuChanged"; revision: 515 }
        Signal { name: "subMenuChanged"; revision: 515 }
        Signal { name: "implicitTextPaddingChanged"; revision: 1544 }
        Signal { name: "textPaddingChanged"; revision: 1544 }
    }
    Component {
        file: "private/qquickmenuseparator_p.h"
        name: "QQuickMenuSeparator"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/MenuSeparator 2.1",
            "QtQuick.Templates/MenuSeparator 2.4",
            "QtQuick.Templates/MenuSeparator 2.5",
            "QtQuick.Templates/MenuSeparator 2.7",
            "QtQuick.Templates/MenuSeparator 2.11",
            "QtQuick.Templates/MenuSeparator 6.0",
            "QtQuick.Templates/MenuSeparator 6.3",
            "QtQuick.Templates/MenuSeparator 6.7"
        ]
        exportMetaObjectRevisions: [513, 516, 517, 519, 523, 1536, 1539, 1543]
    }
    Component {
        file: "private/qquickmonthgrid_p.h"
        name: "QQuickMonthGrid"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/AbstractMonthGrid 6.3",
            "QtQuick.Templates/AbstractMonthGrid 6.7"
        ]
        exportMetaObjectRevisions: [1539, 1543]
        Property {
            name: "month"
            type: "int"
            read: "month"
            write: "setMonth"
            notify: "monthChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "year"
            type: "int"
            read: "year"
            write: "setYear"
            notify: "yearChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "source"
            type: "QVariant"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "title"
            type: "QString"
            read: "title"
            write: "setTitle"
            notify: "titleChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 4
            isFinal: true
        }
        Signal { name: "monthChanged" }
        Signal { name: "yearChanged" }
        Signal { name: "sourceChanged" }
        Signal { name: "titleChanged" }
        Signal { name: "delegateChanged" }
        Signal {
            name: "pressed"
            Parameter { name: "date"; type: "QDate" }
        }
        Signal {
            name: "released"
            Parameter { name: "date"; type: "QDate" }
        }
        Signal {
            name: "clicked"
            Parameter { name: "date"; type: "QDate" }
        }
        Signal {
            name: "pressAndHold"
            Parameter { name: "date"; type: "QDate" }
        }
    }
    Component {
        file: "private/qquicknativeicon_p.h"
        name: "QQuickNativeIcon"
        accessSemantics: "value"
        Property { name: "source"; type: "QUrl"; read: "source"; write: "setSource"; index: 0; isFinal: true }
        Property { name: "name"; type: "QString"; read: "name"; write: "setName"; index: 1; isFinal: true }
        Property { name: "mask"; type: "bool"; read: "isMask"; write: "setMask"; index: 2; isFinal: true }
    }
    Component {
        file: "private/qquickoverlay_p.h"
        name: "QQuickOverlay"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Templates/Overlay 2.3",
            "QtQuick.Templates/Overlay 2.4",
            "QtQuick.Templates/Overlay 2.7",
            "QtQuick.Templates/Overlay 2.11",
            "QtQuick.Templates/Overlay 6.0",
            "QtQuick.Templates/Overlay 6.3",
            "QtQuick.Templates/Overlay 6.7"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [515, 516, 519, 523, 1536, 1539, 1543]
        attachedType: "QQuickOverlayAttached"
        Property {
            name: "modal"
            type: "QQmlComponent"
            isPointer: true
            read: "modal"
            write: "setModal"
            notify: "modalChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "modeless"
            type: "QQmlComponent"
            isPointer: true
            read: "modeless"
            write: "setModeless"
            notify: "modelessChanged"
            index: 1
            isFinal: true
        }
        Signal { name: "modalChanged" }
        Signal { name: "modelessChanged" }
        Signal { name: "pressed" }
        Signal { name: "released" }
    }
    Component {
        file: "private/qquickoverlay_p.h"
        name: "QQuickOverlayAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "overlay"
            type: "QQuickOverlay"
            isPointer: true
            read: "overlay"
            notify: "overlayChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "modal"
            type: "QQmlComponent"
            isPointer: true
            read: "modal"
            write: "setModal"
            notify: "modalChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "modeless"
            type: "QQmlComponent"
            isPointer: true
            read: "modeless"
            write: "setModeless"
            notify: "modelessChanged"
            index: 2
            isFinal: true
        }
        Signal { name: "overlayChanged" }
        Signal { name: "modalChanged" }
        Signal { name: "modelessChanged" }
        Signal { name: "pressed" }
        Signal { name: "released" }
    }
    Component {
        file: "private/qquickpage_p.h"
        name: "QQuickPage"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickPane"
        exports: [
            "QtQuick.Templates/Page 2.0",
            "QtQuick.Templates/Page 2.1",
            "QtQuick.Templates/Page 2.4",
            "QtQuick.Templates/Page 2.5",
            "QtQuick.Templates/Page 2.7",
            "QtQuick.Templates/Page 2.11",
            "QtQuick.Templates/Page 6.0",
            "QtQuick.Templates/Page 6.3",
            "QtQuick.Templates/Page 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        Property {
            name: "title"
            type: "QString"
            read: "title"
            write: "setTitle"
            reset: "resetTitle"
            notify: "titleChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "header"
            type: "QQuickItem"
            isPointer: true
            read: "header"
            write: "setHeader"
            notify: "headerChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "footer"
            type: "QQuickItem"
            isPointer: true
            read: "footer"
            write: "setFooter"
            notify: "footerChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "implicitHeaderWidth"
            revision: 517
            type: "double"
            read: "implicitHeaderWidth"
            notify: "implicitHeaderWidthChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitHeaderHeight"
            revision: 517
            type: "double"
            read: "implicitHeaderHeight"
            notify: "implicitHeaderHeightChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitFooterWidth"
            revision: 517
            type: "double"
            read: "implicitFooterWidth"
            notify: "implicitFooterWidthChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitFooterHeight"
            revision: 517
            type: "double"
            read: "implicitFooterHeight"
            notify: "implicitFooterHeightChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Signal { name: "titleChanged" }
        Signal { name: "headerChanged" }
        Signal { name: "footerChanged" }
        Signal { name: "implicitHeaderWidthChanged" }
        Signal { name: "implicitHeaderHeightChanged" }
        Signal { name: "implicitFooterWidthChanged" }
        Signal { name: "implicitFooterHeightChanged" }
    }
    Component {
        file: "private/qquickpageindicator_p.h"
        name: "QQuickPageIndicator"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/PageIndicator 2.0",
            "QtQuick.Templates/PageIndicator 2.1",
            "QtQuick.Templates/PageIndicator 2.4",
            "QtQuick.Templates/PageIndicator 2.5",
            "QtQuick.Templates/PageIndicator 2.7",
            "QtQuick.Templates/PageIndicator 2.11",
            "QtQuick.Templates/PageIndicator 6.0",
            "QtQuick.Templates/PageIndicator 6.3",
            "QtQuick.Templates/PageIndicator 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        Property {
            name: "count"
            type: "int"
            read: "count"
            write: "setCount"
            notify: "countChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "currentIndex"
            type: "int"
            read: "currentIndex"
            write: "setCurrentIndex"
            notify: "currentIndexChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "interactive"
            type: "bool"
            read: "isInteractive"
            write: "setInteractive"
            notify: "interactiveChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 3
            isFinal: true
        }
        Signal { name: "countChanged" }
        Signal { name: "currentIndexChanged" }
        Signal { name: "interactiveChanged" }
        Signal { name: "delegateChanged" }
    }
    Component {
        file: "private/qquickpane_p.h"
        name: "QQuickPane"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/Pane 2.0",
            "QtQuick.Templates/Pane 2.1",
            "QtQuick.Templates/Pane 2.4",
            "QtQuick.Templates/Pane 2.5",
            "QtQuick.Templates/Pane 2.7",
            "QtQuick.Templates/Pane 2.11",
            "QtQuick.Templates/Pane 6.0",
            "QtQuick.Templates/Pane 6.3",
            "QtQuick.Templates/Pane 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        Property {
            name: "contentWidth"
            type: "double"
            read: "contentWidth"
            write: "setContentWidth"
            reset: "resetContentWidth"
            notify: "contentWidthChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "contentHeight"
            type: "double"
            read: "contentHeight"
            write: "setContentHeight"
            reset: "resetContentHeight"
            notify: "contentHeightChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "contentData"
            type: "QObject"
            isList: true
            read: "contentData"
            index: 2
            privateClass: "QQuickPanePrivate"
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "contentChildren"
            type: "QQuickItem"
            isList: true
            read: "contentChildren"
            notify: "contentChildrenChanged"
            index: 3
            privateClass: "QQuickPanePrivate"
            isReadonly: true
            isFinal: true
        }
        Signal { name: "contentWidthChanged" }
        Signal { name: "contentHeightChanged" }
        Signal { name: "contentChildrenChanged" }
    }
    Component {
        file: "private/qquickpopup_p.h"
        name: "QQuickPopup"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus", "QQuickSafeAreaAttachable"]
        deferredNames: ["background", "contentItem"]
        exports: [
            "QtQuick.Templates/Popup 2.0",
            "QtQuick.Templates/Popup 2.1",
            "QtQuick.Templates/Popup 2.3",
            "QtQuick.Templates/Popup 2.5",
            "QtQuick.Templates/Popup 6.0",
            "QtQuick.Templates/Popup 6.8"
        ]
        exportMetaObjectRevisions: [512, 513, 515, 517, 1536, 1544]
        Enum {
            name: "ClosePolicy"
            alias: "ClosePolicyFlag"
            isFlag: true
            values: [
                "NoAutoClose",
                "CloseOnPressOutside",
                "CloseOnPressOutsideParent",
                "CloseOnReleaseOutside",
                "CloseOnReleaseOutsideParent",
                "CloseOnEscape"
            ]
        }
        Enum {
            name: "TransformOrigin"
            values: [
                "TopLeft",
                "Top",
                "TopRight",
                "Left",
                "Center",
                "Right",
                "BottomLeft",
                "Bottom",
                "BottomRight"
            ]
        }
        Enum {
            name: "PopupType"
            values: ["Item", "Window", "Native"]
        }
        Property {
            name: "x"
            type: "double"
            read: "x"
            write: "setX"
            notify: "xChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "y"
            type: "double"
            read: "y"
            write: "setY"
            notify: "yChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "z"
            type: "double"
            read: "z"
            write: "setZ"
            reset: "resetZ"
            notify: "zChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "width"
            type: "double"
            read: "width"
            write: "setWidth"
            reset: "resetWidth"
            notify: "widthChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "height"
            type: "double"
            read: "height"
            write: "setHeight"
            reset: "resetHeight"
            notify: "heightChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "implicitWidth"
            type: "double"
            read: "implicitWidth"
            write: "setImplicitWidth"
            notify: "implicitWidthChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "implicitHeight"
            type: "double"
            read: "implicitHeight"
            write: "setImplicitHeight"
            notify: "implicitHeightChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "contentWidth"
            type: "double"
            read: "contentWidth"
            write: "setContentWidth"
            notify: "contentWidthChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "contentHeight"
            type: "double"
            read: "contentHeight"
            write: "setContentHeight"
            notify: "contentHeightChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "availableWidth"
            type: "double"
            read: "availableWidth"
            notify: "availableWidthChanged"
            index: 9
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "availableHeight"
            type: "double"
            read: "availableHeight"
            notify: "availableHeightChanged"
            index: 10
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "margins"
            type: "double"
            read: "margins"
            write: "setMargins"
            reset: "resetMargins"
            notify: "marginsChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "topMargin"
            type: "double"
            read: "topMargin"
            write: "setTopMargin"
            reset: "resetTopMargin"
            notify: "topMarginChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "leftMargin"
            type: "double"
            read: "leftMargin"
            write: "setLeftMargin"
            reset: "resetLeftMargin"
            notify: "leftMarginChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "rightMargin"
            type: "double"
            read: "rightMargin"
            write: "setRightMargin"
            reset: "resetRightMargin"
            notify: "rightMarginChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "bottomMargin"
            type: "double"
            read: "bottomMargin"
            write: "setBottomMargin"
            reset: "resetBottomMargin"
            notify: "bottomMarginChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "padding"
            type: "double"
            read: "padding"
            write: "setPadding"
            reset: "resetPadding"
            notify: "paddingChanged"
            index: 16
            isFinal: true
        }
        Property {
            name: "topPadding"
            type: "double"
            read: "topPadding"
            write: "setTopPadding"
            reset: "resetTopPadding"
            notify: "topPaddingChanged"
            index: 17
            isFinal: true
        }
        Property {
            name: "leftPadding"
            type: "double"
            read: "leftPadding"
            write: "setLeftPadding"
            reset: "resetLeftPadding"
            notify: "leftPaddingChanged"
            index: 18
            isFinal: true
        }
        Property {
            name: "rightPadding"
            type: "double"
            read: "rightPadding"
            write: "setRightPadding"
            reset: "resetRightPadding"
            notify: "rightPaddingChanged"
            index: 19
            isFinal: true
        }
        Property {
            name: "bottomPadding"
            type: "double"
            read: "bottomPadding"
            write: "setBottomPadding"
            reset: "resetBottomPadding"
            notify: "bottomPaddingChanged"
            index: 20
            isFinal: true
        }
        Property {
            name: "locale"
            type: "QLocale"
            read: "locale"
            write: "setLocale"
            reset: "resetLocale"
            notify: "localeChanged"
            index: 21
            isFinal: true
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            reset: "resetFont"
            notify: "fontChanged"
            index: 22
            isFinal: true
        }
        Property {
            name: "parent"
            type: "QQuickItem"
            isPointer: true
            read: "parentItem"
            write: "setParentItem"
            reset: "resetParentItem"
            notify: "parentChanged"
            index: 23
            isFinal: true
        }
        Property {
            name: "background"
            type: "QQuickItem"
            isPointer: true
            read: "background"
            write: "setBackground"
            notify: "backgroundChanged"
            index: 24
            isFinal: true
        }
        Property {
            name: "contentItem"
            type: "QQuickItem"
            isPointer: true
            read: "contentItem"
            write: "setContentItem"
            notify: "contentItemChanged"
            index: 25
            isFinal: true
        }
        Property {
            name: "contentData"
            type: "QObject"
            isList: true
            read: "contentData"
            index: 26
            privateClass: "QQuickPopupPrivate"
            isReadonly: true
        }
        Property {
            name: "contentChildren"
            type: "QQuickItem"
            isList: true
            read: "contentChildren"
            notify: "contentChildrenChanged"
            index: 27
            privateClass: "QQuickPopupPrivate"
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "clip"
            type: "bool"
            read: "clip"
            write: "setClip"
            notify: "clipChanged"
            index: 28
            isFinal: true
        }
        Property {
            name: "focus"
            type: "bool"
            read: "hasFocus"
            write: "setFocus"
            notify: "focusChanged"
            index: 29
            isFinal: true
        }
        Property {
            name: "activeFocus"
            type: "bool"
            read: "hasActiveFocus"
            notify: "activeFocusChanged"
            index: 30
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "modal"
            type: "bool"
            read: "isModal"
            write: "setModal"
            notify: "modalChanged"
            index: 31
            isFinal: true
        }
        Property {
            name: "dim"
            type: "bool"
            read: "dim"
            write: "setDim"
            reset: "resetDim"
            notify: "dimChanged"
            index: 32
            isFinal: true
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 33
            isFinal: true
        }
        Property {
            name: "opacity"
            type: "double"
            read: "opacity"
            write: "setOpacity"
            notify: "opacityChanged"
            index: 34
            isFinal: true
        }
        Property {
            name: "scale"
            type: "double"
            read: "scale"
            write: "setScale"
            notify: "scaleChanged"
            index: 35
            isFinal: true
        }
        Property {
            name: "closePolicy"
            type: "ClosePolicy"
            read: "closePolicy"
            write: "setClosePolicy"
            reset: "resetClosePolicy"
            notify: "closePolicyChanged"
            index: 36
            isFinal: true
        }
        Property {
            name: "transformOrigin"
            type: "TransformOrigin"
            read: "transformOrigin"
            write: "setTransformOrigin"
            index: 37
            isFinal: true
        }
        Property {
            name: "enter"
            type: "QQuickTransition"
            isPointer: true
            read: "enter"
            write: "setEnter"
            notify: "enterChanged"
            index: 38
            isFinal: true
        }
        Property {
            name: "exit"
            type: "QQuickTransition"
            isPointer: true
            read: "exit"
            write: "setExit"
            notify: "exitChanged"
            index: 39
            isFinal: true
        }
        Property {
            name: "spacing"
            revision: 513
            type: "double"
            read: "spacing"
            write: "setSpacing"
            reset: "resetSpacing"
            notify: "spacingChanged"
            index: 40
            isFinal: true
        }
        Property {
            name: "opened"
            revision: 515
            type: "bool"
            read: "isOpened"
            notify: "openedChanged"
            index: 41
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "mirrored"
            revision: 515
            type: "bool"
            read: "isMirrored"
            notify: "mirroredChanged"
            index: 42
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "enabled"
            revision: 515
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 43
            isFinal: true
        }
        Property {
            name: "palette"
            revision: 515
            type: "QQuickPalette"
            isPointer: true
            read: "palette"
            write: "setPalette"
            reset: "resetPalette"
            notify: "paletteChanged"
            index: 44
            privateClass: "QQuickPopupPrivate"
        }
        Property {
            name: "horizontalPadding"
            type: "double"
            read: "horizontalPadding"
            write: "setHorizontalPadding"
            reset: "resetHorizontalPadding"
            notify: "horizontalPaddingChanged"
            index: 45
            isFinal: true
        }
        Property {
            name: "verticalPadding"
            type: "double"
            read: "verticalPadding"
            write: "setVerticalPadding"
            reset: "resetVerticalPadding"
            notify: "verticalPaddingChanged"
            index: 46
            isFinal: true
        }
        Property {
            name: "anchors"
            revision: 517
            type: "QQuickPopupAnchors"
            isPointer: true
            read: "getAnchors"
            index: 47
            privateClass: "QQuickPopupPrivate"
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "implicitContentWidth"
            revision: 517
            type: "double"
            read: "implicitContentWidth"
            notify: "implicitContentWidthChanged"
            index: 48
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitContentHeight"
            revision: 517
            type: "double"
            read: "implicitContentHeight"
            notify: "implicitContentHeightChanged"
            index: 49
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitBackgroundWidth"
            revision: 517
            type: "double"
            read: "implicitBackgroundWidth"
            notify: "implicitBackgroundWidthChanged"
            index: 50
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitBackgroundHeight"
            revision: 517
            type: "double"
            read: "implicitBackgroundHeight"
            notify: "implicitBackgroundHeightChanged"
            index: 51
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "topInset"
            revision: 517
            type: "double"
            read: "topInset"
            write: "setTopInset"
            reset: "resetTopInset"
            notify: "topInsetChanged"
            index: 52
            isFinal: true
        }
        Property {
            name: "leftInset"
            revision: 517
            type: "double"
            read: "leftInset"
            write: "setLeftInset"
            reset: "resetLeftInset"
            notify: "leftInsetChanged"
            index: 53
            isFinal: true
        }
        Property {
            name: "rightInset"
            revision: 517
            type: "double"
            read: "rightInset"
            write: "setRightInset"
            reset: "resetRightInset"
            notify: "rightInsetChanged"
            index: 54
            isFinal: true
        }
        Property {
            name: "bottomInset"
            revision: 517
            type: "double"
            read: "bottomInset"
            write: "setBottomInset"
            reset: "resetBottomInset"
            notify: "bottomInsetChanged"
            index: 55
            isFinal: true
        }
        Property {
            name: "popupType"
            revision: 1544
            type: "PopupType"
            read: "popupType"
            write: "setPopupType"
            notify: "popupTypeChanged"
            index: 56
            isFinal: true
        }
        Signal { name: "opened" }
        Signal { name: "closed" }
        Signal { name: "aboutToShow" }
        Signal { name: "aboutToHide" }
        Signal { name: "xChanged" }
        Signal { name: "yChanged" }
        Signal { name: "zChanged" }
        Signal { name: "widthChanged" }
        Signal { name: "heightChanged" }
        Signal { name: "implicitWidthChanged" }
        Signal { name: "implicitHeightChanged" }
        Signal { name: "contentWidthChanged" }
        Signal { name: "contentHeightChanged" }
        Signal { name: "availableWidthChanged" }
        Signal { name: "availableHeightChanged" }
        Signal { name: "marginsChanged" }
        Signal { name: "topMarginChanged" }
        Signal { name: "leftMarginChanged" }
        Signal { name: "rightMarginChanged" }
        Signal { name: "bottomMarginChanged" }
        Signal { name: "paddingChanged" }
        Signal { name: "topPaddingChanged" }
        Signal { name: "leftPaddingChanged" }
        Signal { name: "rightPaddingChanged" }
        Signal { name: "bottomPaddingChanged" }
        Signal { name: "fontChanged" }
        Signal { name: "localeChanged" }
        Signal { name: "parentChanged" }
        Signal { name: "backgroundChanged" }
        Signal { name: "contentItemChanged" }
        Signal { name: "contentChildrenChanged" }
        Signal { name: "clipChanged" }
        Signal { name: "focusChanged" }
        Signal { name: "activeFocusChanged" }
        Signal { name: "modalChanged" }
        Signal { name: "dimChanged" }
        Signal { name: "visibleChanged" }
        Signal { name: "opacityChanged" }
        Signal { name: "scaleChanged" }
        Signal { name: "closePolicyChanged" }
        Signal { name: "enterChanged" }
        Signal { name: "exitChanged" }
        Signal {
            name: "windowChanged"
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
        Signal { name: "spacingChanged"; revision: 513 }
        Signal { name: "openedChanged"; revision: 515 }
        Signal { name: "mirroredChanged"; revision: 515 }
        Signal { name: "enabledChanged"; revision: 515 }
        Signal { name: "paletteChanged"; revision: 515 }
        Signal { name: "paletteCreated"; revision: 515 }
        Signal { name: "horizontalPaddingChanged"; revision: 517 }
        Signal { name: "verticalPaddingChanged"; revision: 517 }
        Signal { name: "implicitContentWidthChanged"; revision: 517 }
        Signal { name: "implicitContentHeightChanged"; revision: 517 }
        Signal { name: "implicitBackgroundWidthChanged"; revision: 517 }
        Signal { name: "implicitBackgroundHeightChanged"; revision: 517 }
        Signal { name: "topInsetChanged"; revision: 517 }
        Signal { name: "leftInsetChanged"; revision: 517 }
        Signal { name: "rightInsetChanged"; revision: 517 }
        Signal { name: "bottomInsetChanged"; revision: 517 }
        Signal { name: "popupTypeChanged"; revision: 1544 }
        Method { name: "open" }
        Method { name: "close" }
        Method {
            name: "forceActiveFocus"
            Parameter { name: "reason"; type: "Qt::FocusReason" }
        }
        Method { name: "forceActiveFocus"; isCloned: true }
    }
    Component {
        file: "private/qquickpopupanchors_p.h"
        name: "QQuickPopupAnchors"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "centerIn"
            type: "QQuickItem"
            isPointer: true
            read: "centerIn"
            write: "setCenterIn"
            reset: "resetCenterIn"
            notify: "centerInChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "centerInChanged" }
    }
    Component {
        file: "private/qquickpopupwindow_p_p.h"
        name: "QQuickPopupWindow"
        accessSemantics: "reference"
        prototype: "QQuickWindowQmlImpl"
    }
    Component {
        file: "private/qquickprogressbar_p.h"
        name: "QQuickProgressBar"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/ProgressBar 2.0",
            "QtQuick.Templates/ProgressBar 2.1",
            "QtQuick.Templates/ProgressBar 2.4",
            "QtQuick.Templates/ProgressBar 2.5",
            "QtQuick.Templates/ProgressBar 2.7",
            "QtQuick.Templates/ProgressBar 2.11",
            "QtQuick.Templates/ProgressBar 6.0",
            "QtQuick.Templates/ProgressBar 6.3",
            "QtQuick.Templates/ProgressBar 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        Property {
            name: "from"
            type: "double"
            read: "from"
            write: "setFrom"
            notify: "fromChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "to"
            type: "double"
            read: "to"
            write: "setTo"
            notify: "toChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "value"
            type: "double"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "position"
            type: "double"
            read: "position"
            notify: "positionChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "visualPosition"
            type: "double"
            read: "visualPosition"
            notify: "visualPositionChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "indeterminate"
            type: "bool"
            read: "isIndeterminate"
            write: "setIndeterminate"
            notify: "indeterminateChanged"
            index: 5
            isFinal: true
        }
        Signal { name: "fromChanged" }
        Signal { name: "toChanged" }
        Signal { name: "valueChanged" }
        Signal { name: "positionChanged" }
        Signal { name: "visualPositionChanged" }
        Signal { name: "indeterminateChanged" }
    }
    Component {
        file: "private/qquickradiobutton_p.h"
        name: "QQuickRadioButton"
        accessSemantics: "reference"
        prototype: "QQuickAbstractButton"
        exports: [
            "QtQuick.Templates/RadioButton 2.0",
            "QtQuick.Templates/RadioButton 2.1",
            "QtQuick.Templates/RadioButton 2.2",
            "QtQuick.Templates/RadioButton 2.3",
            "QtQuick.Templates/RadioButton 2.4",
            "QtQuick.Templates/RadioButton 2.5",
            "QtQuick.Templates/RadioButton 2.7",
            "QtQuick.Templates/RadioButton 2.11",
            "QtQuick.Templates/RadioButton 6.0",
            "QtQuick.Templates/RadioButton 6.3",
            "QtQuick.Templates/RadioButton 6.7",
            "QtQuick.Templates/RadioButton 6.8"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
    }
    Component {
        file: "private/qquickradiodelegate_p.h"
        name: "QQuickRadioDelegate"
        accessSemantics: "reference"
        prototype: "QQuickItemDelegate"
        exports: [
            "QtQuick.Templates/RadioDelegate 2.0",
            "QtQuick.Templates/RadioDelegate 2.1",
            "QtQuick.Templates/RadioDelegate 2.2",
            "QtQuick.Templates/RadioDelegate 2.3",
            "QtQuick.Templates/RadioDelegate 2.4",
            "QtQuick.Templates/RadioDelegate 2.5",
            "QtQuick.Templates/RadioDelegate 2.7",
            "QtQuick.Templates/RadioDelegate 2.11",
            "QtQuick.Templates/RadioDelegate 6.0",
            "QtQuick.Templates/RadioDelegate 6.3",
            "QtQuick.Templates/RadioDelegate 6.7",
            "QtQuick.Templates/RadioDelegate 6.8"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
    }
    Component {
        file: "private/qquickrangeslider_p.h"
        name: "QQuickRangeSlider"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/RangeSlider 2.0",
            "QtQuick.Templates/RangeSlider 2.1",
            "QtQuick.Templates/RangeSlider 2.2",
            "QtQuick.Templates/RangeSlider 2.3",
            "QtQuick.Templates/RangeSlider 2.4",
            "QtQuick.Templates/RangeSlider 2.5",
            "QtQuick.Templates/RangeSlider 2.7",
            "QtQuick.Templates/RangeSlider 2.11",
            "QtQuick.Templates/RangeSlider 6.0",
            "QtQuick.Templates/RangeSlider 6.3",
            "QtQuick.Templates/RangeSlider 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        Enum {
            name: "SnapMode"
            values: ["NoSnap", "SnapAlways", "SnapOnRelease"]
        }
        Property {
            name: "from"
            type: "double"
            read: "from"
            write: "setFrom"
            notify: "fromChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "to"
            type: "double"
            read: "to"
            write: "setTo"
            notify: "toChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "first"
            type: "QQuickRangeSliderNode"
            isPointer: true
            read: "first"
            index: 2
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "second"
            type: "QQuickRangeSliderNode"
            isPointer: true
            read: "second"
            index: 3
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "stepSize"
            type: "double"
            read: "stepSize"
            write: "setStepSize"
            notify: "stepSizeChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "snapMode"
            type: "SnapMode"
            read: "snapMode"
            write: "setSnapMode"
            notify: "snapModeChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "orientation"
            type: "Qt::Orientation"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "live"
            revision: 514
            type: "bool"
            read: "live"
            write: "setLive"
            notify: "liveChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "horizontal"
            revision: 515
            type: "bool"
            read: "isHorizontal"
            notify: "orientationChanged"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "vertical"
            revision: 515
            type: "bool"
            read: "isVertical"
            notify: "orientationChanged"
            index: 9
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "touchDragThreshold"
            revision: 517
            type: "double"
            read: "touchDragThreshold"
            write: "setTouchDragThreshold"
            reset: "resetTouchDragThreshold"
            notify: "touchDragThresholdChanged"
            index: 10
            isFinal: true
        }
        Signal { name: "fromChanged" }
        Signal { name: "toChanged" }
        Signal { name: "stepSizeChanged" }
        Signal { name: "snapModeChanged" }
        Signal { name: "orientationChanged" }
        Signal { name: "liveChanged"; revision: 514 }
        Signal { name: "touchDragThresholdChanged"; revision: 517 }
        Method {
            name: "setValues"
            Parameter { name: "firstValue"; type: "double" }
            Parameter { name: "secondValue"; type: "double" }
        }
        Method {
            name: "valueAt"
            revision: 517
            type: "double"
            isMethodConstant: true
            Parameter { name: "position"; type: "double" }
        }
    }
    Component {
        file: "private/qquickrangeslider_p.h"
        name: "QQuickRangeSliderNode"
        accessSemantics: "reference"
        prototype: "QObject"
        deferredNames: ["handle"]
        Property {
            name: "value"
            type: "double"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "position"
            type: "double"
            read: "position"
            notify: "positionChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "visualPosition"
            type: "double"
            read: "visualPosition"
            notify: "visualPositionChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "handle"
            type: "QQuickItem"
            isPointer: true
            read: "handle"
            write: "setHandle"
            notify: "handleChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "isPressed"
            write: "setPressed"
            notify: "pressedChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "hovered"
            revision: 513
            type: "bool"
            read: "isHovered"
            write: "setHovered"
            notify: "hoveredChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "implicitHandleWidth"
            revision: 517
            type: "double"
            read: "implicitHandleWidth"
            notify: "implicitHandleWidthChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitHandleHeight"
            revision: 517
            type: "double"
            read: "implicitHandleHeight"
            notify: "implicitHandleHeightChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Signal { name: "valueChanged" }
        Signal { name: "positionChanged" }
        Signal { name: "visualPositionChanged" }
        Signal { name: "handleChanged" }
        Signal { name: "pressedChanged" }
        Signal { name: "hoveredChanged"; revision: 513 }
        Signal { name: "moved" }
        Signal { name: "implicitHandleWidthChanged" }
        Signal { name: "implicitHandleHeightChanged" }
        Method { name: "increase" }
        Method { name: "decrease" }
    }
    Component {
        file: "private/qquickroundbutton_p.h"
        name: "QQuickRoundButton"
        accessSemantics: "reference"
        prototype: "QQuickButton"
        exports: [
            "QtQuick.Templates/RoundButton 2.1",
            "QtQuick.Templates/RoundButton 2.2",
            "QtQuick.Templates/RoundButton 2.3",
            "QtQuick.Templates/RoundButton 2.4",
            "QtQuick.Templates/RoundButton 2.5",
            "QtQuick.Templates/RoundButton 2.7",
            "QtQuick.Templates/RoundButton 2.11",
            "QtQuick.Templates/RoundButton 6.0",
            "QtQuick.Templates/RoundButton 6.3",
            "QtQuick.Templates/RoundButton 6.7",
            "QtQuick.Templates/RoundButton 6.8"
        ]
        exportMetaObjectRevisions: [
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
        Property {
            name: "radius"
            type: "double"
            read: "radius"
            write: "setRadius"
            reset: "resetRadius"
            notify: "radiusChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "radiusChanged" }
    }
    Component {
        file: "private/qquickscrollbar_p.h"
        name: "QQuickScrollBar"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/ScrollBar 2.0",
            "QtQuick.Templates/ScrollBar 2.1",
            "QtQuick.Templates/ScrollBar 2.2",
            "QtQuick.Templates/ScrollBar 2.3",
            "QtQuick.Templates/ScrollBar 2.4",
            "QtQuick.Templates/ScrollBar 2.5",
            "QtQuick.Templates/ScrollBar 2.7",
            "QtQuick.Templates/ScrollBar 2.11",
            "QtQuick.Templates/ScrollBar 6.0",
            "QtQuick.Templates/ScrollBar 6.3",
            "QtQuick.Templates/ScrollBar 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        attachedType: "QQuickScrollBarAttached"
        Enum {
            name: "SnapMode"
            values: ["NoSnap", "SnapAlways", "SnapOnRelease"]
        }
        Enum {
            name: "Policy"
            values: ["AsNeeded", "AlwaysOff", "AlwaysOn"]
        }
        Property {
            name: "size"
            type: "double"
            read: "size"
            write: "setSize"
            notify: "sizeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "position"
            type: "double"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "stepSize"
            type: "double"
            read: "stepSize"
            write: "setStepSize"
            notify: "stepSizeChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "active"
            type: "bool"
            read: "isActive"
            write: "setActive"
            notify: "activeChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "isPressed"
            write: "setPressed"
            notify: "pressedChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "orientation"
            type: "Qt::Orientation"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "snapMode"
            revision: 514
            type: "SnapMode"
            read: "snapMode"
            write: "setSnapMode"
            notify: "snapModeChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "interactive"
            revision: 514
            type: "bool"
            read: "isInteractive"
            write: "setInteractive"
            reset: "resetInteractive"
            notify: "interactiveChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "policy"
            revision: 514
            type: "Policy"
            read: "policy"
            write: "setPolicy"
            notify: "policyChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "horizontal"
            revision: 515
            type: "bool"
            read: "isHorizontal"
            notify: "orientationChanged"
            index: 9
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "vertical"
            revision: 515
            type: "bool"
            read: "isVertical"
            notify: "orientationChanged"
            index: 10
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "minimumSize"
            revision: 516
            type: "double"
            read: "minimumSize"
            write: "setMinimumSize"
            notify: "minimumSizeChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "visualSize"
            revision: 516
            type: "double"
            read: "visualSize"
            notify: "visualSizeChanged"
            index: 12
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "visualPosition"
            revision: 516
            type: "double"
            read: "visualPosition"
            notify: "visualPositionChanged"
            index: 13
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "__decreaseVisual"
            type: "QQuickIndicatorButton"
            isPointer: true
            read: "decreaseVisual"
            index: 14
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "__increaseVisual"
            type: "QQuickIndicatorButton"
            isPointer: true
            read: "increaseVisual"
            index: 15
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Signal { name: "sizeChanged" }
        Signal { name: "positionChanged" }
        Signal { name: "stepSizeChanged" }
        Signal { name: "activeChanged" }
        Signal { name: "pressedChanged" }
        Signal { name: "orientationChanged" }
        Signal { name: "snapModeChanged"; revision: 514 }
        Signal { name: "interactiveChanged"; revision: 514 }
        Signal { name: "policyChanged"; revision: 514 }
        Signal { name: "minimumSizeChanged"; revision: 516 }
        Signal { name: "visualSizeChanged"; revision: 516 }
        Signal { name: "visualPositionChanged"; revision: 516 }
        Method { name: "increase" }
        Method { name: "decrease" }
        Method {
            name: "setSize"
            Parameter { name: "size"; type: "double" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "double" }
        }
    }
    Component {
        file: "private/qquickscrollbar_p.h"
        name: "QQuickScrollBarAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "horizontal"
            type: "QQuickScrollBar"
            isPointer: true
            read: "horizontal"
            write: "setHorizontal"
            notify: "horizontalChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "vertical"
            type: "QQuickScrollBar"
            isPointer: true
            read: "vertical"
            write: "setVertical"
            notify: "verticalChanged"
            index: 1
            isFinal: true
        }
        Signal { name: "horizontalChanged" }
        Signal { name: "verticalChanged" }
    }
    Component {
        file: "private/qquickscrollindicator_p.h"
        name: "QQuickScrollIndicator"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/ScrollIndicator 2.0",
            "QtQuick.Templates/ScrollIndicator 2.1",
            "QtQuick.Templates/ScrollIndicator 2.3",
            "QtQuick.Templates/ScrollIndicator 2.4",
            "QtQuick.Templates/ScrollIndicator 2.5",
            "QtQuick.Templates/ScrollIndicator 2.7",
            "QtQuick.Templates/ScrollIndicator 2.11",
            "QtQuick.Templates/ScrollIndicator 6.0",
            "QtQuick.Templates/ScrollIndicator 6.3",
            "QtQuick.Templates/ScrollIndicator 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        attachedType: "QQuickScrollIndicatorAttached"
        Property {
            name: "size"
            type: "double"
            read: "size"
            write: "setSize"
            notify: "sizeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "position"
            type: "double"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "active"
            type: "bool"
            read: "isActive"
            write: "setActive"
            notify: "activeChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "orientation"
            type: "Qt::Orientation"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "horizontal"
            revision: 515
            type: "bool"
            read: "isHorizontal"
            notify: "orientationChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "vertical"
            revision: 515
            type: "bool"
            read: "isVertical"
            notify: "orientationChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "minimumSize"
            revision: 516
            type: "double"
            read: "minimumSize"
            write: "setMinimumSize"
            notify: "minimumSizeChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "visualSize"
            revision: 516
            type: "double"
            read: "visualSize"
            notify: "visualSizeChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "visualPosition"
            revision: 516
            type: "double"
            read: "visualPosition"
            notify: "visualPositionChanged"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Signal { name: "sizeChanged" }
        Signal { name: "positionChanged" }
        Signal { name: "activeChanged" }
        Signal { name: "orientationChanged" }
        Signal { name: "minimumSizeChanged"; revision: 516 }
        Signal { name: "visualSizeChanged"; revision: 516 }
        Signal { name: "visualPositionChanged"; revision: 516 }
        Method {
            name: "setSize"
            Parameter { name: "size"; type: "double" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "double" }
        }
    }
    Component {
        file: "private/qquickscrollindicator_p.h"
        name: "QQuickScrollIndicatorAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "horizontal"
            type: "QQuickScrollIndicator"
            isPointer: true
            read: "horizontal"
            write: "setHorizontal"
            notify: "horizontalChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "vertical"
            type: "QQuickScrollIndicator"
            isPointer: true
            read: "vertical"
            write: "setVertical"
            notify: "verticalChanged"
            index: 1
            isFinal: true
        }
        Signal { name: "horizontalChanged" }
        Signal { name: "verticalChanged" }
    }
    Component {
        file: "private/qquickscrollview_p.h"
        name: "QQuickScrollView"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickPane"
        exports: [
            "QtQuick.Templates/ScrollView 2.2",
            "QtQuick.Templates/ScrollView 2.4",
            "QtQuick.Templates/ScrollView 2.5",
            "QtQuick.Templates/ScrollView 2.7",
            "QtQuick.Templates/ScrollView 2.11",
            "QtQuick.Templates/ScrollView 6.0",
            "QtQuick.Templates/ScrollView 6.3",
            "QtQuick.Templates/ScrollView 6.6",
            "QtQuick.Templates/ScrollView 6.7"
        ]
        exportMetaObjectRevisions: [
            514,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1542,
            1543
        ]
        Property {
            name: "effectiveScrollBarWidth"
            revision: 1542
            type: "double"
            read: "effectiveScrollBarWidth"
            notify: "effectiveScrollBarWidthChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "effectiveScrollBarHeight"
            revision: 1542
            type: "double"
            read: "effectiveScrollBarHeight"
            notify: "effectiveScrollBarHeightChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Signal { name: "effectiveScrollBarWidthChanged"; revision: 1542 }
        Signal { name: "effectiveScrollBarHeightChanged"; revision: 1542 }
    }
    Component {
        file: "private/qquickselectionrectangle_p.h"
        name: "QQuickSelectionRectangle"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/SelectionRectangle 6.2",
            "QtQuick.Templates/SelectionRectangle 6.3",
            "QtQuick.Templates/SelectionRectangle 6.7"
        ]
        exportMetaObjectRevisions: [1538, 1539, 1543]
        attachedType: "QQuickSelectionRectangleAttached"
        Enum {
            name: "SelectionMode"
            values: ["Drag", "PressAndHold", "Auto"]
        }
        Property {
            name: "selectionMode"
            type: "SelectionMode"
            read: "selectionMode"
            write: "setSelectionMode"
            notify: "selectionModeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "target"
            type: "QQuickItem"
            isPointer: true
            read: "target"
            write: "setTarget"
            notify: "targetChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "topLeftHandle"
            type: "QQmlComponent"
            isPointer: true
            read: "topLeftHandle"
            write: "setTopLeftHandle"
            notify: "topLeftHandleChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "bottomRightHandle"
            type: "QQmlComponent"
            isPointer: true
            read: "bottomRightHandle"
            write: "setBottomRightHandle"
            notify: "bottomRightHandleChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "active"
            type: "bool"
            read: "active"
            notify: "activeChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "dragging"
            type: "bool"
            read: "dragging"
            notify: "draggingChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Signal { name: "targetChanged" }
        Signal { name: "activeChanged" }
        Signal { name: "draggingChanged" }
        Signal { name: "topLeftHandleChanged" }
        Signal { name: "bottomRightHandleChanged" }
        Signal { name: "selectionModeChanged" }
    }
    Component {
        file: "private/qquickselectionrectangle_p.h"
        name: "QQuickSelectionRectangleAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "control"
            type: "QQuickSelectionRectangle"
            isPointer: true
            read: "control"
            notify: "controlChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "dragging"
            type: "bool"
            read: "dragging"
            notify: "draggingChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Signal { name: "controlChanged" }
        Signal { name: "draggingChanged" }
    }
    Component {
        file: "private/qquickslider_p.h"
        name: "QQuickSlider"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        deferredNames: ["background", "handle"]
        exports: [
            "QtQuick.Templates/Slider 2.0",
            "QtQuick.Templates/Slider 2.1",
            "QtQuick.Templates/Slider 2.2",
            "QtQuick.Templates/Slider 2.3",
            "QtQuick.Templates/Slider 2.4",
            "QtQuick.Templates/Slider 2.5",
            "QtQuick.Templates/Slider 2.7",
            "QtQuick.Templates/Slider 2.11",
            "QtQuick.Templates/Slider 6.0",
            "QtQuick.Templates/Slider 6.3",
            "QtQuick.Templates/Slider 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        Enum {
            name: "SnapMode"
            values: ["NoSnap", "SnapAlways", "SnapOnRelease"]
        }
        Property {
            name: "from"
            type: "double"
            read: "from"
            write: "setFrom"
            notify: "fromChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "to"
            type: "double"
            read: "to"
            write: "setTo"
            notify: "toChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "value"
            type: "double"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "position"
            type: "double"
            read: "position"
            notify: "positionChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "visualPosition"
            type: "double"
            read: "visualPosition"
            notify: "visualPositionChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "stepSize"
            type: "double"
            read: "stepSize"
            write: "setStepSize"
            notify: "stepSizeChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "snapMode"
            type: "SnapMode"
            read: "snapMode"
            write: "setSnapMode"
            notify: "snapModeChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "isPressed"
            write: "setPressed"
            notify: "pressedChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "orientation"
            type: "Qt::Orientation"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "handle"
            type: "QQuickItem"
            isPointer: true
            read: "handle"
            write: "setHandle"
            notify: "handleChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "live"
            revision: 514
            type: "bool"
            read: "live"
            write: "setLive"
            notify: "liveChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "horizontal"
            revision: 515
            type: "bool"
            read: "isHorizontal"
            notify: "orientationChanged"
            index: 11
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "vertical"
            revision: 515
            type: "bool"
            read: "isVertical"
            notify: "orientationChanged"
            index: 12
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "touchDragThreshold"
            revision: 517
            type: "double"
            read: "touchDragThreshold"
            write: "setTouchDragThreshold"
            reset: "resetTouchDragThreshold"
            notify: "touchDragThresholdChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "implicitHandleWidth"
            revision: 517
            type: "double"
            read: "implicitHandleWidth"
            notify: "implicitHandleWidthChanged"
            index: 14
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitHandleHeight"
            revision: 517
            type: "double"
            read: "implicitHandleHeight"
            notify: "implicitHandleHeightChanged"
            index: 15
            isReadonly: true
            isFinal: true
        }
        Signal { name: "fromChanged" }
        Signal { name: "toChanged" }
        Signal { name: "valueChanged" }
        Signal { name: "positionChanged" }
        Signal { name: "visualPositionChanged" }
        Signal { name: "stepSizeChanged" }
        Signal { name: "snapModeChanged" }
        Signal { name: "pressedChanged" }
        Signal { name: "orientationChanged" }
        Signal { name: "handleChanged" }
        Signal { name: "moved"; revision: 514 }
        Signal { name: "liveChanged"; revision: 514 }
        Signal { name: "touchDragThresholdChanged"; revision: 517 }
        Signal { name: "implicitHandleWidthChanged"; revision: 517 }
        Signal { name: "implicitHandleHeightChanged"; revision: 517 }
        Method { name: "increase" }
        Method { name: "decrease" }
        Method {
            name: "valueAt"
            revision: 513
            type: "double"
            isMethodConstant: true
            Parameter { name: "position"; type: "double" }
        }
    }
    Component {
        file: "private/qquickspinbox_p.h"
        name: "QQuickSpinBox"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/SpinBox 2.0",
            "QtQuick.Templates/SpinBox 2.1",
            "QtQuick.Templates/SpinBox 2.2",
            "QtQuick.Templates/SpinBox 2.3",
            "QtQuick.Templates/SpinBox 2.4",
            "QtQuick.Templates/SpinBox 2.5",
            "QtQuick.Templates/SpinBox 2.7",
            "QtQuick.Templates/SpinBox 2.11",
            "QtQuick.Templates/SpinBox 6.0",
            "QtQuick.Templates/SpinBox 6.3",
            "QtQuick.Templates/SpinBox 6.6",
            "QtQuick.Templates/SpinBox 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1542,
            1543
        ]
        Property {
            name: "from"
            type: "int"
            read: "from"
            write: "setFrom"
            notify: "fromChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "to"
            type: "int"
            read: "to"
            write: "setTo"
            notify: "toChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "value"
            type: "int"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "stepSize"
            type: "int"
            read: "stepSize"
            write: "setStepSize"
            notify: "stepSizeChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "editable"
            type: "bool"
            read: "isEditable"
            write: "setEditable"
            notify: "editableChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "live"
            revision: 1542
            type: "bool"
            read: "isLive"
            write: "setLive"
            notify: "liveChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "validator"
            type: "QValidator"
            isPointer: true
            read: "validator"
            write: "setValidator"
            notify: "validatorChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "textFromValue"
            type: "QJSValue"
            read: "textFromValue"
            write: "setTextFromValue"
            notify: "textFromValueChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "valueFromText"
            type: "QJSValue"
            read: "valueFromText"
            write: "setValueFromText"
            notify: "valueFromTextChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "up"
            type: "QQuickIndicatorButton"
            isPointer: true
            read: "up"
            index: 9
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "down"
            type: "QQuickIndicatorButton"
            isPointer: true
            read: "down"
            index: 10
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "inputMethodHints"
            revision: 514
            type: "Qt::InputMethodHints"
            read: "inputMethodHints"
            write: "setInputMethodHints"
            notify: "inputMethodHintsChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "inputMethodComposing"
            revision: 514
            type: "bool"
            read: "isInputMethodComposing"
            notify: "inputMethodComposingChanged"
            index: 12
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "wrap"
            revision: 515
            type: "bool"
            read: "wrap"
            write: "setWrap"
            notify: "wrapChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "displayText"
            revision: 516
            type: "QString"
            read: "displayText"
            notify: "displayTextChanged"
            index: 14
            isReadonly: true
            isFinal: true
        }
        Signal { name: "fromChanged" }
        Signal { name: "toChanged" }
        Signal { name: "valueChanged" }
        Signal { name: "stepSizeChanged" }
        Signal { name: "editableChanged" }
        Signal { name: "liveChanged"; revision: 1542 }
        Signal { name: "validatorChanged" }
        Signal { name: "textFromValueChanged" }
        Signal { name: "valueFromTextChanged" }
        Signal { name: "valueModified"; revision: 514 }
        Signal { name: "inputMethodHintsChanged"; revision: 514 }
        Signal { name: "inputMethodComposingChanged"; revision: 514 }
        Signal { name: "wrapChanged"; revision: 515 }
        Signal { name: "displayTextChanged"; revision: 516 }
        Method { name: "increase" }
        Method { name: "decrease" }
    }
    Component {
        file: "private/qquicksplitview_p.h"
        name: "QQuickSplitHandleAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick.Templates/SplitHandle 2.13",
            "QtQuick.Templates/SplitHandle 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [525, 1536]
        attachedType: "QQuickSplitHandleAttached"
        Property {
            name: "hovered"
            type: "bool"
            read: "isHovered"
            notify: "hoveredChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "isPressed"
            notify: "pressedChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Signal { name: "hoveredChanged" }
        Signal { name: "pressedChanged" }
    }
    Component {
        file: "private/qquicksplitview_p.h"
        name: "QQuickSplitView"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickContainer"
        exports: [
            "QtQuick.Templates/SplitView 2.13",
            "QtQuick.Templates/SplitView 6.0",
            "QtQuick.Templates/SplitView 6.3",
            "QtQuick.Templates/SplitView 6.7"
        ]
        exportMetaObjectRevisions: [525, 1536, 1539, 1543]
        attachedType: "QQuickSplitViewAttached"
        Property {
            name: "orientation"
            type: "Qt::Orientation"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "resizing"
            type: "bool"
            read: "isResizing"
            notify: "resizingChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "handle"
            type: "QQmlComponent"
            isPointer: true
            read: "handle"
            write: "setHandle"
            notify: "handleChanged"
            index: 2
            isFinal: true
        }
        Signal { name: "orientationChanged" }
        Signal { name: "resizingChanged" }
        Signal { name: "handleChanged" }
        Method { name: "saveState"; type: "QVariant" }
        Method {
            name: "restoreState"
            type: "bool"
            Parameter { name: "state"; type: "QVariant" }
        }
    }
    Component {
        file: "private/qquicksplitview_p.h"
        name: "QQuickSplitViewAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "view"
            type: "QQuickSplitView"
            isPointer: true
            read: "view"
            notify: "viewChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "minimumWidth"
            type: "double"
            read: "minimumWidth"
            write: "setMinimumWidth"
            reset: "resetMinimumWidth"
            notify: "minimumWidthChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "minimumHeight"
            type: "double"
            read: "minimumHeight"
            write: "setMinimumHeight"
            reset: "resetMinimumHeight"
            notify: "minimumHeightChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "preferredWidth"
            type: "double"
            read: "preferredWidth"
            write: "setPreferredWidth"
            reset: "resetPreferredWidth"
            notify: "preferredWidthChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "preferredHeight"
            type: "double"
            read: "preferredHeight"
            write: "setPreferredHeight"
            reset: "resetPreferredHeight"
            notify: "preferredHeightChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "maximumWidth"
            type: "double"
            read: "maximumWidth"
            write: "setMaximumWidth"
            reset: "resetMaximumWidth"
            notify: "maximumWidthChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "maximumHeight"
            type: "double"
            read: "maximumHeight"
            write: "setMaximumHeight"
            reset: "resetMaximumHeight"
            notify: "maximumHeightChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "fillHeight"
            type: "bool"
            read: "fillHeight"
            write: "setFillHeight"
            notify: "fillHeightChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "fillWidth"
            type: "bool"
            read: "fillWidth"
            write: "setFillWidth"
            notify: "fillWidthChanged"
            index: 8
            isFinal: true
        }
        Signal { name: "viewChanged" }
        Signal { name: "minimumWidthChanged" }
        Signal { name: "minimumHeightChanged" }
        Signal { name: "preferredWidthChanged" }
        Signal { name: "preferredHeightChanged" }
        Signal { name: "maximumWidthChanged" }
        Signal { name: "maximumHeightChanged" }
        Signal { name: "fillWidthChanged" }
        Signal { name: "fillHeightChanged" }
    }
    Component {
        file: "private/qquickstackview_p.h"
        name: "QQuickStackView"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/StackView 2.0",
            "QtQuick.Templates/StackView 2.1",
            "QtQuick.Templates/StackView 2.3",
            "QtQuick.Templates/StackView 2.4",
            "QtQuick.Templates/StackView 2.5",
            "QtQuick.Templates/StackView 2.7",
            "QtQuick.Templates/StackView 2.11",
            "QtQuick.Templates/StackView 6.0",
            "QtQuick.Templates/StackView 6.3",
            "QtQuick.Templates/StackView 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        attachedType: "QQuickStackViewAttached"
        Enum {
            name: "Status"
            values: ["Inactive", "Deactivating", "Activating", "Active"]
        }
        Enum {
            name: "LoadBehavior"
            values: ["DontLoad", "ForceLoad"]
        }
        Enum {
            name: "Operation"
            values: [
                "Transition",
                "Immediate",
                "PushTransition",
                "ReplaceTransition",
                "PopTransition"
            ]
        }
        Property {
            name: "busy"
            type: "bool"
            read: "isBusy"
            notify: "busyChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "depth"
            type: "int"
            read: "depth"
            notify: "depthChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "currentItem"
            type: "QQuickItem"
            isPointer: true
            read: "currentItem"
            notify: "currentItemChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "initialItem"
            type: "QJSValue"
            read: "initialItem"
            write: "setInitialItem"
            index: 3
            isFinal: true
        }
        Property {
            name: "popEnter"
            type: "QQuickTransition"
            isPointer: true
            read: "popEnter"
            write: "setPopEnter"
            notify: "popEnterChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "popExit"
            type: "QQuickTransition"
            isPointer: true
            read: "popExit"
            write: "setPopExit"
            notify: "popExitChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "pushEnter"
            type: "QQuickTransition"
            isPointer: true
            read: "pushEnter"
            write: "setPushEnter"
            notify: "pushEnterChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "pushExit"
            type: "QQuickTransition"
            isPointer: true
            read: "pushExit"
            write: "setPushExit"
            notify: "pushExitChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "replaceEnter"
            type: "QQuickTransition"
            isPointer: true
            read: "replaceEnter"
            write: "setReplaceEnter"
            notify: "replaceEnterChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "replaceExit"
            type: "QQuickTransition"
            isPointer: true
            read: "replaceExit"
            write: "setReplaceExit"
            notify: "replaceExitChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "empty"
            revision: 515
            type: "bool"
            read: "isEmpty"
            notify: "emptyChanged"
            index: 10
            isReadonly: true
            isFinal: true
        }
        Signal { name: "busyChanged" }
        Signal { name: "depthChanged" }
        Signal { name: "currentItemChanged" }
        Signal { name: "popEnterChanged" }
        Signal { name: "popExitChanged" }
        Signal { name: "pushEnterChanged" }
        Signal { name: "pushExitChanged" }
        Signal { name: "replaceEnterChanged" }
        Signal { name: "replaceExitChanged" }
        Signal { name: "emptyChanged"; revision: 515 }
        Method {
            name: "clear"
            Parameter { name: "operation"; type: "Operation" }
        }
        Method { name: "clear"; isCloned: true }
        Method {
            name: "get"
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "index"; type: "int" }
            Parameter { name: "behavior"; type: "QQuickStackView::LoadBehavior" }
        }
        Method {
            name: "get"
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "find"
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "callback"; type: "QJSValue" }
            Parameter { name: "behavior"; type: "QQuickStackView::LoadBehavior" }
        }
        Method {
            name: "find"
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method { name: "push"; isJavaScriptFunction: true }
        Method { name: "pop"; isJavaScriptFunction: true }
        Method { name: "replace"; isJavaScriptFunction: true }
        Method {
            name: "pushItems"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "args"; type: "QQuickStackViewArg"; isList: true }
            Parameter { name: "operation"; type: "Operation" }
        }
        Method {
            name: "pushItems"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "args"; type: "QQuickStackViewArg"; isList: true }
        }
        Method {
            name: "pushItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "properties"; type: "QVariantMap" }
            Parameter { name: "operation"; type: "Operation" }
        }
        Method {
            name: "pushItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "properties"; type: "QVariantMap" }
        }
        Method {
            name: "pushItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "pushItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "component"; type: "QQmlComponent"; isPointer: true }
            Parameter { name: "properties"; type: "QVariantMap" }
            Parameter { name: "operation"; type: "Operation" }
        }
        Method {
            name: "pushItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "component"; type: "QQmlComponent"; isPointer: true }
            Parameter { name: "properties"; type: "QVariantMap" }
        }
        Method {
            name: "pushItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "component"; type: "QQmlComponent"; isPointer: true }
        }
        Method {
            name: "pushItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "url"; type: "QUrl" }
            Parameter { name: "properties"; type: "QVariantMap" }
            Parameter { name: "operation"; type: "Operation" }
        }
        Method {
            name: "pushItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "url"; type: "QUrl" }
            Parameter { name: "properties"; type: "QVariantMap" }
        }
        Method {
            name: "pushItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "popToItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "operation"; type: "Operation" }
        }
        Method {
            name: "popToItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "popToIndex"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "index"; type: "int" }
            Parameter { name: "operation"; type: "Operation" }
        }
        Method {
            name: "popToIndex"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "popCurrentItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "operation"; type: "Operation" }
        }
        Method {
            name: "popCurrentItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
        }
        Method {
            name: "replaceCurrentItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "args"; type: "QQuickStackViewArg"; isList: true }
            Parameter { name: "operation"; type: "Operation" }
        }
        Method {
            name: "replaceCurrentItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "args"; type: "QQuickStackViewArg"; isList: true }
        }
        Method {
            name: "replaceCurrentItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "properties"; type: "QVariantMap" }
            Parameter { name: "operation"; type: "Operation" }
        }
        Method {
            name: "replaceCurrentItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "properties"; type: "QVariantMap" }
        }
        Method {
            name: "replaceCurrentItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "replaceCurrentItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "component"; type: "QQmlComponent"; isPointer: true }
            Parameter { name: "properties"; type: "QVariantMap" }
            Parameter { name: "operation"; type: "Operation" }
        }
        Method {
            name: "replaceCurrentItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "component"; type: "QQmlComponent"; isPointer: true }
            Parameter { name: "properties"; type: "QVariantMap" }
        }
        Method {
            name: "replaceCurrentItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "component"; type: "QQmlComponent"; isPointer: true }
        }
        Method {
            name: "replaceCurrentItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            Parameter { name: "url"; type: "QUrl" }
            Parameter { name: "properties"; type: "QVariantMap" }
            Parameter { name: "operation"; type: "Operation" }
        }
        Method {
            name: "replaceCurrentItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "url"; type: "QUrl" }
            Parameter { name: "properties"; type: "QVariantMap" }
        }
        Method {
            name: "replaceCurrentItem"
            revision: 1543
            type: "QQuickItem"
            isPointer: true
            isCloned: true
            Parameter { name: "url"; type: "QUrl" }
        }
    }
    Component {
        file: "private/qquickstackview_p.h"
        name: "QQuickStackViewArg"
        accessSemantics: "value"
        Method {
            name: "QQuickStackViewArg"
            isConstructor: true
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "QQuickStackViewArg"
            isConstructor: true
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "QQuickStackViewArg"
            isConstructor: true
            Parameter { name: "component"; type: "QQmlComponent"; isPointer: true }
        }
        Method {
            name: "QQuickStackViewArg"
            isConstructor: true
            Parameter { name: "properties"; type: "QVariantMap" }
        }
    }
    Component {
        file: "private/qquickstackview_p.h"
        name: "QQuickStackViewAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "index"
            type: "int"
            read: "index"
            notify: "indexChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "view"
            type: "QQuickStackView"
            isPointer: true
            read: "view"
            notify: "viewChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "status"
            type: "QQuickStackView::Status"
            read: "status"
            notify: "statusChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            reset: "resetVisible"
            notify: "visibleChanged"
            index: 3
            isFinal: true
        }
        Signal { name: "indexChanged" }
        Signal { name: "viewChanged" }
        Signal { name: "statusChanged" }
        Signal { name: "activated" }
        Signal { name: "activating" }
        Signal { name: "deactivated" }
        Signal { name: "deactivating" }
        Signal { name: "removed" }
        Signal { name: "visibleChanged" }
    }
    Component {
        file: "private/qquickswipe_p.h"
        name: "QQuickSwipe"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "position"
            type: "double"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "complete"
            type: "bool"
            read: "isComplete"
            notify: "completeChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "left"
            type: "QQmlComponent"
            isPointer: true
            read: "left"
            write: "setLeft"
            notify: "leftChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "behind"
            type: "QQmlComponent"
            isPointer: true
            read: "behind"
            write: "setBehind"
            notify: "behindChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "right"
            type: "QQmlComponent"
            isPointer: true
            read: "right"
            write: "setRight"
            notify: "rightChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "leftItem"
            type: "QQuickItem"
            isPointer: true
            read: "leftItem"
            notify: "leftItemChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "behindItem"
            type: "QQuickItem"
            isPointer: true
            read: "behindItem"
            notify: "behindItemChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "rightItem"
            type: "QQuickItem"
            isPointer: true
            read: "rightItem"
            notify: "rightItemChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "transition"
            type: "QQuickTransition"
            isPointer: true
            read: "transition"
            write: "setTransition"
            notify: "transitionChanged"
            index: 9
            isFinal: true
        }
        Signal { name: "positionChanged" }
        Signal { name: "completeChanged" }
        Signal { name: "leftChanged" }
        Signal { name: "behindChanged" }
        Signal { name: "rightChanged" }
        Signal { name: "leftItemChanged" }
        Signal { name: "behindItemChanged" }
        Signal { name: "rightItemChanged" }
        Signal { name: "completed" }
        Signal { name: "opened" }
        Signal { name: "closed" }
        Signal { name: "enabledChanged" }
        Signal { name: "transitionChanged" }
        Method { name: "close"; revision: 513 }
        Method {
            name: "open"
            revision: 514
            Parameter { name: "side"; type: "QQuickSwipeDelegate::Side" }
        }
    }
    Component {
        file: "private/qquickswipedelegate_p.h"
        name: "QQuickSwipeDelegate"
        accessSemantics: "reference"
        prototype: "QQuickItemDelegate"
        exports: [
            "QtQuick.Templates/SwipeDelegate 2.0",
            "QtQuick.Templates/SwipeDelegate 2.1",
            "QtQuick.Templates/SwipeDelegate 2.2",
            "QtQuick.Templates/SwipeDelegate 2.3",
            "QtQuick.Templates/SwipeDelegate 2.4",
            "QtQuick.Templates/SwipeDelegate 2.5",
            "QtQuick.Templates/SwipeDelegate 2.7",
            "QtQuick.Templates/SwipeDelegate 2.11",
            "QtQuick.Templates/SwipeDelegate 6.0",
            "QtQuick.Templates/SwipeDelegate 6.3",
            "QtQuick.Templates/SwipeDelegate 6.7",
            "QtQuick.Templates/SwipeDelegate 6.8"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
        attachedType: "QQuickSwipeDelegateAttached"
        Enum {
            name: "Side"
            values: ["Left", "Right"]
        }
        Property {
            name: "swipe"
            type: "QQuickSwipe"
            isPointer: true
            read: "swipe"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qquickswipedelegate_p.h"
        name: "QQuickSwipeDelegateAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "pressed"
            type: "bool"
            read: "isPressed"
            notify: "pressedChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Signal { name: "pressedChanged" }
        Signal { name: "clicked" }
    }
    Component {
        file: "private/qquickswipeview_p.h"
        name: "QQuickSwipeView"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickContainer"
        exports: [
            "QtQuick.Templates/SwipeView 2.0",
            "QtQuick.Templates/SwipeView 2.1",
            "QtQuick.Templates/SwipeView 2.2",
            "QtQuick.Templates/SwipeView 2.3",
            "QtQuick.Templates/SwipeView 2.4",
            "QtQuick.Templates/SwipeView 2.5",
            "QtQuick.Templates/SwipeView 2.7",
            "QtQuick.Templates/SwipeView 2.11",
            "QtQuick.Templates/SwipeView 6.0",
            "QtQuick.Templates/SwipeView 6.3",
            "QtQuick.Templates/SwipeView 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        attachedType: "QQuickSwipeViewAttached"
        Property {
            name: "interactive"
            revision: 513
            type: "bool"
            read: "isInteractive"
            write: "setInteractive"
            notify: "interactiveChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "orientation"
            revision: 514
            type: "Qt::Orientation"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "horizontal"
            revision: 515
            type: "bool"
            read: "isHorizontal"
            notify: "orientationChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "vertical"
            revision: 515
            type: "bool"
            read: "isVertical"
            notify: "orientationChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Signal { name: "interactiveChanged"; revision: 513 }
        Signal { name: "orientationChanged"; revision: 514 }
    }
    Component {
        file: "private/qquickswipeview_p.h"
        name: "QQuickSwipeViewAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "index"
            type: "int"
            read: "index"
            notify: "indexChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "isCurrentItem"
            type: "bool"
            read: "isCurrentItem"
            notify: "isCurrentItemChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "view"
            type: "QQuickSwipeView"
            isPointer: true
            read: "view"
            notify: "viewChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "isNextItem"
            revision: 513
            type: "bool"
            read: "isNextItem"
            notify: "isNextItemChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "isPreviousItem"
            revision: 513
            type: "bool"
            read: "isPreviousItem"
            notify: "isPreviousItemChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Signal { name: "indexChanged" }
        Signal { name: "isCurrentItemChanged" }
        Signal { name: "viewChanged" }
        Signal { name: "isNextItemChanged" }
        Signal { name: "isPreviousItemChanged" }
    }
    Component {
        file: "private/qquickswitch_p.h"
        name: "QQuickSwitch"
        accessSemantics: "reference"
        prototype: "QQuickAbstractButton"
        exports: [
            "QtQuick.Templates/Switch 2.0",
            "QtQuick.Templates/Switch 2.1",
            "QtQuick.Templates/Switch 2.2",
            "QtQuick.Templates/Switch 2.3",
            "QtQuick.Templates/Switch 2.4",
            "QtQuick.Templates/Switch 2.5",
            "QtQuick.Templates/Switch 2.7",
            "QtQuick.Templates/Switch 2.11",
            "QtQuick.Templates/Switch 6.0",
            "QtQuick.Templates/Switch 6.3",
            "QtQuick.Templates/Switch 6.7",
            "QtQuick.Templates/Switch 6.8"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
        Property {
            name: "position"
            type: "double"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "visualPosition"
            type: "double"
            read: "visualPosition"
            notify: "visualPositionChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Signal { name: "positionChanged" }
        Signal { name: "visualPositionChanged" }
    }
    Component {
        file: "private/qquickswitchdelegate_p.h"
        name: "QQuickSwitchDelegate"
        accessSemantics: "reference"
        prototype: "QQuickItemDelegate"
        exports: [
            "QtQuick.Templates/SwitchDelegate 2.0",
            "QtQuick.Templates/SwitchDelegate 2.1",
            "QtQuick.Templates/SwitchDelegate 2.2",
            "QtQuick.Templates/SwitchDelegate 2.3",
            "QtQuick.Templates/SwitchDelegate 2.4",
            "QtQuick.Templates/SwitchDelegate 2.5",
            "QtQuick.Templates/SwitchDelegate 2.7",
            "QtQuick.Templates/SwitchDelegate 2.11",
            "QtQuick.Templates/SwitchDelegate 6.0",
            "QtQuick.Templates/SwitchDelegate 6.3",
            "QtQuick.Templates/SwitchDelegate 6.7",
            "QtQuick.Templates/SwitchDelegate 6.8"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
        Property {
            name: "position"
            type: "double"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "visualPosition"
            type: "double"
            read: "visualPosition"
            notify: "visualPositionChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Signal { name: "positionChanged" }
        Signal { name: "visualPositionChanged" }
    }
    Component {
        file: "private/qquicktabbar_p.h"
        name: "QQuickTabBar"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickContainer"
        exports: [
            "QtQuick.Templates/TabBar 2.0",
            "QtQuick.Templates/TabBar 2.1",
            "QtQuick.Templates/TabBar 2.3",
            "QtQuick.Templates/TabBar 2.4",
            "QtQuick.Templates/TabBar 2.5",
            "QtQuick.Templates/TabBar 2.7",
            "QtQuick.Templates/TabBar 2.11",
            "QtQuick.Templates/TabBar 6.0",
            "QtQuick.Templates/TabBar 6.3",
            "QtQuick.Templates/TabBar 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        attachedType: "QQuickTabBarAttached"
        Enum {
            name: "Position"
            values: ["Header", "Footer"]
        }
        Property {
            name: "position"
            type: "Position"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "positionChanged" }
    }
    Component {
        file: "private/qquicktabbar_p.h"
        name: "QQuickTabBarAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "index"
            type: "int"
            read: "index"
            notify: "indexChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "tabBar"
            type: "QQuickTabBar"
            isPointer: true
            read: "tabBar"
            notify: "tabBarChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "position"
            type: "QQuickTabBar::Position"
            read: "position"
            notify: "positionChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Signal { name: "indexChanged" }
        Signal { name: "tabBarChanged" }
        Signal { name: "positionChanged" }
    }
    Component {
        file: "private/qquicktabbutton_p.h"
        name: "QQuickTabButton"
        accessSemantics: "reference"
        prototype: "QQuickAbstractButton"
        exports: [
            "QtQuick.Templates/TabButton 2.0",
            "QtQuick.Templates/TabButton 2.1",
            "QtQuick.Templates/TabButton 2.2",
            "QtQuick.Templates/TabButton 2.3",
            "QtQuick.Templates/TabButton 2.4",
            "QtQuick.Templates/TabButton 2.5",
            "QtQuick.Templates/TabButton 2.7",
            "QtQuick.Templates/TabButton 2.11",
            "QtQuick.Templates/TabButton 6.0",
            "QtQuick.Templates/TabButton 6.3",
            "QtQuick.Templates/TabButton 6.7",
            "QtQuick.Templates/TabButton 6.8"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
    }
    Component {
        file: "private/qquicktableviewdelegate_p.h"
        name: "QQuickTableViewDelegate"
        accessSemantics: "reference"
        prototype: "QQuickItemDelegate"
        exports: ["QtQuick.Templates/TableViewDelegate 6.9"]
        exportMetaObjectRevisions: [1545]
        Property {
            name: "tableView"
            type: "QQuickTableView"
            isPointer: true
            read: "tableView"
            write: "setTableView"
            notify: "tableViewChanged"
            index: 0
            isFinal: true
            isRequired: true
        }
        Property {
            name: "current"
            type: "bool"
            read: "current"
            write: "setCurrent"
            notify: "currentChanged"
            index: 1
            isFinal: true
            isRequired: true
        }
        Property {
            name: "selected"
            type: "bool"
            read: "selected"
            write: "setSelected"
            notify: "selectedChanged"
            index: 2
            isFinal: true
            isRequired: true
        }
        Property {
            name: "editing"
            type: "bool"
            read: "editing"
            write: "setEditing"
            notify: "editingChanged"
            index: 3
            isFinal: true
            isRequired: true
        }
        Signal { name: "tableViewChanged" }
        Signal { name: "currentChanged" }
        Signal { name: "selectedChanged" }
        Signal { name: "editingChanged" }
    }
    Component {
        file: "private/qquicktextarea_p.h"
        name: "QQuickTextArea"
        accessSemantics: "reference"
        prototype: "QQuickTextEdit"
        deferredNames: ["background"]
        exports: [
            "QtQuick.Templates/TextArea 2.0",
            "QtQuick.Templates/TextArea 2.1",
            "QtQuick.Templates/TextArea 2.2",
            "QtQuick.Templates/TextArea 2.3",
            "QtQuick.Templates/TextArea 2.4",
            "QtQuick.Templates/TextArea 2.5",
            "QtQuick.Templates/TextArea 2.6",
            "QtQuick.Templates/TextArea 2.7",
            "QtQuick.Templates/TextArea 2.10",
            "QtQuick.Templates/TextArea 2.11",
            "QtQuick.Templates/TextArea 6.0",
            "QtQuick.Templates/TextArea 6.2",
            "QtQuick.Templates/TextArea 6.3",
            "QtQuick.Templates/TextArea 6.4",
            "QtQuick.Templates/TextArea 6.7",
            "QtQuick.Templates/TextArea 6.9"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            518,
            519,
            522,
            523,
            1536,
            1538,
            1539,
            1540,
            1543,
            1545
        ]
        attachedType: "QQuickTextAreaAttached"
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 0
        }
        Property {
            name: "implicitWidth"
            type: "double"
            read: "implicitWidth"
            write: "setImplicitWidth"
            notify: "implicitWidthChanged3"
            index: 1
            isFinal: true
        }
        Property {
            name: "implicitHeight"
            type: "double"
            read: "implicitHeight"
            write: "setImplicitHeight"
            notify: "implicitHeightChanged3"
            index: 2
            isFinal: true
        }
        Property {
            name: "background"
            type: "QQuickItem"
            isPointer: true
            read: "background"
            write: "setBackground"
            notify: "backgroundChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "placeholderText"
            type: "QString"
            read: "placeholderText"
            write: "setPlaceholderText"
            notify: "placeholderTextChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "focusReason"
            type: "Qt::FocusReason"
            read: "focusReason"
            write: "setFocusReason"
            notify: "focusReasonChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "hovered"
            revision: 513
            type: "bool"
            read: "isHovered"
            notify: "hoveredChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "hoverEnabled"
            revision: 513
            type: "bool"
            read: "isHoverEnabled"
            write: "setHoverEnabled"
            reset: "resetHoverEnabled"
            notify: "hoverEnabledChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "placeholderTextColor"
            revision: 517
            type: "QColor"
            read: "placeholderTextColor"
            write: "setPlaceholderTextColor"
            notify: "placeholderTextColorChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "implicitBackgroundWidth"
            revision: 517
            type: "double"
            read: "implicitBackgroundWidth"
            notify: "implicitBackgroundWidthChanged"
            index: 9
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitBackgroundHeight"
            revision: 517
            type: "double"
            read: "implicitBackgroundHeight"
            notify: "implicitBackgroundHeightChanged"
            index: 10
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "topInset"
            revision: 517
            type: "double"
            read: "topInset"
            write: "setTopInset"
            reset: "resetTopInset"
            notify: "topInsetChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "leftInset"
            revision: 517
            type: "double"
            read: "leftInset"
            write: "setLeftInset"
            reset: "resetLeftInset"
            notify: "leftInsetChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "rightInset"
            revision: 517
            type: "double"
            read: "rightInset"
            write: "setRightInset"
            reset: "resetRightInset"
            notify: "rightInsetChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "bottomInset"
            revision: 517
            type: "double"
            read: "bottomInset"
            write: "setBottomInset"
            reset: "resetBottomInset"
            notify: "bottomInsetChanged"
            index: 14
            isFinal: true
        }
        Signal { name: "fontChanged" }
        Signal { name: "implicitWidthChanged3" }
        Signal { name: "implicitHeightChanged3" }
        Signal { name: "backgroundChanged" }
        Signal { name: "placeholderTextChanged" }
        Signal { name: "focusReasonChanged" }
        Signal {
            name: "pressAndHold"
            Parameter { name: "event"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "pressed"
            revision: 513
            Parameter { name: "event"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "released"
            revision: 513
            Parameter { name: "event"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal { name: "hoveredChanged"; revision: 513 }
        Signal { name: "hoverEnabledChanged"; revision: 513 }
        Signal { name: "placeholderTextColorChanged"; revision: 517 }
        Signal { name: "implicitBackgroundWidthChanged"; revision: 517 }
        Signal { name: "implicitBackgroundHeightChanged"; revision: 517 }
        Signal { name: "topInsetChanged"; revision: 517 }
        Signal { name: "leftInsetChanged"; revision: 517 }
        Signal { name: "rightInsetChanged"; revision: 517 }
        Signal { name: "bottomInsetChanged"; revision: 517 }
    }
    Component {
        file: "private/qquicktextarea_p.h"
        name: "QQuickTextAreaAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "flickable"
            type: "QQuickTextArea"
            isPointer: true
            read: "flickable"
            write: "setFlickable"
            notify: "flickableChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "flickableChanged" }
    }
    Component {
        file: "private/qquicktextfield_p.h"
        name: "QQuickTextField"
        accessSemantics: "reference"
        prototype: "QQuickTextInput"
        deferredNames: ["background"]
        exports: [
            "QtQuick.Templates/TextField 2.0",
            "QtQuick.Templates/TextField 2.1",
            "QtQuick.Templates/TextField 2.2",
            "QtQuick.Templates/TextField 2.4",
            "QtQuick.Templates/TextField 2.5",
            "QtQuick.Templates/TextField 2.6",
            "QtQuick.Templates/TextField 2.7",
            "QtQuick.Templates/TextField 2.9",
            "QtQuick.Templates/TextField 2.11",
            "QtQuick.Templates/TextField 6.0",
            "QtQuick.Templates/TextField 6.2",
            "QtQuick.Templates/TextField 6.3",
            "QtQuick.Templates/TextField 6.4",
            "QtQuick.Templates/TextField 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            516,
            517,
            518,
            519,
            521,
            523,
            1536,
            1538,
            1539,
            1540,
            1543
        ]
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 0
        }
        Property {
            name: "implicitWidth"
            type: "double"
            read: "implicitWidth"
            write: "setImplicitWidth"
            notify: "implicitWidthChanged3"
            index: 1
            isFinal: true
        }
        Property {
            name: "implicitHeight"
            type: "double"
            read: "implicitHeight"
            write: "setImplicitHeight"
            notify: "implicitHeightChanged3"
            index: 2
            isFinal: true
        }
        Property {
            name: "background"
            type: "QQuickItem"
            isPointer: true
            read: "background"
            write: "setBackground"
            notify: "backgroundChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "placeholderText"
            type: "QString"
            read: "placeholderText"
            write: "setPlaceholderText"
            notify: "placeholderTextChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "focusReason"
            type: "Qt::FocusReason"
            read: "focusReason"
            write: "setFocusReason"
            notify: "focusReasonChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "hovered"
            revision: 513
            type: "bool"
            read: "isHovered"
            notify: "hoveredChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "hoverEnabled"
            revision: 513
            type: "bool"
            read: "isHoverEnabled"
            write: "setHoverEnabled"
            reset: "resetHoverEnabled"
            notify: "hoverEnabledChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "placeholderTextColor"
            revision: 517
            type: "QColor"
            read: "placeholderTextColor"
            write: "setPlaceholderTextColor"
            notify: "placeholderTextColorChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "implicitBackgroundWidth"
            revision: 517
            type: "double"
            read: "implicitBackgroundWidth"
            notify: "implicitBackgroundWidthChanged"
            index: 9
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "implicitBackgroundHeight"
            revision: 517
            type: "double"
            read: "implicitBackgroundHeight"
            notify: "implicitBackgroundHeightChanged"
            index: 10
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "topInset"
            revision: 517
            type: "double"
            read: "topInset"
            write: "setTopInset"
            reset: "resetTopInset"
            notify: "topInsetChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "leftInset"
            revision: 517
            type: "double"
            read: "leftInset"
            write: "setLeftInset"
            reset: "resetLeftInset"
            notify: "leftInsetChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "rightInset"
            revision: 517
            type: "double"
            read: "rightInset"
            write: "setRightInset"
            reset: "resetRightInset"
            notify: "rightInsetChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "bottomInset"
            revision: 517
            type: "double"
            read: "bottomInset"
            write: "setBottomInset"
            reset: "resetBottomInset"
            notify: "bottomInsetChanged"
            index: 14
            isFinal: true
        }
        Signal { name: "fontChanged" }
        Signal { name: "implicitWidthChanged3" }
        Signal { name: "implicitHeightChanged3" }
        Signal { name: "backgroundChanged" }
        Signal { name: "placeholderTextChanged" }
        Signal { name: "focusReasonChanged" }
        Signal {
            name: "pressAndHold"
            Parameter { name: "event"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "pressed"
            revision: 513
            Parameter { name: "event"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "released"
            revision: 513
            Parameter { name: "event"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal { name: "hoveredChanged"; revision: 513 }
        Signal { name: "hoverEnabledChanged"; revision: 513 }
        Signal { name: "placeholderTextColorChanged"; revision: 517 }
        Signal { name: "implicitBackgroundWidthChanged"; revision: 517 }
        Signal { name: "implicitBackgroundHeightChanged"; revision: 517 }
        Signal { name: "topInsetChanged"; revision: 517 }
        Signal { name: "leftInsetChanged"; revision: 517 }
        Signal { name: "rightInsetChanged"; revision: 517 }
        Signal { name: "bottomInsetChanged"; revision: 517 }
    }
    Component {
        file: "private/qquicktoolbar_p.h"
        name: "QQuickToolBar"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickPane"
        exports: [
            "QtQuick.Templates/ToolBar 2.0",
            "QtQuick.Templates/ToolBar 2.1",
            "QtQuick.Templates/ToolBar 2.4",
            "QtQuick.Templates/ToolBar 2.5",
            "QtQuick.Templates/ToolBar 2.7",
            "QtQuick.Templates/ToolBar 2.11",
            "QtQuick.Templates/ToolBar 6.0",
            "QtQuick.Templates/ToolBar 6.3",
            "QtQuick.Templates/ToolBar 6.7"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543
        ]
        Enum {
            name: "Position"
            values: ["Header", "Footer"]
        }
        Property {
            name: "position"
            type: "Position"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "positionChanged" }
    }
    Component {
        file: "private/qquicktoolbutton_p.h"
        name: "QQuickToolButton"
        accessSemantics: "reference"
        prototype: "QQuickButton"
        exports: [
            "QtQuick.Templates/ToolButton 2.0",
            "QtQuick.Templates/ToolButton 2.1",
            "QtQuick.Templates/ToolButton 2.2",
            "QtQuick.Templates/ToolButton 2.3",
            "QtQuick.Templates/ToolButton 2.4",
            "QtQuick.Templates/ToolButton 2.5",
            "QtQuick.Templates/ToolButton 2.7",
            "QtQuick.Templates/ToolButton 2.11",
            "QtQuick.Templates/ToolButton 6.0",
            "QtQuick.Templates/ToolButton 6.3",
            "QtQuick.Templates/ToolButton 6.7",
            "QtQuick.Templates/ToolButton 6.8"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            515,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1544
        ]
    }
    Component {
        file: "private/qquicktoolseparator_p.h"
        name: "QQuickToolSeparator"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/ToolSeparator 2.1",
            "QtQuick.Templates/ToolSeparator 2.4",
            "QtQuick.Templates/ToolSeparator 2.5",
            "QtQuick.Templates/ToolSeparator 2.7",
            "QtQuick.Templates/ToolSeparator 2.11",
            "QtQuick.Templates/ToolSeparator 6.0",
            "QtQuick.Templates/ToolSeparator 6.3",
            "QtQuick.Templates/ToolSeparator 6.7"
        ]
        exportMetaObjectRevisions: [513, 516, 517, 519, 523, 1536, 1539, 1543]
        Property {
            name: "orientation"
            type: "Qt::Orientation"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "horizontal"
            type: "bool"
            read: "isHorizontal"
            notify: "orientationChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "vertical"
            type: "bool"
            read: "isVertical"
            notify: "orientationChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Signal { name: "orientationChanged" }
    }
    Component {
        file: "private/qquicktooltip_p.h"
        name: "QQuickToolTip"
        accessSemantics: "reference"
        defaultProperty: "contentData"
        prototype: "QQuickPopup"
        exports: [
            "QtQuick.Templates/ToolTip 2.0",
            "QtQuick.Templates/ToolTip 2.1",
            "QtQuick.Templates/ToolTip 2.3",
            "QtQuick.Templates/ToolTip 2.5",
            "QtQuick.Templates/ToolTip 6.0",
            "QtQuick.Templates/ToolTip 6.8"
        ]
        exportMetaObjectRevisions: [512, 513, 515, 517, 1536, 1544]
        attachedType: "QQuickToolTipAttached"
        Property {
            name: "delay"
            type: "int"
            read: "delay"
            write: "setDelay"
            notify: "delayChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "timeout"
            type: "int"
            read: "timeout"
            write: "setTimeout"
            notify: "timeoutChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 2
            isFinal: true
        }
        Signal { name: "textChanged" }
        Signal { name: "delayChanged" }
        Signal { name: "timeoutChanged" }
        Method {
            name: "show"
            revision: 517
            Parameter { name: "text"; type: "QString" }
            Parameter { name: "ms"; type: "int" }
        }
        Method {
            name: "show"
            revision: 517
            isCloned: true
            Parameter { name: "text"; type: "QString" }
        }
        Method { name: "hide"; revision: 517 }
    }
    Component {
        file: "private/qquicktooltip_p.h"
        name: "QQuickToolTipAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "delay"
            type: "int"
            read: "delay"
            write: "setDelay"
            notify: "delayChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "timeout"
            type: "int"
            read: "timeout"
            write: "setTimeout"
            notify: "timeoutChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "toolTip"
            type: "QQuickToolTip"
            isPointer: true
            read: "toolTip"
            index: 4
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Signal { name: "textChanged" }
        Signal { name: "delayChanged" }
        Signal { name: "timeoutChanged" }
        Signal { name: "visibleChanged" }
        Method {
            name: "show"
            Parameter { name: "text"; type: "QString" }
            Parameter { name: "ms"; type: "int" }
        }
        Method {
            name: "show"
            isCloned: true
            Parameter { name: "text"; type: "QString" }
        }
        Method { name: "hide" }
    }
    Component {
        file: "private/qquicktreeviewdelegate_p.h"
        name: "QQuickTreeViewDelegate"
        accessSemantics: "reference"
        prototype: "QQuickItemDelegate"
        exports: [
            "QtQuick.Templates/TreeViewDelegate 6.3",
            "QtQuick.Templates/TreeViewDelegate 6.4",
            "QtQuick.Templates/TreeViewDelegate 6.5",
            "QtQuick.Templates/TreeViewDelegate 6.7",
            "QtQuick.Templates/TreeViewDelegate 6.8"
        ]
        exportMetaObjectRevisions: [1539, 1540, 1541, 1543, 1544]
        Property {
            name: "indentation"
            type: "double"
            read: "indentation"
            write: "setIndentation"
            notify: "indentationChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "leftMargin"
            type: "double"
            read: "leftMargin"
            write: "setLeftMargin"
            notify: "leftMarginChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "rightMargin"
            type: "double"
            read: "rightMargin"
            write: "setRightMargin"
            notify: "rightMarginChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "treeView"
            type: "QQuickTreeView"
            isPointer: true
            read: "treeView"
            write: "setTreeView"
            notify: "treeviewChanged"
            index: 3
            isFinal: true
            isRequired: true
        }
        Property {
            name: "isTreeNode"
            type: "bool"
            read: "isTreeNode"
            write: "setIsTreeNode"
            notify: "isTreeNodeChanged"
            index: 4
            isFinal: true
            isRequired: true
        }
        Property {
            name: "hasChildren"
            type: "bool"
            read: "hasChildren"
            write: "setHasChildren"
            notify: "hasChildrenChanged"
            index: 5
            isFinal: true
            isRequired: true
        }
        Property {
            name: "expanded"
            type: "bool"
            read: "expanded"
            write: "setExpanded"
            notify: "expandedChanged"
            index: 6
            isFinal: true
            isRequired: true
        }
        Property {
            name: "depth"
            type: "int"
            read: "depth"
            write: "setDepth"
            notify: "depthChanged"
            index: 7
            isFinal: true
            isRequired: true
        }
        Property {
            name: "current"
            revision: 1540
            type: "bool"
            read: "current"
            write: "setCurrent"
            notify: "currentChanged"
            index: 8
            isFinal: true
            isRequired: true
        }
        Property {
            name: "selected"
            revision: 1540
            type: "bool"
            read: "selected"
            write: "setSelected"
            notify: "selectedChanged"
            index: 9
            isFinal: true
            isRequired: true
        }
        Property {
            name: "editing"
            revision: 1541
            type: "bool"
            read: "editing"
            write: "setEditing"
            notify: "editingChanged"
            index: 10
            isFinal: true
            isRequired: true
        }
        Signal { name: "indicatorChanged" }
        Signal { name: "indentationChanged" }
        Signal { name: "isTreeNodeChanged" }
        Signal { name: "hasChildrenChanged" }
        Signal { name: "expandedChanged" }
        Signal { name: "depthChanged" }
        Signal { name: "treeviewChanged" }
        Signal { name: "leftMarginChanged" }
        Signal { name: "rightMarginChanged" }
        Signal { name: "currentChanged"; revision: 1540 }
        Signal { name: "selectedChanged"; revision: 1540 }
        Signal { name: "editingChanged"; revision: 1541 }
    }
    Component {
        file: "private/qquicktumbler_p.h"
        name: "QQuickTumbler"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/Tumbler 2.0",
            "QtQuick.Templates/Tumbler 2.1",
            "QtQuick.Templates/Tumbler 2.2",
            "QtQuick.Templates/Tumbler 2.4",
            "QtQuick.Templates/Tumbler 2.5",
            "QtQuick.Templates/Tumbler 2.7",
            "QtQuick.Templates/Tumbler 2.11",
            "QtQuick.Templates/Tumbler 6.0",
            "QtQuick.Templates/Tumbler 6.3",
            "QtQuick.Templates/Tumbler 6.7",
            "QtQuick.Templates/Tumbler 6.9"
        ]
        exportMetaObjectRevisions: [
            512,
            513,
            514,
            516,
            517,
            519,
            523,
            1536,
            1539,
            1543,
            1545
        ]
        attachedType: "QQuickTumblerAttached"
        Enum {
            name: "PositionMode"
            values: [
                "Beginning",
                "Center",
                "End",
                "Visible",
                "Contain",
                "SnapPosition"
            ]
        }
        Property {
            name: "model"
            type: "QVariant"
            read: "model"
            write: "setModel"
            notify: "modelChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "currentIndex"
            type: "int"
            read: "currentIndex"
            write: "setCurrentIndex"
            notify: "currentIndexChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "currentItem"
            type: "QQuickItem"
            isPointer: true
            read: "currentItem"
            notify: "currentItemChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "visibleItemCount"
            type: "int"
            read: "visibleItemCount"
            write: "setVisibleItemCount"
            notify: "visibleItemCountChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "wrap"
            revision: 513
            type: "bool"
            read: "wrap"
            write: "setWrap"
            reset: "resetWrap"
            notify: "wrapChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "moving"
            revision: 514
            type: "bool"
            read: "isMoving"
            notify: "movingChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "flickDeceleration"
            revision: 1545
            type: "double"
            read: "flickDeceleration"
            write: "setFlickDeceleration"
            reset: "resetFlickDeceleration"
            notify: "flickDecelerationChanged"
            index: 8
            isFinal: true
        }
        Signal { name: "modelChanged" }
        Signal { name: "countChanged" }
        Signal { name: "currentIndexChanged" }
        Signal { name: "currentItemChanged" }
        Signal { name: "delegateChanged" }
        Signal { name: "visibleItemCountChanged" }
        Signal { name: "wrapChanged"; revision: 513 }
        Signal { name: "movingChanged"; revision: 514 }
        Signal { name: "flickDecelerationChanged"; revision: 1545 }
        Method { name: "_q_updateItemWidths" }
        Method { name: "_q_updateItemHeights" }
        Method { name: "_q_onViewCurrentIndexChanged" }
        Method { name: "_q_onViewCountChanged" }
        Method { name: "_q_onViewOffsetChanged" }
        Method { name: "_q_onViewContentYChanged" }
        Method {
            name: "positionViewAtIndex"
            revision: 517
            Parameter { name: "index"; type: "int" }
            Parameter { name: "mode"; type: "PositionMode" }
        }
    }
    Component {
        file: "private/qquicktumbler_p.h"
        name: "QQuickTumblerAttached"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "tumbler"
            type: "QQuickTumbler"
            isPointer: true
            read: "tumbler"
            index: 0
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "displacement"
            type: "double"
            read: "displacement"
            notify: "displacementChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Signal { name: "displacementChanged" }
    }
    Component {
        file: "private/qquickheaderview_p.h"
        name: "QQuickVerticalHeaderView"
        accessSemantics: "reference"
        prototype: "QQuickHeaderViewBase"
        exports: [
            "QtQuick.Templates/VerticalHeaderView 2.15",
            "QtQuick.Templates/VerticalHeaderView 6.0",
            "QtQuick.Templates/VerticalHeaderView 6.2",
            "QtQuick.Templates/VerticalHeaderView 6.3",
            "QtQuick.Templates/VerticalHeaderView 6.4",
            "QtQuick.Templates/VerticalHeaderView 6.5",
            "QtQuick.Templates/VerticalHeaderView 6.6",
            "QtQuick.Templates/VerticalHeaderView 6.7",
            "QtQuick.Templates/VerticalHeaderView 6.8",
            "QtQuick.Templates/VerticalHeaderView 6.9"
        ]
        exportMetaObjectRevisions: [
            527,
            1536,
            1538,
            1539,
            1540,
            1541,
            1542,
            1543,
            1544,
            1545
        ]
        Property {
            name: "movableRows"
            revision: 1544
            type: "bool"
            read: "movableRows"
            write: "setMovableRows"
            notify: "movableRowsChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "movableRowsChanged"; revision: 1544 }
    }
    Component {
        file: "private/qquickweeknumbercolumn_p.h"
        name: "QQuickWeekNumberColumn"
        accessSemantics: "reference"
        prototype: "QQuickControl"
        exports: [
            "QtQuick.Templates/AbstractWeekNumberColumn 6.3",
            "QtQuick.Templates/AbstractWeekNumberColumn 6.7"
        ]
        exportMetaObjectRevisions: [1539, 1543]
        Property {
            name: "month"
            type: "int"
            read: "month"
            write: "setMonth"
            notify: "monthChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "year"
            type: "int"
            read: "year"
            write: "setYear"
            notify: "yearChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "source"
            type: "QVariant"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 3
            isFinal: true
        }
        Signal { name: "monthChanged" }
        Signal { name: "yearChanged" }
        Signal { name: "sourceChanged" }
        Signal { name: "delegateChanged" }
    }
}
