import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qt3dquick3dlogicforeign_p.h"
        name: "Qt3DLogic::QFrameAction"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: ["Qt3D.Logic/FrameAction 2.0", "Qt3D.Logic/FrameAction 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Signal {
            name: "triggered"
            Parameter { name: "dt"; type: "float" }
        }
    }
    Component {
        file: "qcomponent.h"
        name: "Qt3DCore::QComponent"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        Property {
            name: "isShareable"
            type: "bool"
            read: "isShareable"
            write: "setShareable"
            notify: "shareableChanged"
            index: 0
        }
        Signal {
            name: "shareableChanged"
            Parameter { name: "isShareable"; type: "bool" }
        }
        Signal {
            name: "addedToEntity"
            Parameter { name: "entity"; type: "QEntity"; isPointer: true }
        }
        Signal {
            name: "removedFromEntity"
            Parameter { name: "entity"; type: "QEntity"; isPointer: true }
        }
        Method {
            name: "setShareable"
            Parameter { name: "isShareable"; type: "bool" }
        }
    }
    Component {
        file: "qnode.h"
        name: "Qt3DCore::QNode"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "parent"
            type: "Qt3DCore::QNode"
            isPointer: true
            read: "parentNode"
            write: "setParent"
            notify: "parentChanged"
            index: 0
        }
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 1
        }
        Signal {
            name: "parentChanged"
            Parameter { name: "parent"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "enabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal { name: "nodeDestroyed" }
        Method {
            name: "setParent"
            Parameter { name: "parent"; type: "QNode"; isPointer: true }
        }
        Method {
            name: "setEnabled"
            Parameter { name: "isEnabled"; type: "bool" }
        }
        Method { name: "_q_postConstructorInit" }
        Method {
            name: "_q_addChild"
            Parameter { type: "Qt3DCore::QNode"; isPointer: true }
        }
        Method {
            name: "_q_removeChild"
            Parameter { type: "Qt3DCore::QNode"; isPointer: true }
        }
        Method {
            name: "_q_setParentHelper"
            Parameter { type: "Qt3DCore::QNode"; isPointer: true }
        }
    }
}
