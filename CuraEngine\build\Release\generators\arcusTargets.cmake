# Load the debug and release variables
file(GLOB DATA_FILES "${CMAKE_CURRENT_LIST_DIR}/arcus-*-data.cmake")

foreach(f ${DATA_FILES})
    include(${f})
endforeach()

# Create the targets for all the components
foreach(_COMPONENT ${arcus_COMPONENT_NAMES} )
    if(NOT TARGET ${_COMPONENT})
        add_library(${_COMPONENT} INTERFACE IMPORTED)
        message(${arcus_MESSAGE_MODE} "Conan: Component target declared '${_COMPONENT}'")
    endif()
endforeach()

if(NOT TARGET arcus::arcus)
    add_library(arcus::arcus INTERFACE IMPORTED)
    message(${arcus_MESSAGE_MODE} "Conan: Target declared 'arcus::arcus'")
endif()
# Load the debug and release library finders
file(GLOB CONFIG_FILES "${CMAKE_CURRENT_LIST_DIR}/arcus-Target-*.cmake")

foreach(f ${CONFIG_FILES})
    include(${f})
endforeach()