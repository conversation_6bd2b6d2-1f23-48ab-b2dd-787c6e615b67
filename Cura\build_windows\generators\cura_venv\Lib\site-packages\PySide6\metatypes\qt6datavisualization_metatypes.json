[{"classes": [{"className": "CameraHelper", "lineNumber": 27, "object": true, "qualifiedClassName": "CameraHelper", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "camerahelper_p.h", "outputRevision": 69}, {"classes": [{"className": "Abstract3DController", "lineNumber": 143, "object": true, "qualifiedClassName": "Abstract3DController", "signals": [{"access": "public", "arguments": [{"name": "quality", "type": "QAbstract3DGraph::ShadowQuality"}], "index": 0, "name": "shadowQualityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "inputHandler", "type": "QAbstract3DInputHandler*"}], "index": 1, "name": "activeInputHandlerChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "activeTheme", "type": "Q3DTheme*"}], "index": 2, "name": "activeThemeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "QAbstract3DGraph::SelectionFlags"}], "index": 3, "name": "selectionMode<PERSON>hanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "needRender", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 5, "name": "axisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 6, "name": "axisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 7, "name": "axisZChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "type", "type": "QAbstract3DGraph::ElementType"}], "index": 8, "name": "elementSelected", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 9, "name": "measureFpsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fps", "type": "qreal"}], "index": 10, "name": "currentFpsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 11, "name": "orthoProjectionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ratio", "type": "qreal"}], "index": 12, "name": "aspectRatioChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ratio", "type": "qreal"}], "index": 13, "name": "horizontalAspectRatioChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hints", "type": "QAbstract3DGraph::OptimizationHints"}], "index": 14, "name": "optimizationHintsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 15, "name": "polarChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "offset", "type": "float"}], "index": 16, "name": "radialLabelOffsetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 17, "name": "reflectionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reflectivity", "type": "qreal"}], "index": 18, "name": "reflectivityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "locale", "type": "QLocale"}], "index": 19, "name": "localeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QVector3D"}], "index": 20, "name": "queriedGraphPositionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "margin", "type": "qreal"}], "index": 21, "name": "marginChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 22, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "title", "type": "QString"}], "index": 23, "name": "handleAxisTitleChanged", "returnType": "void"}, {"access": "public", "index": 24, "name": "handleAxisLabelsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "float"}, {"name": "max", "type": "float"}], "index": 25, "name": "handleAxisRangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 26, "name": "handleAxisSegmentCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 27, "name": "handleAxisSubSegmentCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "autoAdjust", "type": "bool"}], "index": 28, "name": "handleAxisAutoAdjustRangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "format", "type": "QString"}], "index": 29, "name": "handleAxisLabelFormatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 30, "name": "handleAxisReversedChanged", "returnType": "void"}, {"access": "public", "index": 31, "name": "handleAxisFormatterDirty", "returnType": "void"}, {"access": "public", "arguments": [{"name": "angle", "type": "float"}], "index": 32, "name": "handleAxisLabelAutoRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "index": 33, "name": "handleAxisTitleVisibilityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fixed", "type": "bool"}], "index": 34, "name": "handleAxisTitleFixedChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "view", "type": "QAbstract3DInputHandler::InputView"}], "index": 35, "name": "handleInputViewChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QPoint"}], "index": 36, "name": "handleInputPositionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "index": 37, "name": "handleSeriesVisibilityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "style", "type": "Q3DTheme::ColorStyle"}], "index": 38, "name": "handleThemeColorStyleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QList<QColor>"}], "index": 39, "name": "handleThemeBaseColorsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QList<QLinearGradient>"}], "index": 40, "name": "handleThemeBaseGradientsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 41, "name": "handleThemeSingleHighlightColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QLinearGradient"}], "index": 42, "name": "handleThemeSingleHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 43, "name": "handleThemeMultiHighlightColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QLinearGradient"}], "index": 44, "name": "handleThemeMultiHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "theme", "type": "Q3DTheme::Theme"}], "index": 45, "name": "handleThemeTypeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "quality", "type": "QAbstract3DGraph::ShadowQuality"}], "index": 46, "name": "handleRequestShadowQuality", "returnType": "void"}, {"access": "public", "index": 47, "name": "updateCustomItem", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "abstract3dcontroller_p.h", "outputRevision": 69}, {"classes": [{"className": "Abstract3<PERSON><PERSON><PERSON>", "lineNumber": 37, "object": true, "qualifiedClassName": "Abstract3<PERSON><PERSON><PERSON>", "signals": [{"access": "public", "index": 0, "name": "needRender", "returnType": "void"}, {"access": "public", "arguments": [{"name": "quality", "type": "QAbstract3DGraph::ShadowQuality"}], "index": 1, "name": "requestShadowQuality", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "protected", "name": "QOpenGLFunctions"}]}], "inputFile": "abstract3drenderer_p.h", "outputRevision": 69}, {"classes": [{"className": "AxisRender<PERSON>ache", "lineNumber": 23, "object": true, "qualifiedClassName": "AxisRender<PERSON>ache", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "axisrendercache_p.h", "outputRevision": 69}, {"classes": [{"className": "Bars3DController", "lineNumber": 46, "object": true, "qualifiedClassName": "Bars3DController", "signals": [{"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "index": 0, "name": "primarySeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "index": 1, "name": "selectedSeriesChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 2, "name": "handleArrayReset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 3, "name": "handleRowsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 4, "name": "handleRowsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 5, "name": "handleRowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 6, "name": "handleRowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rowIndex", "type": "int"}, {"name": "columnIndex", "type": "int"}], "index": 7, "name": "handleItemChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "handleDataRowLabelsChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "handleDataColumnLabelsChanged", "returnType": "void"}, {"access": "public", "index": 10, "name": "handleRowColorsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Abstract3DController"}]}], "inputFile": "bars3dcontroller_p.h", "outputRevision": 69}, {"classes": [{"className": "Bars3<PERSON><PERSON><PERSON>", "lineNumber": 34, "object": true, "qualifiedClassName": "Bars3<PERSON><PERSON><PERSON>", "slots": [{"access": "public", "arguments": [{"name": "uniform", "type": "bool"}], "index": 0, "name": "updateMultiSeriesScaling", "returnType": "void"}, {"access": "public", "arguments": [{"name": "thicknessRatio", "type": "GLfloat"}, {"name": "spacing", "type": "QSizeF"}, {"name": "relative", "type": "bool"}], "index": 1, "name": "updateBarSpecs", "returnType": "void"}, {"access": "public", "arguments": [{"name": "thicknessRatio", "type": "GLfloat"}, {"name": "spacing", "type": "QSizeF"}], "index": 2, "isCloned": true, "name": "updateBarSpecs", "returnType": "void"}, {"access": "public", "arguments": [{"name": "thicknessRatio", "type": "GLfloat"}], "index": 3, "isCloned": true, "name": "updateBarSpecs", "returnType": "void"}, {"access": "public", "index": 4, "isCloned": true, "name": "updateBarSpecs", "returnType": "void"}, {"access": "public", "arguments": [{"name": "margin", "type": "QSizeF"}], "index": 5, "name": "updateBarSeriesMargin", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isSlicing", "type": "bool"}], "index": 6, "name": "updateSlicingActive", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QPoint"}, {"name": "series", "type": "QBar3DSeries*"}], "index": 7, "name": "updateSelectedBar", "returnType": "void"}, {"access": "public", "index": 8, "isConst": true, "name": "clickedPosition", "returnType": "QPoint"}, {"access": "public", "index": 9, "name": "resetClickedStatus", "returnType": "void"}, {"access": "public", "arguments": [{"name": "orientation", "type": "QAbstract3DAxis::AxisOrientation"}, {"name": "min", "type": "float"}, {"name": "max", "type": "float"}], "index": 10, "name": "updateAxisRange", "returnType": "void"}, {"access": "public", "arguments": [{"name": "orientation", "type": "QAbstract3DAxis::AxisOrientation"}, {"name": "enable", "type": "bool"}], "index": 11, "name": "updateAxisReversed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Abstract3<PERSON><PERSON><PERSON>"}]}], "inputFile": "bars3drenderer_p.h", "outputRevision": 69}, {"classes": [{"className": "Drawer", "lineNumber": 35, "object": true, "qualifiedClassName": "Drawer", "signals": [{"access": "public", "index": 0, "name": "drawerChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QOpenGLFunctions"}]}], "inputFile": "drawer_p.h", "outputRevision": 69}, {"classes": [{"className": "Q3DBars", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "multiSeriesUniform", "notify": "multiSeriesUniformChanged", "read": "isMultiSeriesUniform", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMultiSeriesUniform"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "barThickness", "notify": "barThicknessChanged", "read": "barThickness", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setBarThickness"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "barSpacing", "notify": "barSpacingChanged", "read": "barSpacing", "required": false, "scriptable": true, "stored": true, "type": "QSizeF", "user": false, "write": "setBarSpacing"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "barSpacingRelative", "notify": "barSpacingRelativeChanged", "read": "isBarSpacingRelative", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBarSpacingRelative"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "barSeriesMargin", "notify": "barSeriesMarginChanged", "read": "barSeriesMargin", "required": false, "revision": 1539, "scriptable": true, "stored": true, "type": "QSizeF", "user": false, "write": "setBarSeriesMargin"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "rowAxis", "notify": "rowAxisChanged", "read": "rowAxis", "required": false, "scriptable": true, "stored": true, "type": "QCategory3DAxis*", "user": false, "write": "setRowAxis"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "columnAxis", "notify": "columnAxisChanged", "read": "columnAxis", "required": false, "scriptable": true, "stored": true, "type": "QCategory3DAxis*", "user": false, "write": "setColumnAxis"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "valueAxis", "notify": "valueAxisChanged", "read": "valueAxis", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setValueAxis"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "primarySeries", "notify": "primarySeriesChanged", "read": "primarySeries", "required": false, "scriptable": true, "stored": true, "type": "QBar3DSeries*", "user": false, "write": "setPrimarySeries"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "selectedSeries", "notify": "selectedSeriesChanged", "read": "selectedSeries", "required": false, "scriptable": true, "stored": true, "type": "QBar3DSeries*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "floorLevel", "notify": "floorLevelChanged", "read": "floorLevel", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setFloorLevel"}], "qualifiedClassName": "Q3DBars", "signals": [{"access": "public", "arguments": [{"name": "uniform", "type": "bool"}], "index": 0, "name": "multiSeriesUniformChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "thicknessRatio", "type": "float"}], "index": 1, "name": "barThicknessChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "spacing", "type": "QSizeF"}], "index": 2, "name": "barSpacingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "relative", "type": "bool"}], "index": 3, "name": "barSpacingRelativeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "margin", "type": "QSizeF"}], "index": 4, "name": "barSeriesMarginChanged", "returnType": "void", "revision": 1539}, {"access": "public", "arguments": [{"name": "axis", "type": "QCategory3DAxis*"}], "index": 5, "name": "rowAxisChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QCategory3DAxis*"}], "index": 6, "name": "columnAxisChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "index": 7, "name": "valueAxisChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "index": 8, "name": "primarySeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "index": 9, "name": "selectedSeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "level", "type": "float"}], "index": 10, "name": "floorLevelChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstract3DGraph"}]}], "inputFile": "q3dbars.h", "outputRevision": 69}, {"classes": [{"className": "Q3DBarsPrivate", "lineNumber": 24, "object": true, "qualifiedClassName": "Q3DBarsPrivate", "superClasses": [{"access": "public", "name": "QAbstract3DGraphPrivate"}]}], "inputFile": "q3dbars_p.h", "outputRevision": 69}, {"classes": [{"className": "Q3DCamera", "enums": [{"isClass": false, "isFlag": false, "name": "CameraPreset", "values": ["CameraPresetNone", "CameraPresetFrontLow", "CameraPresetFront", "CameraPresetFrontHigh", "CameraPresetLeftLow", "CameraPresetLeft", "CameraPresetLeftHigh", "CameraPresetRightLow", "CameraPresetRight", "CameraPresetRightHigh", "CameraPresetBehindLow", "CameraPresetBehind", "CameraPresetBehindHigh", "CameraPresetIsometricLeft", "CameraPresetIsometricLeftHigh", "CameraPresetIsometricRight", "CameraPresetIsometricRightHigh", "CameraPresetDirectlyAbove", "CameraPresetDirectlyAboveCW45", "CameraPresetDirectlyAboveCCW45", "CameraPresetFrontBelow", "CameraPresetLeftBelow", "CameraPresetRightBelow", "CameraPresetBehindBelow", "CameraPresetDirectlyBelow"]}], "lineNumber": 13, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "xRotation", "notify": "xRotationChanged", "read": "xRotation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setXRotation"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "yRotation", "notify": "yRotationChanged", "read": "yRotation", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setYRotation"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "zoomLevel", "notify": "zoomLevelChanged", "read": "zoomLevel", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setZoomLevel"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "cameraPreset", "notify": "cameraPresetChanged", "read": "cameraPreset", "required": false, "scriptable": true, "stored": true, "type": "CameraPreset", "user": false, "write": "setCameraPreset"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "wrapXRotation", "notify": "wrapXRotationChanged", "read": "wrapXRotation", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setWrapXRotation"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "wrapYRotation", "notify": "wrapYRotationChanged", "read": "wrapYRotation", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setWrapYRotation"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "target", "notify": "targetChanged", "read": "target", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "minZoomLevel", "notify": "minZoomLevelChanged", "read": "minZoomLevel", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMinZoomLevel"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "maxZoomLevel", "notify": "maxZoomLevelChanged", "read": "maxZoomLevel", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMaxZoomLevel"}], "qualifiedClassName": "Q3DCamera", "signals": [{"access": "public", "arguments": [{"name": "rotation", "type": "float"}], "index": 0, "name": "xRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rotation", "type": "float"}], "index": 1, "name": "yRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zoomLevel", "type": "float"}], "index": 2, "name": "zoomLevelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "preset", "type": "Q3DCamera::CameraPreset"}], "index": 3, "name": "cameraPresetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isEnabled", "type": "bool"}], "index": 4, "name": "wrapXRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isEnabled", "type": "bool"}], "index": 5, "name": "wrapYRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "target", "type": "QVector3D"}], "index": 6, "name": "targetChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "zoomLevel", "type": "float"}], "index": 7, "name": "minZoomLevelChanged", "returnType": "void", "revision": 258}, {"access": "public", "arguments": [{"name": "zoomLevel", "type": "float"}], "index": 8, "name": "maxZoomLevelChanged", "returnType": "void", "revision": 258}], "superClasses": [{"access": "public", "name": "Q3DObject"}]}], "inputFile": "q3dcamera.h", "outputRevision": 69}, {"classes": [{"className": "Q3DCameraPrivate", "lineNumber": 25, "object": true, "qualifiedClassName": "Q3DCameraPrivate", "signals": [{"access": "public", "arguments": [{"name": "rotation", "type": "float"}], "index": 0, "name": "minXRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rotation", "type": "float"}], "index": 1, "name": "minYRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rotation", "type": "float"}], "index": 2, "name": "maxXRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rotation", "type": "float"}], "index": 3, "name": "maxYRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "viewMatrix", "type": "QMatrix4x4"}], "index": 4, "name": "viewMatrixChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 5, "name": "viewMatrixAutoUpdateChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "q3dcamera_p.h", "outputRevision": 69}, {"classes": [{"className": "Q3DLight", "lineNumber": 13, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "autoPosition", "notify": "autoPositionChanged", "read": "isAutoPosition", "required": false, "revision": 259, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoPosition"}], "qualifiedClassName": "Q3DLight", "signals": [{"access": "public", "arguments": [{"name": "autoPosition", "type": "bool"}], "index": 0, "name": "autoPositionChanged", "returnType": "void", "revision": 259}], "superClasses": [{"access": "public", "name": "Q3DObject"}]}], "inputFile": "q3dlight.h", "outputRevision": 69}, {"classes": [{"className": "Q3DObject", "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "parentScene", "read": "parentScene", "required": false, "scriptable": true, "stored": true, "type": "Q3DScene*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setPosition"}], "qualifiedClassName": "Q3DObject", "signals": [{"access": "public", "arguments": [{"name": "position", "type": "QVector3D"}], "index": 0, "name": "positionChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "q3dobject.h", "outputRevision": 69}, {"classes": [{"className": "Q3DScatter", "lineNumber": 15, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisZ", "notify": "axisZChanged", "read": "axisZ", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisZ"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "selectedSeries", "notify": "selectedSeriesChanged", "read": "selectedSeries", "required": false, "scriptable": true, "stored": true, "type": "QScatter3DSeries*", "user": false}], "qualifiedClassName": "Q3DScatter", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "index": 0, "name": "axisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "index": 1, "name": "axisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "index": 2, "name": "axisZChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QScatter3DSeries*"}], "index": 3, "name": "selectedSeriesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstract3DGraph"}]}], "inputFile": "q3dscatter.h", "outputRevision": 69}, {"classes": [{"className": "Q3DScatterPrivate", "lineNumber": 24, "object": true, "qualifiedClassName": "Q3DScatterPrivate", "superClasses": [{"access": "public", "name": "QAbstract3DGraphPrivate"}]}], "inputFile": "q3dscatter_p.h", "outputRevision": 69}, {"classes": [{"className": "Q3DScene", "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "viewport", "notify": "viewportChanged", "read": "viewport", "required": false, "scriptable": true, "stored": true, "type": "QRect", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "primarySubViewport", "notify": "primarySubViewportChanged", "read": "primarySubViewport", "required": false, "scriptable": true, "stored": true, "type": "QRect", "user": false, "write": "setPrimarySubViewport"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "secondarySubViewport", "notify": "secondarySubViewportChanged", "read": "secondarySubViewport", "required": false, "scriptable": true, "stored": true, "type": "QRect", "user": false, "write": "setSecondarySubViewport"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "selectionQueryPosition", "notify": "selectionQueryPositionChanged", "read": "selectionQueryPosition", "required": false, "scriptable": true, "stored": true, "type": "QPoint", "user": false, "write": "setSelectionQueryPosition"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "secondarySubviewOnTop", "notify": "secondarySubviewOnTopChanged", "read": "isSecondarySubviewOnTop", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSecondarySubviewOnTop"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "slicingActive", "notify": "slicingActiveChanged", "read": "isSlicingActive", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSlicingActive"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "activeCamera", "notify": "activeCameraChanged", "read": "activeCamera", "required": false, "scriptable": true, "stored": true, "type": "Q3DCamera*", "user": false, "write": "setActiveCamera"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "activeLight", "notify": "activeLightChanged", "read": "activeLight", "required": false, "scriptable": true, "stored": true, "type": "Q3DLight*", "user": false, "write": "setActiveLight"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "devicePixelRatio", "notify": "devicePixelRatioChanged", "read": "devicePixelRatio", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setDevicePixelRatio"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "graphPositionQuery", "notify": "graphPositionQueryChanged", "read": "graphPositionQuery", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "QPoint", "user": false, "write": "setGraphPositionQuery"}], "qualifiedClassName": "Q3DScene", "signals": [{"access": "public", "arguments": [{"name": "viewport", "type": "QRect"}], "index": 0, "name": "viewportChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "subViewport", "type": "QRect"}], "index": 1, "name": "primarySubViewportChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "subViewport", "type": "QRect"}], "index": 2, "name": "secondarySubViewportChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isSecondaryOnTop", "type": "bool"}], "index": 3, "name": "secondarySubviewOnTopChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "isSlicingActive", "type": "bool"}], "index": 4, "name": "slicingActiveChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "camera", "type": "Q3DCamera*"}], "index": 5, "name": "activeCameraChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "light", "type": "Q3DLight*"}], "index": 6, "name": "activeLightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pixelRatio", "type": "float"}], "index": 7, "name": "devicePixelRatioChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QPoint"}], "index": 8, "name": "selectionQueryPositionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QPoint"}], "index": 9, "name": "graphPositionQueryChanged", "returnType": "void", "revision": 258}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "q3dscene.h", "outputRevision": 69}, {"classes": [{"className": "Q3DScenePrivate", "lineNumber": 54, "object": true, "qualifiedClassName": "Q3DScenePrivate", "signals": [{"access": "public", "index": 0, "name": "needRender", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "q3dscene_p.h", "outputRevision": 69}, {"classes": [{"className": "Q3DSurface", "lineNumber": 15, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "axisX", "notify": "axisXChanged", "read": "axisX", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisX"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "axisY", "notify": "axisYChanged", "read": "axisY", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisY"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "axisZ", "notify": "axisZChanged", "read": "axisZ", "required": false, "scriptable": true, "stored": true, "type": "QValue3DAxis*", "user": false, "write": "setAxisZ"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "selectedSeries", "notify": "selectedSeriesChanged", "read": "selectedSeries", "required": false, "scriptable": true, "stored": true, "type": "QSurface3DSeries*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "flipHorizontalGrid", "notify": "flipHorizontalGridChanged", "read": "flipHorizontalGrid", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFlipHorizontalGrid"}], "qualifiedClassName": "Q3DSurface", "signals": [{"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "index": 0, "name": "axisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "index": 1, "name": "axisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QValue3DAxis*"}], "index": 2, "name": "axisZChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QSurface3DSeries*"}], "index": 3, "name": "selectedSeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "flip", "type": "bool"}], "index": 4, "name": "flipHorizontalGridChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstract3DGraph"}]}], "inputFile": "q3dsurface.h", "outputRevision": 69}, {"classes": [{"className": "Q3DSurfacePrivate", "lineNumber": 24, "object": true, "qualifiedClassName": "Q3DSurfacePrivate", "superClasses": [{"access": "public", "name": "QAbstract3DGraphPrivate"}]}], "inputFile": "q3dsurface_p.h", "outputRevision": 69}, {"classes": [{"className": "QAbstract3DGraph", "enums": [{"isClass": false, "isFlag": false, "name": "SelectionFlag", "values": ["SelectionNone", "SelectionItem", "SelectionRow", "SelectionItemAndRow", "SelectionColumn", "SelectionItemAndColumn", "SelectionRowAndColumn", "SelectionItemRowAndColumn", "SelectionSlice", "SelectionMultiSeries"]}, {"alias": "SelectionFlag", "isClass": false, "isFlag": true, "name": "SelectionFlags", "values": ["SelectionNone", "SelectionItem", "SelectionRow", "SelectionItemAndRow", "SelectionColumn", "SelectionItemAndColumn", "SelectionRowAndColumn", "SelectionItemRowAndColumn", "SelectionSlice", "SelectionMultiSeries"]}, {"isClass": false, "isFlag": false, "name": "ShadowQuality", "values": ["ShadowQualityNone", "ShadowQualityLow", "ShadowQualityMedium", "ShadowQualityHigh", "ShadowQualitySoftLow", "ShadowQualitySoftMedium", "ShadowQualitySoftHigh"]}, {"isClass": false, "isFlag": false, "name": "ElementType", "values": ["ElementNone", "ElementSeries", "ElementAxisXLabel", "ElementAxisYLabel", "ElementAxisZLabel", "ElementCustomItem"]}, {"isClass": false, "isFlag": false, "name": "OptimizationHint", "values": ["OptimizationDefault", "OptimizationStatic"]}, {"alias": "OptimizationHint", "isClass": false, "isFlag": true, "name": "OptimizationHints", "values": ["OptimizationDefault", "OptimizationStatic"]}], "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "activeInputHandler", "notify": "activeInputHandlerChanged", "read": "activeInputHandler", "required": false, "scriptable": true, "stored": true, "type": "QAbstract3DInputHandler*", "user": false, "write": "setActiveInputHandler"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "activeTheme", "notify": "activeThemeChanged", "read": "activeTheme", "required": false, "scriptable": true, "stored": true, "type": "Q3DTheme*", "user": false, "write": "setActiveTheme"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "selectionMode", "notify": "selectionMode<PERSON>hanged", "read": "selectionMode", "required": false, "scriptable": true, "stored": true, "type": "SelectionFlags", "user": false, "write": "setSelectionMode"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "shadowQuality", "notify": "shadowQualityChanged", "read": "shadowQuality", "required": false, "scriptable": true, "stored": true, "type": "ShadowQuality", "user": false, "write": "setShadowQuality"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "scene", "read": "scene", "required": false, "scriptable": true, "stored": true, "type": "Q3DScene*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "measureFps", "notify": "measureFpsChanged", "read": "measureFps", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMeasureFps"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "currentFps", "notify": "currentFpsChanged", "read": "currentFps", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "orthoProjection", "notify": "orthoProjectionChanged", "read": "isOrthoProjection", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setOrthoProjection"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "selectedElement", "notify": "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "selectedElement", "required": false, "scriptable": true, "stored": true, "type": "ElementType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "aspectRatio", "notify": "aspectRatioChanged", "read": "aspectRatio", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setAspectRatio"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "optimizationHints", "notify": "optimizationHintsChanged", "read": "optimizationHints", "required": false, "scriptable": true, "stored": true, "type": "OptimizationHints", "user": false, "write": "setOptimizationHints"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "polar", "notify": "polarChanged", "read": "isPolar", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPolar"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "radialLabelOffset", "notify": "radialLabelOffsetChanged", "read": "radialLabelOffset", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadialLabelOffset"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "horizontalAspectRatio", "notify": "horizontalAspectRatioChanged", "read": "horizontalAspectRatio", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setHorizontalAspectRatio"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "reflection", "notify": "reflectionChanged", "read": "isReflection", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReflection"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "reflectivity", "notify": "reflectivityChanged", "read": "reflectivity", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setReflectivity"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "locale", "notify": "localeChanged", "read": "locale", "required": false, "scriptable": true, "stored": true, "type": "QLocale", "user": false, "write": "setLocale"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "queriedGraphPosition", "notify": "queriedGraphPositionChanged", "read": "queriedGraphPosition", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "margin", "notify": "marginChanged", "read": "margin", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "QAbstract3DGraph", "signals": [{"access": "public", "arguments": [{"name": "inputHandler", "type": "QAbstract3DInputHandler*"}], "index": 0, "name": "activeInputHandlerChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "theme", "type": "Q3DTheme*"}], "index": 1, "name": "activeThemeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "QAbstract3DGraph::SelectionFlags"}], "index": 2, "name": "selectionMode<PERSON>hanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "quality", "type": "QAbstract3DGraph::ShadowQuality"}], "index": 3, "name": "shadowQualityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "type", "type": "QAbstract3DGraph::ElementType"}], "index": 4, "name": "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 5, "name": "measureFpsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fps", "type": "qreal"}], "index": 6, "name": "currentFpsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 7, "name": "orthoProjectionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ratio", "type": "qreal"}], "index": 8, "name": "aspectRatioChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "hints", "type": "QAbstract3DGraph::OptimizationHints"}], "index": 9, "name": "optimizationHintsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 10, "name": "polarChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "offset", "type": "float"}], "index": 11, "name": "radialLabelOffsetChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ratio", "type": "qreal"}], "index": 12, "name": "horizontalAspectRatioChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 13, "name": "reflectionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reflectivity", "type": "qreal"}], "index": 14, "name": "reflectivityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "locale", "type": "QLocale"}], "index": 15, "name": "localeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QVector3D"}], "index": 16, "name": "queriedGraphPositionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "margin", "type": "qreal"}], "index": 17, "name": "marginChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QWindow"}, {"access": "protected", "name": "QOpenGLFunctions"}]}], "inputFile": "qabstract3dgraph.h", "outputRevision": 69}, {"classes": [{"className": "QAbstract3DGraphPrivate", "lineNumber": 30, "object": true, "qualifiedClassName": "QAbstract3DGraphPrivate", "slots": [{"access": "public", "index": 0, "name": "renderLater", "returnType": "void"}, {"access": "public", "index": 1, "name": "renderNow", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 2, "name": "handleAxisXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 3, "name": "handleAxisYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "axis", "type": "QAbstract3DAxis*"}], "index": 4, "name": "handleAxisZChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstract3dgraph_p.h", "outputRevision": 69}, {"classes": [{"className": "Scatter3DController", "lineNumber": 37, "object": true, "qualifiedClassName": "Scatter3DController", "signals": [{"access": "public", "arguments": [{"name": "series", "type": "QScatter3DSeries*"}], "index": 0, "name": "selectedSeriesChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 1, "name": "handleArrayReset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 2, "name": "handleItemsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 3, "name": "handleItemsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 4, "name": "handleItemsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 5, "name": "handleItemsInserted", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Abstract3DController"}]}], "inputFile": "scatter3dcontroller_p.h", "outputRevision": 69}, {"classes": [{"className": "Scatter3DRenderer", "lineNumber": 31, "object": true, "qualifiedClassName": "Scatter3DRenderer", "slots": [{"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "series", "type": "QScatter3DSeries*"}], "index": 0, "name": "updateSelectedItem", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Abstract3<PERSON><PERSON><PERSON>"}]}], "inputFile": "scatter3drenderer_p.h", "outputRevision": 69}, {"classes": [{"className": "SelectionPointer", "lineNumber": 27, "object": true, "qualifiedClassName": "SelectionPointer", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "protected", "name": "QOpenGLFunctions"}]}], "inputFile": "selectionpointer_p.h", "outputRevision": 69}, {"classes": [{"className": "Surface3DController", "lineNumber": 42, "object": true, "qualifiedClassName": "Surface3DController", "signals": [{"access": "public", "arguments": [{"name": "series", "type": "QSurface3DSeries*"}], "index": 0, "name": "selectedSeriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "flip", "type": "bool"}], "index": 1, "name": "flipHorizontalGridChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 2, "name": "handleArrayReset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 3, "name": "handleRowsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 4, "name": "handleRowsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 5, "name": "handleRowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 6, "name": "handleRowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rowIndex", "type": "int"}, {"name": "columnIndex", "type": "int"}], "index": 7, "name": "handleItemChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "supported", "type": "bool"}], "index": 8, "name": "handleFlatShadingSupportedChange", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Abstract3DController"}]}], "inputFile": "surface3dcontroller_p.h", "outputRevision": 69}, {"classes": [{"className": "Surface3<PERSON><PERSON><PERSON>", "lineNumber": 27, "object": true, "qualifiedClassName": "Surface3<PERSON><PERSON><PERSON>", "signals": [{"access": "public", "arguments": [{"name": "supported", "type": "bool"}], "index": 0, "name": "flatShadingSupportedChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "Abstract3<PERSON><PERSON><PERSON>"}]}], "inputFile": "surface3drenderer_p.h", "outputRevision": 69}, {"classes": [{"className": "Q3DTheme", "enums": [{"isClass": false, "isFlag": false, "name": "ColorStyle", "values": ["ColorStyleUniform", "ColorStyleObjectGradient", "ColorStyleRangeGradient"]}, {"isClass": false, "isFlag": false, "name": "Theme", "values": ["ThemeQt", "ThemePrimaryColors", "ThemeDigia", "ThemeStoneMoss", "ThemeArmyBlue", "ThemeRetro", "ThemeEbony", "ThemeIsabelle", "ThemeUserDefined"]}], "lineNumber": 17, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "type", "notify": "typeChanged", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "Theme", "user": false, "write": "setType"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "baseColors", "notify": "baseColorsChanged", "read": "baseColors", "required": false, "scriptable": true, "stored": true, "type": "QList<QColor>", "user": false, "write": "setBaseColors"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "backgroundColor", "notify": "backgroundColorChanged", "read": "backgroundColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setBackgroundColor"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "windowColor", "notify": "windowColorChanged", "read": "windowColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setWindowColor"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "labelTextColor", "notify": "labelTextColorChanged", "read": "labelTextColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLabelTextColor"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "labelBackgroundColor", "notify": "labelBackgroundColorChanged", "read": "labelBackgroundColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLabelBackgroundColor"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "gridLineColor", "notify": "gridLineColorChanged", "read": "gridLineColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setGridLineColor"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "singleHighlightColor", "notify": "singleHighlightColorChanged", "read": "singleHighlightColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSingleHighlightColor"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "multiHighlightColor", "notify": "multiHighlightColorChanged", "read": "multiHighlightColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setMultiHighlightColor"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "lightColor", "notify": "lightColorChanged", "read": "lightColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setLightColor"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "baseGradients", "notify": "baseGradientsChanged", "read": "baseGradients", "required": false, "scriptable": true, "stored": true, "type": "QList<QLinearGradient>", "user": false, "write": "setBaseGradients"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "singleHighlightGradient", "notify": "singleHighlightGradientChanged", "read": "singleHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "QLinearGradient", "user": false, "write": "setSingleHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "multiHighlightGradient", "notify": "multiHighlightGradientChanged", "read": "multiHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "QLinearGradient", "user": false, "write": "setMultiHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "lightStrength", "notify": "lightStrengthChanged", "read": "lightStrength", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setLightStrength"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "ambientLightStrength", "notify": "ambientLightStrengthChanged", "read": "ambientLightStrength", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setAmbientLightStrength"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "highlightLightStrength", "notify": "highlightLightStrengthChanged", "read": "highlightLightStrength", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setHighlightLightStrength"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "labelBorderEnabled", "notify": "labelBorderEnabledChanged", "read": "isLabelBorderEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLabelBorderEnabled"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "backgroundEnabled", "notify": "backgroundEnabledChanged", "read": "isBackgroundEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBackgroundEnabled"}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "gridEnabled", "notify": "gridEnabledChanged", "read": "isGridEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setGridEnabled"}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "labelBackgroundEnabled", "notify": "labelBackgroundEnabledChanged", "read": "isLabelBackgroundEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setLabelBackgroundEnabled"}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "colorStyle", "notify": "colorStyleChanged", "read": "colorStyle", "required": false, "scriptable": true, "stored": true, "type": "ColorStyle", "user": false, "write": "setColorStyle"}], "qualifiedClassName": "Q3DTheme", "signals": [{"access": "public", "arguments": [{"name": "themeType", "type": "Q3DTheme::Theme"}], "index": 0, "name": "typeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "colors", "type": "QList<QColor>"}], "index": 1, "name": "baseColorsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 2, "name": "backgroundColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 3, "name": "windowColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 4, "name": "labelTextColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 5, "name": "labelBackgroundColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 6, "name": "gridLineColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 7, "name": "singleHighlightColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 8, "name": "multiHighlightColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 9, "name": "lightColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradients", "type": "QList<QLinearGradient>"}], "index": 10, "name": "baseGradientsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QLinearGradient"}], "index": 11, "name": "singleHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QLinearGradient"}], "index": 12, "name": "multiHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "strength", "type": "float"}], "index": 13, "name": "lightStrengthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "strength", "type": "float"}], "index": 14, "name": "ambientLightStrengthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "strength", "type": "float"}], "index": 15, "name": "highlightLightStrengthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 16, "name": "labelBorderEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "index": 17, "name": "fontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 18, "name": "backgroundEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 19, "name": "gridEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 20, "name": "labelBackgroundEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "style", "type": "Q3DTheme::ColorStyle"}], "index": 21, "name": "colorStyleChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "q3dtheme.h", "outputRevision": 69}, {"classes": [{"className": "Q3DThemePrivate", "lineNumber": 73, "object": true, "qualifiedClassName": "Q3DThemePrivate", "signals": [{"access": "public", "index": 0, "name": "needRender", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "q3dtheme_p.h", "outputRevision": 69}, {"classes": [{"className": "ThemeManager", "lineNumber": 23, "object": true, "qualifiedClassName": "ThemeManager", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "thememanager_p.h", "outputRevision": 69}, {"classes": [{"className": "QAbstract3DAxis", "enums": [{"isClass": false, "isFlag": false, "name": "AxisOrientation", "values": ["AxisOrientationNone", "AxisOrientationX", "AxisOrientationY", "AxisOrientationZ"]}, {"isClass": false, "isFlag": false, "name": "AxisType", "values": ["AxisTypeNone", "AxisTypeCategory", "AxisTypeValue"]}], "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "title", "notify": "titleChanged", "read": "title", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTitle"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "labels", "notify": "labelsChanged", "read": "labels", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "orientation", "notify": "orientationChanged", "read": "orientation", "required": false, "scriptable": true, "stored": true, "type": "AxisOrientation", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "type", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "AxisType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "min", "notify": "minC<PERSON>ed", "read": "min", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMin"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "max", "notify": "max<PERSON><PERSON>ed", "read": "max", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMax"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "autoAdjustRange", "notify": "autoAdjustRangeChanged", "read": "isAutoAdjustRange", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoAdjustRange"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "labelAutoRotation", "notify": "labelAutoRotationChanged", "read": "labelAutoRotation", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setLabelAutoRotation"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "titleVisible", "notify": "titleVisibilityChanged", "read": "isTitleVisible", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setTitleVisible"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "titleFixed", "notify": "titleFixed<PERSON>hanged", "read": "isTitleFixed", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setTitleFixed"}], "qualifiedClassName": "QAbstract3DAxis", "signals": [{"access": "public", "arguments": [{"name": "newTitle", "type": "QString"}], "index": 0, "name": "titleChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "labelsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "orientation", "type": "QAbstract3DAxis::AxisOrientation"}], "index": 2, "name": "orientationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "index": 3, "name": "minC<PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "index": 4, "name": "max<PERSON><PERSON>ed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "min", "type": "float"}, {"name": "max", "type": "float"}], "index": 5, "name": "rangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "autoAdjust", "type": "bool"}], "index": 6, "name": "autoAdjustRangeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "angle", "type": "float"}], "index": 7, "name": "labelAutoRotationChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "index": 8, "name": "titleVisibilityChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "fixed", "type": "bool"}], "index": 9, "name": "titleFixed<PERSON>hanged", "returnType": "void", "revision": 257}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstract3daxis.h", "outputRevision": 69}, {"classes": [{"className": "QAbstract3DAxisPrivate", "lineNumber": 22, "object": true, "qualifiedClassName": "QAbstract3DAxisPrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstract3daxis_p.h", "outputRevision": 69}, {"classes": [{"className": "QCategory3DAxis", "lineNumber": 13, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "labels", "notify": "labelsChanged", "read": "labels", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "QCategory3DAxis", "signals": [{"access": "public", "index": 0, "name": "labelsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstract3DAxis"}]}], "inputFile": "qcategory3daxis.h", "outputRevision": 69}, {"classes": [{"className": "QCategory3DAxisPrivate", "lineNumber": 22, "object": true, "qualifiedClassName": "QCategory3DAxisPrivate", "superClasses": [{"access": "public", "name": "QAbstract3DAxisPrivate"}]}], "inputFile": "qcategory3daxis_p.h", "outputRevision": 69}, {"classes": [{"className": "QLogValue3DAxisFormatter", "lineNumber": 13, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "base", "notify": "baseChanged", "read": "base", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setBase"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "autoSubGrid", "notify": "autoSubGridChanged", "read": "autoSubGrid", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoSubGrid"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "showEdgeLabels", "notify": "showEdgeLabelsChanged", "read": "showEdgeLabels", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setShowEdgeLabels"}], "qualifiedClassName": "QLogValue3DAxisFormatter", "signals": [{"access": "public", "arguments": [{"name": "base", "type": "qreal"}], "index": 0, "name": "baseChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 1, "name": "autoSubGridChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 2, "name": "showEdgeLabelsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QValue3DAxisFormatter"}]}], "inputFile": "qlogvalue3daxisformatter.h", "outputRevision": 69}, {"classes": [{"className": "QLogValue3DAxisFormatterPrivate", "lineNumber": 23, "object": true, "qualifiedClassName": "QLogValue3DAxisFormatterPrivate", "superClasses": [{"access": "public", "name": "QValue3DAxisFormatterPrivate"}]}], "inputFile": "qlogvalue3daxisformatter_p.h", "outputRevision": 69}, {"classes": [{"className": "QValue3DAxis", "lineNumber": 14, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "segmentCount", "notify": "segmentCountChanged", "read": "segmentCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSegmentCount"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "subSegmentCount", "notify": "subSegmentCountChanged", "read": "subSegmentCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSubSegmentCount"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "labelFormat", "notify": "labelFormatChanged", "read": "labelFormat", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setLabelFormat"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "formatter", "notify": "formatterChanged", "read": "formatter", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QValue3DAxisFormatter*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "reversed", "notify": "reversedChanged", "read": "reversed", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReversed"}], "qualifiedClassName": "QValue3DAxis", "signals": [{"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 0, "name": "segmentCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 1, "name": "subSegmentCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "format", "type": "QString"}], "index": 2, "name": "labelFormatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "formatter", "type": "QValue3DAxisFormatter*"}], "index": 3, "name": "formatterChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 4, "name": "reversedChanged", "returnType": "void", "revision": 257}], "superClasses": [{"access": "public", "name": "QAbstract3DAxis"}]}], "inputFile": "qvalue3daxis.h", "outputRevision": 69}, {"classes": [{"className": "QValue3DAxisPrivate", "lineNumber": 22, "object": true, "qualifiedClassName": "QValue3DAxisPrivate", "signals": [{"access": "public", "index": 0, "name": "formatterDirty", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstract3DAxisPrivate"}]}], "inputFile": "qvalue3daxis_p.h", "outputRevision": 69}, {"classes": [{"className": "QValue3DAxisFormatter", "lineNumber": 19, "object": true, "qualifiedClassName": "QValue3DAxisFormatter", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qvalue3daxisformatter.h", "outputRevision": 69}, {"classes": [{"className": "QValue3DAxisFormatterPrivate", "lineNumber": 26, "object": true, "qualifiedClassName": "QValue3DAxisFormatterPrivate", "slots": [{"access": "public", "index": 0, "name": "markDirtyNoLabelChange", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qvalue3daxisformatter_p.h", "outputRevision": 69}, {"classes": [{"className": "AbstractItemModelHandler", "lineNumber": 24, "object": true, "qualifiedClassName": "AbstractItemModelHandler", "signals": [{"access": "public", "arguments": [{"name": "itemModel", "type": "const QAbstractItemModel*"}], "index": 0, "name": "itemModelChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "index": 1, "name": "handleColumnsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceParent", "type": "QModelIndex"}, {"name": "sourceStart", "type": "int"}, {"name": "sourceEnd", "type": "int"}, {"name": "destinationParent", "type": "QModelIndex"}, {"name": "destinationColumn", "type": "int"}], "index": 2, "name": "handleColumnsMoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "index": 3, "name": "handleColumnsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}, {"name": "roles", "type": "QList<int>"}], "index": 4, "name": "handleDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "index": 5, "isCloned": true, "name": "handleDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parents", "type": "QList<QPersistentModelIndex>"}, {"name": "hint", "type": "QAbstractItemModel::LayoutChangeHint"}], "index": 6, "name": "handleLayoutChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parents", "type": "QList<QPersistentModelIndex>"}], "index": 7, "isCloned": true, "name": "handleLayoutChanged", "returnType": "void"}, {"access": "public", "index": 8, "isCloned": true, "name": "handleLayoutChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "handleModelReset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "index": 10, "name": "handleRowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sourceParent", "type": "QModelIndex"}, {"name": "sourceStart", "type": "int"}, {"name": "sourceEnd", "type": "int"}, {"name": "destinationParent", "type": "QModelIndex"}, {"name": "destinationRow", "type": "int"}], "index": 11, "name": "handleRowsMoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "index": 12, "name": "handleRowsRemoved", "returnType": "void"}, {"access": "public", "index": 13, "name": "handleMappingChanged", "returnType": "void"}, {"access": "public", "index": 14, "name": "handlePendingResolve", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "abstractitemmodelhandler_p.h", "outputRevision": 69}, {"classes": [{"className": "BarItemModelHandler", "lineNumber": 22, "object": true, "qualifiedClassName": "BarItemModelHandler", "slots": [{"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}, {"name": "roles", "type": "QList<int>"}], "index": 0, "name": "handleDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "index": 1, "isCloned": true, "name": "handleDataChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "AbstractItemModelHandler"}]}], "inputFile": "baritemmodelhandler_p.h", "outputRevision": 69}, {"classes": [{"className": "QAbstract3DSeries", "enums": [{"isClass": false, "isFlag": false, "name": "SeriesType", "values": ["SeriesTypeNone", "SeriesTypeBar", "SeriesTypeScatter", "SeriesTypeSurface"]}, {"isClass": false, "isFlag": false, "name": "<PERSON><PERSON>", "values": ["MeshUserDefined", "MeshBar", "MeshCube", "MeshPyramid", "MeshCone", "Mesh<PERSON><PERSON><PERSON>", "MeshBevelBar", "MeshBevelCube", "MeshSphere", "MeshMinimal", "MeshArrow", "MeshPoint"]}], "lineNumber": 18, "methods": [{"access": "public", "arguments": [{"name": "axis", "type": "QVector3D"}, {"name": "angle", "type": "float"}], "index": 16, "name": "setMeshAxisAndAngle", "returnType": "void"}], "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "type", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "SeriesType", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "itemLabelFormat", "notify": "itemLabelFormatChanged", "read": "itemLabelFormat", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setItemLabelFormat"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "visible", "notify": "visibilityChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "mesh", "notify": "meshChanged", "read": "mesh", "required": false, "scriptable": true, "stored": true, "type": "<PERSON><PERSON>", "user": false, "write": "<PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "meshSmooth", "notify": "meshSmoothChanged", "read": "isMeshSmooth", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setMeshSmooth"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "meshRotation", "notify": "meshRotationChanged", "read": "meshRotation", "required": false, "scriptable": true, "stored": true, "type": "QQuaternion", "user": false, "write": "setMeshRotation"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "userDefinedMesh", "notify": "userDefinedMeshChanged", "read": "userDefinedMesh", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setUserDefinedMesh"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "colorStyle", "notify": "colorStyleChanged", "read": "colorStyle", "required": false, "scriptable": true, "stored": true, "type": "Q3DTheme::ColorStyle", "user": false, "write": "setColorStyle"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "baseColor", "notify": "baseColorChanged", "read": "baseColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setBaseColor"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "baseGradient", "notify": "baseGradientChanged", "read": "baseGradient", "required": false, "scriptable": true, "stored": true, "type": "QLinearGradient", "user": false, "write": "setBaseGradient"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "singleHighlightColor", "notify": "singleHighlightColorChanged", "read": "singleHighlightColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSingleHighlightColor"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "singleHighlightGradient", "notify": "singleHighlightGradientChanged", "read": "singleHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "QLinearGradient", "user": false, "write": "setSingleHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "multiHighlightColor", "notify": "multiHighlightColorChanged", "read": "multiHighlightColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setMultiHighlightColor"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "multiHighlightGradient", "notify": "multiHighlightGradientChanged", "read": "multiHighlightGradient", "required": false, "scriptable": true, "stored": true, "type": "QLinearGradient", "user": false, "write": "setMultiHighlightGradient"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "itemLabel", "notify": "itemLabelChanged", "read": "itemLabel", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "itemLabelVisible", "notify": "itemLabelVisibilityChanged", "read": "isItemLabelVisible", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setItemLabelVisible"}], "qualifiedClassName": "QAbstract3DSeries", "signals": [{"access": "public", "arguments": [{"name": "format", "type": "QString"}], "index": 0, "name": "itemLabelFormatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "index": 1, "name": "visibilityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mesh", "type": "QAbstract3DSeries::Mesh"}], "index": 2, "name": "meshChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 3, "name": "meshSmoothChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rotation", "type": "QQuaternion"}], "index": 4, "name": "meshRotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fileName", "type": "QString"}], "index": 5, "name": "userDefinedMeshChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "style", "type": "Q3DTheme::ColorStyle"}], "index": 6, "name": "colorStyleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 7, "name": "baseColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QLinearGradient"}], "index": 8, "name": "baseGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 9, "name": "singleHighlightColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QLinearGradient"}], "index": 10, "name": "singleHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 11, "name": "multiHighlightColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "gradient", "type": "QLinearGradient"}], "index": 12, "name": "multiHighlightGradientChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 13, "name": "nameChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "label", "type": "QString"}], "index": 14, "name": "itemLabelChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "index": 15, "name": "itemLabelVisibilityChanged", "returnType": "void", "revision": 257}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstract3dseries.h", "outputRevision": 69}, {"classes": [{"className": "QAbstract3DSeriesPrivate", "lineNumber": 83, "object": true, "qualifiedClassName": "QAbstract3DSeriesPrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstract3dseries_p.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractDataProxy", "enums": [{"isClass": false, "isFlag": false, "name": "DataType", "values": ["DataTypeNone", "DataTypeBar", "DataTypeScatter", "DataTypeSurface"]}], "lineNumber": 15, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "type", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "DataType", "user": false}], "qualifiedClassName": "QAbstractDataProxy", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractdataproxy.h", "outputRevision": 69}, {"classes": [{"className": "QAbstractDataProxyPrivate", "lineNumber": 24, "object": true, "qualifiedClassName": "QAbstractDataProxyPrivate", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractdataproxy_p.h", "outputRevision": 69}, {"classes": [{"className": "QBar3DSeries", "lineNumber": 15, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "dataProxy", "notify": "dataProxyChanged", "read": "dataProxy", "required": false, "scriptable": true, "stored": true, "type": "QBarDataProxy*", "user": false, "write": "setDataProxy"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "<PERSON><PERSON><PERSON>", "notify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QPoint", "user": false, "write": "setSelectedBar"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "meshAngle", "notify": "meshAngleChanged", "read": "meshAngle", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMeshAngle"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "rowColors", "notify": "rowColorsChanged", "read": "rowColors", "required": false, "revision": 1539, "scriptable": true, "stored": true, "type": "QList<QColor>", "user": false, "write": "setRowColors"}], "qualifiedClassName": "QBar3DSeries", "signals": [{"access": "public", "arguments": [{"name": "proxy", "type": "QBarDataProxy*"}], "index": 0, "name": "dataProxyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QPoint"}], "index": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "angle", "type": "float"}], "index": 2, "name": "meshAngleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rowcolors", "type": "QList<QColor>"}], "index": 3, "name": "rowColorsChanged", "returnType": "void", "revision": 1539}], "superClasses": [{"access": "public", "name": "QAbstract3DSeries"}]}], "inputFile": "qbar3dseries.h", "outputRevision": 69}, {"classes": [{"className": "QBar3DSeriesPrivate", "lineNumber": 22, "object": true, "qualifiedClassName": "QBar3DSeriesPrivate", "superClasses": [{"access": "public", "name": "QAbstract3DSeriesPrivate"}]}], "inputFile": "qbar3dseries_p.h", "outputRevision": 69}, {"classes": [{"className": "QBarDataProxy", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rowCount", "notify": "rowCountChanged", "read": "rowCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "notify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "set<PERSON>ow<PERSON>abe<PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "columnLabels", "notify": "columnLabelsChanged", "read": "columnLabels", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setColumnLabels"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "series", "notify": "seriesChanged", "read": "series", "required": false, "scriptable": true, "stored": true, "type": "QBar3DSeries*", "user": false}], "qualifiedClassName": "QBarDataProxy", "signals": [{"access": "public", "index": 0, "name": "arrayReset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 1, "name": "rowsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 2, "name": "rowsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 3, "name": "rowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 4, "name": "rowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rowIndex", "type": "int"}, {"name": "columnIndex", "type": "int"}], "index": 5, "name": "itemChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 6, "name": "rowCountChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 8, "name": "columnLabelsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QBar3DSeries*"}], "index": 9, "name": "seriesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractDataProxy"}]}], "inputFile": "qbardataproxy.h", "outputRevision": 69}, {"classes": [{"className": "QBarDataProxyPrivate", "lineNumber": 22, "object": true, "qualifiedClassName": "QBarDataProxyPrivate", "superClasses": [{"access": "public", "name": "QAbstractDataProxyPrivate"}]}], "inputFile": "qbardataproxy_p.h", "outputRevision": 69}, {"classes": [{"className": "QCustom3DItem", "lineNumber": 17, "methods": [{"access": "public", "arguments": [{"name": "axis", "type": "QVector3D"}, {"name": "angle", "type": "float"}], "index": 9, "name": "setRotationAxisAndAngle", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "meshFile", "notify": "meshFileChanged", "read": "meshFile", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setMeshFile"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "textureFile", "notify": "textureFileChanged", "read": "textureFile", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTextureFile"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "positionAbsolute", "notify": "positionAbsoluteChanged", "read": "isPositionAbsolute", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPositionAbsolute"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "scaling", "notify": "scalingChanged", "read": "scaling", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setScaling"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "rotation", "notify": "rotationChanged", "read": "rotation", "required": false, "scriptable": true, "stored": true, "type": "QQuaternion", "user": false, "write": "setRotation"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "visible", "notify": "visibleChanged", "read": "isVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "shadowCasting", "notify": "shadowCastingChanged", "read": "isShadowCasting", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setShadowCasting"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "scalingAbsolute", "notify": "scalingAbsoluteChanged", "read": "isScalingAbsolute", "required": false, "revision": 258, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setScalingAbsolute"}], "qualifiedClassName": "QCustom3DItem", "signals": [{"access": "public", "arguments": [{"name": "meshFile", "type": "QString"}], "index": 0, "name": "meshFileChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureFile", "type": "QString"}], "index": 1, "name": "textureFileChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QVector3D"}], "index": 2, "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "positionAbsolute", "type": "bool"}], "index": 3, "name": "positionAbsoluteChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scaling", "type": "QVector3D"}], "index": 4, "name": "scalingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rotation", "type": "QQuaternion"}], "index": 5, "name": "rotationChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "index": 6, "name": "visibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shadowCasting", "type": "bool"}], "index": 7, "name": "shadowCastingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scalingAbsolute", "type": "bool"}], "index": 8, "name": "scalingAbsoluteChanged", "returnType": "void", "revision": 258}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qcustom3ditem.h", "outputRevision": 69}, {"classes": [{"className": "QCustom3DItemPrivate", "lineNumber": 43, "object": true, "qualifiedClassName": "QCustom3DItemPrivate", "signals": [{"access": "public", "index": 0, "name": "needUpdate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qcustom3ditem_p.h", "outputRevision": 69}, {"classes": [{"className": "QCustom3DLabel", "lineNumber": 18, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "text", "notify": "textChanged", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "textColor", "notify": "textColorChanged", "read": "textColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setTextColor"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "backgroundColor", "notify": "backgroundColorChanged", "read": "backgroundColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setBackgroundColor"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "borderEnabled", "notify": "borderEnabledChanged", "read": "isBorderEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBorderEnabled"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "backgroundEnabled", "notify": "backgroundEnabledChanged", "read": "isBackgroundEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setBackgroundEnabled"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "facingCamera", "notify": "facingCameraChanged", "read": "isFacingCamera", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFacingCamera"}], "qualifiedClassName": "QCustom3DLabel", "signals": [{"access": "public", "arguments": [{"name": "text", "type": "QString"}], "index": 0, "name": "textChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "font", "type": "QFont"}], "index": 1, "name": "fontChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 2, "name": "textColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 3, "name": "backgroundColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 4, "name": "borderEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 5, "name": "backgroundEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 6, "name": "facingCameraChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QCustom3DItem"}]}], "inputFile": "qcustom3dlabel.h", "outputRevision": 69}, {"classes": [{"className": "QCustom3DLabelPrivate", "lineNumber": 22, "object": true, "qualifiedClassName": "QCustom3DLabelPrivate", "superClasses": [{"access": "public", "name": "QCustom3DItemPrivate"}]}], "inputFile": "qcustom3dlabel_p.h", "outputRevision": 69}, {"classes": [{"className": "QCustom3DVolume", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "textureWidth", "notify": "textureWidthChanged", "read": "textureWidth", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTextureWidth"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "textureHeight", "notify": "textureHeightChanged", "read": "textureHeight", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTextureHeight"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "textureDepth", "notify": "textureDepthChanged", "read": "textureDepth", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTextureDepth"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "sliceIndexX", "notify": "sliceIndexXChanged", "read": "sliceIndexX", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSliceIndexX"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "sliceIndexY", "notify": "sliceIndexYChanged", "read": "sliceIndexY", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSliceIndexY"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "sliceIndexZ", "notify": "sliceIndexZChanged", "read": "sliceIndexZ", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSliceIndexZ"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "colorTable", "notify": "colorTableChanged", "read": "colorTable", "required": false, "scriptable": true, "stored": true, "type": "QList<QRgb>", "user": false, "write": "setColorTable"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "textureData", "notify": "textureDataChanged", "read": "textureData", "required": false, "scriptable": true, "stored": true, "type": "QList<uchar>*", "user": false, "write": "setTextureData"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "alphaMultiplier", "notify": "alphaMultiplierChanged", "read": "alphaMultiplier", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setAlphaMultiplier"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "preserveOpacity", "notify": "preserveOpacityChanged", "read": "preserveOpacity", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setPreserveOpacity"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "useHighDefShader", "notify": "useHighDefShaderChanged", "read": "useHighDefShader", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setUseHighDefShader"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "drawSlices", "notify": "drawSlicesChanged", "read": "drawSlices", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setDrawSlices"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "drawSliceFrames", "notify": "drawSliceFramesChanged", "read": "drawSliceFrames", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setDrawSliceFrames"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "sliceFrameColor", "notify": "sliceFrameColorChanged", "read": "sliceFrameColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSliceFrameColor"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "sliceFrameWidths", "notify": "sliceFrameWidthsChanged", "read": "sliceFrameWidths", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setSliceFrameWidths"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "sliceFrameGaps", "notify": "sliceFrameGapsChanged", "read": "sliceFrameGaps", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setSliceFrameGaps"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "sliceFrameThicknesses", "notify": "sliceFrameThicknessesChanged", "read": "sliceFrameThicknesses", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setSliceFrameThicknesses"}], "qualifiedClassName": "QCustom3DVolume", "signals": [{"access": "public", "arguments": [{"name": "value", "type": "int"}], "index": 0, "name": "textureWidthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "int"}], "index": 1, "name": "textureHeightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "int"}], "index": 2, "name": "textureDepthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "int"}], "index": 3, "name": "sliceIndexXChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "int"}], "index": 4, "name": "sliceIndexYChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "int"}], "index": 5, "name": "sliceIndexZChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "colorTableChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QList<uchar>*"}], "index": 7, "name": "textureDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "format", "type": "QImage::Format"}], "index": 8, "name": "textureFormatChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mult", "type": "float"}], "index": 9, "name": "alphaMultiplierChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 10, "name": "preserveOpacityChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 11, "name": "useHighDefShaderChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 12, "name": "drawSlicesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 13, "name": "drawSliceFramesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 14, "name": "sliceFrameColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "values", "type": "QVector3D"}], "index": 15, "name": "sliceFrameWidthsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "values", "type": "QVector3D"}], "index": 16, "name": "sliceFrameGapsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "values", "type": "QVector3D"}], "index": 17, "name": "sliceFrameThicknessesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QCustom3DItem"}]}], "inputFile": "qcustom3dvolume.h", "outputRevision": 69}, {"classes": [{"className": "QCustom3DVolumePrivate", "lineNumber": 43, "object": true, "qualifiedClassName": "QCustom3DVolumePrivate", "superClasses": [{"access": "public", "name": "QCustom3DItemPrivate"}]}], "inputFile": "qcustom3dvolume_p.h", "outputRevision": 69}, {"classes": [{"className": "QHeightMapSurfaceDataProxy", "lineNumber": 15, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "heightMap", "notify": "heightMapChanged", "read": "heightMap", "required": false, "scriptable": true, "stored": true, "type": "QImage", "user": false, "write": "setHeightMap"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "heightMapFile", "notify": "heightMapFileChanged", "read": "heightMapFile", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setHeightMapFile"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "minXValue", "notify": "minXValueChanged", "read": "minXValue", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMinXValue"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "maxXValue", "notify": "maxXValueChanged", "read": "maxXValue", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMaxXValue"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "minZValue", "notify": "minZValueChanged", "read": "minZValue", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMinZValue"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "maxZValue", "notify": "maxZ<PERSON><PERSON>ue<PERSON>hanged", "read": "maxZValue", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMaxZValue"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "minYValue", "notify": "minYValueChanged", "read": "minYValue", "required": false, "revision": 1539, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMinYValue"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "maxYValue", "notify": "maxYValueChanged", "read": "maxYValue", "required": false, "revision": 1539, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setMaxYValue"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "autoScaleY", "notify": "autoScaleYChanged", "read": "autoScaleY", "required": false, "revision": 1539, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoScaleY"}], "qualifiedClassName": "QHeightMapSurfaceDataProxy", "signals": [{"access": "public", "arguments": [{"name": "image", "type": "QImage"}], "index": 0, "name": "heightMapChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "filename", "type": "QString"}], "index": 1, "name": "heightMapFileChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "index": 2, "name": "minXValueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "index": 3, "name": "maxXValueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "index": 4, "name": "minZValueChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "index": 5, "name": "maxZ<PERSON><PERSON>ue<PERSON>hanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "index": 6, "name": "minYValueChanged", "returnType": "void", "revision": 1539}, {"access": "public", "arguments": [{"name": "value", "type": "float"}], "index": 7, "name": "maxYValueChanged", "returnType": "void", "revision": 1539}, {"access": "public", "arguments": [{"name": "enabled", "type": "bool"}], "index": 8, "name": "autoScaleYChanged", "returnType": "void", "revision": 1539}], "superClasses": [{"access": "public", "name": "QSurfaceDataProxy"}]}], "inputFile": "qheightmapsurfacedataproxy.h", "outputRevision": 69}, {"classes": [{"className": "QHeightMapSurfaceDataProxyPrivate", "lineNumber": 23, "object": true, "qualifiedClassName": "QHeightMapSurfaceDataProxyPrivate", "superClasses": [{"access": "public", "name": "QSurfaceDataProxyPrivate"}]}], "inputFile": "qheightmapsurfacedataproxy_p.h", "outputRevision": 69}, {"classes": [{"className": "QItemModelBarDataProxy", "enums": [{"isClass": false, "isFlag": false, "name": "MultiMatchBehavior", "values": ["MMBFirst", "MMBLast", "MMBAverage", "MMBCumulative"]}], "lineNumber": 15, "methods": [{"access": "public", "arguments": [{"name": "category", "type": "QString"}], "index": 19, "name": "rowCategoryIndex", "returnType": "int"}, {"access": "public", "arguments": [{"name": "category", "type": "QString"}], "index": 20, "name": "columnCategoryIndex", "returnType": "int"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "itemModel", "notify": "itemModelChanged", "read": "itemModel", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setItemModel"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "rowRole", "notify": "rowRoleChanged", "read": "rowRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRowRole"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "columnRole", "notify": "columnRoleChanged", "read": "columnRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setColumnRole"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "valueRole", "notify": "valueRoleChanged", "read": "valueRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setValueRole"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "rotationRole", "notify": "rotationRoleChanged", "read": "rotationRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRotationRole"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "rowCategories", "notify": "rowCategoriesChanged", "read": "rowCategories", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setRowCategories"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "columnCategories", "notify": "columnCategoriesChanged", "read": "columnCategories", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setColumnCategories"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "useModelCategories", "notify": "useModelCategoriesChanged", "read": "useModelCategories", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setUseModelCategories"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "autoRowCategories", "notify": "autoRowCategoriesChanged", "read": "autoRowCategories", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoRowCategories"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "autoColumnCategories", "notify": "autoColumnCategoriesChanged", "read": "autoColumnCategories", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoColumnCategories"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "rowRolePattern", "notify": "rowRolePatternChanged", "read": "rowRolePattern", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setRowRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "columnRolePattern", "notify": "columnRolePatternChanged", "read": "columnRolePattern", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setColumnRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "valueRolePattern", "notify": "valueRolePatternChanged", "read": "valueRolePattern", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setValueRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "rotationRolePattern", "notify": "rotationRolePatternChanged", "read": "rotationRolePattern", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setRotationRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "row<PERSON><PERSON><PERSON><PERSON>lace", "notify": "rowRoleReplaceChanged", "read": "row<PERSON><PERSON><PERSON><PERSON>lace", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRowRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "columnRole<PERSON>eplace", "notify": "columnRoleReplaceChanged", "read": "columnRole<PERSON>eplace", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setColumnRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "valueRoleReplace", "notify": "valueRoleReplaceChanged", "read": "valueRoleReplace", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "set<PERSON><PERSON><PERSON><PERSON>oleReplace"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "rotation<PERSON><PERSON><PERSON><PERSON><PERSON>", "notify": "rotationRoleReplaceChanged", "read": "rotation<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRotationRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "multiMatchBehavior", "notify": "multiMatchBehaviorChanged", "read": "multiMatchBehavior", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "MultiMatchBehavior", "user": false, "write": "setMultiMatchBehavior"}], "qualifiedClassName": "QItemModelBarDataProxy", "signals": [{"access": "public", "arguments": [{"name": "itemModel", "type": "const QAbstractItemModel*"}], "index": 0, "name": "itemModelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "index": 1, "name": "rowRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "index": 2, "name": "columnRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "index": 3, "name": "valueRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "index": 4, "name": "rotationRoleChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "rowCategoriesChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "columnCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 7, "name": "useModelCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 8, "name": "autoRowCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 9, "name": "autoColumnCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "index": 10, "name": "rowRolePatternChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "index": 11, "name": "columnRolePatternChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "index": 12, "name": "valueRolePatternChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "index": 13, "name": "rotationRolePatternChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "index": 14, "name": "rowRoleReplaceChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "index": 15, "name": "columnRoleReplaceChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "index": 16, "name": "valueRoleReplaceChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "index": 17, "name": "rotationRoleReplaceChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "behavior", "type": "MultiMatchBehavior"}], "index": 18, "name": "multiMatchBehaviorChanged", "returnType": "void", "revision": 257}], "superClasses": [{"access": "public", "name": "QBarDataProxy"}]}], "inputFile": "qitemmodelbardataproxy.h", "outputRevision": 69}, {"classes": [{"className": "QItemModelBarDataProxyPrivate", "lineNumber": 24, "object": true, "qualifiedClassName": "QItemModelBarDataProxyPrivate", "superClasses": [{"access": "public", "name": "QBarDataProxyPrivate"}]}], "inputFile": "qitemmodelbardataproxy_p.h", "outputRevision": 69}, {"classes": [{"className": "QItemModelScatterDataProxy", "lineNumber": 16, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "itemModel", "notify": "itemModelChanged", "read": "itemModel", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setItemModel"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "xPosRole", "notify": "xPosRoleChanged", "read": "xPosRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setXPosRole"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "yPosRole", "notify": "yPosRoleChanged", "read": "yPosRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setYPosRole"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "zPosRole", "notify": "zPosRoleChanged", "read": "zPosRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setZPosRole"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "rotationRole", "notify": "rotationRoleChanged", "read": "rotationRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRotationRole"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "xPosRolePattern", "notify": "xPosRolePatternChanged", "read": "xPosRolePattern", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setXPosRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "yPosRolePattern", "notify": "yPosRolePatternChanged", "read": "yPosRolePattern", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setYPosRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "zPosRolePattern", "notify": "zPosRolePatternChanged", "read": "zPosRolePattern", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setZPosRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "rotationRolePattern", "notify": "rotationRolePatternChanged", "read": "rotationRolePattern", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setRotationRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "xPosRoleReplace", "notify": "xPosRoleReplaceChanged", "read": "xPosRoleReplace", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setXPosRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "yPosRoleReplace", "notify": "yPosRoleReplaceChanged", "read": "yPosRoleReplace", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setYPosRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "zPosRoleReplace", "notify": "zPosRoleReplaceChanged", "read": "zPosRoleReplace", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setZPosRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "rotation<PERSON><PERSON><PERSON><PERSON><PERSON>", "notify": "rotationRoleReplaceChanged", "read": "rotation<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRotationRoleReplace"}], "qualifiedClassName": "QItemModelScatterDataProxy", "signals": [{"access": "public", "arguments": [{"name": "itemModel", "type": "const QAbstractItemModel*"}], "index": 0, "name": "itemModelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "index": 1, "name": "xPosRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "index": 2, "name": "yPosRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "index": 3, "name": "zPosRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "index": 4, "name": "rotationRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "index": 5, "name": "xPosRolePatternChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "index": 6, "name": "yPosRolePatternChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "index": 7, "name": "zPosRolePatternChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "index": 8, "name": "rotationRolePatternChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "index": 9, "name": "rotationRoleReplaceChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "index": 10, "name": "xPosRoleReplaceChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "index": 11, "name": "yPosRoleReplaceChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "index": 12, "name": "zPosRoleReplaceChanged", "returnType": "void", "revision": 257}], "superClasses": [{"access": "public", "name": "QScatterDataProxy"}]}], "inputFile": "qitemmodelscatterdataproxy.h", "outputRevision": 69}, {"classes": [{"className": "QItemModelScatterDataProxyPrivate", "lineNumber": 24, "object": true, "qualifiedClassName": "QItemModelScatterDataProxyPrivate", "superClasses": [{"access": "public", "name": "QScatterDataProxyPrivate"}]}], "inputFile": "qitemmodelscatterdataproxy_p.h", "outputRevision": 69}, {"classes": [{"className": "QItemModelSurfaceDataProxy", "enums": [{"isClass": false, "isFlag": false, "name": "MultiMatchBehavior", "values": ["MMBFirst", "MMBLast", "MMBAverage", "MMBCumulativeY"]}], "lineNumber": 16, "methods": [{"access": "public", "arguments": [{"name": "category", "type": "QString"}], "index": 22, "name": "rowCategoryIndex", "returnType": "int"}, {"access": "public", "arguments": [{"name": "category", "type": "QString"}], "index": 23, "name": "columnCategoryIndex", "returnType": "int"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "itemModel", "notify": "itemModelChanged", "read": "itemModel", "required": false, "scriptable": true, "stored": true, "type": "QAbstractItemModel*", "user": false, "write": "setItemModel"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "rowRole", "notify": "rowRoleChanged", "read": "rowRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRowRole"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "columnRole", "notify": "columnRoleChanged", "read": "columnRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setColumnRole"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "xPosRole", "notify": "xPosRoleChanged", "read": "xPosRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setXPosRole"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "yPosRole", "notify": "yPosRoleChanged", "read": "yPosRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setYPosRole"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "zPosRole", "notify": "zPosRoleChanged", "read": "zPosRole", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setZPosRole"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "rowCategories", "notify": "rowCategoriesChanged", "read": "rowCategories", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setRowCategories"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "columnCategories", "notify": "columnCategoriesChanged", "read": "columnCategories", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setColumnCategories"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "useModelCategories", "notify": "useModelCategoriesChanged", "read": "useModelCategories", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setUseModelCategories"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "autoRowCategories", "notify": "autoRowCategoriesChanged", "read": "autoRowCategories", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoRowCategories"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "autoColumnCategories", "notify": "autoColumnCategoriesChanged", "read": "autoColumnCategories", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoColumnCategories"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "rowRolePattern", "notify": "rowRolePatternChanged", "read": "rowRolePattern", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setRowRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "columnRolePattern", "notify": "columnRolePatternChanged", "read": "columnRolePattern", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setColumnRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "xPosRolePattern", "notify": "xPosRolePatternChanged", "read": "xPosRolePattern", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setXPosRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "yPosRolePattern", "notify": "yPosRolePatternChanged", "read": "yPosRolePattern", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setYPosRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "zPosRolePattern", "notify": "zPosRolePatternChanged", "read": "zPosRolePattern", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QRegularExpression", "user": false, "write": "setZPosRolePattern"}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "row<PERSON><PERSON><PERSON><PERSON>lace", "notify": "rowRoleReplaceChanged", "read": "row<PERSON><PERSON><PERSON><PERSON>lace", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRowRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "columnRole<PERSON>eplace", "notify": "columnRoleReplaceChanged", "read": "columnRole<PERSON>eplace", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setColumnRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "xPosRoleReplace", "notify": "xPosRoleReplaceChanged", "read": "xPosRoleReplace", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setXPosRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "yPosRoleReplace", "notify": "yPosRoleReplaceChanged", "read": "yPosRoleReplace", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setYPosRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "zPosRoleReplace", "notify": "zPosRoleReplaceChanged", "read": "zPosRoleReplace", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setZPosRoleReplace"}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "multiMatchBehavior", "notify": "multiMatchBehaviorChanged", "read": "multiMatchBehavior", "required": false, "revision": 257, "scriptable": true, "stored": true, "type": "MultiMatchBehavior", "user": false, "write": "setMultiMatchBehavior"}], "qualifiedClassName": "QItemModelSurfaceDataProxy", "signals": [{"access": "public", "arguments": [{"name": "itemModel", "type": "const QAbstractItemModel*"}], "index": 0, "name": "itemModelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "index": 1, "name": "rowRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "index": 2, "name": "columnRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "index": 3, "name": "xPosRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "index": 4, "name": "yPosRoleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "role", "type": "QString"}], "index": 5, "name": "zPosRoleChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "rowCategoriesChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "columnCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 8, "name": "useModelCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 9, "name": "autoRowCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 10, "name": "autoColumnCategoriesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "index": 11, "name": "rowRolePatternChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "index": 12, "name": "columnRolePatternChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "index": 13, "name": "xPosRolePatternChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "index": 14, "name": "yPosRolePatternChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "pattern", "type": "QRegularExpression"}], "index": 15, "name": "zPosRolePatternChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "index": 16, "name": "rowRoleReplaceChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "index": 17, "name": "columnRoleReplaceChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "index": 18, "name": "xPosRoleReplaceChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "index": 19, "name": "yPosRoleReplaceChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "replace", "type": "QString"}], "index": 20, "name": "zPosRoleReplaceChanged", "returnType": "void", "revision": 257}, {"access": "public", "arguments": [{"name": "behavior", "type": "MultiMatchBehavior"}], "index": 21, "name": "multiMatchBehaviorChanged", "returnType": "void", "revision": 257}], "superClasses": [{"access": "public", "name": "QSurfaceDataProxy"}]}], "inputFile": "qitemmodelsurfacedataproxy.h", "outputRevision": 69}, {"classes": [{"className": "QItemModelSurfaceDataProxyPrivate", "lineNumber": 24, "object": true, "qualifiedClassName": "QItemModelSurfaceDataProxyPrivate", "superClasses": [{"access": "public", "name": "QSurfaceDataProxyPrivate"}]}], "inputFile": "qitemmodelsurfacedataproxy_p.h", "outputRevision": 69}, {"classes": [{"className": "QScatter3DSeries", "lineNumber": 14, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "dataProxy", "notify": "dataProxyChanged", "read": "dataProxy", "required": false, "scriptable": true, "stored": true, "type": "QScatterDataProxy*", "user": false, "write": "setDataProxy"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "selectedItem", "notify": "selectedItemChanged", "read": "selectedItem", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSelectedItem"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "itemSize", "notify": "itemSizeChanged", "read": "itemSize", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setItemSize"}], "qualifiedClassName": "QScatter3DSeries", "signals": [{"access": "public", "arguments": [{"name": "proxy", "type": "QScatterDataProxy*"}], "index": 0, "name": "dataProxyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 1, "name": "selectedItemChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "size", "type": "float"}], "index": 2, "name": "itemSizeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstract3DSeries"}]}], "inputFile": "qscatter3dseries.h", "outputRevision": 69}, {"classes": [{"className": "QScatter3DSeriesPrivate", "lineNumber": 22, "object": true, "qualifiedClassName": "QScatter3DSeriesPrivate", "superClasses": [{"access": "public", "name": "QAbstract3DSeriesPrivate"}]}], "inputFile": "qscatter3dseries_p.h", "outputRevision": 69}, {"classes": [{"className": "QScatterDataProxy", "lineNumber": 19, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "itemCount", "notify": "itemCountChanged", "read": "itemCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "series", "notify": "seriesChanged", "read": "series", "required": false, "scriptable": true, "stored": true, "type": "QScatter3DSeries*", "user": false}], "qualifiedClassName": "QScatterDataProxy", "signals": [{"access": "public", "index": 0, "name": "arrayReset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 1, "name": "itemsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 2, "name": "itemsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 3, "name": "itemsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 4, "name": "itemsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 5, "name": "itemCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QScatter3DSeries*"}], "index": 6, "name": "seriesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractDataProxy"}]}], "inputFile": "qscatterdataproxy.h", "outputRevision": 69}, {"classes": [{"className": "QScatterDataProxyPrivate", "lineNumber": 25, "object": true, "qualifiedClassName": "QScatterDataProxyPrivate", "superClasses": [{"access": "public", "name": "QAbstractDataProxyPrivate"}]}], "inputFile": "qscatterdataproxy_p.h", "outputRevision": 69}, {"classes": [{"className": "QSurface3DSeries", "enums": [{"isClass": false, "isFlag": false, "name": "DrawFlag", "values": ["DrawWireframe", "DrawSurface", "DrawSurfaceAndWireframe"]}, {"alias": "DrawFlag", "isClass": false, "isFlag": true, "name": "DrawFlags", "values": ["DrawWireframe", "DrawSurface", "DrawSurfaceAndWireframe"]}], "lineNumber": 14, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "dataProxy", "notify": "dataProxyChanged", "read": "dataProxy", "required": false, "scriptable": true, "stored": true, "type": "QSurfaceDataProxy*", "user": false, "write": "setDataProxy"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "selectedPoint", "notify": "selectedPointChanged", "read": "selectedPoint", "required": false, "scriptable": true, "stored": true, "type": "QPoint", "user": false, "write": "setSelectedPoint"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "flatShadingEnabled", "notify": "flatShadingEnabledChanged", "read": "isFlatShadingEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setFlatShadingEnabled"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "flatShadingSupported", "notify": "flatShadingSupportedChanged", "read": "isFlatShadingSupported", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "drawMode", "notify": "drawModeChanged", "read": "drawMode", "required": false, "scriptable": true, "stored": true, "type": "DrawFlags", "user": false, "write": "setDrawMode"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "texture", "notify": "textureChanged", "read": "texture", "required": false, "scriptable": true, "stored": true, "type": "QImage", "user": false, "write": "setTexture"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "textureFile", "notify": "textureFileChanged", "read": "textureFile", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setTextureFile"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "wireframeColor", "notify": "wireframeColorChanged", "read": "wireframeColor", "required": false, "revision": 1539, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setWireframeColor"}], "qualifiedClassName": "QSurface3DSeries", "signals": [{"access": "public", "arguments": [{"name": "proxy", "type": "QSurfaceDataProxy*"}], "index": 0, "name": "dataProxyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QPoint"}], "index": 1, "name": "selectedPointChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 2, "name": "flatShadingEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 3, "name": "flatShadingSupportedChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "mode", "type": "QSurface3DSeries::DrawFlags"}], "index": 4, "name": "drawModeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "image", "type": "QImage"}], "index": 5, "name": "textureChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "filename", "type": "QString"}], "index": 6, "name": "textureFileChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 7, "name": "wireframeColorChanged", "returnType": "void", "revision": 1539}], "superClasses": [{"access": "public", "name": "QAbstract3DSeries"}]}], "inputFile": "qsurface3dseries.h", "outputRevision": 69}, {"classes": [{"className": "QSurface3DSeriesPrivate", "lineNumber": 22, "object": true, "qualifiedClassName": "QSurface3DSeriesPrivate", "superClasses": [{"access": "public", "name": "QAbstract3DSeriesPrivate"}]}], "inputFile": "qsurface3dseries_p.h", "outputRevision": 69}, {"classes": [{"className": "QSurfaceDataProxy", "lineNumber": 20, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rowCount", "notify": "rowCountChanged", "read": "rowCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "columnCount", "notify": "columnCountChanged", "read": "columnCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "series", "notify": "seriesChanged", "read": "series", "required": false, "scriptable": true, "stored": true, "type": "QSurface3DSeries*", "user": false}], "qualifiedClassName": "QSurfaceDataProxy", "signals": [{"access": "public", "index": 0, "name": "arrayReset", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 1, "name": "rowsAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 2, "name": "rowsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 3, "name": "rowsRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "startIndex", "type": "int"}, {"name": "count", "type": "int"}], "index": 4, "name": "rowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rowIndex", "type": "int"}, {"name": "columnIndex", "type": "int"}], "index": 5, "name": "itemChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 6, "name": "rowCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 7, "name": "columnCountChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "series", "type": "QSurface3DSeries*"}], "index": 8, "name": "seriesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractDataProxy"}]}], "inputFile": "qsurfacedataproxy.h", "outputRevision": 69}, {"classes": [{"className": "QSurfaceDataProxyPrivate", "lineNumber": 24, "object": true, "qualifiedClassName": "QSurfaceDataProxyPrivate", "superClasses": [{"access": "public", "name": "QAbstractDataProxyPrivate"}]}], "inputFile": "qsurfacedataproxy_p.h", "outputRevision": 69}, {"classes": [{"className": "ScatterItemModelHandler", "lineNumber": 22, "object": true, "qualifiedClassName": "ScatterItemModelHandler", "slots": [{"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}, {"name": "roles", "type": "QList<int>"}], "index": 0, "name": "handleDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "index": 1, "isCloned": true, "name": "handleDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "index": 2, "name": "handleRowsInserted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "parent", "type": "QModelIndex"}, {"name": "start", "type": "int"}, {"name": "end", "type": "int"}], "index": 3, "name": "handleRowsRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "AbstractItemModelHandler"}]}], "inputFile": "scatteritemmodelhandler_p.h", "outputRevision": 69}, {"classes": [{"className": "SurfaceItemModelHandler", "lineNumber": 22, "object": true, "qualifiedClassName": "SurfaceItemModelHandler", "slots": [{"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}, {"name": "roles", "type": "QList<int>"}], "index": 0, "name": "handleDataChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "topLeft", "type": "QModelIndex"}, {"name": "bottomRight", "type": "QModelIndex"}], "index": 1, "isCloned": true, "name": "handleDataChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "AbstractItemModelHandler"}]}], "inputFile": "surfaceitemmodelhandler_p.h", "outputRevision": 69}, {"classes": [{"className": "Q3DInputHandler", "lineNumber": 13, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "rotationEnabled", "notify": "rotationEnabledChanged", "read": "isRotationEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setRotationEnabled"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "zoomEnabled", "notify": "zoomEnabledChanged", "read": "isZoomEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setZoomEnabled"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "selectionEnabled", "notify": "selection<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "isSelectionEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSelectionEnabled"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "zoomAtTargetEnabled", "notify": "zoomAtTargetEnabledChanged", "read": "isZoomAtTargetEnabled", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setZoomAtTargetEnabled"}], "qualifiedClassName": "Q3DInputHandler", "signals": [{"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 0, "name": "rotationEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 1, "name": "zoomEnabledChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 2, "name": "selection<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "arguments": [{"name": "enable", "type": "bool"}], "index": 3, "name": "zoomAtTargetEnabledChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstract3DInputHandler"}]}], "inputFile": "q3dinputhandler.h", "outputRevision": 69}, {"classes": [{"className": "Q3DInputHandlerPrivate", "lineNumber": 24, "object": true, "qualifiedClassName": "Q3DInputHandlerPrivate", "slots": [{"access": "public", "arguments": [{"name": "scene", "type": "Q3DScene*"}], "index": 0, "name": "handleSceneChange", "returnType": "void"}, {"access": "public", "index": 1, "name": "handleQueriedGraphPositionChange", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "q3dinputhandler_p.h", "outputRevision": 69}, {"classes": [{"className": "QAbstract3DInputHandler", "enums": [{"isClass": false, "isFlag": false, "name": "InputView", "values": ["InputViewNone", "InputViewOnPrimary", "InputViewOnSecondary"]}], "lineNumber": 19, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "inputView", "notify": "inputViewChanged", "read": "inputView", "required": false, "scriptable": true, "stored": true, "type": "InputView", "user": false, "write": "setInputView"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "inputPosition", "notify": "positionChanged", "read": "inputPosition", "required": false, "scriptable": true, "stored": true, "type": "QPoint", "user": false, "write": "setInputPosition"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "scene", "notify": "sceneChanged", "read": "scene", "required": false, "scriptable": true, "stored": true, "type": "Q3DScene*", "user": false, "write": "setScene"}], "qualifiedClassName": "QAbstract3DInputHandler", "signals": [{"access": "public", "arguments": [{"name": "position", "type": "QPoint"}], "index": 0, "name": "positionChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "view", "type": "QAbstract3DInputHandler::InputView"}], "index": 1, "name": "inputViewChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scene", "type": "Q3DScene*"}], "index": 2, "name": "sceneChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstract3dinputhandler.h", "outputRevision": 69}, {"classes": [{"className": "QTouch3DInputHandler", "lineNumber": 13, "object": true, "qualifiedClassName": "QTouch3DInputHandler", "superClasses": [{"access": "public", "name": "Q3DInputHandler"}]}], "inputFile": "qtouch3dinputhandler.h", "outputRevision": 69}, {"classes": [{"className": "QTouch3DInputHandlerPrivate", "lineNumber": 26, "object": true, "qualifiedClassName": "QTouch3DInputHandlerPrivate", "superClasses": [{"access": "public", "name": "Q3DInputHandlerPrivate"}]}], "inputFile": "qtouch3dinputhandler_p.h", "outputRevision": 69}]