<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="pytest in TestBuildVolume.py"  type="tests" factoryName="py.test" nameIsGenerated="true">
    <module name="Cura" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <envs>
      <env name="PYTHONUNBUFFERED" value="1" />
      <env name="CURA_RESOURCES" value="C:\Users\<USER>\.conan2\p\b\cura_f4136a7fa62af\p\res" />
      <env name="CURA_ENGINE_SEARCH_PATH" value="C:\Users\<USER>\.conan2\p\b\cura_f4136a7fa62af\p\res\extruders" />
      <env name="PYTHONPATH" value="$PROJECT_DIR$\build\generators\cura_venv\Lib\site-packages;C:\Users\<USER>\.conan2\p\b\urani4f3b56a88dfef\p\plugins;C:\Users\<USER>\.conan2\p\b\urani4f3b56a88dfef\p\site-packages;C:\Users\<USER>\.conan2\p\b\pyarceb17c3a0b4179\p\lib;C:\Users\<USER>\.conan2\p\b\dulci916b52e078a5a\p\lib\pyDulcificum;C:\Users\<USER>\.conan2\p\b\pysavb7d270904410a\p\lib;C:\Users\<USER>\.conan2\p\b\pynescbb9442ee73ab\p\lib" />
      <env name="PATH" value="C:\Users\<USER>\.conan2\p\b\arcus03cb8757a37eb\p\bin;C:\Users\<USER>\.conan2\p\b\savit55e0cf679ac9a\p\bin;C:\Users\<USER>\.conan2\p\b\nest275ecece7e9cfe\p\bin;C:\Users\<USER>\.conan2\p\b\nlopt1a158b713fb53\p\bin;C:\Users\<USER>\.conan2\p\b\clipp0167ed8a6fefa\p\bin;C:\Users\<USER>\.conan2\p\b\cpythc127f0102dd85\p\bin;c:\Users\<USER>\.vscode\extensions\ms-python.python-2025.8.0-win32-arm64\python_files\deactivate\powershell;C:\Mac\Home\Desktop\CuraProject\cura_venv_windows\Scripts;c:\Users\<USER>\.vscode\extensions\ms-python.python-2025.8.0-win32-arm64\python_files\deactivate\powershell;C:\Mac\Home\Desktop\CuraProject\cura_venv_windows\Scripts;C:\Program Files\Parallels\Parallels Tools\Applications;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\STMicroelectronics\st_toolset\asm;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\CMake\bin;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Programs\Python\Python311-arm64\include\cpython;c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages;C:\Program Files\STMicroelectronics\STM32Cube\STM32CubeProgrammer\bin;C:\Program Files\dotnet\x64;C:\Program Files (x86)\COSMIC\FSE_Compilers\CXSTM8;C:\Program Files (x86)\COSMIC\FSE_Compilers\CXSTM32;C:\Program Files\nodejs\;C:\gcc-arm-none-eabi-10.3-2021.10\bin;C:\OpenOCD-20240916-0.12.0\drivers;C:\OpenOCD-20240916-0.12.0\bin;C:\w64devkit\bin;C:\Program Files (x86)\STMicroelectro;C:\Users\<USER>\AppData\Local\Programs\Python\Python311-arm64\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311-arm64\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2023.3.3\bin;;c:\users\<USER>\appdata\local\programs\python\python310\lib\site-packages;C:\Users\<USER>\AppData\Local\Programs\Python\Python311-arm64\include\cpython;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\dotnet\x64;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\JetBrains\PyCharm 2025.1.2\bin;;C:\Users\<USER>\.conan2\p\b\cpythc127f0102dd85\p\bin;windows\arduino\amd64;windows\arduino\CP210x_6.7.4;windows\arduino\FTDI USB Drivers\amd64" />
      <env name="PYTHON" value="C:\Users\<USER>\.conan2\p\b\cpythc127f0102dd85\p\bin\python.exe" />
      <env name="PYTHON_ROOT" value="C:\Users\<USER>\.conan2\p\b\cpythc127f0102dd85\p" />
    </envs>
    <option name="SDK_HOME" value="$PROJECT_DIR$\build\generators\cura_venv\Scripts\python.exe" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/tests" />
    <option name="IS_MODULE_SDK" value="true" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
    <option name="_new_keywords" value="&quot;&quot;" />
    <option name="_new_parameters" value="&quot;&quot;" />
    <option name="_new_additionalArguments" value="&quot;&quot;" />
    <option name="_new_target" value="&quot;$PROJECT_DIR$/tests/TestBuildVolume.py&quot;" />
    <option name="_new_targetType" value="&quot;PATH&quot;" />
    <method v="2" />
  </configuration>
</component>