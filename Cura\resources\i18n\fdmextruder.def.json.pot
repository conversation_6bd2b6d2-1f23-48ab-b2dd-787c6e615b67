#
msgid ""
msgstr ""
"Project-Id-Version: Uranium json setting files\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2023-06-08 16:32+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"






msgctxt "machine_settings label"
msgid "Machine"
msgstr ""

msgctxt "machine_settings description"
msgid "Machine specific settings"
msgstr ""

msgctxt "extruder_nr label"
msgid "Extruder"
msgstr ""

msgctxt "extruder_nr description"
msgid "The extruder train used for printing. This is used in multi-extrusion."
msgstr ""

msgctxt "extruder_prime_pos_z label"
msgid "Extruder Prime Z Position"
msgstr ""

msgctxt "extruder_prime_pos_z description"
msgid "The Z coordinate of the position where the nozzle primes at the start of printing."
msgstr ""

msgctxt "machine_extruder_cooling_fan_number label"
msgid "Extruder Print Cooling Fan"
msgstr ""

msgctxt "machine_extruder_cooling_fan_number description"
msgid "The number of the print cooling fan associated with this extruder. Only change this from the default value of 0 when you have a different print cooling fan for each extruder."
msgstr ""

msgctxt "machine_extruder_end_code label"
msgid "Extruder End G-Code"
msgstr ""

msgctxt "machine_extruder_end_code description"
msgid "End g-code to execute when switching away from this extruder."
msgstr ""

msgctxt "machine_extruder_end_pos_abs label"
msgid "Extruder End Position Absolute"
msgstr ""

msgctxt "machine_extruder_end_pos_abs description"
msgid "Make the extruder ending position absolute rather than relative to the last-known location of the head."
msgstr ""

msgctxt "machine_extruder_end_pos_x label"
msgid "Extruder End Position X"
msgstr ""

msgctxt "machine_extruder_end_pos_x description"
msgid "The x-coordinate of the ending position when turning the extruder off."
msgstr ""

msgctxt "machine_extruder_end_pos_y label"
msgid "Extruder End Position Y"
msgstr ""

msgctxt "machine_extruder_end_pos_y description"
msgid "The y-coordinate of the ending position when turning the extruder off."
msgstr ""

msgctxt "machine_extruder_start_code label"
msgid "Extruder Start G-Code"
msgstr ""

msgctxt "machine_extruder_start_code description"
msgid "Start g-code to execute when switching to this extruder."
msgstr ""

msgctxt "machine_extruder_start_pos_abs label"
msgid "Extruder Start Position Absolute"
msgstr ""

msgctxt "machine_extruder_start_pos_abs description"
msgid "Make the extruder starting position absolute rather than relative to the last-known location of the head."
msgstr ""

msgctxt "machine_extruder_start_pos_x label"
msgid "Extruder Start Position X"
msgstr ""

msgctxt "machine_extruder_start_pos_x description"
msgid "The x-coordinate of the starting position when turning the extruder on."
msgstr ""

msgctxt "machine_extruder_start_pos_y label"
msgid "Extruder Start Position Y"
msgstr ""

msgctxt "machine_extruder_start_pos_y description"
msgid "The y-coordinate of the starting position when turning the extruder on."
msgstr ""

msgctxt "machine_nozzle_id label"
msgid "Nozzle ID"
msgstr ""

msgctxt "machine_nozzle_id description"
msgid "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\"."
msgstr ""

msgctxt "machine_nozzle_offset_x label"
msgid "Nozzle X Offset"
msgstr ""

msgctxt "machine_nozzle_offset_x description"
msgid "The x-coordinate of the offset of the nozzle."
msgstr ""

msgctxt "machine_nozzle_offset_y label"
msgid "Nozzle Y Offset"
msgstr ""

msgctxt "machine_nozzle_offset_y description"
msgid "The y-coordinate of the offset of the nozzle."
msgstr ""

msgctxt "machine_nozzle_size label"
msgid "Nozzle Diameter"
msgstr ""

msgctxt "machine_nozzle_size description"
msgid "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size."
msgstr ""

msgctxt "material label"
msgid "Material"
msgstr ""

msgctxt "material description"
msgid "Material"
msgstr ""

msgctxt "material_diameter label"
msgid "Diameter"
msgstr ""

msgctxt "material_diameter description"
msgid "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament."
msgstr ""

msgctxt "platform_adhesion label"
msgid "Build Plate Adhesion"
msgstr ""

msgctxt "platform_adhesion description"
msgid "Adhesion"
msgstr ""

msgctxt "extruder_prime_pos_x label"
msgid "Extruder Prime X Position"
msgstr ""

msgctxt "extruder_prime_pos_x description"
msgid "The X coordinate of the position where the nozzle primes at the start of printing."
msgstr ""

msgctxt "extruder_prime_pos_y label"
msgid "Extruder Prime Y Position"
msgstr ""

msgctxt "extruder_prime_pos_y description"
msgid "The Y coordinate of the position where the nozzle primes at the start of printing."
msgstr ""

