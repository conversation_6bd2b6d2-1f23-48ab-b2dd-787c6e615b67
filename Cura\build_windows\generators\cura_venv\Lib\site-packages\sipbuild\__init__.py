# SPDX-License-Identifier: BSD-2-Clause

# Copyright (c) 2024 <PERSON> <<EMAIL>>


# Publish the public API.
from .abstract_builder import AbstractBuilder
from .abstract_project import AbstractProject
from .bindings import Bindings
from .buildable import (Buildable, BuildableBindings, BuildableExecutable,
        BuildableFromSources, BuildableModule)
from .builder import Builder
from .configurable import Option
from .exceptions import handle_exception, UserException
from .installable import Installable
from .project import Project
from .pyproject import (PyProjectOptionException,
        PyProjectUndefinedOptionException)
from .setuptools_builder import SetuptoolsBuilder
from .version import SIP_VERSION, SIP_VERSION_STR

# This is deprecated so allow it to fail.
try:
    from .distutils_builder import DistutilsBuilder
except ImportError:
    pass
