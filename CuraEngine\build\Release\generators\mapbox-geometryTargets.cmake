# Load the debug and release variables
file(GLOB DATA_FILES "${CMAKE_CURRENT_LIST_DIR}/mapbox-geometry-*-data.cmake")

foreach(f ${DATA_FILES})
    include(${f})
endforeach()

# Create the targets for all the components
foreach(_COMPONENT ${mapbox-geometry_COMPONENT_NAMES} )
    if(NOT TARGET ${_COMPONENT})
        add_library(${_COMPONENT} INTERFACE IMPORTED)
        message(${mapbox-geometry_MESSAGE_MODE} "Conan: Component target declared '${_COMPONENT}'")
    endif()
endforeach()

if(NOT TARGET mapbox-geometry::mapbox-geometry)
    add_library(mapbox-geometry::mapbox-geometry INTERFACE IMPORTED)
    message(${mapbox-geometry_MESSAGE_MODE} "Conan: Target declared 'mapbox-geometry::mapbox-geometry'")
endif()
# Load the debug and release library finders
file(GLOB CONFIG_FILES "${CMAKE_CURRENT_LIST_DIR}/mapbox-geometry-Target-*.cmake")

foreach(f ${CONFIG_FILES})
    include(${f})
endforeach()