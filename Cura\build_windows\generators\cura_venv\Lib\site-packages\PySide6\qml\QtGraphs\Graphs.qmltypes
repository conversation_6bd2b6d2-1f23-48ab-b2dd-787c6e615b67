import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "q3dscene.h"
        name: "Q3DScene"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtGraphs/Scene3D 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property {
            name: "viewport"
            type: "QRect"
            read: "viewport"
            notify: "viewportChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "primarySubViewport"
            type: "QRect"
            read: "primarySubViewport"
            write: "setPrimarySubViewport"
            notify: "primarySubViewportChanged"
            index: 1
        }
        Property {
            name: "secondarySubViewport"
            type: "QRect"
            read: "secondarySubViewport"
            write: "setSecondarySubViewport"
            notify: "secondarySubViewportChanged"
            index: 2
        }
        Property {
            name: "selectionQueryPosition"
            type: "QPoint"
            read: "selectionQueryPosition"
            write: "setSelectionQueryPosition"
            notify: "selectionQueryPositionChanged"
            index: 3
        }
        Property {
            name: "secondarySubviewOnTop"
            type: "bool"
            read: "isSecondarySubviewOnTop"
            write: "setSecondarySubviewOnTop"
            notify: "secondarySubviewOnTopChanged"
            index: 4
        }
        Property {
            name: "slicingActive"
            type: "bool"
            read: "isSlicingActive"
            write: "setSlicingActive"
            notify: "slicingActiveChanged"
            index: 5
        }
        Property {
            name: "devicePixelRatio"
            type: "double"
            read: "devicePixelRatio"
            write: "setDevicePixelRatio"
            notify: "devicePixelRatioChanged"
            index: 6
        }
        Property {
            name: "graphPositionQuery"
            type: "QPoint"
            read: "graphPositionQuery"
            write: "setGraphPositionQuery"
            notify: "graphPositionQueryChanged"
            index: 7
        }
        Property {
            name: "invalidSelectionPoint"
            type: "QPoint"
            read: "invalidSelectionPoint"
            index: 8
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "viewportChanged"
            Parameter { name: "viewport"; type: "QRect" }
        }
        Signal {
            name: "primarySubViewportChanged"
            Parameter { name: "subViewport"; type: "QRect" }
        }
        Signal {
            name: "secondarySubViewportChanged"
            Parameter { name: "subViewport"; type: "QRect" }
        }
        Signal {
            name: "secondarySubviewOnTopChanged"
            Parameter { name: "isSecondaryOnTop"; type: "bool" }
        }
        Signal {
            name: "slicingActiveChanged"
            Parameter { name: "isSlicingActive"; type: "bool" }
        }
        Signal {
            name: "devicePixelRatioChanged"
            Parameter { name: "pixelRatio"; type: "double" }
        }
        Signal {
            name: "selectionQueryPositionChanged"
            Parameter { name: "position"; type: "QPoint" }
        }
        Signal {
            name: "graphPositionQueryChanged"
            Parameter { name: "position"; type: "QPoint" }
        }
        Signal { name: "needRender" }
    }
    Component {
        file: "qabstract3daxis.h"
        name: "QAbstract3DAxis"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtGraphs/Abstract3DAxis 6.0",
            "QtGraphs/Abstract3DAxis 6.9"
        ]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536, 1545]
        Enum {
            name: "AxisOrientation"
            isScoped: true
            values: ["None", "X", "Y", "Z"]
        }
        Enum {
            name: "AxisType"
            isScoped: true
            values: ["None", "Category", "Value"]
        }
        Property {
            name: "title"
            type: "QString"
            read: "title"
            write: "setTitle"
            notify: "titleChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "labels"
            type: "QStringList"
            read: "labels"
            write: "setLabels"
            notify: "labelsChanged"
            index: 1
        }
        Property {
            name: "labelsVisible"
            type: "bool"
            read: "labelsVisible"
            write: "setLabelsVisible"
            notify: "labelVisibleChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "orientation"
            type: "QAbstract3DAxis::AxisOrientation"
            read: "orientation"
            notify: "orientationChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "type"
            type: "QAbstract3DAxis::AxisType"
            read: "type"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "min"
            type: "float"
            read: "min"
            write: "setMin"
            notify: "minChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "max"
            type: "float"
            read: "max"
            write: "setMax"
            notify: "maxChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "autoAdjustRange"
            type: "bool"
            read: "isAutoAdjustRange"
            write: "setAutoAdjustRange"
            notify: "autoAdjustRangeChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "labelAutoAngle"
            type: "float"
            read: "labelAutoAngle"
            write: "setLabelAutoAngle"
            notify: "labelAutoAngleChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "scaleLabelsByCount"
            revision: 1545
            type: "bool"
            read: "isScaleLabelsByCount"
            write: "setScaleLabelsByCount"
            notify: "scaleLabelsByCountChanged"
            index: 9
        }
        Property {
            name: "labelSize"
            revision: 1545
            type: "double"
            read: "labelSize"
            write: "setLabelSize"
            notify: "labelSizeChanged"
            index: 10
        }
        Property {
            name: "titleVisible"
            type: "bool"
            read: "isTitleVisible"
            write: "setTitleVisible"
            notify: "titleVisibleChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "titleFixed"
            type: "bool"
            read: "isTitleFixed"
            write: "setTitleFixed"
            notify: "titleFixedChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "titleOffset"
            type: "float"
            read: "titleOffset"
            write: "setTitleOffset"
            notify: "titleOffsetChanged"
            index: 13
            isFinal: true
        }
        Signal {
            name: "titleChanged"
            Parameter { name: "newTitle"; type: "QString" }
        }
        Signal { name: "labelsChanged" }
        Signal {
            name: "orientationChanged"
            Parameter { name: "orientation"; type: "QAbstract3DAxis::AxisOrientation" }
        }
        Signal {
            name: "minChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "maxChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "rangeChanged"
            Parameter { name: "min"; type: "float" }
            Parameter { name: "max"; type: "float" }
        }
        Signal {
            name: "autoAdjustRangeChanged"
            Parameter { name: "autoAdjust"; type: "bool" }
        }
        Signal {
            name: "scaleLabelsByCountChanged"
            revision: 1545
            Parameter { name: "adjust"; type: "bool" }
        }
        Signal {
            name: "labelSizeChanged"
            revision: 1545
            Parameter { name: "size"; type: "double" }
        }
        Signal {
            name: "labelAutoAngleChanged"
            Parameter { name: "angle"; type: "float" }
        }
        Signal {
            name: "titleVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "labelVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "titleFixedChanged"
            Parameter { name: "fixed"; type: "bool" }
        }
        Signal {
            name: "titleOffsetChanged"
            Parameter { name: "offset"; type: "float" }
        }
    }
    Component {
        file: "qabstract3dseries.h"
        name: "QAbstract3DSeries"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtGraphs/Abstract3DSeries 6.0"]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "SeriesType"
            isScoped: true
            values: ["None", "Bar", "Scatter", "Surface"]
        }
        Enum {
            name: "Mesh"
            isScoped: true
            values: [
                "UserDefined",
                "Bar",
                "Cube",
                "Pyramid",
                "Cone",
                "Cylinder",
                "BevelBar",
                "BevelCube",
                "Sphere",
                "Minimal",
                "Arrow",
                "Point"
            ]
        }
        Property {
            name: "type"
            type: "QAbstract3DSeries::SeriesType"
            read: "type"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "itemLabelFormat"
            type: "QString"
            read: "itemLabelFormat"
            write: "setItemLabelFormat"
            notify: "itemLabelFormatChanged"
            index: 1
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 2
        }
        Property {
            name: "mesh"
            type: "QAbstract3DSeries::Mesh"
            read: "mesh"
            write: "setMesh"
            notify: "meshChanged"
            index: 3
        }
        Property {
            name: "meshSmooth"
            type: "bool"
            read: "isMeshSmooth"
            write: "setMeshSmooth"
            notify: "meshSmoothChanged"
            index: 4
        }
        Property {
            name: "meshRotation"
            type: "QQuaternion"
            read: "meshRotation"
            write: "setMeshRotation"
            notify: "meshRotationChanged"
            index: 5
        }
        Property {
            name: "userDefinedMesh"
            type: "QString"
            read: "userDefinedMesh"
            write: "setUserDefinedMesh"
            notify: "userDefinedMeshChanged"
            index: 6
        }
        Property {
            name: "colorStyle"
            type: "QGraphsTheme::ColorStyle"
            read: "colorStyle"
            write: "setColorStyle"
            notify: "colorStyleChanged"
            index: 7
        }
        Property {
            name: "baseColor"
            type: "QColor"
            read: "baseColor"
            write: "setBaseColor"
            notify: "baseColorChanged"
            index: 8
        }
        Property {
            name: "baseGradient"
            type: "QLinearGradient"
            read: "baseGradient"
            write: "setBaseGradient"
            notify: "baseGradientChanged"
            index: 9
        }
        Property {
            name: "singleHighlightColor"
            type: "QColor"
            read: "singleHighlightColor"
            write: "setSingleHighlightColor"
            notify: "singleHighlightColorChanged"
            index: 10
        }
        Property {
            name: "singleHighlightGradient"
            type: "QLinearGradient"
            read: "singleHighlightGradient"
            write: "setSingleHighlightGradient"
            notify: "singleHighlightGradientChanged"
            index: 11
        }
        Property {
            name: "multiHighlightColor"
            type: "QColor"
            read: "multiHighlightColor"
            write: "setMultiHighlightColor"
            notify: "multiHighlightColorChanged"
            index: 12
        }
        Property {
            name: "multiHighlightGradient"
            type: "QLinearGradient"
            read: "multiHighlightGradient"
            write: "setMultiHighlightGradient"
            notify: "multiHighlightGradientChanged"
            index: 13
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 14
        }
        Property {
            name: "itemLabel"
            type: "QString"
            read: "itemLabel"
            notify: "itemLabelChanged"
            index: 15
            isReadonly: true
        }
        Property {
            name: "itemLabelVisible"
            type: "bool"
            read: "isItemLabelVisible"
            write: "setItemLabelVisible"
            notify: "itemLabelVisibleChanged"
            index: 16
        }
        Signal {
            name: "itemLabelFormatChanged"
            Parameter { name: "format"; type: "QString" }
        }
        Signal {
            name: "visibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "meshChanged"
            Parameter { name: "mesh"; type: "QAbstract3DSeries::Mesh" }
        }
        Signal {
            name: "meshSmoothChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "meshRotationChanged"
            Parameter { name: "rotation"; type: "QQuaternion" }
        }
        Signal {
            name: "userDefinedMeshChanged"
            Parameter { name: "fileName"; type: "QString" }
        }
        Signal {
            name: "colorStyleChanged"
            Parameter { name: "style"; type: "QGraphsTheme::ColorStyle" }
        }
        Signal {
            name: "baseColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "baseGradientChanged"
            Parameter { name: "gradient"; type: "QLinearGradient" }
        }
        Signal {
            name: "singleHighlightColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "singleHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QLinearGradient" }
        }
        Signal {
            name: "multiHighlightColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "multiHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QLinearGradient" }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Signal {
            name: "itemLabelChanged"
            Parameter { name: "label"; type: "QString" }
        }
        Signal {
            name: "itemLabelVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Method {
            name: "setMeshAxisAndAngle"
            Parameter { name: "axis"; type: "QVector3D" }
            Parameter { name: "angle"; type: "float" }
        }
    }
    Component {
        file: "qabstractanimation.h"
        name: "QAbstractAnimation"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "Direction"
            values: ["Forward", "Backward"]
        }
        Enum {
            name: "State"
            values: ["Stopped", "Paused", "Running"]
        }
        Property {
            name: "state"
            type: "State"
            bindable: "bindableState"
            read: "state"
            notify: "stateChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "loopCount"
            type: "int"
            bindable: "bindableLoopCount"
            read: "loopCount"
            write: "setLoopCount"
            index: 1
        }
        Property {
            name: "currentTime"
            type: "int"
            bindable: "bindableCurrentTime"
            read: "currentTime"
            write: "setCurrentTime"
            index: 2
        }
        Property {
            name: "currentLoop"
            type: "int"
            bindable: "bindableCurrentLoop"
            read: "currentLoop"
            notify: "currentLoopChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "direction"
            type: "Direction"
            bindable: "bindableDirection"
            read: "direction"
            write: "setDirection"
            notify: "directionChanged"
            index: 4
        }
        Property { name: "duration"; type: "int"; read: "duration"; index: 5; isReadonly: true }
        Signal { name: "finished" }
        Signal {
            name: "stateChanged"
            Parameter { name: "newState"; type: "QAbstractAnimation::State" }
            Parameter { name: "oldState"; type: "QAbstractAnimation::State" }
        }
        Signal {
            name: "currentLoopChanged"
            Parameter { name: "currentLoop"; type: "int" }
        }
        Signal {
            name: "directionChanged"
            Parameter { type: "QAbstractAnimation::Direction" }
        }
        Method {
            name: "start"
            Parameter { name: "policy"; type: "QAbstractAnimation::DeletionPolicy" }
        }
        Method { name: "start"; isCloned: true }
        Method { name: "pause" }
        Method { name: "resume" }
        Method {
            name: "setPaused"
            Parameter { type: "bool" }
        }
        Method { name: "stop" }
        Method {
            name: "setCurrentTime"
            Parameter { name: "msecs"; type: "int" }
        }
    }
    Component {
        file: "qabstractaxis.h"
        name: "QAbstractAxis"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtGraphs/AbstractAxis 6.0", "QtGraphs/AbstractAxis 6.9"]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536, 1545]
        Enum {
            name: "AxisType"
            isScoped: true
            values: ["Value", "BarCategory", "DateTime"]
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "lineVisible"
            type: "bool"
            read: "isLineVisible"
            write: "setLineVisible"
            notify: "lineVisibleChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "labelsVisible"
            type: "bool"
            read: "labelsVisible"
            write: "setLabelsVisible"
            notify: "labelsVisibleChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "labelsAngle"
            type: "double"
            read: "labelsAngle"
            write: "setLabelsAngle"
            notify: "labelsAngleChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "labelDelegate"
            type: "QQmlComponent"
            isPointer: true
            read: "labelDelegate"
            write: "setLabelDelegate"
            notify: "labelDelegateChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "gridVisible"
            type: "bool"
            read: "isGridVisible"
            write: "setGridVisible"
            notify: "gridVisibleChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "subGridVisible"
            type: "bool"
            read: "isSubGridVisible"
            write: "setSubGridVisible"
            notify: "subGridVisibleChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "titleText"
            type: "QString"
            read: "titleText"
            write: "setTitleText"
            notify: "titleTextChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "titleColor"
            type: "QColor"
            read: "titleColor"
            write: "setTitleColor"
            notify: "titleColorChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "titleVisible"
            type: "bool"
            read: "isTitleVisible"
            write: "setTitleVisible"
            notify: "titleVisibleChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "titleFont"
            type: "QFont"
            read: "titleFont"
            write: "setTitleFont"
            notify: "titleFontChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "alignment"
            revision: 1545
            type: "Qt::Alignment"
            read: "alignment"
            write: "setAlignment"
            notify: "alignmentChanged"
            index: 11
        }
        Signal {
            name: "visibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "lineVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "labelsVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "labelsAngleChanged"
            Parameter { name: "angle"; type: "double" }
        }
        Signal { name: "labelDelegateChanged" }
        Signal {
            name: "gridVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "subGridVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "titleTextChanged"
            Parameter { name: "title"; type: "QString" }
        }
        Signal {
            name: "titleColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "titleVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "titleFontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "alignmentChanged"
            revision: 1545
            Parameter { name: "alignment"; type: "Qt::Alignment" }
        }
        Signal { name: "update" }
        Signal {
            name: "rangeChanged"
            Parameter { name: "min"; type: "double" }
            Parameter { name: "max"; type: "double" }
        }
    }
    Component {
        file: "qabstractdataproxy.h"
        name: "QAbstractDataProxy"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtGraphs/AbstractDataProxy 6.0"]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "DataType"
            isScoped: true
            values: ["None", "Bar", "Scatter", "Surface"]
        }
        Property {
            name: "type"
            type: "QAbstractDataProxy::DataType"
            read: "type"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "qabstractseries.h"
        name: "QAbstractSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        Enum {
            name: "SeriesType"
            isScoped: true
            values: ["Line", "Area", "Bar", "Pie", "Scatter", "Spline"]
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "selectable"
            type: "bool"
            read: "isSelectable"
            write: "setSelectable"
            notify: "selectableChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "hoverable"
            type: "bool"
            read: "isHoverable"
            write: "setHoverable"
            notify: "hoverableChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "opacity"
            type: "double"
            read: "opacity"
            write: "setOpacity"
            notify: "opacityChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "valuesMultiplier"
            type: "double"
            read: "valuesMultiplier"
            write: "setValuesMultiplier"
            notify: "valuesMultiplierChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "type"
            type: "SeriesType"
            read: "type"
            index: 6
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 7
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "legendData"
            type: "QLegendData"
            isList: true
            read: "legendData"
            notify: "legendDataChanged"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Signal { name: "update" }
        Signal { name: "nameChanged" }
        Signal { name: "visibleChanged" }
        Signal { name: "selectableChanged" }
        Signal { name: "hoverableChanged" }
        Signal { name: "opacityChanged" }
        Signal { name: "valuesMultiplierChanged" }
        Signal { name: "legendDataChanged" }
        Signal {
            name: "hoverEnter"
            Parameter { name: "seriesName"; type: "QString" }
            Parameter { name: "position"; type: "QPointF" }
            Parameter { name: "value"; type: "QPointF" }
        }
        Signal {
            name: "hoverExit"
            Parameter { name: "seriesName"; type: "QString" }
            Parameter { name: "position"; type: "QPointF" }
        }
        Signal {
            name: "hover"
            Parameter { name: "seriesName"; type: "QString" }
            Parameter { name: "position"; type: "QPointF" }
            Parameter { name: "value"; type: "QPointF" }
        }
    }
    Component {
        file: "qareaseries.h"
        name: "QAreaSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QAbstractSeries"
        exports: ["QtGraphs/AreaSeries 6.0", "QtGraphs/AreaSeries 6.9"]
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536, 1545]
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "selectedColor"
            type: "QColor"
            read: "selectedColor"
            write: "setSelectedColor"
            notify: "selectedColorChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "borderColor"
            type: "QColor"
            read: "borderColor"
            write: "setBorderColor"
            notify: "borderColorChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "selectedBorderColor"
            type: "QColor"
            read: "selectedBorderColor"
            write: "setSelectedBorderColor"
            notify: "selectedBorderColorChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "borderWidth"
            type: "double"
            read: "borderWidth"
            write: "setBorderWidth"
            notify: "borderWidthChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "selected"
            type: "bool"
            read: "isSelected"
            write: "setSelected"
            notify: "selectedChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "upperSeries"
            type: "QXYSeries"
            isPointer: true
            read: "upperSeries"
            write: "setUpperSeries"
            notify: "upperSeriesChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "lowerSeries"
            type: "QXYSeries"
            isPointer: true
            read: "lowerSeries"
            write: "setLowerSeries"
            notify: "lowerSeriesChanged"
            index: 7
            isFinal: true
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "newColor"; type: "QColor" }
        }
        Signal {
            name: "selectedColorChanged"
            Parameter { name: "newSelectedColor"; type: "QColor" }
        }
        Signal {
            name: "borderColorChanged"
            Parameter { name: "newBorderColor"; type: "QColor" }
        }
        Signal {
            name: "selectedBorderColorChanged"
            Parameter { name: "newSelectedBorderColor"; type: "QColor" }
        }
        Signal { name: "borderWidthChanged" }
        Signal { name: "selectedChanged" }
        Signal { name: "upperSeriesChanged" }
        Signal { name: "lowerSeriesChanged" }
        Signal {
            name: "clicked"
            revision: 1545
            Parameter { name: "point"; type: "QPoint" }
        }
        Signal {
            name: "doubleClicked"
            revision: 1545
            Parameter { name: "point"; type: "QPoint" }
        }
        Signal {
            name: "pressed"
            revision: 1545
            Parameter { name: "point"; type: "QPoint" }
        }
        Signal {
            name: "released"
            revision: 1545
            Parameter { name: "point"; type: "QPoint" }
        }
    }
    Component {
        file: "qbar3dseries.h"
        name: "QBar3DSeries"
        accessSemantics: "reference"
        prototype: "QAbstract3DSeries"
        exports: ["QtGraphs/QBar3DSeries 6.0", "QtGraphs/QBar3DSeries 6.9"]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536, 1545]
        Property {
            name: "dataProxy"
            type: "QBarDataProxy"
            isPointer: true
            read: "dataProxy"
            write: "setDataProxy"
            notify: "dataProxyChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "selectedBar"
            type: "QPoint"
            read: "selectedBar"
            write: "setSelectedBar"
            notify: "selectedBarChanged"
            index: 1
        }
        Property {
            name: "meshAngle"
            type: "float"
            read: "meshAngle"
            write: "setMeshAngle"
            notify: "meshAngleChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "rowColors"
            type: "QColor"
            isList: true
            read: "rowColors"
            write: "setRowColors"
            notify: "rowColorsChanged"
            index: 3
        }
        Property {
            name: "rowLabels"
            type: "QStringList"
            read: "rowLabels"
            write: "setRowLabels"
            notify: "rowLabelsChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "columnLabels"
            type: "QStringList"
            read: "columnLabels"
            write: "setColumnLabels"
            notify: "columnLabelsChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "dataArray"
            type: "QBarDataArray"
            read: "dataArray"
            write: "setDataArray"
            notify: "dataArrayChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "valueColoringEnabled"
            revision: 1545
            type: "bool"
            read: "isValueColoringEnabled"
            write: "setValueColoringEnabled"
            notify: "valueColoringEnabledChanged"
            index: 7
        }
        Signal {
            name: "dataProxyChanged"
            Parameter { name: "proxy"; type: "QBarDataProxy"; isPointer: true }
        }
        Signal {
            name: "selectedBarChanged"
            Parameter { name: "position"; type: "QPoint" }
        }
        Signal {
            name: "meshAngleChanged"
            Parameter { name: "angle"; type: "float" }
        }
        Signal {
            name: "rowColorsChanged"
            Parameter { name: "rowcolors"; type: "QColor"; isList: true }
        }
        Signal { name: "rowLabelsChanged" }
        Signal { name: "columnLabelsChanged" }
        Signal {
            name: "dataArrayChanged"
            Parameter { name: "array"; type: "QBarDataArray" }
        }
        Signal {
            name: "valueColoringEnabledChanged"
            revision: 1545
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        file: "qbarcategoryaxis.h"
        name: "QBarCategoryAxis"
        accessSemantics: "reference"
        prototype: "QAbstractAxis"
        exports: [
            "QtGraphs/BarCategoryAxis 6.0",
            "QtGraphs/BarCategoryAxis 6.9"
        ]
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536, 1545]
        Property {
            name: "categories"
            type: "QStringList"
            read: "categories"
            write: "setCategories"
            notify: "categoriesChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "min"
            type: "QString"
            read: "min"
            write: "setMin"
            notify: "minChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "max"
            type: "QString"
            read: "max"
            write: "setMax"
            notify: "maxChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "count"
            type: "qsizetype"
            read: "count"
            notify: "countChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Signal { name: "categoriesChanged" }
        Signal {
            name: "minChanged"
            Parameter { name: "min"; type: "QString" }
        }
        Signal {
            name: "maxChanged"
            Parameter { name: "max"; type: "QString" }
        }
        Signal {
            name: "categoryRangeChanged"
            Parameter { name: "min"; type: "QString" }
            Parameter { name: "max"; type: "QString" }
        }
        Signal { name: "countChanged" }
        Method {
            name: "append"
            Parameter { name: "categories"; type: "QStringList" }
        }
        Method {
            name: "append"
            Parameter { name: "category"; type: "QString" }
        }
        Method {
            name: "remove"
            Parameter { name: "category"; type: "QString" }
        }
        Method {
            name: "remove"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "insert"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "category"; type: "QString" }
        }
        Method {
            name: "replace"
            Parameter { name: "oldCategory"; type: "QString" }
            Parameter { name: "newCategory"; type: "QString" }
        }
        Method { name: "clear" }
        Method {
            name: "at"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "index"; type: "qsizetype" }
        }
    }
    Component {
        file: "qbardataproxy.h"
        name: "QBarDataProxy"
        accessSemantics: "reference"
        prototype: "QAbstractDataProxy"
        exports: ["QtGraphs/BarDataProxy 6.0"]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "RemoveLabels"
            isScoped: true
            values: ["No", "Yes"]
        }
        Property {
            name: "rowCount"
            type: "qsizetype"
            read: "rowCount"
            notify: "rowCountChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "colCount"
            type: "qsizetype"
            read: "colCount"
            notify: "colCountChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "series"
            type: "QBar3DSeries"
            isPointer: true
            read: "series"
            notify: "seriesChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Signal { name: "arrayReset" }
        Signal {
            name: "rowsAdded"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "rowsChanged"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "rowsRemoved"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "rowsInserted"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "itemChanged"
            Parameter { name: "rowIndex"; type: "qsizetype" }
            Parameter { name: "columnIndex"; type: "qsizetype" }
        }
        Signal {
            name: "rowCountChanged"
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "colCountChanged"
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "seriesChanged"
            Parameter { name: "series"; type: "QBar3DSeries"; isPointer: true }
        }
    }
    Component {
        file: "qbarmodelmapper.h"
        name: "QBarModelMapper"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtGraphs/BarModelMapper 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "series"
            type: "QBarSeries"
            isPointer: true
            read: "series"
            write: "setSeries"
            notify: "seriesChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            read: "model"
            write: "setModel"
            notify: "modelChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "firstBarSetSection"
            type: "qsizetype"
            read: "firstBarSetSection"
            write: "setFirstBarSetSection"
            notify: "firstBarSetSectionChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "lastBarSetSection"
            type: "qsizetype"
            read: "lastBarSetSection"
            write: "setLastBarSetSection"
            notify: "lastBarSetSectionChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "first"
            type: "qsizetype"
            read: "first"
            write: "setFirst"
            notify: "firstChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "count"
            type: "qsizetype"
            read: "count"
            write: "setCount"
            notify: "countChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "orientation"
            type: "Qt::Orientation"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 6
            isFinal: true
        }
        Signal { name: "seriesChanged" }
        Signal { name: "modelChanged" }
        Signal { name: "firstBarSetSectionChanged" }
        Signal { name: "lastBarSetSectionChanged" }
        Signal { name: "firstChanged" }
        Signal { name: "countChanged" }
        Signal { name: "orientationChanged" }
        Method {
            name: "onValuesAdded"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method { name: "onBarLabelChanged" }
        Method {
            name: "onBarValueChanged"
            Parameter { name: "index"; type: "qsizetype" }
        }
    }
    Component {
        file: "qbarseries.h"
        name: "QBarSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QAbstractSeries"
        exports: ["QtGraphs/BarSeries 6.0", "QtGraphs/BarSeries 6.9"]
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536, 1545]
        Enum {
            name: "LabelsPosition"
            isScoped: true
            values: ["Center", "InsideEnd", "InsideBase", "OutsideEnd"]
        }
        Enum {
            name: "BarsType"
            isScoped: true
            values: ["Groups", "Stacked", "StackedPercent"]
        }
        Property {
            name: "seriesColors"
            type: "QColor"
            isList: true
            read: "seriesColors"
            write: "setSeriesColors"
            notify: "seriesColorsChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "borderColors"
            type: "QColor"
            isList: true
            read: "borderColors"
            write: "setBorderColors"
            notify: "borderColorsChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "barsType"
            type: "BarsType"
            read: "barsType"
            write: "setBarsType"
            notify: "barsTypeChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "barWidth"
            type: "double"
            read: "barWidth"
            write: "setBarWidth"
            notify: "barWidthChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "count"
            type: "qsizetype"
            read: "count"
            notify: "countChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "labelsVisible"
            type: "bool"
            read: "labelsVisible"
            write: "setLabelsVisible"
            notify: "labelsVisibleChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "labelsFormat"
            type: "QString"
            read: "labelsFormat"
            write: "setLabelsFormat"
            notify: "labelsFormatChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "labelsPosition"
            type: "LabelsPosition"
            read: "labelsPosition"
            write: "setLabelsPosition"
            notify: "labelsPositionChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "labelsMargin"
            type: "double"
            read: "labelsMargin"
            write: "setLabelsMargin"
            notify: "labelsMarginChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "labelsAngle"
            type: "double"
            read: "labelsAngle"
            write: "setLabelsAngle"
            notify: "labelsAngleChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "labelsPrecision"
            type: "int"
            read: "labelsPrecision"
            write: "setLabelsPrecision"
            notify: "labelsPrecisionChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "barDelegate"
            type: "QQmlComponent"
            isPointer: true
            read: "barDelegate"
            write: "setBarDelegate"
            notify: "barDelegateChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "barSets"
            type: "QList<QBarSet*>"
            read: "barSets"
            notify: "barSetsChanged"
            index: 12
            isReadonly: true
            isFinal: true
        }
        Signal { name: "updatedBars" }
        Signal { name: "seriesColorsChanged" }
        Signal { name: "borderColorsChanged" }
        Signal { name: "countChanged" }
        Signal { name: "barWidthChanged" }
        Signal {
            name: "labelsVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "labelsFormatChanged"
            Parameter { name: "format"; type: "QString" }
        }
        Signal {
            name: "labelsPositionChanged"
            Parameter { name: "position"; type: "QBarSeries::LabelsPosition" }
        }
        Signal {
            name: "barsTypeChanged"
            Parameter { name: "type"; type: "QBarSeries::BarsType" }
        }
        Signal {
            name: "labelsMarginChanged"
            Parameter { name: "margin"; type: "double" }
        }
        Signal {
            name: "labelsAngleChanged"
            Parameter { name: "angle"; type: "double" }
        }
        Signal {
            name: "labelsPrecisionChanged"
            Parameter { name: "precision"; type: "int" }
        }
        Signal { name: "barDelegateChanged" }
        Signal {
            name: "barsetsAdded"
            Parameter { name: "sets"; type: "QList<QBarSet*>" }
        }
        Signal {
            name: "barsetsReplaced"
            Parameter { name: "sets"; type: "QList<QBarSet*>" }
        }
        Signal {
            name: "barsetsRemoved"
            Parameter { name: "sets"; type: "QList<QBarSet*>" }
        }
        Signal {
            name: "setValueChanged"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Signal {
            name: "setValueAdded"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Signal {
            name: "setValueRemoved"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Signal { name: "barSetsChanged" }
        Signal {
            name: "clicked"
            revision: 1545
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Signal {
            name: "doubleClicked"
            revision: 1545
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Signal {
            name: "pressed"
            revision: 1545
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Signal {
            name: "released"
            revision: 1545
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "barset"; type: "QBarSet"; isPointer: true }
        }
        Method { name: "selectAll" }
        Method { name: "deselectAll" }
        Method {
            name: "handleSetValueChange"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "handleSetValueAdd"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "handleSetValueRemove"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "append"
            type: "bool"
            Parameter { name: "set"; type: "QBarSet"; isPointer: true }
        }
        Method {
            name: "take"
            type: "bool"
            Parameter { name: "set"; type: "QBarSet"; isPointer: true }
        }
        Method { name: "count"; type: "qsizetype"; isMethodConstant: true }
        Method {
            name: "append"
            type: "bool"
            Parameter { name: "sets"; type: "QList<QBarSet*>" }
        }
        Method {
            name: "remove"
            type: "bool"
            Parameter { name: "set"; type: "QBarSet"; isPointer: true }
        }
        Method {
            name: "insert"
            type: "bool"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "set"; type: "QBarSet"; isPointer: true }
        }
        Method { name: "clear" }
        Method {
            name: "replace"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "set"; type: "QBarSet"; isPointer: true }
        }
        Method {
            name: "at"
            type: "QBarSet"
            isPointer: true
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "find"
            type: "qsizetype"
            isMethodConstant: true
            Parameter { name: "set"; type: "QBarSet"; isPointer: true }
        }
        Method {
            name: "removeMultiple"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "remove"
            type: "bool"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "replace"
            type: "bool"
            Parameter { name: "oldValue"; type: "QBarSet"; isPointer: true }
            Parameter { name: "newValue"; type: "QBarSet"; isPointer: true }
        }
        Method {
            name: "replace"
            type: "bool"
            Parameter { name: "sets"; type: "QList<QBarSet*>" }
        }
    }
    Component {
        file: "qbarset.h"
        name: "QBarSet"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtGraphs/BarSet 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "label"
            type: "QString"
            read: "label"
            write: "setLabel"
            notify: "labelChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "selectedColor"
            type: "QColor"
            read: "selectedColor"
            write: "setSelectedColor"
            notify: "selectedColorChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "borderColor"
            type: "QColor"
            read: "borderColor"
            write: "setBorderColor"
            notify: "borderColorChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "labelColor"
            type: "QColor"
            read: "labelColor"
            write: "setLabelColor"
            notify: "labelColorChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "values"
            type: "QVariantList"
            read: "values"
            write: "setValues"
            notify: "valuesChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "borderWidth"
            type: "double"
            read: "borderWidth"
            write: "setBorderWidth"
            notify: "borderWidthChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "count"
            type: "qsizetype"
            read: "count"
            notify: "countChanged"
            index: 7
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "selectedBars"
            type: "qsizetype"
            isList: true
            read: "selectedBars"
            notify: "selectedBarsChanged"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Signal { name: "update" }
        Signal { name: "labelChanged" }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "borderColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "labelColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal { name: "valuesChanged" }
        Signal {
            name: "selectedColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal { name: "countChanged" }
        Signal {
            name: "borderWidthChanged"
            Parameter { name: "width"; type: "double" }
        }
        Signal {
            name: "valuesAdded"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "valuesRemoved"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Signal { name: "updatedBars" }
        Signal {
            name: "valueAdded"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "valueRemoved"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "selectedBarsChanged"
            Parameter { name: "indexes"; type: "qsizetype"; isList: true }
        }
        Method {
            name: "append"
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "append"
            Parameter { name: "values"; type: "double"; isList: true }
        }
        Method {
            name: "insert"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "remove"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "remove"
            isCloned: true
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "replace"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "at"
            type: "double"
            isMethodConstant: true
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method { name: "count"; type: "qsizetype"; isMethodConstant: true }
        Method { name: "sum"; type: "double"; isMethodConstant: true }
        Method { name: "clear" }
        Method {
            name: "isBarSelected"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "selectBar"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "deselectBar"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "setBarSelected"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "selected"; type: "bool" }
        }
        Method { name: "selectAllBars" }
        Method { name: "deselectAllBars" }
        Method {
            name: "selectBars"
            Parameter { name: "indexes"; type: "qsizetype"; isList: true }
        }
        Method {
            name: "deselectBars"
            Parameter { name: "indexes"; type: "qsizetype"; isList: true }
        }
        Method {
            name: "toggleSelection"
            Parameter { name: "indexes"; type: "qsizetype"; isList: true }
        }
    }
    Component {
        file: "qcategory3daxis.h"
        name: "QCategory3DAxis"
        accessSemantics: "reference"
        prototype: "QAbstract3DAxis"
        exports: [
            "QtGraphs/Category3DAxis 6.0",
            "QtGraphs/Category3DAxis 6.9"
        ]
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536, 1545]
        Property {
            name: "labels"
            type: "QStringList"
            read: "labels"
            write: "setLabels"
            notify: "labelsChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "rowLabelsChanged" }
        Signal { name: "columnLabelsChanged" }
    }
    Component {
        file: "qcustom3ditem.h"
        name: "QCustom3DItem"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtGraphs/Custom3DItem 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "meshFile"
            type: "QString"
            read: "meshFile"
            write: "setMeshFile"
            notify: "meshFileChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "textureFile"
            type: "QString"
            read: "textureFile"
            write: "setTextureFile"
            notify: "textureFileChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "position"
            type: "QVector3D"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "positionAbsolute"
            type: "bool"
            read: "isPositionAbsolute"
            write: "setPositionAbsolute"
            notify: "positionAbsoluteChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "scaling"
            type: "QVector3D"
            read: "scaling"
            write: "setScaling"
            notify: "scalingChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "rotation"
            type: "QQuaternion"
            read: "rotation"
            write: "setRotation"
            notify: "rotationChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "shadowCasting"
            type: "bool"
            read: "isShadowCasting"
            write: "setShadowCasting"
            notify: "shadowCastingChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "scalingAbsolute"
            type: "bool"
            read: "isScalingAbsolute"
            write: "setScalingAbsolute"
            notify: "scalingAbsoluteChanged"
            index: 8
            isFinal: true
        }
        Signal {
            name: "meshFileChanged"
            Parameter { name: "meshFile"; type: "QString" }
        }
        Signal {
            name: "textureFileChanged"
            Parameter { name: "textureFile"; type: "QString" }
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "QVector3D" }
        }
        Signal {
            name: "positionAbsoluteChanged"
            Parameter { name: "positionAbsolute"; type: "bool" }
        }
        Signal {
            name: "scalingChanged"
            Parameter { name: "scaling"; type: "QVector3D" }
        }
        Signal {
            name: "rotationChanged"
            Parameter { name: "rotation"; type: "QQuaternion" }
        }
        Signal {
            name: "visibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "shadowCastingChanged"
            Parameter { name: "shadowCasting"; type: "bool" }
        }
        Signal {
            name: "scalingAbsoluteChanged"
            Parameter { name: "scalingAbsolute"; type: "bool" }
        }
        Signal { name: "needUpdate" }
        Method {
            name: "setRotationAxisAndAngle"
            Parameter { name: "axis"; type: "QVector3D" }
            Parameter { name: "angle"; type: "float" }
        }
    }
    Component {
        file: "qcustom3dlabel.h"
        name: "QCustom3DLabel"
        accessSemantics: "reference"
        prototype: "QCustom3DItem"
        exports: ["QtGraphs/Custom3DLabel 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "textColor"
            type: "QColor"
            read: "textColor"
            write: "setTextColor"
            notify: "textColorChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "backgroundColor"
            type: "QColor"
            read: "backgroundColor"
            write: "setBackgroundColor"
            notify: "backgroundColorChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "borderVisible"
            type: "bool"
            read: "isBorderVisible"
            write: "setBorderVisible"
            notify: "borderVisibleChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "backgroundVisible"
            type: "bool"
            read: "isBackgroundVisible"
            write: "setBackgroundVisible"
            notify: "backgroundVisibleChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "facingCamera"
            type: "bool"
            read: "isFacingCamera"
            write: "setFacingCamera"
            notify: "facingCameraChanged"
            index: 6
            isFinal: true
        }
        Signal {
            name: "textChanged"
            Parameter { name: "text"; type: "QString" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "textColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "backgroundColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "borderVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "backgroundVisibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "facingCameraChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        file: "qcustom3dvolume.h"
        name: "QCustom3DVolume"
        accessSemantics: "reference"
        prototype: "QCustom3DItem"
        exports: ["QtGraphs/Custom3DVolume 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "textureWidth"
            type: "int"
            read: "textureWidth"
            write: "setTextureWidth"
            notify: "textureWidthChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "textureHeight"
            type: "int"
            read: "textureHeight"
            write: "setTextureHeight"
            notify: "textureHeightChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "textureDepth"
            type: "int"
            read: "textureDepth"
            write: "setTextureDepth"
            notify: "textureDepthChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "sliceIndexX"
            type: "int"
            read: "sliceIndexX"
            write: "setSliceIndexX"
            notify: "sliceIndexXChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "sliceIndexY"
            type: "int"
            read: "sliceIndexY"
            write: "setSliceIndexY"
            notify: "sliceIndexYChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "sliceIndexZ"
            type: "int"
            read: "sliceIndexZ"
            write: "setSliceIndexZ"
            notify: "sliceIndexZChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "colorTable"
            type: "QRgb"
            isList: true
            read: "colorTable"
            write: "setColorTable"
            notify: "colorTableChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "textureData"
            type: "QList<uchar>"
            isPointer: true
            read: "textureData"
            write: "setTextureData"
            notify: "textureDataChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "alphaMultiplier"
            type: "float"
            read: "alphaMultiplier"
            write: "setAlphaMultiplier"
            notify: "alphaMultiplierChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "preserveOpacity"
            type: "bool"
            read: "preserveOpacity"
            write: "setPreserveOpacity"
            notify: "preserveOpacityChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "useHighDefShader"
            type: "bool"
            read: "useHighDefShader"
            write: "setUseHighDefShader"
            notify: "useHighDefShaderChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "drawSlices"
            type: "bool"
            read: "drawSlices"
            write: "setDrawSlices"
            notify: "drawSlicesChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "drawSliceFrames"
            type: "bool"
            read: "drawSliceFrames"
            write: "setDrawSliceFrames"
            notify: "drawSliceFramesChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "sliceFrameColor"
            type: "QColor"
            read: "sliceFrameColor"
            write: "setSliceFrameColor"
            notify: "sliceFrameColorChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "sliceFrameWidths"
            type: "QVector3D"
            read: "sliceFrameWidths"
            write: "setSliceFrameWidths"
            notify: "sliceFrameWidthsChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "sliceFrameGaps"
            type: "QVector3D"
            read: "sliceFrameGaps"
            write: "setSliceFrameGaps"
            notify: "sliceFrameGapsChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "sliceFrameThicknesses"
            type: "QVector3D"
            read: "sliceFrameThicknesses"
            write: "setSliceFrameThicknesses"
            notify: "sliceFrameThicknessesChanged"
            index: 16
            isFinal: true
        }
        Signal {
            name: "textureWidthChanged"
            Parameter { name: "value"; type: "int" }
        }
        Signal {
            name: "textureHeightChanged"
            Parameter { name: "value"; type: "int" }
        }
        Signal {
            name: "textureDepthChanged"
            Parameter { name: "value"; type: "int" }
        }
        Signal {
            name: "sliceIndexXChanged"
            Parameter { name: "value"; type: "int" }
        }
        Signal {
            name: "sliceIndexYChanged"
            Parameter { name: "value"; type: "int" }
        }
        Signal {
            name: "sliceIndexZChanged"
            Parameter { name: "value"; type: "int" }
        }
        Signal { name: "colorTableChanged" }
        Signal {
            name: "textureDataChanged"
            Parameter { name: "data"; type: "QList<uchar>"; isPointer: true }
        }
        Signal {
            name: "textureFormatChanged"
            Parameter { name: "format"; type: "QImage::Format" }
        }
        Signal {
            name: "alphaMultiplierChanged"
            Parameter { name: "mult"; type: "float" }
        }
        Signal {
            name: "preserveOpacityChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "useHighDefShaderChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "drawSlicesChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "drawSliceFramesChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "sliceFrameColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "sliceFrameWidthsChanged"
            Parameter { name: "values"; type: "QVector3D" }
        }
        Signal {
            name: "sliceFrameGapsChanged"
            Parameter { name: "values"; type: "QVector3D" }
        }
        Signal {
            name: "sliceFrameThicknessesChanged"
            Parameter { name: "values"; type: "QVector3D" }
        }
    }
    Component {
        file: "qdatetimeaxis.h"
        name: "QDateTimeAxis"
        accessSemantics: "reference"
        prototype: "QAbstractAxis"
        exports: ["QtGraphs/DateTimeAxis 6.0", "QtGraphs/DateTimeAxis 6.9"]
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536, 1545]
        Property {
            name: "min"
            type: "QDateTime"
            read: "min"
            write: "setMin"
            notify: "minChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "max"
            type: "QDateTime"
            read: "max"
            write: "setMax"
            notify: "maxChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "labelFormat"
            type: "QString"
            read: "labelFormat"
            write: "setLabelFormat"
            notify: "labelFormatChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "subTickCount"
            type: "int"
            read: "subTickCount"
            write: "setSubTickCount"
            notify: "subTickCountChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "tickInterval"
            type: "double"
            read: "tickInterval"
            write: "setTickInterval"
            notify: "tickIntervalChanged"
            index: 4
            isFinal: true
        }
        Signal {
            name: "minChanged"
            Parameter { name: "min"; type: "QDateTime" }
        }
        Signal {
            name: "maxChanged"
            Parameter { name: "max"; type: "QDateTime" }
        }
        Signal {
            name: "labelFormatChanged"
            Parameter { name: "format"; type: "QString" }
        }
        Signal { name: "tickIntervalChanged" }
        Signal { name: "subTickCountChanged" }
    }
    Component {
        file: "private/qgraphanimation_p.h"
        name: "QGraphAnimation"
        accessSemantics: "reference"
        prototype: "QVariantAnimation"
        Enum {
            name: "AnimationState"
            isScoped: true
            values: ["Playing", "Stopped"]
        }
        Enum {
            name: "GraphAnimationType"
            isScoped: true
            values: ["GraphPoint", "ControlPoint"]
        }
        Property {
            name: "animating"
            type: "AnimationState"
            read: "animating"
            write: "setAnimating"
            notify: "animatingChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "animatingChanged" }
        Method {
            name: "valueUpdated"
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component {
        file: "private/qgraphpointanimation_p.h"
        name: "QGraphPointAnimation"
        accessSemantics: "reference"
        prototype: "QXYSeriesAnimation"
        exports: ["QtGraphs/GraphPointAnimation 6.0"]
        exportMetaObjectRevisions: [1536]
        Method {
            name: "valueUpdated"
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component {
        file: "private/qgraphtransition_p.h"
        name: "QGraphTransition"
        accessSemantics: "reference"
        defaultProperty: "animations"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtGraphs/GraphTransition 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "TransitionType"
            isScoped: true
            values: ["None", "PointAdded", "PointReplaced", "PointRemoved"]
        }
        Property {
            name: "animations"
            type: "QObject"
            isList: true
            read: "animations"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "qgraphstheme.h"
        name: "QGraphsLine"
        accessSemantics: "value"
        exports: ["QtGraphs/graphsline 6.0"]
        isStructured: true
        exportMetaObjectRevisions: [1536]
        Property {
            name: "mainColor"
            type: "QColor"
            read: "mainColor"
            write: "setMainColor"
            index: 0
            isFinal: true
        }
        Property {
            name: "subColor"
            type: "QColor"
            read: "subColor"
            write: "setSubColor"
            index: 1
            isFinal: true
        }
        Property {
            name: "mainWidth"
            type: "double"
            read: "mainWidth"
            write: "setMainWidth"
            index: 2
            isFinal: true
        }
        Property {
            name: "subWidth"
            type: "double"
            read: "subWidth"
            write: "setSubWidth"
            index: 3
            isFinal: true
        }
        Property {
            name: "labelTextColor"
            type: "QColor"
            read: "labelTextColor"
            write: "setLabelTextColor"
            index: 4
            isFinal: true
        }
    }
    Component {
        file: "qgraphstheme.h"
        name: "QGraphsTheme"
        accessSemantics: "reference"
        defaultProperty: "themeChildren"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtGraphs/GraphsTheme 6.0"]
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "Theme"
            isScoped: true
            values: [
                "QtGreen",
                "QtGreenNeon",
                "MixSeries",
                "OrangeSeries",
                "YellowSeries",
                "BlueSeries",
                "PurpleSeries",
                "GreySeries",
                "UserDefined"
            ]
        }
        Enum {
            name: "ColorStyle"
            isScoped: true
            values: ["Uniform", "ObjectGradient", "RangeGradient"]
        }
        Enum {
            name: "ForceTheme"
            isScoped: true
            values: ["No", "Yes"]
        }
        Enum {
            name: "ColorScheme"
            isScoped: true
            values: ["Automatic", "Light", "Dark"]
        }
        Property {
            name: "colorScheme"
            type: "QGraphsTheme::ColorScheme"
            read: "colorScheme"
            write: "setColorScheme"
            notify: "colorSchemeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "theme"
            type: "QGraphsTheme::Theme"
            read: "theme"
            write: "setTheme"
            notify: "themeChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "colorStyle"
            type: "QGraphsTheme::ColorStyle"
            read: "colorStyle"
            write: "setColorStyle"
            notify: "colorStyleChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "backgroundColor"
            type: "QColor"
            read: "backgroundColor"
            write: "setBackgroundColor"
            notify: "backgroundColorChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "backgroundVisible"
            type: "bool"
            read: "isBackgroundVisible"
            write: "setBackgroundVisible"
            notify: "backgroundVisibleChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "plotAreaBackgroundColor"
            type: "QColor"
            read: "plotAreaBackgroundColor"
            write: "setPlotAreaBackgroundColor"
            notify: "plotAreaBackgroundColorChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "plotAreaBackgroundVisible"
            type: "bool"
            read: "isPlotAreaBackgroundVisible"
            write: "setPlotAreaBackgroundVisible"
            notify: "plotAreaBackgroundVisibleChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "gridVisible"
            type: "bool"
            read: "isGridVisible"
            write: "setGridVisible"
            notify: "gridVisibleChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "axisXLabelFont"
            type: "QFont"
            read: "axisXLabelFont"
            write: "setAxisXLabelFont"
            notify: "axisXLabelFontChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "axisYLabelFont"
            type: "QFont"
            read: "axisYLabelFont"
            write: "setAxisYLabelFont"
            notify: "axisYLabelFontChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "axisZLabelFont"
            type: "QFont"
            read: "axisZLabelFont"
            write: "setAxisZLabelFont"
            notify: "axisZLabelFontChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "grid"
            type: "QGraphsLine"
            read: "grid"
            write: "setGrid"
            notify: "gridChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "axisX"
            type: "QGraphsLine"
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "axisY"
            type: "QGraphsLine"
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "axisZ"
            type: "QGraphsLine"
            read: "axisZ"
            write: "setAxisZ"
            notify: "axisZChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "labelFont"
            type: "QFont"
            read: "labelFont"
            write: "setLabelFont"
            notify: "labelFontChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "labelsVisible"
            type: "bool"
            read: "labelsVisible"
            write: "setLabelsVisible"
            notify: "labelsVisibleChanged"
            index: 16
            isFinal: true
        }
        Property {
            name: "labelBackgroundColor"
            type: "QColor"
            read: "labelBackgroundColor"
            write: "setLabelBackgroundColor"
            notify: "labelBackgroundColorChanged"
            index: 17
            isFinal: true
        }
        Property {
            name: "labelTextColor"
            type: "QColor"
            read: "labelTextColor"
            write: "setLabelTextColor"
            notify: "labelTextColorChanged"
            index: 18
            isFinal: true
        }
        Property {
            name: "labelBackgroundVisible"
            type: "bool"
            read: "isLabelBackgroundVisible"
            write: "setLabelBackgroundVisible"
            notify: "labelBackgroundVisibleChanged"
            index: 19
            isFinal: true
        }
        Property {
            name: "labelBorderVisible"
            type: "bool"
            read: "isLabelBorderVisible"
            write: "setLabelBorderVisible"
            notify: "labelBorderVisibleChanged"
            index: 20
            isFinal: true
        }
        Property {
            name: "seriesColors"
            type: "QColor"
            isList: true
            read: "seriesColors"
            write: "setSeriesColors"
            notify: "seriesColorsChanged"
            index: 21
            isFinal: true
        }
        Property {
            name: "borderColors"
            type: "QColor"
            isList: true
            read: "borderColors"
            write: "setBorderColors"
            notify: "borderColorsChanged"
            index: 22
            isFinal: true
        }
        Property {
            name: "borderWidth"
            type: "double"
            read: "borderWidth"
            write: "setBorderWidth"
            notify: "borderWidthChanged"
            index: 23
            isFinal: true
        }
        Property {
            name: "baseColors"
            type: "QQuickGraphsColor"
            isList: true
            read: "baseColorsQML"
            index: 24
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "baseGradients"
            type: "QQuickGradient"
            isList: true
            read: "baseGradientsQML"
            index: 25
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "themeChildren"
            type: "QObject"
            isList: true
            read: "themeChildren"
            index: 26
            isReadonly: true
            isFinal: true
            isPropertyConstant: true
        }
        Property {
            name: "singleHighlightColor"
            type: "QColor"
            read: "singleHighlightColor"
            write: "setSingleHighlightColor"
            notify: "singleHighlightColorChanged"
            index: 27
            isFinal: true
        }
        Property {
            name: "multiHighlightColor"
            type: "QColor"
            read: "multiHighlightColor"
            write: "setMultiHighlightColor"
            notify: "multiHighlightColorChanged"
            index: 28
            isFinal: true
        }
        Property {
            name: "singleHighlightGradient"
            type: "QQuickGradient"
            isPointer: true
            read: "singleHighlightGradientQML"
            write: "setSingleHighlightGradientQML"
            notify: "singleHighlightGradientQMLChanged"
            index: 29
            isFinal: true
        }
        Property {
            name: "multiHighlightGradient"
            type: "QQuickGradient"
            isPointer: true
            read: "multiHighlightGradientQML"
            write: "setMultiHighlightGradientQML"
            notify: "multiHighlightGradientQMLChanged"
            index: 30
            isFinal: true
        }
        Signal { name: "update" }
        Signal { name: "colorSchemeChanged" }
        Signal {
            name: "themeChanged"
            Parameter { name: "theme"; type: "QGraphsTheme::Theme" }
        }
        Signal {
            name: "colorStyleChanged"
            Parameter { name: "type"; type: "QGraphsTheme::ColorStyle" }
        }
        Signal { name: "backgroundColorChanged" }
        Signal { name: "backgroundVisibleChanged" }
        Signal { name: "plotAreaBackgroundColorChanged" }
        Signal { name: "plotAreaBackgroundVisibleChanged" }
        Signal { name: "gridVisibleChanged" }
        Signal { name: "labelsVisibleChanged" }
        Signal { name: "labelBackgroundColorChanged" }
        Signal { name: "labelTextColorChanged" }
        Signal {
            name: "singleHighlightColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "multiHighlightColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "singleHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QLinearGradient" }
        }
        Signal {
            name: "multiHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QLinearGradient" }
        }
        Signal { name: "labelFontChanged" }
        Signal { name: "labelBackgroundVisibleChanged" }
        Signal { name: "labelBorderVisibleChanged" }
        Signal {
            name: "seriesColorsChanged"
            Parameter { name: "list"; type: "QColor"; isList: true }
        }
        Signal {
            name: "seriesGradientsChanged"
            Parameter { name: "list"; type: "QLinearGradient"; isList: true }
        }
        Signal { name: "borderColorsChanged" }
        Signal { name: "borderWidthChanged" }
        Signal { name: "singleHighlightGradientQMLChanged" }
        Signal { name: "multiHighlightGradientQMLChanged" }
        Signal { name: "axisXLabelFontChanged" }
        Signal { name: "axisYLabelFontChanged" }
        Signal { name: "axisZLabelFontChanged" }
        Signal { name: "gridChanged" }
        Signal { name: "axisXChanged" }
        Signal { name: "axisYChanged" }
        Signal { name: "axisZChanged" }
        Method { name: "handleBaseColorUpdate" }
        Method { name: "handleBaseGradientUpdate" }
    }
    Component {
        file: "private/qgraphsview_p.h"
        name: "QGraphsView"
        accessSemantics: "reference"
        defaultProperty: "seriesList"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtGraphs/GraphsView 6.0",
            "QtGraphs/GraphsView 6.3",
            "QtGraphs/GraphsView 6.7",
            "QtGraphs/GraphsView 6.9"
        ]
        exportMetaObjectRevisions: [1536, 1539, 1543, 1545]
        Enum {
            name: "ZoomStyle"
            isScoped: true
            values: ["None", "Center"]
        }
        Enum {
            name: "PanStyle"
            isScoped: true
            values: ["None", "Drag"]
        }
        Property {
            name: "theme"
            type: "QGraphsTheme"
            isPointer: true
            read: "theme"
            write: "setTheme"
            notify: "themeChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "seriesList"
            type: "QObject"
            isList: true
            read: "seriesList"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "marginTop"
            type: "double"
            read: "marginTop"
            write: "setMarginTop"
            notify: "marginTopChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "marginBottom"
            type: "double"
            read: "marginBottom"
            write: "setMarginBottom"
            notify: "marginBottomChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "marginLeft"
            type: "double"
            read: "marginLeft"
            write: "setMarginLeft"
            notify: "marginLeftChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "marginRight"
            type: "double"
            read: "marginRight"
            write: "setMarginRight"
            notify: "marginRightChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "plotArea"
            revision: 1545
            type: "QRectF"
            read: "plotArea"
            notify: "plotAreaChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "axisXSmoothing"
            type: "double"
            read: "axisXSmoothing"
            write: "setAxisXSmoothing"
            notify: "axisXSmoothingChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "axisYSmoothing"
            type: "double"
            read: "axisYSmoothing"
            write: "setAxisYSmoothing"
            notify: "axisYSmoothingChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "gridSmoothing"
            type: "double"
            read: "gridSmoothing"
            write: "setGridSmoothing"
            notify: "gridSmoothingChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "shadowVisible"
            type: "bool"
            read: "isShadowVisible"
            write: "setShadowVisible"
            notify: "shadowVisibleChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "shadowColor"
            type: "QColor"
            read: "shadowColor"
            write: "setShadowColor"
            notify: "shadowColorChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "shadowBarWidth"
            type: "double"
            read: "shadowBarWidth"
            write: "setShadowBarWidth"
            notify: "shadowBarWidthChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "shadowXOffset"
            type: "double"
            read: "shadowXOffset"
            write: "setShadowXOffset"
            notify: "shadowXOffsetChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "shadowYOffset"
            type: "double"
            read: "shadowYOffset"
            write: "setShadowYOffset"
            notify: "shadowYOffsetChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "shadowSmoothing"
            type: "double"
            read: "shadowSmoothing"
            write: "setShadowSmoothing"
            notify: "shadowSmoothingChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "axisX"
            type: "QAbstractAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 16
            isFinal: true
        }
        Property {
            name: "axisY"
            type: "QAbstractAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 17
            isFinal: true
        }
        Property {
            name: "orientation"
            type: "Qt::Orientation"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 18
            isFinal: true
        }
        Property {
            name: "zoomStyle"
            revision: 1545
            type: "ZoomStyle"
            read: "zoomStyle"
            write: "setZoomStyle"
            notify: "zoomStyleChanged"
            index: 19
        }
        Property {
            name: "panStyle"
            revision: 1545
            type: "PanStyle"
            read: "panStyle"
            write: "setPanStyle"
            notify: "panStyleChanged"
            index: 20
        }
        Property {
            name: "zoomSensitivity"
            revision: 1545
            type: "double"
            read: "zoomSensitivity"
            write: "setZoomSensitivity"
            notify: "zoomSensitivityChanged"
            index: 21
        }
        Property {
            name: "zoomAreaEnabled"
            revision: 1545
            type: "bool"
            read: "zoomAreaEnabled"
            write: "setZoomAreaEnabled"
            notify: "zoomAreaEnabledChanged"
            index: 22
        }
        Property {
            name: "zoomAreaDelegate"
            revision: 1545
            type: "QQmlComponent"
            isPointer: true
            read: "zoomAreaDelegate"
            write: "setZoomAreaDelegate"
            notify: "zoomAreaDelegateChanged"
            index: 23
        }
        Signal { name: "themeChanged" }
        Signal { name: "marginTopChanged" }
        Signal { name: "marginBottomChanged" }
        Signal { name: "marginLeftChanged" }
        Signal { name: "marginRightChanged" }
        Signal { name: "plotAreaChanged"; revision: 1545 }
        Signal {
            name: "hoverEnter"
            Parameter { name: "seriesName"; type: "QString" }
            Parameter { name: "position"; type: "QPointF" }
            Parameter { name: "value"; type: "QPointF" }
        }
        Signal {
            name: "hoverExit"
            Parameter { name: "seriesName"; type: "QString" }
            Parameter { name: "position"; type: "QPointF" }
        }
        Signal {
            name: "hover"
            Parameter { name: "seriesName"; type: "QString" }
            Parameter { name: "position"; type: "QPointF" }
            Parameter { name: "value"; type: "QPointF" }
        }
        Signal { name: "axisXSmoothingChanged" }
        Signal { name: "axisYSmoothingChanged" }
        Signal { name: "gridSmoothingChanged" }
        Signal { name: "shadowVisibleChanged" }
        Signal { name: "shadowColorChanged" }
        Signal { name: "shadowBarWidthChanged" }
        Signal { name: "shadowXOffsetChanged" }
        Signal { name: "shadowYOffsetChanged" }
        Signal { name: "shadowSmoothingChanged" }
        Signal { name: "axisXChanged" }
        Signal { name: "axisYChanged" }
        Signal { name: "orientationChanged" }
        Signal { name: "zoomStyleChanged"; revision: 1545 }
        Signal { name: "panStyleChanged"; revision: 1545 }
        Signal { name: "zoomAreaEnabledChanged"; revision: 1545 }
        Signal { name: "zoomAreaDelegateChanged"; revision: 1545 }
        Signal { name: "zoomSensitivityChanged"; revision: 1545 }
        Method {
            name: "addSeries"
            Parameter { name: "series"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "insertSeries"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "series"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "removeSeries"
            Parameter { name: "series"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "removeSeries"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "hasSeries"
            type: "bool"
            Parameter { name: "series"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "qheightmapsurfacedataproxy.h"
        name: "QHeightMapSurfaceDataProxy"
        accessSemantics: "reference"
        prototype: "QSurfaceDataProxy"
        exports: ["QtGraphs/HeightMapSurfaceDataProxy 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "heightMap"
            type: "QImage"
            read: "heightMap"
            write: "setHeightMap"
            notify: "heightMapChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "heightMapFile"
            type: "QString"
            read: "heightMapFile"
            write: "setHeightMapFile"
            notify: "heightMapFileChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "minXValue"
            type: "float"
            read: "minXValue"
            write: "setMinXValue"
            notify: "minXValueChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "maxXValue"
            type: "float"
            read: "maxXValue"
            write: "setMaxXValue"
            notify: "maxXValueChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "minZValue"
            type: "float"
            read: "minZValue"
            write: "setMinZValue"
            notify: "minZValueChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "maxZValue"
            type: "float"
            read: "maxZValue"
            write: "setMaxZValue"
            notify: "maxZValueChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "minYValue"
            type: "float"
            read: "minYValue"
            write: "setMinYValue"
            notify: "minYValueChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "maxYValue"
            type: "float"
            read: "maxYValue"
            write: "setMaxYValue"
            notify: "maxYValueChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "autoScaleY"
            type: "bool"
            read: "autoScaleY"
            write: "setAutoScaleY"
            notify: "autoScaleYChanged"
            index: 8
            isFinal: true
        }
        Signal {
            name: "heightMapChanged"
            Parameter { name: "image"; type: "QImage" }
        }
        Signal {
            name: "heightMapFileChanged"
            Parameter { name: "filename"; type: "QString" }
        }
        Signal {
            name: "minXValueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "maxXValueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "minZValueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "maxZValueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "minYValueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "maxYValueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "autoScaleYChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        file: "qitemmodelbardataproxy.h"
        name: "QItemModelBarDataProxy"
        accessSemantics: "reference"
        prototype: "QBarDataProxy"
        exports: ["QtGraphs/ItemModelBarDataProxy 6.0"]
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "MultiMatchBehavior"
            isScoped: true
            values: ["First", "Last", "Average", "Cumulative"]
        }
        Property {
            name: "itemModel"
            type: "QAbstractItemModel"
            isPointer: true
            read: "itemModel"
            write: "setItemModel"
            notify: "itemModelChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "rowRole"
            type: "QString"
            read: "rowRole"
            write: "setRowRole"
            notify: "rowRoleChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "columnRole"
            type: "QString"
            read: "columnRole"
            write: "setColumnRole"
            notify: "columnRoleChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "valueRole"
            type: "QString"
            read: "valueRole"
            write: "setValueRole"
            notify: "valueRoleChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "rotationRole"
            type: "QString"
            read: "rotationRole"
            write: "setRotationRole"
            notify: "rotationRoleChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "rowCategories"
            type: "QStringList"
            read: "rowCategories"
            write: "setRowCategories"
            notify: "rowCategoriesChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "columnCategories"
            type: "QStringList"
            read: "columnCategories"
            write: "setColumnCategories"
            notify: "columnCategoriesChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "useModelCategories"
            type: "bool"
            read: "useModelCategories"
            write: "setUseModelCategories"
            notify: "useModelCategoriesChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "autoRowCategories"
            type: "bool"
            read: "autoRowCategories"
            write: "setAutoRowCategories"
            notify: "autoRowCategoriesChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "autoColumnCategories"
            type: "bool"
            read: "autoColumnCategories"
            write: "setAutoColumnCategories"
            notify: "autoColumnCategoriesChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "rowRolePattern"
            type: "QRegularExpression"
            read: "rowRolePattern"
            write: "setRowRolePattern"
            notify: "rowRolePatternChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "columnRolePattern"
            type: "QRegularExpression"
            read: "columnRolePattern"
            write: "setColumnRolePattern"
            notify: "columnRolePatternChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "valueRolePattern"
            type: "QRegularExpression"
            read: "valueRolePattern"
            write: "setValueRolePattern"
            notify: "valueRolePatternChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "rotationRolePattern"
            type: "QRegularExpression"
            read: "rotationRolePattern"
            write: "setRotationRolePattern"
            notify: "rotationRolePatternChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "rowRoleReplace"
            type: "QString"
            read: "rowRoleReplace"
            write: "setRowRoleReplace"
            notify: "rowRoleReplaceChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "columnRoleReplace"
            type: "QString"
            read: "columnRoleReplace"
            write: "setColumnRoleReplace"
            notify: "columnRoleReplaceChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "valueRoleReplace"
            type: "QString"
            read: "valueRoleReplace"
            write: "setValueRoleReplace"
            notify: "valueRoleReplaceChanged"
            index: 16
            isFinal: true
        }
        Property {
            name: "rotationRoleReplace"
            type: "QString"
            read: "rotationRoleReplace"
            write: "setRotationRoleReplace"
            notify: "rotationRoleReplaceChanged"
            index: 17
            isFinal: true
        }
        Property {
            name: "multiMatchBehavior"
            type: "QItemModelBarDataProxy::MultiMatchBehavior"
            read: "multiMatchBehavior"
            write: "setMultiMatchBehavior"
            notify: "multiMatchBehaviorChanged"
            index: 18
            isFinal: true
        }
        Signal {
            name: "itemModelChanged"
            Parameter { name: "itemModel"; type: "QAbstractItemModel"; isPointer: true; isTypeConstant: true }
        }
        Signal {
            name: "rowRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "columnRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "valueRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "rotationRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal { name: "rowCategoriesChanged" }
        Signal { name: "columnCategoriesChanged" }
        Signal {
            name: "useModelCategoriesChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "autoRowCategoriesChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "autoColumnCategoriesChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "rowRolePatternChanged"
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "columnRolePatternChanged"
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "valueRolePatternChanged"
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "rotationRolePatternChanged"
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "rowRoleReplaceChanged"
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "columnRoleReplaceChanged"
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "valueRoleReplaceChanged"
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "rotationRoleReplaceChanged"
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "multiMatchBehaviorChanged"
            Parameter { name: "behavior"; type: "QItemModelBarDataProxy::MultiMatchBehavior" }
        }
        Method {
            name: "rowCategoryIndex"
            type: "qsizetype"
            Parameter { name: "category"; type: "QString" }
        }
        Method {
            name: "columnCategoryIndex"
            type: "qsizetype"
            Parameter { name: "category"; type: "QString" }
        }
    }
    Component {
        file: "qitemmodelscatterdataproxy.h"
        name: "QItemModelScatterDataProxy"
        accessSemantics: "reference"
        prototype: "QScatterDataProxy"
        exports: ["QtGraphs/ItemModelScatterDataProxy 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "itemModel"
            type: "QAbstractItemModel"
            isPointer: true
            read: "itemModel"
            write: "setItemModel"
            notify: "itemModelChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "xPosRole"
            type: "QString"
            read: "xPosRole"
            write: "setXPosRole"
            notify: "xPosRoleChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "yPosRole"
            type: "QString"
            read: "yPosRole"
            write: "setYPosRole"
            notify: "yPosRoleChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "zPosRole"
            type: "QString"
            read: "zPosRole"
            write: "setZPosRole"
            notify: "zPosRoleChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "rotationRole"
            type: "QString"
            read: "rotationRole"
            write: "setRotationRole"
            notify: "rotationRoleChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "xPosRolePattern"
            type: "QRegularExpression"
            read: "xPosRolePattern"
            write: "setXPosRolePattern"
            notify: "xPosRolePatternChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "yPosRolePattern"
            type: "QRegularExpression"
            read: "yPosRolePattern"
            write: "setYPosRolePattern"
            notify: "yPosRolePatternChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "zPosRolePattern"
            type: "QRegularExpression"
            read: "zPosRolePattern"
            write: "setZPosRolePattern"
            notify: "zPosRolePatternChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "rotationRolePattern"
            type: "QRegularExpression"
            read: "rotationRolePattern"
            write: "setRotationRolePattern"
            notify: "rotationRolePatternChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "xPosRoleReplace"
            type: "QString"
            read: "xPosRoleReplace"
            write: "setXPosRoleReplace"
            notify: "xPosRoleReplaceChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "yPosRoleReplace"
            type: "QString"
            read: "yPosRoleReplace"
            write: "setYPosRoleReplace"
            notify: "yPosRoleReplaceChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "zPosRoleReplace"
            type: "QString"
            read: "zPosRoleReplace"
            write: "setZPosRoleReplace"
            notify: "zPosRoleReplaceChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "rotationRoleReplace"
            type: "QString"
            read: "rotationRoleReplace"
            write: "setRotationRoleReplace"
            notify: "rotationRoleReplaceChanged"
            index: 12
            isFinal: true
        }
        Signal {
            name: "itemModelChanged"
            Parameter { name: "itemModel"; type: "QAbstractItemModel"; isPointer: true; isTypeConstant: true }
        }
        Signal {
            name: "xPosRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "yPosRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "zPosRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "rotationRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "xPosRolePatternChanged"
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "yPosRolePatternChanged"
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "zPosRolePatternChanged"
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "rotationRolePatternChanged"
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "rotationRoleReplaceChanged"
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "xPosRoleReplaceChanged"
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "yPosRoleReplaceChanged"
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "zPosRoleReplaceChanged"
            Parameter { name: "replace"; type: "QString" }
        }
    }
    Component {
        file: "qitemmodelsurfacedataproxy.h"
        name: "QItemModelSurfaceDataProxy"
        accessSemantics: "reference"
        prototype: "QSurfaceDataProxy"
        exports: ["QtGraphs/ItemModelSurfaceDataProxy 6.0"]
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "MultiMatchBehavior"
            isScoped: true
            values: ["First", "Last", "Average", "CumulativeY"]
        }
        Property {
            name: "itemModel"
            type: "QAbstractItemModel"
            isPointer: true
            read: "itemModel"
            write: "setItemModel"
            notify: "itemModelChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "rowRole"
            type: "QString"
            read: "rowRole"
            write: "setRowRole"
            notify: "rowRoleChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "columnRole"
            type: "QString"
            read: "columnRole"
            write: "setColumnRole"
            notify: "columnRoleChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "xPosRole"
            type: "QString"
            read: "xPosRole"
            write: "setXPosRole"
            notify: "xPosRoleChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "yPosRole"
            type: "QString"
            read: "yPosRole"
            write: "setYPosRole"
            notify: "yPosRoleChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "zPosRole"
            type: "QString"
            read: "zPosRole"
            write: "setZPosRole"
            notify: "zPosRoleChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "rowCategories"
            type: "QStringList"
            read: "rowCategories"
            write: "setRowCategories"
            notify: "rowCategoriesChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "columnCategories"
            type: "QStringList"
            read: "columnCategories"
            write: "setColumnCategories"
            notify: "columnCategoriesChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "useModelCategories"
            type: "bool"
            read: "useModelCategories"
            write: "setUseModelCategories"
            notify: "useModelCategoriesChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "autoRowCategories"
            type: "bool"
            read: "autoRowCategories"
            write: "setAutoRowCategories"
            notify: "autoRowCategoriesChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "autoColumnCategories"
            type: "bool"
            read: "autoColumnCategories"
            write: "setAutoColumnCategories"
            notify: "autoColumnCategoriesChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "rowRolePattern"
            type: "QRegularExpression"
            read: "rowRolePattern"
            write: "setRowRolePattern"
            notify: "rowRolePatternChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "columnRolePattern"
            type: "QRegularExpression"
            read: "columnRolePattern"
            write: "setColumnRolePattern"
            notify: "columnRolePatternChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "xPosRolePattern"
            type: "QRegularExpression"
            read: "xPosRolePattern"
            write: "setXPosRolePattern"
            notify: "xPosRolePatternChanged"
            index: 13
            isFinal: true
        }
        Property {
            name: "yPosRolePattern"
            type: "QRegularExpression"
            read: "yPosRolePattern"
            write: "setYPosRolePattern"
            notify: "yPosRolePatternChanged"
            index: 14
            isFinal: true
        }
        Property {
            name: "zPosRolePattern"
            type: "QRegularExpression"
            read: "zPosRolePattern"
            write: "setZPosRolePattern"
            notify: "zPosRolePatternChanged"
            index: 15
            isFinal: true
        }
        Property {
            name: "rowRoleReplace"
            type: "QString"
            read: "rowRoleReplace"
            write: "setRowRoleReplace"
            notify: "rowRoleReplaceChanged"
            index: 16
            isFinal: true
        }
        Property {
            name: "columnRoleReplace"
            type: "QString"
            read: "columnRoleReplace"
            write: "setColumnRoleReplace"
            notify: "columnRoleReplaceChanged"
            index: 17
            isFinal: true
        }
        Property {
            name: "xPosRoleReplace"
            type: "QString"
            read: "xPosRoleReplace"
            write: "setXPosRoleReplace"
            notify: "xPosRoleReplaceChanged"
            index: 18
            isFinal: true
        }
        Property {
            name: "yPosRoleReplace"
            type: "QString"
            read: "yPosRoleReplace"
            write: "setYPosRoleReplace"
            notify: "yPosRoleReplaceChanged"
            index: 19
            isFinal: true
        }
        Property {
            name: "zPosRoleReplace"
            type: "QString"
            read: "zPosRoleReplace"
            write: "setZPosRoleReplace"
            notify: "zPosRoleReplaceChanged"
            index: 20
            isFinal: true
        }
        Property {
            name: "multiMatchBehavior"
            type: "QItemModelSurfaceDataProxy::MultiMatchBehavior"
            read: "multiMatchBehavior"
            write: "setMultiMatchBehavior"
            notify: "multiMatchBehaviorChanged"
            index: 21
            isFinal: true
        }
        Signal {
            name: "itemModelChanged"
            Parameter { name: "itemModel"; type: "QAbstractItemModel"; isPointer: true; isTypeConstant: true }
        }
        Signal {
            name: "rowRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "columnRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "xPosRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "yPosRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "zPosRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal { name: "rowCategoriesChanged" }
        Signal { name: "columnCategoriesChanged" }
        Signal {
            name: "useModelCategoriesChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "autoRowCategoriesChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "autoColumnCategoriesChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "rowRolePatternChanged"
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "columnRolePatternChanged"
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "xPosRolePatternChanged"
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "yPosRolePatternChanged"
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "zPosRolePatternChanged"
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "rowRoleReplaceChanged"
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "columnRoleReplaceChanged"
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "xPosRoleReplaceChanged"
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "yPosRoleReplaceChanged"
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "zPosRoleReplaceChanged"
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "multiMatchBehaviorChanged"
            Parameter { name: "behavior"; type: "QItemModelSurfaceDataProxy::MultiMatchBehavior" }
        }
        Method {
            name: "rowCategoryIndex"
            type: "qsizetype"
            Parameter { name: "category"; type: "QString" }
        }
        Method {
            name: "columnCategoryIndex"
            type: "qsizetype"
            Parameter { name: "category"; type: "QString" }
        }
    }
    Component {
        file: "qlineseries.h"
        name: "QLineSeries"
        accessSemantics: "reference"
        prototype: "QXYSeries"
        exports: ["QtGraphs/LineSeries 6.0", "QtGraphs/LineSeries 6.9"]
        exportMetaObjectRevisions: [1536, 1545]
        Property {
            name: "width"
            type: "double"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "capStyle"
            type: "Qt::PenCapStyle"
            read: "capStyle"
            write: "setCapStyle"
            notify: "capStyleChanged"
            index: 1
            isFinal: true
        }
        Signal { name: "widthChanged" }
        Signal { name: "capStyleChanged" }
    }
    Component {
        file: "qlogvalue3daxisformatter.h"
        name: "QLogValue3DAxisFormatter"
        accessSemantics: "reference"
        prototype: "QValue3DAxisFormatter"
        exports: ["QtGraphs/LogValue3DAxisFormatter 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "base"
            type: "double"
            read: "base"
            write: "setBase"
            notify: "baseChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "autoSubGrid"
            type: "bool"
            read: "autoSubGrid"
            write: "setAutoSubGrid"
            notify: "autoSubGridChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "edgeLabelsVisible"
            type: "bool"
            read: "edgeLabelsVisible"
            write: "setEdgeLabelsVisible"
            notify: "edgeLabelsVisibleChanged"
            index: 2
            isFinal: true
        }
        Signal {
            name: "baseChanged"
            Parameter { name: "base"; type: "double" }
        }
        Signal {
            name: "autoSubGridChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "edgeLabelsVisibleChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        file: "qpiemodelmapper.h"
        name: "QPieModelMapper"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtGraphs/PieModelMapper 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "series"
            type: "QPieSeries"
            isPointer: true
            read: "series"
            write: "setSeries"
            notify: "seriesChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            read: "model"
            write: "setModel"
            notify: "modelChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "valuesSection"
            type: "qsizetype"
            read: "valuesSection"
            write: "setValuesSection"
            notify: "valuesSectionChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "labelsSection"
            type: "qsizetype"
            read: "labelsSection"
            write: "setLabelsSection"
            notify: "labelsSectionChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "first"
            type: "qsizetype"
            read: "first"
            write: "setFirst"
            notify: "firstChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "count"
            type: "qsizetype"
            read: "count"
            write: "setCount"
            notify: "countChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "orientation"
            type: "Qt::Orientation"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 6
            isFinal: true
        }
        Signal { name: "seriesChanged" }
        Signal { name: "modelChanged" }
        Signal { name: "valuesSectionChanged" }
        Signal { name: "labelsSectionChanged" }
        Signal { name: "firstChanged" }
        Signal { name: "countChanged" }
        Signal { name: "orientationChanged" }
        Method { name: "onSliceLabelChanged" }
        Method { name: "onSliceValueChanged" }
    }
    Component {
        file: "qpieseries.h"
        name: "QPieSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QAbstractSeries"
        exports: ["QtGraphs/PieSeries 6.0", "QtGraphs/PieSeries 6.9"]
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536, 1545]
        Property {
            name: "horizontalPosition"
            type: "double"
            read: "horizontalPosition"
            write: "setHorizontalPosition"
            notify: "horizontalPositionChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "verticalPosition"
            type: "double"
            read: "verticalPosition"
            write: "setVerticalPosition"
            notify: "verticalPositionChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "pieSize"
            type: "double"
            read: "pieSize"
            write: "setPieSize"
            notify: "pieSizeChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "startAngle"
            type: "double"
            read: "startAngle"
            write: "setStartAngle"
            notify: "startAngleChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "endAngle"
            type: "double"
            read: "endAngle"
            write: "setEndAngle"
            notify: "endAngleChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "count"
            type: "qsizetype"
            read: "count"
            notify: "countChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "sum"
            type: "double"
            read: "sum"
            notify: "sumChanged"
            index: 6
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "holeSize"
            type: "double"
            read: "holeSize"
            write: "setHoleSize"
            notify: "holeSizeChanged"
            index: 7
            isFinal: true
        }
        Signal {
            name: "added"
            Parameter { name: "slices"; type: "QList<QPieSlice*>" }
        }
        Signal {
            name: "removed"
            Parameter { name: "slices"; type: "QList<QPieSlice*>" }
        }
        Signal {
            name: "replaced"
            Parameter { name: "slices"; type: "QList<QPieSlice*>" }
        }
        Signal { name: "countChanged" }
        Signal { name: "sumChanged" }
        Signal { name: "pieSizeChanged" }
        Signal { name: "startAngleChanged" }
        Signal { name: "endAngleChanged" }
        Signal { name: "horizontalPositionChanged" }
        Signal { name: "verticalPositionChanged" }
        Signal { name: "holeSizeChanged" }
        Signal {
            name: "clicked"
            revision: 1545
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
        Signal {
            name: "doubleClicked"
            revision: 1545
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
        Signal {
            name: "pressed"
            revision: 1545
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
        Signal {
            name: "released"
            revision: 1545
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
        Method { name: "handleSliceChange" }
        Method {
            name: "append"
            type: "bool"
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
        Method {
            name: "append"
            type: "bool"
            Parameter { name: "slices"; type: "QList<QPieSlice*>" }
        }
        Method {
            name: "insert"
            type: "bool"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
        Method {
            name: "remove"
            type: "bool"
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
        Method { name: "clear" }
        Method {
            name: "append"
            type: "QPieSlice"
            isPointer: true
            Parameter { name: "label"; type: "QString" }
            Parameter { name: "value"; type: "double" }
        }
        Method {
            name: "at"
            type: "QPieSlice"
            isPointer: true
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "find"
            type: "QPieSlice"
            isPointer: true
            Parameter { name: "label"; type: "QString" }
        }
        Method {
            name: "replace"
            type: "bool"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
        Method {
            name: "removeMultiple"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "remove"
            type: "bool"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "replace"
            type: "bool"
            Parameter { name: "oldSlice"; type: "QPieSlice"; isPointer: true }
            Parameter { name: "newSlice"; type: "QPieSlice"; isPointer: true }
        }
        Method {
            name: "replace"
            type: "bool"
            Parameter { name: "slices"; type: "QList<QPieSlice*>" }
        }
        Method {
            name: "take"
            type: "bool"
            Parameter { name: "slice"; type: "QPieSlice"; isPointer: true }
        }
    }
    Component {
        file: "qpieslice.h"
        name: "QPieSlice"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtGraphs/PieSlice 6.0"]
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "LabelPosition"
            isScoped: true
            values: [
                "Outside",
                "InsideHorizontal",
                "InsideTangential",
                "InsideNormal"
            ]
        }
        Property {
            name: "label"
            type: "QString"
            read: "label"
            write: "setLabel"
            notify: "labelChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "labelVisible"
            type: "bool"
            read: "isLabelVisible"
            write: "setLabelVisible"
            notify: "labelVisibleChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "labelPosition"
            type: "LabelPosition"
            read: "labelPosition"
            write: "setLabelPosition"
            notify: "labelPositionChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "labelColor"
            type: "QColor"
            read: "labelColor"
            write: "setLabelColor"
            notify: "labelColorChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "labelFont"
            type: "QFont"
            read: "labelFont"
            write: "setLabelFont"
            notify: "labelFontChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "labelArmLengthFactor"
            type: "double"
            read: "labelArmLengthFactor"
            write: "setLabelArmLengthFactor"
            notify: "labelArmLengthFactorChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "borderColor"
            type: "QColor"
            read: "borderColor"
            write: "setBorderColor"
            notify: "borderColorChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "borderWidth"
            type: "double"
            read: "borderWidth"
            write: "setBorderWidth"
            notify: "borderWidthChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "value"
            type: "double"
            read: "value"
            write: "setValue"
            notify: "valueChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "exploded"
            type: "bool"
            read: "isExploded"
            write: "setExploded"
            notify: "explodedChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "explodeDistanceFactor"
            type: "double"
            read: "explodeDistanceFactor"
            write: "setExplodeDistanceFactor"
            notify: "explodeDistanceFactorChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "percentage"
            type: "double"
            read: "percentage"
            notify: "percentageChanged"
            index: 12
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "startAngle"
            type: "double"
            read: "startAngle"
            notify: "startAngleChanged"
            index: 13
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "angleSpan"
            type: "double"
            read: "angleSpan"
            notify: "angleSpanChanged"
            index: 14
            isReadonly: true
            isFinal: true
        }
        Signal { name: "labelChanged" }
        Signal { name: "labelVisibleChanged" }
        Signal { name: "labelFontChanged" }
        Signal { name: "labelColorChanged" }
        Signal { name: "valueChanged" }
        Signal { name: "explodedChanged" }
        Signal { name: "explodeDistanceFactorChanged" }
        Signal { name: "percentageChanged" }
        Signal { name: "startAngleChanged" }
        Signal { name: "angleSpanChanged" }
        Signal { name: "sliceChanged" }
        Signal { name: "labelPositionChanged" }
        Signal { name: "labelArmLengthFactorChanged" }
        Signal { name: "colorChanged" }
        Signal { name: "borderColorChanged" }
        Signal { name: "borderWidthChanged" }
    }
    Component {
        file: "private/qquickgraphsbarsseries_p.h"
        name: "QQuickGraphsBar3DSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QBar3DSeries"
        exports: ["QtGraphs/Bar3DSeries 6.0", "QtGraphs/Bar3DSeries 6.9"]
        exportMetaObjectRevisions: [1536, 1545]
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "selectedBar"
            type: "QPointF"
            read: "selectedBar"
            write: "setSelectedBar"
            notify: "selectedBarChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "invalidSelectionPosition"
            type: "QPointF"
            read: "invalidSelectionPosition"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "baseGradient"
            type: "QQuickGradient"
            isPointer: true
            read: "baseGradient"
            write: "setBaseGradient"
            notify: "baseGradientChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "singleHighlightGradient"
            type: "QQuickGradient"
            isPointer: true
            read: "singleHighlightGradient"
            write: "setSingleHighlightGradient"
            notify: "singleHighlightGradientChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "multiHighlightGradient"
            type: "QQuickGradient"
            isPointer: true
            read: "multiHighlightGradient"
            write: "setMultiHighlightGradient"
            notify: "multiHighlightGradientChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "rowColors"
            type: "QQuickGraphsColor"
            isList: true
            read: "rowColors"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "selectedBarChanged"
            Parameter { name: "position"; type: "QPointF" }
        }
        Signal {
            name: "baseGradientChanged"
            Parameter { name: "gradient"; type: "QQuickGradient"; isPointer: true }
        }
        Signal {
            name: "singleHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QQuickGradient"; isPointer: true }
        }
        Signal {
            name: "multiHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QQuickGradient"; isPointer: true }
        }
        Method { name: "handleBaseGradientUpdate" }
        Method { name: "handleSingleHighlightGradientUpdate" }
        Method { name: "handleMultiHighlightGradientUpdate" }
        Method { name: "handleRowColorUpdate" }
    }
    Component {
        file: "private/qquickgraphsbars_p.h"
        name: "QQuickGraphsBars"
        accessSemantics: "reference"
        defaultProperty: "seriesList"
        prototype: "QQuickGraphsItem"
        exports: [
            "QtGraphs/Bars3D 6.0",
            "QtGraphs/Bars3D 6.2",
            "QtGraphs/Bars3D 6.3",
            "QtGraphs/Bars3D 6.4",
            "QtGraphs/Bars3D 6.6",
            "QtGraphs/Bars3D 6.7",
            "QtGraphs/Bars3D 6.8",
            "QtGraphs/Bars3D 6.9"
        ]
        exportMetaObjectRevisions: [
            1536,
            1538,
            1539,
            1540,
            1542,
            1543,
            1544,
            1545
        ]
        Property {
            name: "rowAxis"
            type: "QCategory3DAxis"
            isPointer: true
            read: "rowAxis"
            write: "setRowAxis"
            notify: "rowAxisChanged"
            index: 0
        }
        Property {
            name: "valueAxis"
            type: "QValue3DAxis"
            isPointer: true
            read: "valueAxis"
            write: "setValueAxis"
            notify: "valueAxisChanged"
            index: 1
        }
        Property {
            name: "columnAxis"
            type: "QCategory3DAxis"
            isPointer: true
            read: "columnAxis"
            write: "setColumnAxis"
            notify: "columnAxisChanged"
            index: 2
        }
        Property {
            name: "multiSeriesUniform"
            type: "bool"
            read: "isMultiSeriesUniform"
            write: "setMultiSeriesUniform"
            notify: "multiSeriesUniformChanged"
            index: 3
        }
        Property {
            name: "barThickness"
            type: "float"
            read: "barThickness"
            write: "setBarThickness"
            notify: "barThicknessChanged"
            index: 4
        }
        Property {
            name: "barSpacing"
            type: "QSizeF"
            read: "barSpacing"
            write: "setBarSpacing"
            notify: "barSpacingChanged"
            index: 5
        }
        Property {
            name: "barSpacingRelative"
            type: "bool"
            read: "isBarSpacingRelative"
            write: "setBarSpacingRelative"
            notify: "barSpacingRelativeChanged"
            index: 6
        }
        Property {
            name: "barSeriesMargin"
            type: "QSizeF"
            read: "barSeriesMargin"
            write: "setBarSeriesMargin"
            notify: "barSeriesMarginChanged"
            index: 7
        }
        Property {
            name: "seriesList"
            type: "QBar3DSeries"
            isList: true
            read: "seriesList"
            index: 8
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "selectedSeries"
            type: "QBar3DSeries"
            isPointer: true
            read: "selectedSeries"
            notify: "selectedSeriesChanged"
            index: 9
            isReadonly: true
        }
        Property {
            name: "primarySeries"
            type: "QBar3DSeries"
            isPointer: true
            read: "primarySeries"
            write: "setPrimarySeries"
            notify: "primarySeriesChanged"
            index: 10
        }
        Property {
            name: "floorLevel"
            type: "float"
            read: "floorLevel"
            write: "setFloorLevel"
            notify: "floorLevelChanged"
            index: 11
        }
        Signal {
            name: "rowAxisChanged"
            Parameter { name: "axis"; type: "QCategory3DAxis"; isPointer: true }
        }
        Signal {
            name: "valueAxisChanged"
            Parameter { name: "axis"; type: "QValue3DAxis"; isPointer: true }
        }
        Signal {
            name: "columnAxisChanged"
            Parameter { name: "axis"; type: "QCategory3DAxis"; isPointer: true }
        }
        Signal {
            name: "multiSeriesUniformChanged"
            Parameter { name: "uniform"; type: "bool" }
        }
        Signal {
            name: "barThicknessChanged"
            Parameter { name: "thicknessRatio"; type: "float" }
        }
        Signal {
            name: "barSpacingChanged"
            Parameter { name: "spacing"; type: "QSizeF" }
        }
        Signal {
            name: "barSpacingRelativeChanged"
            Parameter { name: "relative"; type: "bool" }
        }
        Signal {
            name: "barSeriesMarginChanged"
            Parameter { name: "margin"; type: "QSizeF" }
        }
        Signal {
            name: "meshFileNameChanged"
            Parameter { name: "filename"; type: "QString" }
        }
        Signal {
            name: "primarySeriesChanged"
            Parameter { name: "series"; type: "QBar3DSeries"; isPointer: true }
        }
        Signal {
            name: "selectedSeriesChanged"
            Parameter { name: "series"; type: "QBar3DSeries"; isPointer: true }
        }
        Signal {
            name: "floorLevelChanged"
            Parameter { name: "level"; type: "float" }
        }
        Method {
            name: "handleAxisXChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisYChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisZChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleSeriesMeshChanged"
            Parameter { name: "mesh"; type: "QAbstract3DSeries::Mesh" }
        }
        Method {
            name: "handleMeshSmoothChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Method { name: "handleCameraRotationChanged" }
        Method { name: "handleArrayReset" }
        Method {
            name: "handleRowsAdded"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "handleRowsChanged"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "handleRowsRemoved"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "handleRowsInserted"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "handleItemChanged"
            Parameter { name: "rowIndex"; type: "qsizetype" }
            Parameter { name: "columnIndex"; type: "qsizetype" }
        }
        Method { name: "handleDataRowLabelsChanged" }
        Method { name: "handleDataColumnLabelsChanged" }
        Method { name: "handleRowColorsChanged" }
        Method { name: "handleValueColoringChanged" }
        Method {
            name: "addSeries"
            Parameter { name: "series"; type: "QBar3DSeries"; isPointer: true }
        }
        Method {
            name: "removeSeries"
            Parameter { name: "series"; type: "QBar3DSeries"; isPointer: true }
        }
        Method {
            name: "insertSeries"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "series"; type: "QBar3DSeries"; isPointer: true }
        }
        Method { name: "clearSelection" }
    }
    Component {
        file: "private/qquickgraphscolor_p.h"
        name: "QQuickGraphsColor"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtGraphs/Color 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 0
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
    }
    Component {
        file: "private/qquickgraphsitem_p.h"
        name: "QQuickGraphsItem"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DViewport"
        exports: [
            "QtGraphs/GraphsItem3D 6.0",
            "QtGraphs/GraphsItem3D 6.2",
            "QtGraphs/GraphsItem3D 6.3",
            "QtGraphs/GraphsItem3D 6.4",
            "QtGraphs/GraphsItem3D 6.6",
            "QtGraphs/GraphsItem3D 6.7",
            "QtGraphs/GraphsItem3D 6.8",
            "QtGraphs/GraphsItem3D 6.9"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [
            1536,
            1538,
            1539,
            1540,
            1542,
            1543,
            1544,
            1545
        ]
        Property {
            name: "rootNode"
            revision: 1545
            type: "QQuick3DNode"
            isPointer: true
            read: "rootNode"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "selectionMode"
            type: "QtGraphs3D::SelectionFlags"
            read: "selectionMode"
            write: "setSelectionMode"
            notify: "selectionModeChanged"
            index: 1
        }
        Property {
            name: "shadowQuality"
            type: "QtGraphs3D::ShadowQuality"
            read: "shadowQuality"
            write: "setShadowQuality"
            notify: "shadowQualityChanged"
            index: 2
        }
        Property {
            name: "msaaSamples"
            type: "int"
            read: "msaaSamples"
            write: "setMsaaSamples"
            notify: "msaaSamplesChanged"
            index: 3
        }
        Property {
            name: "scene"
            type: "Q3DScene"
            isPointer: true
            read: "scene"
            notify: "sceneChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "theme"
            type: "QGraphsTheme"
            isPointer: true
            read: "theme"
            write: "setTheme"
            notify: "themeChanged"
            index: 5
        }
        Property {
            name: "renderingMode"
            type: "QtGraphs3D::RenderingMode"
            read: "renderingMode"
            write: "setRenderingMode"
            notify: "renderingModeChanged"
            index: 6
        }
        Property {
            name: "transparencyTechnique"
            revision: 1545
            type: "QtGraphs3D::TransparencyTechnique"
            read: "transparencyTechnique"
            write: "setTransparencyTechnique"
            notify: "transparencyTechniqueChanged"
            index: 7
        }
        Property {
            name: "measureFps"
            type: "bool"
            read: "measureFps"
            write: "setMeasureFps"
            notify: "measureFpsChanged"
            index: 8
        }
        Property {
            name: "currentFps"
            type: "int"
            read: "currentFps"
            notify: "currentFpsChanged"
            index: 9
            isReadonly: true
        }
        Property {
            name: "customItemList"
            type: "QCustom3DItem"
            isList: true
            read: "customItemList"
            index: 10
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "orthoProjection"
            type: "bool"
            read: "isOrthoProjection"
            write: "setOrthoProjection"
            notify: "orthoProjectionChanged"
            index: 11
        }
        Property {
            name: "selectedElement"
            type: "QtGraphs3D::ElementType"
            read: "selectedElement"
            notify: "selectedElementChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "aspectRatio"
            type: "double"
            read: "aspectRatio"
            write: "setAspectRatio"
            notify: "aspectRatioChanged"
            index: 13
        }
        Property {
            name: "optimizationHint"
            type: "QtGraphs3D::OptimizationHint"
            read: "optimizationHint"
            write: "setOptimizationHint"
            notify: "optimizationHintChanged"
            index: 14
        }
        Property {
            name: "polar"
            type: "bool"
            read: "isPolar"
            write: "setPolar"
            notify: "polarChanged"
            index: 15
        }
        Property {
            name: "labelMargin"
            type: "float"
            read: "labelMargin"
            write: "setLabelMargin"
            notify: "labelMarginChanged"
            index: 16
        }
        Property {
            name: "radialLabelOffset"
            type: "float"
            read: "radialLabelOffset"
            write: "setRadialLabelOffset"
            notify: "radialLabelOffsetChanged"
            index: 17
        }
        Property {
            name: "horizontalAspectRatio"
            type: "double"
            read: "horizontalAspectRatio"
            write: "setHorizontalAspectRatio"
            notify: "horizontalAspectRatioChanged"
            index: 18
        }
        Property {
            name: "locale"
            type: "QLocale"
            read: "locale"
            write: "setLocale"
            notify: "localeChanged"
            index: 19
        }
        Property {
            name: "queriedGraphPosition"
            type: "QVector3D"
            read: "queriedGraphPosition"
            notify: "queriedGraphPositionChanged"
            index: 20
            isReadonly: true
        }
        Property {
            name: "margin"
            type: "double"
            read: "margin"
            write: "setMargin"
            notify: "marginChanged"
            index: 21
        }
        Property {
            name: "cameraXRotation"
            type: "float"
            read: "cameraXRotation"
            write: "setCameraXRotation"
            notify: "cameraXRotationChanged"
            index: 22
        }
        Property {
            name: "cameraYRotation"
            type: "float"
            read: "cameraYRotation"
            write: "setCameraYRotation"
            notify: "cameraYRotationChanged"
            index: 23
        }
        Property {
            name: "minCameraXRotation"
            revision: 1545
            type: "float"
            read: "minCameraXRotation"
            write: "setMinCameraXRotation"
            notify: "minCameraXRotationChanged"
            index: 24
        }
        Property {
            name: "maxCameraXRotation"
            revision: 1545
            type: "float"
            read: "maxCameraXRotation"
            write: "setMaxCameraXRotation"
            notify: "maxCameraXRotationChanged"
            index: 25
        }
        Property {
            name: "minCameraYRotation"
            revision: 1545
            type: "float"
            read: "minCameraYRotation"
            write: "setMinCameraYRotation"
            notify: "minCameraYRotationChanged"
            index: 26
        }
        Property {
            name: "maxCameraYRotation"
            revision: 1545
            type: "float"
            read: "maxCameraYRotation"
            write: "setMaxCameraYRotation"
            notify: "maxCameraYRotationChanged"
            index: 27
        }
        Property {
            name: "cameraZoomLevel"
            type: "float"
            read: "cameraZoomLevel"
            write: "setCameraZoomLevel"
            notify: "cameraZoomLevelChanged"
            index: 28
        }
        Property {
            name: "cameraPreset"
            type: "QtGraphs3D::CameraPreset"
            read: "cameraPreset"
            write: "setCameraPreset"
            notify: "cameraPresetChanged"
            index: 29
        }
        Property {
            name: "cameraTargetPosition"
            type: "QVector3D"
            read: "cameraTargetPosition"
            write: "setCameraTargetPosition"
            notify: "cameraTargetPositionChanged"
            index: 30
        }
        Property {
            name: "minCameraZoomLevel"
            type: "float"
            read: "minCameraZoomLevel"
            write: "setMinCameraZoomLevel"
            notify: "minCameraZoomLevelChanged"
            index: 31
        }
        Property {
            name: "maxCameraZoomLevel"
            type: "float"
            read: "maxCameraZoomLevel"
            write: "setMaxCameraZoomLevel"
            notify: "maxCameraZoomLevelChanged"
            index: 32
        }
        Property {
            name: "wrapCameraXRotation"
            type: "bool"
            read: "wrapCameraXRotation"
            write: "setWrapCameraXRotation"
            notify: "wrapCameraXRotationChanged"
            index: 33
        }
        Property {
            name: "wrapCameraYRotation"
            type: "bool"
            read: "wrapCameraYRotation"
            write: "setWrapCameraYRotation"
            notify: "wrapCameraYRotationChanged"
            index: 34
        }
        Property {
            name: "rotationEnabled"
            type: "bool"
            read: "rotationEnabled"
            write: "setRotationEnabled"
            notify: "rotationEnabledChanged"
            index: 35
        }
        Property {
            name: "zoomAtTargetEnabled"
            type: "bool"
            read: "zoomAtTargetEnabled"
            write: "setZoomAtTargetEnabled"
            notify: "zoomAtTargetEnabledChanged"
            index: 36
        }
        Property {
            name: "selectionEnabled"
            type: "bool"
            read: "selectionEnabled"
            write: "setSelectionEnabled"
            notify: "selectionEnabledChanged"
            index: 37
        }
        Property {
            name: "zoomEnabled"
            type: "bool"
            read: "zoomEnabled"
            write: "setZoomEnabled"
            notify: "zoomEnabledChanged"
            index: 38
        }
        Property {
            name: "lightColor"
            type: "QColor"
            read: "lightColor"
            write: "setLightColor"
            notify: "lightColorChanged"
            index: 39
        }
        Property {
            name: "ambientLightStrength"
            type: "float"
            read: "ambientLightStrength"
            write: "setAmbientLightStrength"
            notify: "ambientLightStrengthChanged"
            index: 40
        }
        Property {
            name: "lightStrength"
            type: "float"
            read: "lightStrength"
            write: "setLightStrength"
            notify: "lightStrengthChanged"
            index: 41
        }
        Property {
            name: "shadowStrength"
            type: "float"
            read: "shadowStrength"
            write: "setShadowStrength"
            notify: "shadowStrengthChanged"
            index: 42
        }
        Property {
            name: "gridLineType"
            type: "QtGraphs3D::GridLineType"
            read: "gridLineType"
            write: "setGridLineType"
            notify: "gridLineTypeChanged"
            index: 43
            isFinal: true
        }
        Signal {
            name: "selectionModeChanged"
            Parameter { name: "mode"; type: "QtGraphs3D::SelectionFlags" }
        }
        Signal {
            name: "shadowQualityChanged"
            Parameter { name: "quality"; type: "QtGraphs3D::ShadowQuality" }
        }
        Signal {
            name: "shadowsSupportedChanged"
            Parameter { name: "supported"; type: "bool" }
        }
        Signal {
            name: "msaaSamplesChanged"
            Parameter { name: "samples"; type: "int" }
        }
        Signal {
            name: "themeChanged"
            Parameter { name: "theme"; type: "QGraphsTheme"; isPointer: true }
        }
        Signal {
            name: "renderingModeChanged"
            Parameter { name: "mode"; type: "QtGraphs3D::RenderingMode" }
        }
        Signal {
            name: "transparencyTechniqueChanged"
            revision: 1545
            Parameter { name: "technique"; type: "QtGraphs3D::TransparencyTechnique" }
        }
        Signal {
            name: "measureFpsChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "currentFpsChanged"
            Parameter { name: "fps"; type: "int" }
        }
        Signal {
            name: "selectedElementChanged"
            Parameter { name: "type"; type: "QtGraphs3D::ElementType" }
        }
        Signal {
            name: "orthoProjectionChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "aspectRatioChanged"
            Parameter { name: "ratio"; type: "double" }
        }
        Signal {
            name: "optimizationHintChanged"
            Parameter { name: "hint"; type: "QtGraphs3D::OptimizationHint" }
        }
        Signal {
            name: "polarChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "labelMarginChanged"
            Parameter { name: "margin"; type: "float" }
        }
        Signal {
            name: "radialLabelOffsetChanged"
            Parameter { name: "offset"; type: "float" }
        }
        Signal {
            name: "horizontalAspectRatioChanged"
            Parameter { name: "ratio"; type: "double" }
        }
        Signal {
            name: "localeChanged"
            Parameter { name: "locale"; type: "QLocale" }
        }
        Signal {
            name: "queriedGraphPositionChanged"
            Parameter { name: "data"; type: "QVector3D" }
        }
        Signal {
            name: "marginChanged"
            Parameter { name: "margin"; type: "double" }
        }
        Signal {
            name: "cameraPresetChanged"
            Parameter { name: "preset"; type: "QtGraphs3D::CameraPreset" }
        }
        Signal {
            name: "cameraXRotationChanged"
            Parameter { name: "rotation"; type: "float" }
        }
        Signal {
            name: "cameraYRotationChanged"
            Parameter { name: "rotation"; type: "float" }
        }
        Signal {
            name: "cameraZoomLevelChanged"
            Parameter { name: "zoomLevel"; type: "float" }
        }
        Signal {
            name: "cameraTargetPositionChanged"
            Parameter { name: "target"; type: "QVector3D" }
        }
        Signal {
            name: "minCameraZoomLevelChanged"
            Parameter { name: "zoomLevel"; type: "float" }
        }
        Signal {
            name: "maxCameraZoomLevelChanged"
            Parameter { name: "zoomLevel"; type: "float" }
        }
        Signal {
            name: "minCameraXRotationChanged"
            revision: 1545
            Parameter { name: "rotation"; type: "float" }
        }
        Signal {
            name: "minCameraYRotationChanged"
            revision: 1545
            Parameter { name: "rotation"; type: "float" }
        }
        Signal {
            name: "maxCameraXRotationChanged"
            revision: 1545
            Parameter { name: "rotation"; type: "float" }
        }
        Signal {
            name: "maxCameraYRotationChanged"
            revision: 1545
            Parameter { name: "rotation"; type: "float" }
        }
        Signal {
            name: "wrapCameraXRotationChanged"
            Parameter { name: "wrap"; type: "bool" }
        }
        Signal {
            name: "wrapCameraYRotationChanged"
            Parameter { name: "wrap"; type: "bool" }
        }
        Signal { name: "needRender" }
        Signal { name: "themeTypeChanged" }
        Signal {
            name: "axisXChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Signal {
            name: "axisZChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Signal {
            name: "activeThemeChanged"
            Parameter { name: "activeTheme"; type: "QGraphsTheme"; isPointer: true }
        }
        Signal {
            name: "tapped"
            Parameter { name: "eventPoint"; type: "QEventPoint" }
            Parameter { name: "button"; type: "Qt::MouseButton" }
        }
        Signal {
            name: "doubleTapped"
            Parameter { name: "eventPoint"; type: "QEventPoint" }
            Parameter { name: "button"; type: "Qt::MouseButton" }
        }
        Signal { name: "longPressed" }
        Signal {
            name: "dragged"
            Parameter { name: "delta"; type: "QVector2D" }
        }
        Signal {
            name: "wheel"
            Parameter { name: "event"; type: "QQuickWheelEvent"; isPointer: true }
        }
        Signal {
            name: "pinch"
            Parameter { name: "delta"; type: "double" }
        }
        Signal {
            name: "mouseMove"
            Parameter { name: "mousePos"; type: "QPoint" }
        }
        Signal {
            name: "zoomEnabledChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "zoomAtTargetEnabledChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "rotationEnabledChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "selectionEnabledChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal { name: "ambientLightStrengthChanged" }
        Signal { name: "lightStrengthChanged" }
        Signal { name: "shadowStrengthChanged" }
        Signal { name: "lightColorChanged" }
        Signal { name: "gridLineTypeChanged" }
        Method {
            name: "handleAxisXChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisYChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisZChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method { name: "handleFpsChanged" }
        Method {
            name: "windowDestroyed"
            Parameter { name: "obj"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "handleAxisTitleChanged"
            Parameter { name: "title"; type: "QString" }
        }
        Method { name: "handleAxisLabelsChanged" }
        Method {
            name: "handleAxisRangeChanged"
            Parameter { name: "min"; type: "float" }
            Parameter { name: "max"; type: "float" }
        }
        Method {
            name: "handleAxisSegmentCountChanged"
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "handleAxisSubSegmentCountChanged"
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "handleAxisAutoAdjustRangeChanged"
            Parameter { name: "autoAdjust"; type: "bool" }
        }
        Method {
            name: "handleAxisScaleLabelsByCountChanged"
            Parameter { name: "adjust"; type: "bool" }
        }
        Method {
            name: "handleAxisLabelSizeChanged"
            Parameter { name: "size"; type: "double" }
        }
        Method {
            name: "handleAxisLabelFormatChanged"
            Parameter { name: "format"; type: "QString" }
        }
        Method {
            name: "handleAxisReversedChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Method { name: "handleAxisFormatterDirty" }
        Method {
            name: "handleAxisLabelAutoRotationChanged"
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "handleAxisTitleVisibilityChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Method {
            name: "handleAxisLabelVisibilityChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Method {
            name: "handleAxisTitleFixedChanged"
            Parameter { name: "fixed"; type: "bool" }
        }
        Method {
            name: "handleAxisTitleOffsetChanged"
            Parameter { name: "offset"; type: "float" }
        }
        Method {
            name: "handleInputPositionChanged"
            Parameter { name: "position"; type: "QPoint" }
        }
        Method {
            name: "handleSeriesVisibilityChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Method {
            name: "handleThemeColorStyleChanged"
            Parameter { name: "style"; type: "QGraphsTheme::ColorStyle" }
        }
        Method {
            name: "handleThemeBaseColorsChanged"
            Parameter { name: "color"; type: "QColor"; isList: true }
        }
        Method {
            name: "handleThemeBaseGradientsChanged"
            Parameter { name: "gradient"; type: "QLinearGradient"; isList: true }
        }
        Method {
            name: "handleThemeSingleHighlightColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Method {
            name: "handleThemeSingleHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QLinearGradient" }
        }
        Method {
            name: "handleThemeMultiHighlightColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Method {
            name: "handleThemeMultiHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QLinearGradient" }
        }
        Method {
            name: "handleThemeTypeChanged"
            Parameter { name: "theme"; type: "QGraphsTheme::Theme" }
        }
        Method {
            name: "handleRequestShadowQuality"
            Parameter { name: "quality"; type: "QtGraphs3D::ShadowQuality" }
        }
        Method { name: "updateCustomItem" }
        Method {
            name: "hasSeries"
            type: "bool"
            Parameter { name: "series"; type: "QAbstract3DSeries"; isPointer: true }
        }
        Method { name: "clearSelection" }
        Method {
            name: "addCustomItem"
            type: "qsizetype"
            Parameter { name: "item"; type: "QCustom3DItem"; isPointer: true }
        }
        Method { name: "removeCustomItems" }
        Method {
            name: "removeCustomItem"
            Parameter { name: "item"; type: "QCustom3DItem"; isPointer: true }
        }
        Method {
            name: "removeCustomItemAt"
            Parameter { name: "position"; type: "QVector3D" }
        }
        Method {
            name: "releaseCustomItem"
            Parameter { name: "item"; type: "QCustom3DItem"; isPointer: true }
        }
        Method { name: "selectedLabelIndex"; type: "int"; isMethodConstant: true }
        Method {
            name: "selectedAxis"
            type: "QAbstract3DAxis"
            isPointer: true
            isMethodConstant: true
        }
        Method { name: "selectedCustomItemIndex"; type: "qsizetype"; isMethodConstant: true }
        Method {
            name: "selectedCustomItem"
            type: "QCustom3DItem"
            isPointer: true
            isMethodConstant: true
        }
        Method { name: "setDefaultInputHandler" }
        Method { name: "unsetDefaultInputHandler" }
        Method { name: "unsetDefaultTapHandler" }
        Method { name: "unsetDefaultDragHandler" }
        Method { name: "unsetDefaultWheelHandler" }
        Method { name: "unsetDefaultPinchHandler" }
        Method {
            name: "setDragButton"
            Parameter { name: "button"; type: "Qt::MouseButtons" }
        }
        Method {
            name: "doPicking"
            type: "bool"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "doRayPicking"
            revision: 1545
            type: "bool"
            Parameter { name: "origin"; type: "QVector3D" }
            Parameter { name: "direction"; type: "QVector3D" }
        }
    }
    Component {
        file: "private/qquickgraphsscatter_p.h"
        name: "QQuickGraphsScatter"
        accessSemantics: "reference"
        defaultProperty: "seriesList"
        prototype: "QQuickGraphsItem"
        exports: [
            "QtGraphs/Scatter3D 6.0",
            "QtGraphs/Scatter3D 6.2",
            "QtGraphs/Scatter3D 6.3",
            "QtGraphs/Scatter3D 6.4",
            "QtGraphs/Scatter3D 6.6",
            "QtGraphs/Scatter3D 6.7",
            "QtGraphs/Scatter3D 6.8",
            "QtGraphs/Scatter3D 6.9"
        ]
        exportMetaObjectRevisions: [
            1536,
            1538,
            1539,
            1540,
            1542,
            1543,
            1544,
            1545
        ]
        Property {
            name: "axisX"
            type: "QValue3DAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 0
        }
        Property {
            name: "axisY"
            type: "QValue3DAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 1
        }
        Property {
            name: "axisZ"
            type: "QValue3DAxis"
            isPointer: true
            read: "axisZ"
            write: "setAxisZ"
            notify: "axisZChanged"
            index: 2
        }
        Property {
            name: "selectedSeries"
            type: "QScatter3DSeries"
            isPointer: true
            read: "selectedSeries"
            notify: "selectedSeriesChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "seriesList"
            type: "QScatter3DSeries"
            isList: true
            read: "seriesList"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "axisXChanged"
            Parameter { name: "axis"; type: "QValue3DAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            Parameter { name: "axis"; type: "QValue3DAxis"; isPointer: true }
        }
        Signal {
            name: "axisZChanged"
            Parameter { name: "axis"; type: "QValue3DAxis"; isPointer: true }
        }
        Signal {
            name: "selectedSeriesChanged"
            Parameter { name: "series"; type: "QScatter3DSeries"; isPointer: true }
        }
        Method {
            name: "handleAxisXChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisYChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisZChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method { name: "handleSeriesMeshChanged" }
        Method {
            name: "handleMeshSmoothChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Method { name: "handleArrayReset" }
        Method {
            name: "handleItemsAdded"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "handleItemsChanged"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "handleItemsRemoved"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "handleItemsInserted"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method { name: "cameraRotationChanged" }
        Method { name: "clearSelection" }
        Method {
            name: "addSeries"
            Parameter { name: "series"; type: "QScatter3DSeries"; isPointer: true }
        }
        Method {
            name: "removeSeries"
            Parameter { name: "series"; type: "QScatter3DSeries"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickgraphsscatterseries_p.h"
        name: "QQuickGraphsScatter3DSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QScatter3DSeries"
        exports: ["QtGraphs/Scatter3DSeries 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "baseGradient"
            type: "QQuickGradient"
            isPointer: true
            read: "baseGradient"
            write: "setBaseGradient"
            notify: "baseGradientChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "singleHighlightGradient"
            type: "QQuickGradient"
            isPointer: true
            read: "singleHighlightGradient"
            write: "setSingleHighlightGradient"
            notify: "singleHighlightGradientChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "multiHighlightGradient"
            type: "QQuickGradient"
            isPointer: true
            read: "multiHighlightGradient"
            write: "setMultiHighlightGradient"
            notify: "multiHighlightGradientChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "invalidSelectionIndex"
            type: "qsizetype"
            read: "invalidSelectionIndex"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "baseGradientChanged"
            Parameter { name: "gradient"; type: "QQuickGradient"; isPointer: true }
        }
        Signal {
            name: "singleHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QQuickGradient"; isPointer: true }
        }
        Signal {
            name: "multiHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QQuickGradient"; isPointer: true }
        }
        Method { name: "handleBaseGradientUpdate" }
        Method { name: "handleSingleHighlightGradientUpdate" }
        Method { name: "handleMultiHighlightGradientUpdate" }
    }
    Component {
        file: "private/qquickgraphssplineseries_p.h"
        name: "QQuickGraphsSpline3DSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QSpline3DSeries"
        exports: ["QtGraphs/Spline3DSeries 6.9"]
        exportMetaObjectRevisions: [1545]
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "baseGradient"
            type: "QQuickGradient"
            isPointer: true
            read: "baseGradient"
            write: "setBaseGradient"
            notify: "baseGradientChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "singleHighlightGradient"
            type: "QQuickGradient"
            isPointer: true
            read: "singleHighlightGradient"
            write: "setSingleHighlightGradient"
            notify: "singleHighlightGradientChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "multiHighlightGradient"
            type: "QQuickGradient"
            isPointer: true
            read: "multiHighlightGradient"
            write: "setMultiHighlightGradient"
            notify: "multiHighlightGradientChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "invalidSelectionIndex"
            type: "int"
            read: "invalidSelectionIndex"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "baseGradientChanged"
            Parameter { name: "gradient"; type: "QQuickGradient"; isPointer: true }
        }
        Signal {
            name: "singleHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QQuickGradient"; isPointer: true }
        }
        Signal {
            name: "multiHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QQuickGradient"; isPointer: true }
        }
        Method { name: "handleBaseGradientUpdate" }
        Method { name: "handleSingleHighlightGradientUpdate" }
        Method { name: "handleMultiHighlightGradientUpdate" }
    }
    Component {
        file: "private/qquickgraphssurface_p.h"
        name: "QQuickGraphsSurface"
        accessSemantics: "reference"
        defaultProperty: "seriesList"
        prototype: "QQuickGraphsItem"
        exports: [
            "QtGraphs/Surface3D 6.0",
            "QtGraphs/Surface3D 6.2",
            "QtGraphs/Surface3D 6.3",
            "QtGraphs/Surface3D 6.4",
            "QtGraphs/Surface3D 6.6",
            "QtGraphs/Surface3D 6.7",
            "QtGraphs/Surface3D 6.8",
            "QtGraphs/Surface3D 6.9"
        ]
        exportMetaObjectRevisions: [
            1536,
            1538,
            1539,
            1540,
            1542,
            1543,
            1544,
            1545
        ]
        Property {
            name: "axisX"
            type: "QValue3DAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 0
        }
        Property {
            name: "axisY"
            type: "QValue3DAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 1
        }
        Property {
            name: "axisZ"
            type: "QValue3DAxis"
            isPointer: true
            read: "axisZ"
            write: "setAxisZ"
            notify: "axisZChanged"
            index: 2
        }
        Property {
            name: "selectedSeries"
            type: "QSurface3DSeries"
            isPointer: true
            read: "selectedSeries"
            notify: "selectedSeriesChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "seriesList"
            type: "QSurface3DSeries"
            isList: true
            read: "seriesList"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "flipHorizontalGrid"
            type: "bool"
            read: "flipHorizontalGrid"
            write: "setFlipHorizontalGrid"
            notify: "flipHorizontalGridChanged"
            index: 5
        }
        Signal {
            name: "axisXChanged"
            Parameter { name: "axis"; type: "QValue3DAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            Parameter { name: "axis"; type: "QValue3DAxis"; isPointer: true }
        }
        Signal {
            name: "axisZChanged"
            Parameter { name: "axis"; type: "QValue3DAxis"; isPointer: true }
        }
        Signal {
            name: "selectedSeriesChanged"
            Parameter { name: "series"; type: "QSurface3DSeries"; isPointer: true }
        }
        Signal {
            name: "flipHorizontalGridChanged"
            Parameter { name: "flip"; type: "bool" }
        }
        Method {
            name: "handleAxisXChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisYChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisZChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method { name: "handleShadingChanged" }
        Method { name: "handleWireframeColorChanged" }
        Method {
            name: "handleFlipHorizontalGridChanged"
            Parameter { name: "flip"; type: "bool" }
        }
        Method { name: "handleArrayReset" }
        Method {
            name: "handleRowsAdded"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "handleRowsChanged"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "handleRowsRemoved"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "handleRowsInserted"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "handleItemChanged"
            Parameter { name: "rowIndex"; type: "qsizetype" }
            Parameter { name: "columnIndex"; type: "qsizetype" }
        }
        Method {
            name: "handleFlatShadingSupportedChange"
            Parameter { name: "supported"; type: "bool" }
        }
        Method {
            name: "addSeries"
            Parameter { name: "series"; type: "QSurface3DSeries"; isPointer: true }
        }
        Method {
            name: "removeSeries"
            Parameter { name: "series"; type: "QSurface3DSeries"; isPointer: true }
        }
        Method { name: "clearSelection" }
    }
    Component {
        file: "private/qquickgraphssurfaceseries_p.h"
        name: "QQuickGraphsSurface3DSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QSurface3DSeries"
        exports: ["QtGraphs/Surface3DSeries 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "selectedPoint"
            type: "QPointF"
            read: "selectedPoint"
            write: "setSelectedPoint"
            notify: "selectedPointChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "invalidSelectionPosition"
            type: "QPointF"
            read: "invalidSelectionPosition"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "baseGradient"
            type: "QQuickGradient"
            isPointer: true
            read: "baseGradient"
            write: "setBaseGradient"
            notify: "baseGradientChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "singleHighlightGradient"
            type: "QQuickGradient"
            isPointer: true
            read: "singleHighlightGradient"
            write: "setSingleHighlightGradient"
            notify: "singleHighlightGradientChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "multiHighlightGradient"
            type: "QQuickGradient"
            isPointer: true
            read: "multiHighlightGradient"
            write: "setMultiHighlightGradient"
            notify: "multiHighlightGradientChanged"
            index: 5
            isFinal: true
        }
        Signal {
            name: "selectedPointChanged"
            Parameter { name: "position"; type: "QPointF" }
        }
        Signal {
            name: "baseGradientChanged"
            Parameter { name: "gradient"; type: "QQuickGradient"; isPointer: true }
        }
        Signal {
            name: "singleHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QQuickGradient"; isPointer: true }
        }
        Signal {
            name: "multiHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QQuickGradient"; isPointer: true }
        }
        Signal { name: "gradientsChanged" }
        Method { name: "handleBaseGradientUpdate" }
        Method { name: "handleSingleHighlightGradientUpdate" }
        Method { name: "handleMultiHighlightGradientUpdate" }
    }
    Component {
        file: "qscatter3dseries.h"
        name: "QScatter3DSeries"
        accessSemantics: "reference"
        prototype: "QAbstract3DSeries"
        exports: ["QtGraphs/QScatter3DSeries 6.0"]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536]
        Property {
            name: "dataProxy"
            type: "QScatterDataProxy"
            isPointer: true
            read: "dataProxy"
            write: "setDataProxy"
            notify: "dataProxyChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "selectedItem"
            type: "qsizetype"
            read: "selectedItem"
            write: "setSelectedItem"
            notify: "selectedItemChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "itemSize"
            type: "float"
            read: "itemSize"
            write: "setItemSize"
            notify: "itemSizeChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "dataArray"
            type: "QScatterDataArray"
            read: "dataArray"
            write: "setDataArray"
            notify: "dataArrayChanged"
            index: 3
            isFinal: true
        }
        Signal {
            name: "dataProxyChanged"
            Parameter { name: "proxy"; type: "QScatterDataProxy"; isPointer: true }
        }
        Signal {
            name: "selectedItemChanged"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Signal {
            name: "itemSizeChanged"
            Parameter { name: "size"; type: "float" }
        }
        Signal {
            name: "dataArrayChanged"
            Parameter { name: "array"; type: "QScatterDataArray" }
        }
    }
    Component {
        file: "qscatterdataproxy.h"
        name: "QScatterDataProxy"
        accessSemantics: "reference"
        prototype: "QAbstractDataProxy"
        exports: ["QtGraphs/ScatterDataProxy 6.0"]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536]
        Property {
            name: "itemCount"
            type: "qsizetype"
            read: "itemCount"
            notify: "itemCountChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "series"
            type: "QScatter3DSeries"
            isPointer: true
            read: "series"
            notify: "seriesChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Signal { name: "arrayReset" }
        Signal {
            name: "itemsAdded"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "itemsChanged"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "itemsRemoved"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "itemsInserted"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "itemCountChanged"
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "seriesChanged"
            Parameter { name: "series"; type: "QScatter3DSeries"; isPointer: true }
        }
    }
    Component {
        file: "qscatterseries.h"
        name: "QScatterSeries"
        accessSemantics: "reference"
        prototype: "QXYSeries"
        exports: ["QtGraphs/ScatterSeries 6.0", "QtGraphs/ScatterSeries 6.9"]
        exportMetaObjectRevisions: [1536, 1545]
    }
    Component {
        file: "qspline3dseries.h"
        name: "QSpline3DSeries"
        accessSemantics: "reference"
        prototype: "QScatter3DSeries"
        exports: ["QtGraphs/QSpline3DSeries 6.9"]
        isCreatable: false
        exportMetaObjectRevisions: [1545]
        Property {
            name: "splineVisible"
            type: "bool"
            read: "isSplineVisible"
            write: "setSplineVisible"
            notify: "splineVisibilityChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "splineTension"
            type: "double"
            read: "splineTension"
            write: "setSplineTension"
            notify: "splineTensionChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "splineKnotting"
            type: "double"
            read: "splineKnotting"
            write: "setSplineKnotting"
            notify: "splineKnottingChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "splineLooping"
            type: "bool"
            read: "isSplineLooping"
            write: "setSplineLooping"
            notify: "splineLoopingChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "splineColor"
            type: "QColor"
            read: "splineColor"
            write: "setSplineColor"
            notify: "splineColorChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "splineResolution"
            type: "int"
            read: "splineResolution"
            write: "setSplineResolution"
            notify: "splineResolutionChanged"
            index: 5
            isFinal: true
        }
        Signal {
            name: "splineVisibilityChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "splineTensionChanged"
            Parameter { name: "tension"; type: "double" }
        }
        Signal {
            name: "splineKnottingChanged"
            Parameter { name: "knotting"; type: "double" }
        }
        Signal {
            name: "splineLoopingChanged"
            Parameter { name: "looping"; type: "bool" }
        }
        Signal {
            name: "splineColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "splineResolutionChanged"
            Parameter { name: "resolution"; type: "int" }
        }
    }
    Component {
        file: "private/qsplinecontrolanimation_p.h"
        name: "QSplineControlAnimation"
        accessSemantics: "reference"
        prototype: "QXYSeriesAnimation"
        exports: ["QtGraphs/SplineControlAnimation 6.0"]
        exportMetaObjectRevisions: [1536]
        Method {
            name: "valueUpdated"
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component {
        file: "qsplineseries.h"
        name: "QSplineSeries"
        accessSemantics: "reference"
        prototype: "QXYSeries"
        exports: ["QtGraphs/SplineSeries 6.0", "QtGraphs/SplineSeries 6.9"]
        exportMetaObjectRevisions: [1536, 1545]
        Property {
            name: "width"
            type: "double"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "capStyle"
            type: "Qt::PenCapStyle"
            read: "capStyle"
            write: "setCapStyle"
            notify: "capStyleChanged"
            index: 1
            isFinal: true
        }
        Signal { name: "widthChanged" }
        Signal { name: "capStyleChanged" }
    }
    Component {
        file: "qsurface3dseries.h"
        name: "QSurface3DSeries"
        accessSemantics: "reference"
        prototype: "QAbstract3DSeries"
        exports: ["QtGraphs/QSurface3DSeries 6.0"]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "DrawFlag"
            isFlag: true
            values: [
                "DrawWireframe",
                "DrawSurface",
                "DrawSurfaceAndWireframe"
            ]
        }
        Enum {
            name: "Shading"
            isScoped: true
            values: ["Smooth", "Flat"]
        }
        Property {
            name: "dataProxy"
            type: "QSurfaceDataProxy"
            isPointer: true
            read: "dataProxy"
            write: "setDataProxy"
            notify: "dataProxyChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "selectedPoint"
            type: "QPoint"
            read: "selectedPoint"
            write: "setSelectedPoint"
            notify: "selectedPointChanged"
            index: 1
        }
        Property {
            name: "flatShadingSupported"
            type: "bool"
            read: "isFlatShadingSupported"
            notify: "flatShadingSupportedChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "drawMode"
            type: "QSurface3DSeries::DrawFlags"
            read: "drawMode"
            write: "setDrawMode"
            notify: "drawModeChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "shading"
            type: "QSurface3DSeries::Shading"
            read: "shading"
            write: "setShading"
            notify: "shadingChanged"
            index: 4
        }
        Property {
            name: "texture"
            type: "QImage"
            read: "texture"
            write: "setTexture"
            notify: "textureChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "textureFile"
            type: "QString"
            read: "textureFile"
            write: "setTextureFile"
            notify: "textureFileChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "wireframeColor"
            type: "QColor"
            read: "wireframeColor"
            write: "setWireframeColor"
            notify: "wireframeColorChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "dataArray"
            type: "QSurfaceDataArray"
            read: "dataArray"
            write: "setDataArray"
            notify: "dataArrayChanged"
            index: 8
            isFinal: true
        }
        Signal {
            name: "dataProxyChanged"
            Parameter { name: "proxy"; type: "QSurfaceDataProxy"; isPointer: true }
        }
        Signal {
            name: "selectedPointChanged"
            Parameter { name: "position"; type: "QPoint" }
        }
        Signal {
            name: "flatShadingSupportedChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "drawModeChanged"
            Parameter { name: "mode"; type: "QSurface3DSeries::DrawFlags" }
        }
        Signal {
            name: "textureChanged"
            Parameter { name: "image"; type: "QImage" }
        }
        Signal {
            name: "textureFileChanged"
            Parameter { name: "filename"; type: "QString" }
        }
        Signal {
            name: "wireframeColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "dataArrayChanged"
            Parameter { name: "array"; type: "QSurfaceDataArray" }
        }
        Signal {
            name: "shadingChanged"
            Parameter { name: "shading"; type: "Shading" }
        }
    }
    Component {
        file: "qsurfacedataproxy.h"
        name: "QSurfaceDataProxy"
        accessSemantics: "reference"
        prototype: "QAbstractDataProxy"
        exports: ["QtGraphs/SurfaceDataProxy 6.0"]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536]
        Property {
            name: "rowCount"
            type: "qsizetype"
            read: "rowCount"
            notify: "rowCountChanged"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "columnCount"
            type: "qsizetype"
            read: "columnCount"
            notify: "columnCountChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "series"
            type: "QSurface3DSeries"
            isPointer: true
            read: "series"
            notify: "seriesChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Signal { name: "arrayReset" }
        Signal {
            name: "rowsAdded"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "rowsChanged"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "rowsRemoved"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "rowsInserted"
            Parameter { name: "startIndex"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "itemChanged"
            Parameter { name: "rowIndex"; type: "qsizetype" }
            Parameter { name: "columnIndex"; type: "qsizetype" }
        }
        Signal {
            name: "rowCountChanged"
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "columnCountChanged"
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "seriesChanged"
            Parameter { name: "series"; type: "QSurface3DSeries"; isPointer: true }
        }
    }
    Component {
        file: "qvalue3daxis.h"
        name: "QValue3DAxis"
        accessSemantics: "reference"
        prototype: "QAbstract3DAxis"
        exports: ["QtGraphs/Value3DAxis 6.0", "QtGraphs/Value3DAxis 6.9"]
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536, 1545]
        Property {
            name: "segmentCount"
            type: "qsizetype"
            read: "segmentCount"
            write: "setSegmentCount"
            notify: "segmentCountChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "subSegmentCount"
            type: "qsizetype"
            read: "subSegmentCount"
            write: "setSubSegmentCount"
            notify: "subSegmentCountChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "labelFormat"
            type: "QString"
            read: "labelFormat"
            write: "setLabelFormat"
            notify: "labelFormatChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "formatter"
            type: "QValue3DAxisFormatter"
            isPointer: true
            read: "formatter"
            write: "setFormatter"
            notify: "formatterChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "reversed"
            type: "bool"
            read: "reversed"
            write: "setReversed"
            notify: "reversedChanged"
            index: 4
            isFinal: true
        }
        Signal {
            name: "segmentCountChanged"
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "subSegmentCountChanged"
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal {
            name: "labelFormatChanged"
            Parameter { name: "format"; type: "QString" }
        }
        Signal {
            name: "formatterChanged"
            Parameter { name: "formatter"; type: "QValue3DAxisFormatter"; isPointer: true }
        }
        Signal {
            name: "reversedChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal { name: "formatterDirty" }
    }
    Component {
        file: "qvalue3daxisformatter.h"
        name: "QValue3DAxisFormatter"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtGraphs/Value3DAxisFormatter 6.0"]
        exportMetaObjectRevisions: [1536]
        Method { name: "markDirtyNoLabelChange" }
    }
    Component {
        file: "qvalueaxis.h"
        name: "QValueAxis"
        accessSemantics: "reference"
        prototype: "QAbstractAxis"
        exports: ["QtGraphs/ValueAxis 6.0", "QtGraphs/ValueAxis 6.9"]
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536, 1545]
        Property {
            name: "min"
            type: "double"
            read: "min"
            write: "setMin"
            notify: "minChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "max"
            type: "double"
            read: "max"
            write: "setMax"
            notify: "maxChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "labelFormat"
            type: "QString"
            read: "labelFormat"
            write: "setLabelFormat"
            notify: "labelFormatChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "labelDecimals"
            type: "int"
            read: "labelDecimals"
            write: "setLabelDecimals"
            notify: "labelDecimalsChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "subTickCount"
            type: "qsizetype"
            read: "subTickCount"
            write: "setSubTickCount"
            notify: "subTickCountChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "tickAnchor"
            type: "double"
            read: "tickAnchor"
            write: "setTickAnchor"
            notify: "tickAnchorChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "tickInterval"
            type: "double"
            read: "tickInterval"
            write: "setTickInterval"
            notify: "tickIntervalChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "zoom"
            revision: 1545
            type: "double"
            read: "zoom"
            write: "setZoom"
            notify: "zoomChanged"
            index: 7
        }
        Property {
            name: "pan"
            revision: 1545
            type: "double"
            read: "pan"
            write: "setPan"
            notify: "panChanged"
            index: 8
        }
        Signal {
            name: "minChanged"
            Parameter { name: "min"; type: "double" }
        }
        Signal {
            name: "maxChanged"
            Parameter { name: "max"; type: "double" }
        }
        Signal {
            name: "rangeChanged"
            Parameter { name: "min"; type: "double" }
            Parameter { name: "max"; type: "double" }
        }
        Signal {
            name: "subTickCountChanged"
            Parameter { name: "subTickCount"; type: "qsizetype" }
        }
        Signal {
            name: "labelFormatChanged"
            Parameter { name: "format"; type: "QString" }
        }
        Signal {
            name: "labelDecimalsChanged"
            Parameter { name: "decimals"; type: "int" }
        }
        Signal {
            name: "tickAnchorChanged"
            Parameter { name: "tickAnchor"; type: "double" }
        }
        Signal {
            name: "tickIntervalChanged"
            Parameter { name: "tickInterval"; type: "double" }
        }
        Signal {
            name: "zoomChanged"
            revision: 1545
            Parameter { name: "zoom"; type: "double" }
        }
        Signal {
            name: "panChanged"
            revision: 1545
            Parameter { name: "pan"; type: "double" }
        }
    }
    Component {
        file: "qvariantanimation.h"
        name: "QVariantAnimation"
        accessSemantics: "reference"
        prototype: "QAbstractAnimation"
        Property {
            name: "startValue"
            type: "QVariant"
            read: "startValue"
            write: "setStartValue"
            index: 0
        }
        Property { name: "endValue"; type: "QVariant"; read: "endValue"; write: "setEndValue"; index: 1 }
        Property {
            name: "currentValue"
            type: "QVariant"
            read: "currentValue"
            notify: "valueChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "duration"
            type: "int"
            bindable: "bindableDuration"
            read: "duration"
            write: "setDuration"
            index: 3
        }
        Property {
            name: "easingCurve"
            type: "QEasingCurve"
            bindable: "bindableEasingCurve"
            read: "easingCurve"
            write: "setEasingCurve"
            index: 4
        }
        Signal {
            name: "valueChanged"
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component {
        file: "qxymodelmapper.h"
        name: "QXYModelMapper"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtGraphs/XYModelMapper 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "series"
            type: "QXYSeries"
            isPointer: true
            read: "series"
            write: "setSeries"
            notify: "seriesChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "model"
            type: "QAbstractItemModel"
            isPointer: true
            read: "model"
            write: "setModel"
            notify: "modelChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "xSection"
            type: "qsizetype"
            read: "xSection"
            write: "setXSection"
            notify: "xSectionChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "ySection"
            type: "qsizetype"
            read: "ySection"
            write: "setYSection"
            notify: "ySectionChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "first"
            type: "qsizetype"
            read: "first"
            write: "setFirst"
            notify: "firstChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "count"
            type: "qsizetype"
            read: "count"
            write: "setCount"
            notify: "countChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "orientation"
            type: "Qt::Orientation"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 6
            isFinal: true
        }
        Signal { name: "seriesChanged" }
        Signal { name: "modelChanged" }
        Signal { name: "xSectionChanged" }
        Signal { name: "ySectionChanged" }
        Signal { name: "firstChanged" }
        Signal { name: "countChanged" }
        Signal { name: "orientationChanged" }
    }
    Component {
        file: "private/qxypoint_p.h"
        name: "QXYPoint"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtGraphs/XYPoint 6.0"]
        exportMetaObjectRevisions: [1536]
        Property { name: "x"; type: "double"; read: "x"; write: "setX"; index: 0; isFinal: true }
        Property { name: "y"; type: "double"; read: "y"; write: "setY"; index: 1; isFinal: true }
    }
    Component {
        file: "qxyseries.h"
        name: "QXYSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QAbstractSeries"
        exports: ["QtGraphs/QXYSeries 6.0", "QtGraphs/QXYSeries 6.9"]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536, 1545]
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "selectedColor"
            type: "QColor"
            read: "selectedColor"
            write: "setSelectedColor"
            notify: "selectedColorChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "pointDelegate"
            type: "QQmlComponent"
            isPointer: true
            read: "pointDelegate"
            write: "setPointDelegate"
            notify: "pointDelegateChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "draggable"
            type: "bool"
            read: "isDraggable"
            write: "setDraggable"
            notify: "draggableChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "selectedPoints"
            type: "qsizetype"
            isList: true
            read: "selectedPoints"
            notify: "selectedPointsChanged"
            index: 4
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "count"
            type: "qsizetype"
            read: "count"
            notify: "countChanged"
            index: 5
            isReadonly: true
            isFinal: true
        }
        Signal {
            name: "pointReplaced"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Signal {
            name: "pointRemoved"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Signal {
            name: "pointAdded"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Signal {
            name: "pointsAdded"
            revision: 1545
            Parameter { name: "start"; type: "qsizetype" }
            Parameter { name: "end"; type: "qsizetype" }
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "selectedColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal { name: "pointsReplaced" }
        Signal {
            name: "pointsRemoved"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Signal { name: "selectedPointsChanged" }
        Signal { name: "pointDelegateChanged" }
        Signal { name: "draggableChanged" }
        Signal { name: "seriesUpdated" }
        Signal { name: "countChanged" }
        Signal {
            name: "clicked"
            revision: 1545
            Parameter { name: "point"; type: "QPoint" }
        }
        Signal {
            name: "doubleClicked"
            revision: 1545
            Parameter { name: "point"; type: "QPoint" }
        }
        Signal {
            name: "pressed"
            revision: 1545
            Parameter { name: "point"; type: "QPoint" }
        }
        Signal {
            name: "released"
            revision: 1545
            Parameter { name: "point"; type: "QPoint" }
        }
        Method {
            name: "append"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "append"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "append"
            Parameter { name: "points"; type: "QPointF"; isList: true }
        }
        Method {
            name: "replace"
            Parameter { name: "oldX"; type: "double" }
            Parameter { name: "oldY"; type: "double" }
            Parameter { name: "newX"; type: "double" }
            Parameter { name: "newY"; type: "double" }
        }
        Method {
            name: "replace"
            Parameter { name: "oldPoint"; type: "QPointF" }
            Parameter { name: "newPoint"; type: "QPointF" }
        }
        Method {
            name: "replace"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "newX"; type: "double" }
            Parameter { name: "newY"; type: "double" }
        }
        Method {
            name: "replace"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "newPoint"; type: "QPointF" }
        }
        Method {
            name: "replace"
            Parameter { name: "points"; type: "QPointF"; isList: true }
        }
        Method {
            name: "remove"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "remove"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "remove"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "insert"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "point"; type: "QPointF" }
        }
        Method { name: "clear" }
        Method {
            name: "at"
            type: "QPointF"
            isMethodConstant: true
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "find"
            type: "qsizetype"
            isMethodConstant: true
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "removeMultiple"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "count"; type: "qsizetype" }
        }
        Method {
            name: "take"
            type: "bool"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "isPointSelected"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "selectPoint"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "deselectPoint"
            Parameter { name: "index"; type: "qsizetype" }
        }
        Method {
            name: "setPointSelected"
            Parameter { name: "index"; type: "qsizetype" }
            Parameter { name: "selected"; type: "bool" }
        }
        Method { name: "selectAllPoints" }
        Method { name: "deselectAllPoints" }
        Method {
            name: "selectPoints"
            Parameter { name: "indexes"; type: "qsizetype"; isList: true }
        }
        Method {
            name: "deselectPoints"
            Parameter { name: "indexes"; type: "qsizetype"; isList: true }
        }
        Method {
            name: "toggleSelection"
            Parameter { name: "indexes"; type: "qsizetype"; isList: true }
        }
    }
    Component {
        file: "private/qxyseriesanimation_p.h"
        name: "QXYSeriesAnimation"
        accessSemantics: "reference"
        prototype: "QGraphAnimation"
        Method {
            name: "valueUpdated"
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component {
        file: "qgraphs3dnamespace.h"
        name: "QtGraphs3D"
        accessSemantics: "none"
        exports: ["QtGraphs/Graphs3D 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "SelectionFlag"
            isFlag: true
            isScoped: true
            values: [
                "None",
                "Item",
                "Row",
                "ItemAndRow",
                "Column",
                "ItemAndColumn",
                "RowAndColumn",
                "ItemRowAndColumn",
                "Slice",
                "MultiSeries"
            ]
        }
        Enum {
            name: "ShadowQuality"
            isScoped: true
            values: [
                "None",
                "Low",
                "Medium",
                "High",
                "SoftLow",
                "SoftMedium",
                "SoftHigh"
            ]
        }
        Enum {
            name: "ElementType"
            isScoped: true
            values: [
                "None",
                "Series",
                "AxisXLabel",
                "AxisYLabel",
                "AxisZLabel",
                "CustomItem"
            ]
        }
        Enum {
            name: "OptimizationHint"
            isScoped: true
            values: ["Default", "Legacy"]
        }
        Enum {
            name: "RenderingMode"
            isScoped: true
            values: ["DirectToBackground", "Indirect"]
        }
        Enum {
            name: "CameraPreset"
            isScoped: true
            values: [
                "NoPreset",
                "FrontLow",
                "Front",
                "FrontHigh",
                "LeftLow",
                "Left",
                "LeftHigh",
                "RightLow",
                "Right",
                "RightHigh",
                "BehindLow",
                "Behind",
                "BehindHigh",
                "IsometricLeft",
                "IsometricLeftHigh",
                "IsometricRight",
                "IsometricRightHigh",
                "DirectlyAbove",
                "DirectlyAboveCW45",
                "DirectlyAboveCCW45",
                "FrontBelow",
                "LeftBelow",
                "RightBelow",
                "BehindBelow",
                "DirectlyBelow"
            ]
        }
        Enum {
            name: "GridLineType"
            isScoped: true
            values: ["Shader", "Geometry"]
        }
        Enum {
            name: "TransparencyTechnique"
            isScoped: true
            values: ["Default", "Approximate", "Accurate"]
        }
    }
}
