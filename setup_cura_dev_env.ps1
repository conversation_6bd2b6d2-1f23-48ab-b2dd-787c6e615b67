# Cura and Uranium Parallel Development Environment Setup Script
# This script sets up a cross-platform development environment for Cura and Uranium
# with system suffixes to avoid conflicts between macOS and Windows

param(
    [switch]$Force,
    [string]$PythonVersion = "3.12"
)

Write-Host "=== Cura & Uranium Parallel Development Environment Setup ===" -ForegroundColor Green
Write-Host "Setting up cross-platform development environment with system suffixes..." -ForegroundColor Yellow

# Check if we're on Windows
if ($IsWindows -or $env:OS -eq "Windows_NT") {
    $SystemSuffix = "windows"
    $VenvActivateScript = "Scripts\activate.ps1"
} else {
    Write-Error "This PowerShell script is designed for Windows. Please use the bash version for macOS/Linux."
    exit 1
}

# Function to check if command exists
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Check Python version
Write-Host "Checking Python version..." -ForegroundColor Cyan
if (Test-Command python) {
    $pythonVersionOutput = python --version 2>&1
    Write-Host "Found: $pythonVersionOutput" -ForegroundColor Green
    
    # Extract version number
    if ($pythonVersionOutput -match "Python (\d+\.\d+)") {
        $currentVersion = [version]$matches[1]
        $requiredVersion = [version]"3.11"

        if ($currentVersion -lt $requiredVersion) {
            Write-Warning "Python $requiredVersion or higher is required. Current version: $currentVersion"
            Write-Host "Please install Python 3.11 or higher from https://python.org" -ForegroundColor Red
            Write-Host "After installation, restart this script." -ForegroundColor Yellow
            exit 1
        } elseif ($currentVersion -lt [version]"3.12") {
            Write-Warning "Python 3.12+ is recommended by Ultimaker, but 3.11+ should work. Current version: $currentVersion"
        }
    }
} else {
    Write-Error "Python not found. Please install Python $PythonVersion or higher."
    exit 1
}

# Create main virtual environment for Conan
$ConanVenvPath = "cura_conan_venv_$SystemSuffix"
Write-Host "Creating Conan virtual environment: $ConanVenvPath" -ForegroundColor Cyan

if (Test-Path $ConanVenvPath) {
    if ($Force) {
        Write-Host "Removing existing virtual environment..." -ForegroundColor Yellow
        Remove-Item -Recurse -Force $ConanVenvPath
    } else {
        Write-Host "Virtual environment already exists. Use -Force to recreate." -ForegroundColor Yellow
    }
}

if (-not (Test-Path $ConanVenvPath)) {
    python -m venv $ConanVenvPath
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to create virtual environment"
        exit 1
    }
}

# Activate virtual environment
Write-Host "Activating virtual environment..." -ForegroundColor Cyan
$activateScript = Join-Path $ConanVenvPath $VenvActivateScript
if (Test-Path $activateScript) {
    & $activateScript
} else {
    Write-Error "Failed to find activation script: $activateScript"
    exit 1
}

# Install Conan 2.7.0
Write-Host "Installing Conan 2.7.0..." -ForegroundColor Cyan
python -m pip install --upgrade pip
python -m pip install "conan==2.7.0"

if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to install Conan"
    exit 1
}

# Verify Conan installation
$conanVersion = conan --version
Write-Host "Installed: $conanVersion" -ForegroundColor Green

# Configure Conan
Write-Host "Configuring Conan..." -ForegroundColor Cyan
conan config install https://github.com/ultimaker/conan-config.git
conan profile detect --force

# Set up Uranium as editable
Write-Host "Setting up Uranium in editable mode..." -ForegroundColor Cyan
Set-Location Uranium
conan editable add . "uranium/5.11.0-alpha.0@ultimaker/testing"
Set-Location ..

# Install Cura dependencies with system suffix
Write-Host "Installing Cura dependencies with system suffix..." -ForegroundColor Cyan
Set-Location Cura

# Run conan install with PyCharm generator and system suffix
$conanInstallCmd = @(
    "conan", "install", ".",
    "--build=missing",
    "--update",
    "-g", "VirtualPythonEnv",
    "-g", "PyCharmRunEnv",
    "-c", "user.generator.virtual_python_env:dev_tools=True",
    "--require-override=uranium/5.11.0-alpha.0@ultimaker/testing"
)

Write-Host "Running: $($conanInstallCmd -join ' ')" -ForegroundColor Yellow
& $conanInstallCmd[0] $conanInstallCmd[1..($conanInstallCmd.Length-1)]

if ($LASTEXITCODE -ne 0) {
    Write-Error "Conan install failed"
    exit 1
}

Set-Location ..

Write-Host "=== Setup Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Open PyCharm and open the Cura project folder" -ForegroundColor White
Write-Host "2. In PyCharm, go to File > Open and select the Uranium folder to attach it" -ForegroundColor White
Write-Host "3. Configure the Python interpreter to use: Cura\build_$SystemSuffix\generators\cura_venv_$SystemSuffix" -ForegroundColor White
Write-Host "4. Use the generated run configurations in PyCharm" -ForegroundColor White
Write-Host ""
Write-Host "To activate the Cura development environment manually:" -ForegroundColor Yellow
Write-Host "cd Cura" -ForegroundColor White
Write-Host ".\build_$SystemSuffix\generators\virtual_python_env.ps1" -ForegroundColor White
Write-Host "python cura_app.py" -ForegroundColor White
Write-Host ""
Write-Host "Virtual environments created with system suffix '$SystemSuffix' for cross-platform compatibility." -ForegroundColor Green
