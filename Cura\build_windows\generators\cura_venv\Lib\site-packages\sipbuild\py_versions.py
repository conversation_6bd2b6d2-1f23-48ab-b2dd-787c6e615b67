# SPDX-License-Identifier: BSD-2-Clause

# Copyright (c) 2025 <PERSON> <<EMAIL>>


# The minimum required version of setuptools.  This is the earliest version
# that generates correct wheel names for PyPI.  Remember to update
# pyproject.toml in the root directory.
MINIMUM_SETUPTOOLS = '75.8.1'

# The oldest supported minor version of Python v3.  Remember to update
# pyproject.toml in the root directory.
OLDEST_SUPPORTED_MINOR = 9
