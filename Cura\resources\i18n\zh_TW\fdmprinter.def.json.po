# Cura JSON setting files
# Copyright (C) 2022 Ultimaker B.V.
# This file is distributed under the same license as the Cura package.
# <AUTHOR> <EMAIL>, 2022.
#
msgid ""
msgstr ""
"Project-Id-Version: Cura 5.1\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2023-11-24 12:51+0000\n"
"PO-Revision-Date: 2022-01-02 20:24+0800\n"
"Last-Translator: <PERSON><PERSON>  <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON> <<EMAIL>>\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 3.0\n"

msgctxt "ironing_inset description"
msgid "A distance to keep from the edges of the model. Ironing all the way to the edge of the mesh may result in a jagged edge on your print."
msgstr "與模型邊緣保持的距離。一直燙平至網格的邊緣可能導致列印品出現鋸齒狀邊緣。"

msgctxt "material_no_load_move_factor description"
msgid "A factor indicating how much the filament gets compressed between the feeder and the nozzle chamber, used to determine how far to move the material for a filament switch."
msgstr "一個用來表示線材在進料器和噴頭腔室之間能被壓縮多少的係數，用來決定線材切換時需要移動多長。"

msgctxt "roofing_angles description"
msgid "A list of integer line directions to use when the top surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "當頂部表層採用線條或鋸齒狀的列印樣式時使用的整數線條方向的清單。清單中的元素隨層的進度依次使用，當達到清單末尾時，它將從頭開始。清單項以逗號分隔，整個清單包含在方括號中。預設使用傳統的預設角度（45 和 135 度）。"

msgctxt "skin_angles description"
msgid "A list of integer line directions to use when the top/bottom layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "當頂部/底部採用線條或鋸齒狀的列印樣式時使用的整數線條方向的清單。清單中的元素隨層的進度依次使用，當達到清單末尾時，它將從頭開始。清單元素以逗號分隔，整個清單包含在方括號中。空的清單代表使用傳統的預設角度（45 和 135 度）。"

msgctxt "support_infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angle 0 degrees."
msgstr "要使用的線條方向清單。清單中的項目隨著列印層增加順序使用，當到達清單的末端時，會再從頭開始。清單項目以逗號分隔，整個清單以中括號括住。預設值為空的清單，表示使用預設角度 0 度。"

msgctxt "support_bottom_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "要使用的線條方向清單。清單中的項目隨著列印層增加順序使用，當到達清單的末端時，會再從頭開始。清單項目以逗號分隔，整個清單以中括號括住。預設值為空的清單，表示使用預設角度（預設 90 度，若介面較厚則以 45 度和 135 度交替）。"

msgctxt "support_interface_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "要使用的線條方向清單。清單中的項目隨著列印層增加順序使用，當到達清單的末端時，會再從頭開始。清單項目以逗號分隔，整個清單以中括號括住。預設值為空的清單，表示使用預設角度（預設 90 度，若介面較厚則以 45 度和 135 度交替）。"

msgctxt "support_roof_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "要使用的線條方向清單。清單中的項目隨著列印層增加順序使用，當到達清單的末端時，會再從頭開始。清單項目以逗號分隔，整個清單以中括號括住。預設值為空的清單，表示使用預設角度（預設 90 度，若介面較厚則以 45 度和 135 度交替）。"

msgctxt "infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees for the lines and zig zag patterns and 45 degrees for all other patterns)."
msgstr "要使用的整數線條方向清單。清單中的元素隨層的進度依次使用，當達到清單末尾時，它將從頭開始。清單元素以逗號分隔，整個清單包含在方括號中。空的清單代表使用傳統的預設角度（線條和鋸齒狀的列印樣式為 45 和 135 度，其他所有的列印樣式為 45 度）。"

msgctxt "nozzle_disallowed_areas description"
msgid "A list of polygons with areas the nozzle is not allowed to enter."
msgstr "禁止噴頭進入區域的多邊形清單。"

msgctxt "machine_disallowed_areas description"
msgid "A list of polygons with areas the print head is not allowed to enter."
msgstr "禁止列印頭進入區域的多邊形清單。"

msgctxt "brim_inside_margin description"
msgid "A part fully enclosed inside another part can generate an outer brim that touches the inside of the other part. This removes all brim within this distance from internal holes."
msgstr ""

msgctxt "support_tree_branch_reach_limit description"
msgid "A recomendation to how far branches can move from the points they support. Branches can violate this value to reach their destination (buildplate or a flat part of the model). Lowering this value will make the support more sturdy, but increase the amount of branches (and because of that material usage/print time) "
msgstr ""

msgctxt "extruder_prime_pos_abs label"
msgid "Absolute Extruder Prime Position"
msgstr "擠出機使用絕對位置"

msgctxt "adaptive_layer_height_variation label"
msgid "Adaptive Layers Maximum Variation"
msgstr "適應性層高最大變化量"

msgctxt "adaptive_layer_height_threshold label"
msgid "Adaptive Layers Topography Size"
msgstr "適應性層高地形尺寸"

msgctxt "adaptive_layer_height_variation_step label"
msgid "Adaptive Layers Variation Step Size"
msgstr "適應性層高變化幅度"

msgctxt "adaptive_layer_height_enabled description"
msgid "Adaptive layers computes the layer heights depending on the shape of the model."
msgstr "適應性層高會依據模型的形狀計算列印的層高。"

msgctxt "infill_wall_line_count description"
msgid ""
"Add extra walls around the infill area. Such walls can make top/bottom skin lines sag down less which means you need less top/bottom skin layers for the same quality at the cost of some extra material.\n"
"This feature can combine with the Connect Infill Polygons to connect all the infill into a single extrusion path without the need for travels or retractions if configured right."
msgstr ""
"在填充區域周圍添加額外的牆壁。這樣的牆壁可以使頂部/底部表層線條較不易下垂，這表示您只要花費一些額外的材料，就可用更少層的頂部/底部表層得到相同的品質。\n"
"此功能可與「連接填充多邊形」結合使用。如果設定正確，可將所有填充連接為單一擠出路徑，不需空跑或回抽。"

msgctxt "platform_adhesion description"
msgid "Adhesion"
msgstr "附著"

msgctxt "material_adhesion_tendency label"
msgid "Adhesion Tendency"
msgstr "附著趨勢"

msgctxt "skin_overlap description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines, as a percentage of the line widths of the skin lines and the innermost wall. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any percentage over 50% may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "以表層線寬和最內壁線寬的百分比，調整內壁和表層中心線（的端點）之間的重疊量。輕微的重疊可以讓牆壁牢固地連接到表層。但要注意在表層和內壁線寬度相等的情形下, 超過 50% 的百分比可能導致表層越過內壁, 因為此時擠出機噴嘴的位置可能已經超過了內壁線條的中間。"

msgctxt "skin_overlap_mm description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any value over half the width of the wall may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "調整內壁和表層中心線（的端點）之間的重疊量。輕微的重疊可以讓牆壁牢固地連接到表層。但要注意在表層和內壁線寬度相等的情形下, 超過線寬一半的值可能導致表層越過內壁, 因為此時擠出機噴嘴的位置可能已經超過了內壁線條的中間。"

msgctxt "infill_sparse_density description"
msgid "Adjusts the density of infill of the print."
msgstr "調整列印填充的密度。"

msgctxt "support_interface_density description"
msgid "Adjusts the density of the roofs and floors of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "調整支撐結構頂板和底板的密度。較高的值會實現更好的突出部分，但支撐將更加難以移除。"

msgctxt "support_tree_top_rate description"
msgid "Adjusts the density of the support structure used to generate the tips of the branches. A higher value results in better overhangs, but the supports are harder to remove. Use Support Roof for very high values or ensure support density is similarly high at the top."
msgstr ""

msgctxt "support_infill_rate description"
msgid "Adjusts the density of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "調整支撐結構的密度。較高的值會實現更好的突出部分，但支撐將更加難以移除。"

msgctxt "material_diameter description"
msgid "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament."
msgstr "調整所使用線材的直徑。這個數值要等同於所使用線材的直徑。"

msgctxt "support_type description"
msgid "Adjusts the placement of the support structures. The placement can be set to touching build plate or everywhere. When set to everywhere the support structures will also be printed on the model."
msgstr "調整支撐結構的位置。位置可以設定為“接觸列印平台”或“每個地方”。當設定為“每個地方”時，在模型上也會列印支撐結構。"

msgctxt "prime_tower_wipe_enabled description"
msgid "After printing the prime tower with one nozzle, wipe the oozed material from the other nozzle off on the prime tower."
msgstr "在一個噴頭列印換料塔後，在換料塔上擦拭另一個噴頭滲出的線材。"

msgctxt "retraction_hop_after_extruder_switch description"
msgid "After the machine switched from one extruder to the other, the build plate is lowered to create clearance between the nozzle and the print. This prevents the nozzle from leaving oozed material on the outside of a print."
msgstr "當機器從一個擠出機切換到另一個時，列印平台會降低以便在噴頭和列印品之間形成空隙。這將防止噴頭在列印品外部留下滲出物。"

msgctxt "retraction_combing option all"
msgid "All"
msgstr "所有"

msgctxt "print_sequence option all_at_once"
msgid "All at Once"
msgstr "同時列印"

msgctxt "resolution description"
msgid "All settings that influence the resolution of the print. These settings have a large impact on the quality (and print time)"
msgstr "影響列印解析度的所有設定。這些設定會對品質（和列印時間）產生顯著影響"

msgctxt "alternate_extra_perimeter label"
msgid "Alternate Extra Wall"
msgstr "交錯額外牆壁"

msgctxt "alternate_carve_order label"
msgid "Alternate Mesh Removal"
msgstr "交互移除網格重疊部分"

msgctxt "material_alternate_walls label"
msgid "Alternate Wall Directions"
msgstr ""

msgctxt "material_alternate_walls description"
msgid "Alternate wall directions every other layer and inset. Useful for materials that can build up stress, like for metal printing."
msgstr ""

msgctxt "machine_buildplate_type option aluminum"
msgid "Aluminum"
msgstr "鋁"

msgctxt "machine_always_write_active_tool label"
msgid "Always Write Active Tool"
msgstr "總是寫入啟用工具"

msgctxt "travel_retract_before_outer_wall description"
msgid "Always retract when moving to start an outer wall."
msgstr "當移動到外牆起始點時總是進行回抽。"

msgctxt "xy_offset description"
msgid "Amount of offset applied to all polygons in each layer. Positive values can compensate for too big holes; negative values can compensate for too small holes."
msgstr "如果模型有挖孔，以便用來組合、鑲嵌時，這個偏移量可以用來微調孔的大小，當設為正值時，模型外擴，孔會變小；若設為負值，模型內縮，孔會變大。"

msgctxt "xy_offset_layer_0 description"
msgid "Amount of offset applied to all polygons in the first layer. A negative value can compensate for squishing of the first layer known as \"elephant's foot\"."
msgstr "套用到第一層所有多邊形的偏移量。負數值可以補償第一層的壓扁量（被稱為“象脚”）。"

msgctxt "support_offset description"
msgid "Amount of offset applied to all support polygons in each layer. Positive values can smooth out the support areas and result in more sturdy support."
msgstr "套用到每一層所有支撐多邊形的偏移量。正值可以讓支撐區域更平滑，並產生更為牢固的支撐。"

msgctxt "support_bottom_offset description"
msgid "Amount of offset applied to the floors of the support."
msgstr "套用到支撐底板多邊形的偏移量。"

msgctxt "support_roof_offset description"
msgid "Amount of offset applied to the roofs of the support."
msgstr "套用到支撐頂板多邊形的偏移量。"

msgctxt "support_interface_offset description"
msgid "Amount of offset applied to the support interface polygons."
msgstr "套用到支撐介面多邊形的偏移量。"

msgctxt "wipe_retraction_amount description"
msgid "Amount to retract the filament so it does not ooze during the wipe sequence."
msgstr "回抽線材的量，使其在擦拭過程中不會滲出。"

msgctxt "sub_div_rad_add description"
msgid "An addition to the radius from the center of each cube to check for the boundary of the model, as to decide whether this cube should be subdivided. Larger values lead to a thicker shell of small cubes near the boundary of the model."
msgstr "每個立方體半徑的增加量，用來檢查模型的邊界，決定是否應該細分該立方體。值越大，靠近模型邊界附近的小立方體的殼越厚。"

msgctxt "anti_overhang_mesh label"
msgid "Anti Overhang Mesh"
msgstr "防突出網格"

msgctxt "material_anti_ooze_retracted_position label"
msgid "Anti-ooze Retracted Position"
msgstr "防滲漏回抽位置"

msgctxt "material_anti_ooze_retraction_speed label"
msgid "Anti-ooze Retraction Speed"
msgstr "防滲漏回抽速度"

msgctxt "machine_use_extruder_offset_to_offset_coords description"
msgid "Apply the extruder offset to the coordinate system. Affects all extruders."
msgstr "開啟擠出機補償並應用於所有擠出機."

msgctxt "interlocking_enable description"
msgid "At the locations where models touch, generate an interlocking beam structure. This improves the adhesion between models, especially models printed in different materials."
msgstr ""

msgctxt "travel_avoid_other_parts label"
msgid "Avoid Printed Parts When Traveling"
msgstr "空跑時避開已列印部分"

msgctxt "travel_avoid_supports label"
msgid "Avoid Supports When Traveling"
msgstr "空跑避開支撐"

msgctxt "z_seam_position option back"
msgid "Back"
msgstr "後方"

msgctxt "z_seam_position option backleft"
msgid "Back Left"
msgstr "左後方"

msgctxt "z_seam_position option backright"
msgid "Back Right"
msgstr "右後方"

msgctxt "machine_gcode_flavor option BFB"
msgid "Bits from Bytes"
msgstr "Bits from Bytes"

msgctxt "magic_mesh_surface_mode option both"
msgid "Both"
msgstr "兩者"

msgctxt "support_interface_priority option nothing"
msgid "Both overlap"
msgstr ""

msgctxt "bottom_layers label"
msgid "Bottom Layers"
msgstr "底部層數"

msgctxt "top_bottom_pattern_0 label"
msgid "Bottom Pattern Initial Layer"
msgstr "起始層列印樣式"

msgctxt "bottom_skin_expand_distance label"
msgid "Bottom Skin Expand Distance"
msgstr "底部表層延伸距離"

msgctxt "bottom_skin_preshrink label"
msgid "Bottom Skin Removal Width"
msgstr "底部表層移除寬度"

msgctxt "bottom_thickness label"
msgid "Bottom Thickness"
msgstr "底部厚度"

msgctxt "support_tree_top_rate label"
msgid "Branch Density"
msgstr ""

msgctxt "support_tree_branch_diameter label"
msgid "Branch Diameter"
msgstr ""

msgctxt "support_tree_branch_diameter_angle label"
msgid "Branch Diameter Angle"
msgstr ""

msgctxt "material_break_preparation_retracted_position label"
msgid "Break Preparation Retracted Position"
msgstr "回抽切斷前位置"

msgctxt "material_break_preparation_speed label"
msgid "Break Preparation Retraction Speed"
msgstr "回抽切斷前速度"

msgctxt "material_break_preparation_temperature label"
msgid "Break Preparation Temperature"
msgstr "回抽切斷溫度"

msgctxt "material_break_retracted_position label"
msgid "Break Retracted Position"
msgstr "回抽切斷位置"

msgctxt "material_break_speed label"
msgid "Break Retraction Speed"
msgstr "回抽切斷速度"

msgctxt "material_break_temperature label"
msgid "Break Temperature"
msgstr "切斷溫度"

msgctxt "support_skip_some_zags label"
msgid "Break Up Support In Chunks"
msgstr "將支撐拆成塊狀"

msgctxt "bridge_fan_speed label"
msgid "Bridge Fan Speed"
msgstr "橋樑風扇轉速"

msgctxt "bridge_enable_more_layers label"
msgid "Bridge Has Multiple Layers"
msgstr "多層橋樑"

msgctxt "bridge_skin_density_2 label"
msgid "Bridge Second Skin Density"
msgstr "橋樑第二表層密度"

msgctxt "bridge_fan_speed_2 label"
msgid "Bridge Second Skin Fan Speed"
msgstr "橋樑第二表層風扇轉速"

msgctxt "bridge_skin_material_flow_2 label"
msgid "Bridge Second Skin Flow"
msgstr "橋樑第二表層流量"

msgctxt "bridge_skin_speed_2 label"
msgid "Bridge Second Skin Speed"
msgstr "橋樑第二表層速度"

msgctxt "bridge_skin_density label"
msgid "Bridge Skin Density"
msgstr "橋樑表層密度"

msgctxt "bridge_skin_material_flow label"
msgid "Bridge Skin Flow"
msgstr "橋樑表層流量"

msgctxt "bridge_skin_speed label"
msgid "Bridge Skin Speed"
msgstr "橋樑表層速度"

msgctxt "bridge_skin_support_threshold label"
msgid "Bridge Skin Support Threshold"
msgstr "橋樑表層支撐門檻值"

msgctxt "bridge_sparse_infill_max_density label"
msgid "Bridge Sparse Infill Max Density"
msgstr "橋樑稀疏填充最大密度"

msgctxt "bridge_skin_density_3 label"
msgid "Bridge Third Skin Density"
msgstr "橋樑第三表層密度"

msgctxt "bridge_fan_speed_3 label"
msgid "Bridge Third Skin Fan Speed"
msgstr "橋樑第三表層風扇轉速"

msgctxt "bridge_skin_material_flow_3 label"
msgid "Bridge Third Skin Flow"
msgstr "橋樑第三表層流量"

msgctxt "bridge_skin_speed_3 label"
msgid "Bridge Third Skin Speed"
msgstr "橋樑第三表層速度"

msgctxt "bridge_wall_coast label"
msgid "Bridge Wall Coasting"
msgstr "橋樑牆壁滑行"

msgctxt "bridge_wall_material_flow label"
msgid "Bridge Wall Flow"
msgstr "橋樑牆壁流量"

msgctxt "bridge_wall_speed label"
msgid "Bridge Wall Speed"
msgstr "橋樑牆壁速度"

msgctxt "adhesion_type option brim"
msgid "Brim"
msgstr "邊緣"

msgctxt "brim_gap label"
msgid "Brim Distance"
msgstr "邊緣間距"

msgctxt "brim_inside_margin label"
msgid "Brim Inside Avoid Margin"
msgstr ""

msgctxt "brim_line_count label"
msgid "Brim Line Count"
msgstr "邊緣線條數量"

msgctxt "brim_outside_only label"
msgid "Brim Only on Outside"
msgstr "僅在外部列印邊緣"

msgctxt "brim_replaces_support label"
msgid "Brim Replaces Support"
msgstr "邊綠取代支撐"

msgctxt "brim_width label"
msgid "Brim Width"
msgstr "邊緣寬度"

msgctxt "platform_adhesion label"
msgid "Build Plate Adhesion"
msgstr "列印平台附著"

msgctxt "adhesion_extruder_nr label"
msgid "Build Plate Adhesion Extruder"
msgstr "列印平台附著擠出機"

msgctxt "adhesion_type label"
msgid "Build Plate Adhesion Type"
msgstr "列印平台附著類型"

msgctxt "machine_buildplate_type label"
msgid "Build Plate Material"
msgstr "列印平台材質"

msgctxt "machine_shape label"
msgid "Build Plate Shape"
msgstr "列印平台形狀"

msgctxt "material_bed_temperature label"
msgid "Build Plate Temperature"
msgstr "列印平台溫度"

msgctxt "material_bed_temperature_layer_0 label"
msgid "Build Plate Temperature Initial Layer"
msgstr "列印平台溫度起始層"

msgctxt "build_volume_temperature label"
msgid "Build Volume Temperature"
msgstr "列印空間溫度"

msgctxt "prime_tower_brim_enable description"
msgid "By enabling this setting, your prime-tower will get a brim, even if the model doesn't. If you want a sturdier base for a high tower, you can increase the base height."
msgstr ""

msgctxt "center_object label"
msgid "Center Object"
msgstr "物件置中"

msgctxt "conical_overhang_enabled description"
msgid "Change the geometry of the printed model such that minimal support is required. Steep overhangs will become shallow overhangs. Overhanging areas will drop down to become more vertical."
msgstr "更改列印模型的幾何形狀，以最大程度減少需要的支撐。陡峭的突出部分將變淺。突出區域將下降變得更垂直。"

msgctxt "support_structure description"
msgid "Chooses between the techniques available to generate support. \"Normal\" support creates a support structure directly below the overhanging parts and drops those areas straight down. \"Tree\" support creates branches towards the overhanging areas that support the model on the tips of those branches, and allows the branches to crawl around the model to support it from the build plate as much as possible."
msgstr "選擇產生支撐的技術。「正常」支撐會在突出部份的下方產生支撐結構，並重直向下延伸。「樹狀」支撐會朝突出部份長出樹枝用樹枝末端支撐模型，並讓分枝在模型周圍爬行盡可能地支撐模型。"

msgctxt "coasting_speed label"
msgid "Coasting Speed"
msgstr "滑行速度"

msgctxt "coasting_volume label"
msgid "Coasting Volume"
msgstr "滑行體積"

msgctxt "coasting_enable description"
msgid "Coasting replaces the last part of an extrusion path with a travel path. The oozed material is used to print the last piece of the extrusion path in order to reduce stringing."
msgstr "滑行會用一個空跑路徑替代擠出路徑的最後部分。滲出線材用於列印擠出路徑的最後部分，以便減少牽絲。"

msgctxt "retraction_combing label"
msgid "Combing Mode"
msgstr "梳理模式"

msgctxt "retraction_combing description"
msgid "Combing keeps the nozzle within already printed areas when traveling. This results in slightly longer travel moves but reduces the need for retractions. If combing is off, the material will retract and the nozzle moves in a straight line to the next point. It is also possible to avoid combing over top/bottom skin areas or to only comb within the infill."
msgstr "梳理模式讓噴頭空跑時保持在已列印的區域內。這將導致稍長的空跑移動但減少了回抽的需求。如果關閉梳理模式，噴頭將會回抽線材，直線移動到下一點。可以設定在頂部/底部表層不使用梳理模式，或只使用在內部填充。"

msgctxt "command_line_settings label"
msgid "Command Line Settings"
msgstr "命令行設定"

msgctxt "infill_pattern option concentric"
msgid "Concentric"
msgstr "同心"

msgctxt "ironing_pattern option concentric"
msgid "Concentric"
msgstr "同心"

msgctxt "roofing_pattern option concentric"
msgid "Concentric"
msgstr "同心"

msgctxt "support_bottom_pattern option concentric"
msgid "Concentric"
msgstr "同心"

msgctxt "support_interface_pattern option concentric"
msgid "Concentric"
msgstr "同心"

msgctxt "support_pattern option concentric"
msgid "Concentric"
msgstr "同心"

msgctxt "support_roof_pattern option concentric"
msgid "Concentric"
msgstr "同心"

msgctxt "top_bottom_pattern option concentric"
msgid "Concentric"
msgstr "同心"

msgctxt "top_bottom_pattern_0 option concentric"
msgid "Concentric"
msgstr "同心"

msgctxt "support_conical_angle label"
msgid "Conical Support Angle"
msgstr "錐形支撐角度"

msgctxt "support_conical_min_width label"
msgid "Conical Support Minimum Width"
msgstr "錐形支撐最小寬度"

msgctxt "zig_zaggify_infill label"
msgid "Connect Infill Lines"
msgstr "連接填充線條"

msgctxt "connect_infill_polygons label"
msgid "Connect Infill Polygons"
msgstr "連接填充多邊形"

msgctxt "zig_zaggify_support label"
msgid "Connect Support Lines"
msgstr "連接支撐線條"

msgctxt "support_connect_zigzags label"
msgid "Connect Support ZigZags"
msgstr "連接支撐鋸齒狀"

msgctxt "connect_skin_polygons label"
msgid "Connect Top/Bottom Polygons"
msgstr "連接頂部/底部多邊形"

msgctxt "connect_infill_polygons description"
msgid "Connect infill paths where they run next to each other. For infill patterns which consist of several closed polygons, enabling this setting greatly reduces the travel time."
msgstr "連接彼此相鄰的填充路徑。 對於由多個閉合多邊形組成的填充圖案，啟用此設定可大大縮短空跑時間。"

msgctxt "support_connect_zigzags description"
msgid "Connect the ZigZags. This will increase the strength of the zig zag support structure."
msgstr "連接鋸齒狀。這將增加鋸齒狀支撐結構的强度。"

msgctxt "zig_zaggify_support description"
msgid "Connect the ends of the support lines together. Enabling this setting can make your support more sturdy and reduce underextrusion, but it will cost more material."
msgstr "將支撐線條的末端連接在一起。啟用此設定能讓支撐更堅固並減少擠出不足的問題，但會花費更多的線材。"

msgctxt "zig_zaggify_infill description"
msgid "Connect the ends where the infill pattern meets the inner wall using a line which follows the shape of the inner wall. Enabling this setting can make the infill adhere to the walls better and reduce the effects of infill on the quality of vertical surfaces. Disabling this setting reduces the amount of material used."
msgstr "使用一條線沿著內牆的形狀，連接填充線條與內牆交會的末端。啟用此設定可以使填充更好地附著在內牆上，並減少對垂直表面品質的影響。關閉此設定可降低材料的使用量。"

msgctxt "connect_skin_polygons description"
msgid "Connect top/bottom skin paths where they run next to each other. For the concentric pattern enabling this setting greatly reduces the travel time, but because the connections can happen midway over infill this feature can reduce the top surface quality."
msgstr "將頂部/底部表層路徑相鄰的位置連接。同心模式時啟用此設定，可以大大地減少移動時間。但因連接可能碰巧在途中跨越填充，所以此功能可能會降低頂部表層的品質。"

msgctxt "z_seam_corner description"
msgid "Control whether corners on the model outline influence the position of the seam. None means that corners have no influence on the seam position. Hide Seam makes the seam more likely to occur on an inside corner. Expose Seam makes the seam more likely to occur on an outside corner. Hide or Expose Seam makes the seam more likely to occur at an inside or outside corner. Smart Hiding allows both inside and outside corners, but chooses inside corners more frequently, if appropriate."
msgstr "控制模型輪廓上的轉角是否影響接縫的位置。「無」表示轉角不影響接縫位置。「隱藏接縫」讓接縫盡量出現在凹角。「暴露接縫」讓接縫盡量出現在凸角。「隱藏或暴露接縫」讓接縫盡量出現在凹角或凸角。「智慧隱藏」允許使用凹角或凸角，但如果狀況合適，會盡可能地選擇凹角。"

msgctxt "infill_multiplier description"
msgid "Convert each infill line to this many lines. The extra lines do not cross over each other, but avoid each other. This makes the infill stiffer, but increases print time and material usage."
msgstr "將每條填充線轉換為此數量。 額外的線條不會相互交叉，而是相互避開。 這會使填充更硬，但增加了列印時間和線材使用。"

msgctxt "machine_nozzle_cool_down_speed label"
msgid "Cool Down Speed"
msgstr "冷卻速度"

msgctxt "cooling description"
msgid "Cooling"
msgstr "冷卻"

msgctxt "cooling label"
msgid "Cooling"
msgstr "冷卻"

msgctxt "infill_pattern option cross"
msgid "Cross"
msgstr "十字形"

msgctxt "support_pattern option cross"
msgid "Cross"
msgstr "十字形"

msgctxt "infill_pattern option cross_3d"
msgid "Cross 3D"
msgstr "立體十字形"

msgctxt "cross_infill_pocket_size label"
msgid "Cross 3D Pocket Size"
msgstr "立體十字形氣囊大小"

msgctxt "cross_support_density_image label"
msgid "Cross Fill Density Image for Support"
msgstr "支撐十字形填充密度圖片"

msgctxt "cross_infill_density_image label"
msgid "Cross Infill Density Image"
msgstr "十字形填充密度圖片"

msgctxt "material_crystallinity label"
msgid "Crystalline Material"
msgstr "晶狀線材"

msgctxt "infill_pattern option cubic"
msgid "Cubic"
msgstr "立方體"

msgctxt "infill_pattern option cubicsubdiv"
msgid "Cubic Subdivision"
msgstr "立方體細分"

msgctxt "sub_div_rad_add label"
msgid "Cubic Subdivision Shell"
msgstr "立方體細分外殼"

msgctxt "cutting_mesh label"
msgid "Cutting Mesh"
msgstr "切割網格"

msgctxt "material_flow_temp_graph description"
msgid "Data linking material flow (in mm3 per second) to temperature (degrees Celsius)."
msgstr "數據連接線材流量（mm3/s）到溫度（攝氏）。"

msgctxt "machine_acceleration label"
msgid "Default Acceleration"
msgstr "預設加速度"

msgctxt "default_material_bed_temperature label"
msgid "Default Build Plate Temperature"
msgstr "列印平台預設溫度"

msgctxt "machine_max_jerk_e label"
msgid "Default Filament Jerk"
msgstr "預設擠出馬達加加速度"

msgctxt "default_material_print_temperature label"
msgid "Default Printing Temperature"
msgstr "預設列印溫度"

msgctxt "machine_max_jerk_xy label"
msgid "Default X-Y Jerk"
msgstr "預設 X-Y 平面加加速度"

msgctxt "machine_max_jerk_z label"
msgid "Default Z Jerk"
msgstr "預設 Z 軸加加速度"

msgctxt "machine_max_jerk_xy description"
msgid "Default jerk for movement in the horizontal plane."
msgstr "水平面移動的預設加加速度。"

msgctxt "machine_max_jerk_z description"
msgid "Default jerk for the motor of the Z-direction."
msgstr "Z 軸方向馬達的預設加加速度。"

msgctxt "machine_max_jerk_e description"
msgid "Default jerk for the motor of the filament."
msgstr "擠出馬達的預設加加速度。"

msgctxt "bridge_settings_enabled description"
msgid "Detect bridges and modify print speed, flow and fan settings while bridges are printed."
msgstr "偵測橋樑，並在列印橋樑時改變列印速度，流量和風扇轉速。"

msgctxt "inset_direction description"
msgid "Determines the order in which walls are printed. Printing outer walls earlier helps with dimensional accuracy, as faults from inner walls cannot propagate to the outside. However printing them later allows them to stack better when overhangs are printed. When there is an uneven amount of total innner walls, the 'center last line' is always printed last."
msgstr ""

msgctxt "infill_mesh_order description"
msgid "Determines the priority of this mesh when considering multiple overlapping infill meshes. Areas where multiple infill meshes overlap will take on the settings of the mesh with the highest rank. An infill mesh with a higher rank will modify the infill of infill meshes with lower rank and normal meshes."
msgstr "決定多個網格重疊填充的優先權. 多個重疊填充區域會採取網格設定之最高值. 較高的填充網格設定會改動較低網格及普通網格之填充."

msgctxt "lightning_infill_support_angle description"
msgid "Determines when a lightning infill layer has to support anything above it. Measured in the angle given the thickness of a layer."
msgstr "決定使用閃電形填充支撐時，層間堆疊的角度."

msgctxt "lightning_infill_overhang_angle description"
msgid "Determines when a lightning infill layer has to support the model above it. Measured in the angle given the thickness."
msgstr "決定使用閃電形填充支撐時，層間堆疊的角度."

msgctxt "material_diameter label"
msgid "Diameter"
msgstr "直徑"

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model label"
msgid "Diameter Increase To Model"
msgstr ""

msgctxt "support_tree_bp_diameter description"
msgid "Diameter every branch tries to achieve when reaching the buildplate. Improves bed adhesion."
msgstr ""

msgctxt "adhesion_type description"
msgid "Different options that help to improve both priming your extrusion and adhesion to the build plate. Brim adds a single layer flat area around the base of your model to prevent warping. Raft adds a thick grid with a roof below the model. Skirt is a line printed around the model, but not connected to the model."
msgstr "幫助改善擠出裝填以及與列印平台附著的不同選項。邊緣會在模型基座周圍添加單層平面區域，以防止翹曲。木筏會在模型底下添加一個有頂板的厚網格。外圍是在模型四周列印的一條線，但並不與模型連接。"

msgctxt "machine_disallowed_areas label"
msgid "Disallowed Areas"
msgstr "禁入區域"

msgctxt "infill_line_distance description"
msgid "Distance between the printed infill lines. This setting is calculated by the infill density and the infill line width."
msgstr "列印填充線條之間的距離。該設定是通過填充密度和填充線寬度計算。"

msgctxt "support_initial_layer_line_distance description"
msgid "Distance between the printed initial layer support structure lines. This setting is calculated by the support density."
msgstr "支撐結構起始層線條之間的距離。該設定通過支撐密度計算。"

msgctxt "support_bottom_line_distance description"
msgid "Distance between the printed support floor lines. This setting is calculated by the Support Floor Density, but can be adjusted separately."
msgstr "支撐底板線條之間的距離。該設定是通過支撐底板密度計算，但可以單獨調整。"

msgctxt "support_roof_line_distance description"
msgid "Distance between the printed support roof lines. This setting is calculated by the Support Roof Density, but can be adjusted separately."
msgstr "支撐頂板線條之間的距離。該設定是通過支撐頂板密度計算，但可以單獨調整。"

msgctxt "support_line_distance description"
msgid "Distance between the printed support structure lines. This setting is calculated by the support density."
msgstr "支撐結構線條之間的距離。該設定通過支撐密度計算。"

msgctxt "support_bottom_distance description"
msgid "Distance from the print to the bottom of the support. Note that this is rounded up to the next layer height."
msgstr ""

msgctxt "support_top_distance description"
msgid "Distance from the top of the support to the print."
msgstr "從支撐頂部到列印品的距離。"

msgctxt "support_z_distance description"
msgid "Distance from the top/bottom of the support structure to the print. This gap provides clearance to remove the supports after the model is printed. The topmost support layer below the model might be a fraction of regular layers."
msgstr ""

msgctxt "infill_wipe_dist description"
msgid "Distance of a travel move inserted after every infill line, to make the infill stick to the walls better. This option is similar to infill overlap, but without extrusion and only on one end of the infill line."
msgstr "每條填充線條後插入的空跑距離，讓填充更好地附著到壁上。此選項與填充重疊類似，但没有擠出，且僅位於填充線條的一端。"

msgctxt "wall_0_wipe_dist description"
msgid "Distance of a travel move inserted after the outer wall, to hide the Z seam better."
msgstr "在列印外壁後插入的空跑距離，以便消除隱藏 Z 縫的銜接痕跡。"

msgctxt "draft_shield_dist description"
msgid "Distance of the draft shield from the print, in the X/Y directions."
msgstr "防風罩與模型在 X/Y 軸方向的距離。"

msgctxt "ooze_shield_dist description"
msgid "Distance of the ooze shield from the print, in the X/Y directions."
msgstr "擦拭牆與模型間的水平（X/Y 方向）距離。"

msgctxt "support_xy_distance_overhang description"
msgid "Distance of the support structure from the overhang in the X/Y directions."
msgstr "支撐結構在 X/Y 方向與突出部分的間距。"

msgctxt "support_xy_distance description"
msgid "Distance of the support structure from the print in the X/Y directions."
msgstr "支撐結構在 X/Y 方向距列印品的距離。"

msgctxt "meshfix_fluid_motion_shift_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr ""

msgctxt "meshfix_fluid_motion_small_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr ""

msgctxt "min_infill_area description"
msgid "Don't generate areas of infill smaller than this (use skin instead)."
msgstr "不要產生小於此面積的填充區域（使用表層取代）。"

msgctxt "draft_shield_height label"
msgid "Draft Shield Height"
msgstr "防風罩高度"

msgctxt "draft_shield_height_limitation label"
msgid "Draft Shield Limitation"
msgstr "防風罩限高"

msgctxt "draft_shield_dist label"
msgid "Draft Shield X/Y Distance"
msgstr "防風罩 X/Y 距離"

msgctxt "support_mesh_drop_down label"
msgid "Drop Down Support Mesh"
msgstr "下拉式支撐網格"

msgctxt "dual label"
msgid "Dual Extrusion"
msgstr "雙重擠出機"

msgctxt "machine_shape option elliptic"
msgid "Elliptic"
msgstr "類圓形"

msgctxt "acceleration_enabled label"
msgid "Enable Acceleration Control"
msgstr "啟用加速度控制"

msgctxt "bridge_settings_enabled label"
msgid "Enable Bridge Settings"
msgstr "啟用橋樑設定"

msgctxt "coasting_enable label"
msgid "Enable Coasting"
msgstr "啟用滑行"

msgctxt "support_conical_enabled label"
msgid "Enable Conical Support"
msgstr "啟用錐形支撐"

msgctxt "draft_shield_enabled label"
msgid "Enable Draft Shield"
msgstr "啟用防風罩"

msgctxt "meshfix_fluid_motion_enabled label"
msgid "Enable Fluid Motion"
msgstr ""

msgctxt "ironing_enabled label"
msgid "Enable Ironing"
msgstr "啟用燙平"

msgctxt "jerk_enabled label"
msgid "Enable Jerk Control"
msgstr "啟用加加速度控制"

msgctxt "machine_nozzle_temp_enabled label"
msgid "Enable Nozzle Temperature Control"
msgstr "啟用噴頭溫度控制"

msgctxt "ooze_shield_enabled label"
msgid "Enable Ooze Shield"
msgstr "啟用擦拭牆"

msgctxt "prime_blob_enable label"
msgid "Enable Prime Blob"
msgstr "啟用少量裝填"

msgctxt "prime_tower_enable label"
msgid "Enable Prime Tower"
msgstr "啟用換料塔"

msgctxt "cool_fan_enabled label"
msgid "Enable Print Cooling"
msgstr "開啟列印冷卻"

msgctxt "retraction_enable label"
msgid "Enable Retraction"
msgstr "啟用回抽"

msgctxt "support_brim_enable label"
msgid "Enable Support Brim"
msgstr "啟用支撐邊緣"

msgctxt "support_bottom_enable label"
msgid "Enable Support Floor"
msgstr "啟用支撐底板"

msgctxt "support_interface_enable label"
msgid "Enable Support Interface"
msgstr "啟用支撐介面"

msgctxt "support_roof_enable label"
msgid "Enable Support Roof"
msgstr "啟用支撐頂板"

msgctxt "acceleration_travel_enabled label"
msgid "Enable Travel Acceleration"
msgstr ""

msgctxt "jerk_travel_enabled label"
msgid "Enable Travel Jerk"
msgstr ""

msgctxt "ooze_shield_enabled description"
msgid "Enable exterior ooze shield. This will create a shell around the model which is likely to wipe a second nozzle if it's at the same height as the first nozzle."
msgstr "啟用外部擦拭牆。這將在模型周圍創建一個外殼，如果與第一個噴頭處於相同的高度，則可能會擦拭第二個噴頭。"

msgctxt "small_skin_on_surface description"
msgid "Enable small (up to 'Small Top/Bottom Width') regions on the topmost skinned layer (exposed to air) to be filled with walls instead of the default pattern."
msgstr ""

msgctxt "jerk_enabled description"
msgid "Enables adjusting the jerk of print head when the velocity in the X or Y axis changes. Increasing the jerk can reduce printing time at the cost of print quality."
msgstr "啟用當 X 或 Y 軸的速度變化時調整列印頭的加加速度。提高加加速度可以通過以列印品質為代價來縮短列印時間。"

msgctxt "acceleration_enabled description"
msgid "Enables adjusting the print head acceleration. Increasing the accelerations can reduce printing time at the cost of print quality."
msgstr "啟用調整噴頭的加速度。增加加速度可以減少列印時間卻會犧牲列印品質。"

msgctxt "cool_fan_enabled description"
msgid "Enables the print cooling fans while printing. The fans improve print quality on layers with short layer times and bridging / overhangs."
msgstr "列印時啟用列印冷卻風扇。風扇可以在列印時間較短的層和橋接/突出部分提高列印品質。"

msgctxt "machine_end_gcode label"
msgid "End G-code"
msgstr "結束 G-code"

msgctxt "material_end_of_filament_purge_length label"
msgid "End of Filament Purge Length"
msgstr "線材更換沖洗長度"

msgctxt "material_end_of_filament_purge_speed label"
msgid "End of Filament Purge Speed"
msgstr "線材更換沖洗速度"

msgctxt "brim_replaces_support description"
msgid "Enforce brim to be printed around the model even if that space would otherwise be occupied by support. This replaces some regions of the first layer of support by brim regions."
msgstr "強制在模型周圍列印邊緣，即使該空間已被支撐佔用。在第一層的部份區域會以邊綠取代支撐。"

msgctxt "support_type option everywhere"
msgid "Everywhere"
msgstr "每個地方"

msgctxt "slicing_tolerance option exclusive"
msgid "Exclusive"
msgstr "排除"

msgctxt "experimental label"
msgid "Experimental"
msgstr "實驗性"

msgctxt "z_seam_corner option z_seam_corner_outer"
msgid "Expose Seam"
msgstr "暴露接縫"

msgctxt "meshfix_extensive_stitching label"
msgid "Extensive Stitching"
msgstr "廣泛縫合"

msgctxt "meshfix_extensive_stitching description"
msgid "Extensive stitching tries to stitch up open holes in the mesh by closing the hole with touching polygons. This option can introduce a lot of processing time."
msgstr "廣泛縫合嘗試通過接觸多邊形來閉合孔洞，以此縫合網格中的開孔。此選項可能會產生大量的處理時間。"

msgctxt "infill_wall_line_count label"
msgid "Extra Infill Wall Count"
msgstr "額外填充牆壁數量"

msgctxt "skin_outline_count label"
msgid "Extra Skin Wall Count"
msgstr "額外表層牆壁計數"

msgctxt "switch_extruder_extra_prime_amount description"
msgid "Extra material to prime after nozzle switching."
msgstr "噴頭切換後額外裝填的線材量。"

msgctxt "extruder_prime_pos_x label"
msgid "Extruder Prime X Position"
msgstr "擠出機 X 軸起始位置"

msgctxt "extruder_prime_pos_y label"
msgid "Extruder Prime Y Position"
msgstr "擠出機 Y 軸起始位置"

msgctxt "extruder_prime_pos_z label"
msgid "Extruder Prime Z Position"
msgstr "擠出機初始 Z 軸位置"

msgctxt "machine_extruders_share_heater label"
msgid "Extruders Share Heater"
msgstr "擠出機共用加熱器"

msgctxt "machine_extruders_share_nozzle label"
msgid "Extruders Share Nozzle"
msgstr "擠出機共用噴頭"

msgctxt "material_extrusion_cool_down_speed label"
msgid "Extrusion Cool Down Speed Modifier"
msgstr "擠出降溫速度修正"

msgctxt "speed_equalize_flow_width_factor description"
msgid "Extrusion width based correction factor on the speed. At 0% the movement speed is kept constant at the Print Speed. At 100% the movement speed is adjusted so that the flow (in mm³/s) is kept constant, i.e. lines half the normal Line Width are printed twice as fast and lines twice as wide are printed half as fast. A value larger than 100% can help to compensate for the higher pressure required to extrude wide lines."
msgstr ""

msgctxt "cool_fan_speed label"
msgid "Fan Speed"
msgstr "風扇轉速"

msgctxt "support_fan_enable label"
msgid "Fan Speed Override"
msgstr "改變風扇轉速"

msgctxt "small_feature_max_length description"
msgid "Feature outlines that are shorter than this length will be printed using Small Feature Speed."
msgstr "輪廓長度小於此值時，使用細部模式速度列印。"

msgctxt "experimental description"
msgid "Features that haven't completely been fleshed out yet."
msgstr "尚未完全的功能。"

msgctxt "machine_feeder_wheel_diameter label"
msgid "Feeder Wheel Diameter"
msgstr "進料輪直徑"

msgctxt "material_final_print_temperature label"
msgid "Final Printing Temperature"
msgstr "最終列印溫度"

msgctxt "machine_firmware_retract label"
msgid "Firmware Retraction"
msgstr "韌體回抽"

msgctxt "support_extruder_nr_layer_0 label"
msgid "First Layer Support Extruder"
msgstr "第一層支撐擠出機"

msgctxt "material_flow label"
msgid "Flow"
msgstr "流量"

msgctxt "speed_equalize_flow_width_factor label"
msgid "Flow Equalization Ratio"
msgstr ""

msgctxt "flow_rate_extrusion_offset_factor label"
msgid "Flow Rate Compensation Factor"
msgstr "流速補償係數"

msgctxt "flow_rate_max_extrusion_offset label"
msgid "Flow Rate Compensation Max Extrusion Offset"
msgstr "流速補償的最大擠出偏移量"

msgctxt "material_flow_temp_graph label"
msgid "Flow Temperature Graph"
msgstr "流量溫度圖"

msgctxt "material_flow_layer_0 description"
msgid "Flow compensation for the first layer: the amount of material extruded on the initial layer is multiplied by this value."
msgstr "第一層的流量補償：在起始層上擠出的線材量會乘以此值。"

msgctxt "skin_material_flow_layer_0 description"
msgid "Flow compensation on bottom lines of the first layer"
msgstr ""

msgctxt "infill_material_flow description"
msgid "Flow compensation on infill lines."
msgstr "填充線條的流量補償。"

msgctxt "support_interface_material_flow description"
msgid "Flow compensation on lines of support roof or floor."
msgstr "支撐頂板或底板線條的流量補償。"

msgctxt "roofing_material_flow description"
msgid "Flow compensation on lines of the areas at the top of the print."
msgstr "頂部區域線條的流量補償。"

msgctxt "prime_tower_flow description"
msgid "Flow compensation on prime tower lines."
msgstr "換料塔線條的流量補償。"

msgctxt "skirt_brim_material_flow description"
msgid "Flow compensation on skirt or brim lines."
msgstr "外圍/邊緣線條的流量補償。"

msgctxt "support_bottom_material_flow description"
msgid "Flow compensation on support floor lines."
msgstr "支撐底板線條的流量補償。"

msgctxt "support_roof_material_flow description"
msgid "Flow compensation on support roof lines."
msgstr "支撐頂板線條的流量補償。"

msgctxt "support_material_flow description"
msgid "Flow compensation on support structure lines."
msgstr "支撐結構線條的流量補償。"

msgctxt "wall_0_material_flow_layer_0 description"
msgid "Flow compensation on the outermost wall line of the first layer."
msgstr ""

msgctxt "wall_0_material_flow description"
msgid "Flow compensation on the outermost wall line."
msgstr "外壁線條的流量補償。"

msgctxt "wall_0_material_flow_roofing description"
msgid "Flow compensation on the top surface outermost wall line."
msgstr ""

msgctxt "wall_x_material_flow_roofing description"
msgid "Flow compensation on top surface wall lines for all wall lines except the outermost one."
msgstr ""

msgctxt "skin_material_flow description"
msgid "Flow compensation on top/bottom lines."
msgstr "頂部/底部線條的流量補償。"

msgctxt "wall_x_material_flow_layer_0 description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one, but only for the first layer"
msgstr ""

msgctxt "wall_x_material_flow description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one."
msgstr "最外層牆壁以外的牆壁線條的流量補償。"

msgctxt "wall_material_flow description"
msgid "Flow compensation on wall lines."
msgstr "牆壁線條的流量補償。"

msgctxt "material_flow description"
msgid "Flow compensation: the amount of material extruded is multiplied by this value."
msgstr "流量補償：擠出的線材量乘以此值。"

msgctxt "meshfix_fluid_motion_angle label"
msgid "Fluid Motion Angle"
msgstr ""

msgctxt "meshfix_fluid_motion_shift_distance label"
msgid "Fluid Motion Shift Distance"
msgstr ""

msgctxt "meshfix_fluid_motion_small_distance label"
msgid "Fluid Motion Small Distance"
msgstr ""

msgctxt "material_flush_purge_length label"
msgid "Flush Purge Length"
msgstr "沖洗長度"

msgctxt "material_flush_purge_speed label"
msgid "Flush Purge Speed"
msgstr "沖洗速度"

msgctxt "min_wall_line_width description"
msgid "For thin structures around once or twice the nozzle size, the line widths need to be altered to adhere to the thickness of the model. This setting controls the minimum line width allowed for the walls. The minimum line widths inherently also determine the maximum line widths, since we transition from N to N+1 walls at some geometry thickness where the N walls are wide and the N+1 walls are narrow. The widest possible wall line is twice the Minimum Wall Line Width."
msgstr ""

msgctxt "z_seam_position option front"
msgid "Front"
msgstr "前方"

msgctxt "z_seam_position option frontleft"
msgid "Front Left"
msgstr "左前方"

msgctxt "z_seam_position option frontright"
msgid "Front Right"
msgstr "右前方"

msgctxt "draft_shield_height_limitation option full"
msgid "Full"
msgstr "完整"

msgctxt "magic_fuzzy_skin_enabled label"
msgid "Fuzzy Skin"
msgstr "絨毛皮膚"

msgctxt "magic_fuzzy_skin_point_density label"
msgid "Fuzzy Skin Density"
msgstr "絨毛皮膚密度"

msgctxt "magic_fuzzy_skin_outside_only label"
msgid "Fuzzy Skin Outside Only"
msgstr "絨毛皮膚只限外層"

msgctxt "magic_fuzzy_skin_point_dist label"
msgid "Fuzzy Skin Point Distance"
msgstr "絨毛皮膚距離"

msgctxt "magic_fuzzy_skin_thickness label"
msgid "Fuzzy Skin Thickness"
msgstr "絨毛皮膚厚度"

msgctxt "machine_gcode_flavor label"
msgid "G-code Flavor"
msgstr "G-code 類型"

msgctxt "machine_end_gcode description"
msgid ""
"G-code commands to be executed at the very end - separated by \n"
"."
msgstr ""
"結束前最後執行的 G-code 命令 - 使用 \n"
" 隔開。"

msgctxt "machine_start_gcode description"
msgid ""
"G-code commands to be executed at the very start - separated by \n"
"."
msgstr ""
"開始時最先執行的 G-code 命令 - 使用 \n"
"隔開。"

msgctxt "material_guid description"
msgid "GUID of the material. This is set automatically."
msgstr "線材的 GUID，此項為自動設定。"

msgctxt "gantry_height label"
msgid "Gantry Height"
msgstr "吊車高度"

msgctxt "interlocking_enable label"
msgid "Generate Interlocking Structure"
msgstr ""

msgctxt "support_enable label"
msgid "Generate Support"
msgstr "產生支撐"

msgctxt "support_brim_enable description"
msgid "Generate a brim within the support infill regions of the first layer. This brim is printed underneath the support, not around it. Enabling this setting increases the adhesion of support to the build plate."
msgstr "在第一層的支撐填充區域內產生邊緣。這些邊緣列印在支撐下面，而不是支撐的周圍。啟用此設定可增加支撐對列印平台的附著力。"

msgctxt "support_interface_enable description"
msgid "Generate a dense interface between the model and the support. This will create a skin at the top of the support on which the model is printed and at the bottom of the support, where it rests on the model."
msgstr "在模型和支撐之間產生一個密度較高的介面。這會在承載模型的支撐頂部和座落在模型上的支撐底部創造出一個介面層。"

msgctxt "support_bottom_enable description"
msgid "Generate a dense slab of material between the bottom of the support and the model. This will create a skin between the model and support."
msgstr "在支撐底部和模型之間產生一個密集的平板。這會在模型和支撐之間形成一個介面層。"

msgctxt "support_roof_enable description"
msgid "Generate a dense slab of material between the top of support and the model. This will create a skin between the model and support."
msgstr "在支撐頂部和模型之間產生一個密集的平板。這會在模型和支撐之間形成一個介面層。"

msgctxt "support_enable description"
msgid "Generate structures to support parts of the model which have overhangs. Without these structures, such parts would collapse during printing."
msgstr "在模型的突出部分產生支撐結構。若不這樣做，這些部分在列印時會倒塌。"

msgctxt "machine_buildplate_type option glass"
msgid "Glass"
msgstr "玻璃"

msgctxt "ironing_enabled description"
msgid "Go over the top surface one additional time, but this time extruding very little material. This is meant to melt the plastic on top further, creating a smoother surface. The pressure in the nozzle chamber is kept high so that the creases in the surface are filled with material."
msgstr "噴頭額外跑過上表層一次，但這次擠出的材料非常少。這是為了進一步融化頂部的塑料，創造更平滑的表面。噴頭中的壓力會保持很高，將表面中的細縫中填滿材料。"

msgctxt "gradual_infill_step_height label"
msgid "Gradual Infill Step Height"
msgstr "漸進填充步階高度"

msgctxt "gradual_infill_steps label"
msgid "Gradual Infill Steps"
msgstr "漸進填充步階數"

msgctxt "gradual_support_infill_step_height label"
msgid "Gradual Support Infill Step Height"
msgstr "漸進支撐填充步階高度"

msgctxt "gradual_support_infill_steps label"
msgid "Gradual Support Infill Steps"
msgstr "漸進支撐填充步階"

msgctxt "cool_min_temperature description"
msgid "Gradually reduce to this temperature when printing at reduced speeds because of minimum layer time."
msgstr ""

msgctxt "infill_pattern option grid"
msgid "Grid"
msgstr "網格"

msgctxt "support_bottom_pattern option grid"
msgid "Grid"
msgstr "網格"

msgctxt "support_interface_pattern option grid"
msgid "Grid"
msgstr "網格"

msgctxt "support_pattern option grid"
msgid "Grid"
msgstr "網格"

msgctxt "support_roof_pattern option grid"
msgid "Grid"
msgstr "網格"

msgctxt "machine_gcode_flavor option Griffin"
msgid "Griffin"
msgstr "Griffin"

msgctxt "group_outer_walls label"
msgid "Group Outer Walls"
msgstr ""

msgctxt "infill_pattern option gyroid"
msgid "Gyroid"
msgstr "螺旋形"

msgctxt "support_pattern option gyroid"
msgid "Gyroid"
msgstr "螺旋形"

msgctxt "machine_heated_build_volume label"
msgid "Has Build Volume Temperature Stabilization"
msgstr "具有列印空間溫度穩定性"

msgctxt "machine_heated_bed label"
msgid "Has Heated Build Plate"
msgstr "有熱床"

msgctxt "machine_nozzle_heat_up_speed label"
msgid "Heat Up Speed"
msgstr "加熱速度"

msgctxt "machine_heat_zone_length label"
msgid "Heat Zone Length"
msgstr "加熱區長度"

msgctxt "draft_shield_height description"
msgid "Height limitation of the draft shield. Above this height no draft shield will be printed."
msgstr "防風罩的高度限制。超過這個高度就不再列印防風罩。"

msgctxt "z_seam_corner option z_seam_corner_inner"
msgid "Hide Seam"
msgstr "隱藏接縫"

msgctxt "z_seam_corner option z_seam_corner_any"
msgid "Hide or Expose Seam"
msgstr "隱藏或暴露接縫"

msgctxt "hole_xy_offset label"
msgid "Hole Horizontal Expansion"
msgstr "孔洞水平擴展"

msgctxt "hole_xy_offset_max_diameter label"
msgid "Hole Horizontal Expansion Max Diameter"
msgstr ""

msgctxt "small_hole_max_size description"
msgid "Holes and part outlines with a diameter smaller than this will be printed using Small Feature Speed."
msgstr "小於此直徑的孔洞和零件輪廓，使用細部模式速度列印。"

msgctxt "xy_offset label"
msgid "Horizontal Expansion"
msgstr "水平擴展"

msgctxt "material_shrinkage_percentage_xy label"
msgid "Horizontal Scaling Factor Shrinkage Compensation"
msgstr ""

msgctxt "material_break_preparation_retracted_position description"
msgid "How far the filament can be stretched before it breaks, while heated."
msgstr "在加熱時，線材在脆斷前可以拉伸多長的距離。"

msgctxt "material_anti_ooze_retracted_position description"
msgid "How far the material needs to be retracted before it stops oozing."
msgstr "停止滲漏要回抽線材多長的距離。"

msgctxt "flow_rate_extrusion_offset_factor description"
msgid "How far to move the filament in order to compensate for changes in flow rate, as a percentage of how far the filament would move in one second of extrusion."
msgstr "為了補償流速變化，線材所移動的距離，以線材一秒內擠出距離的百分比表示。"

msgctxt "material_break_retracted_position description"
msgid "How far to retract the filament in order to break it cleanly."
msgstr "要讓線材脆斷需要回抽長的距離。"

msgctxt "material_break_preparation_speed description"
msgid "How fast the filament needs to be retracted just before breaking it off in a retraction."
msgstr "回抽切斷前，線材回抽的速度。"

msgctxt "material_anti_ooze_retraction_speed description"
msgid "How fast the material needs to be retracted during a filament switch to prevent oozing."
msgstr "在線材切換回抽時，需要多快的速度來防止滲漏。"

msgctxt "material_end_of_filament_purge_speed description"
msgid "How fast to prime the material after replacing an empty spool with a fresh spool of the same material."
msgstr "更換新的一捲相同線材後，用多快的速度擠出線材做沖洗。"

msgctxt "material_flush_purge_speed description"
msgid "How fast to prime the material after switching to a different material."
msgstr "切換到另一線材後，用多快的速度擠出線材做沖洗。"

msgctxt "material_maximum_park_duration description"
msgid "How long the material can be kept out of dry storage safely."
msgstr "線材可在乾燥箱外安全的存放多久。"

msgctxt "machine_steps_per_mm_x description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the X direction."
msgstr "在 X 方向移動一毫米時，步進馬達所需移動的步數。"

msgctxt "machine_steps_per_mm_y description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Y direction."
msgstr "在 Y 方向移動一毫米時，步進馬達所需移動的步數。"

msgctxt "machine_steps_per_mm_z description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Z direction."
msgstr "在 Z 方向移動一毫米時，步進馬達所需移動的步數。"

msgctxt "machine_steps_per_mm_e description"
msgid "How many steps of the stepper motors will result in moving the feeder wheel by one millimeter around its circumference."
msgstr "在擠出輪旋轉一毫米時，步進馬達所需移動的步數."

msgctxt "material_end_of_filament_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when replacing an empty spool with a fresh spool of the same material."
msgstr "更換新的一捲相同線材時，要使用多少線材（以線材長度計算）將噴頭內先前的線材沖洗出來。"

msgctxt "material_flush_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when switching to a different material."
msgstr "切換到另一線材時，要使用多少線材（以線材長度計算）將噴頭內先前的線材沖洗出來。"

msgctxt "machine_extruders_shared_nozzle_initial_retraction description"
msgid "How much the filament of each extruder is assumed to have been retracted from the shared nozzle tip at the completion of the printer-start gcode script; the value should be equal to or greater than the length of the common part of the nozzle's ducts."
msgstr "在完成\"Printer-start G-code\"後，各擠出機將從共用噴頭回抽多少的線材。此數值應等於或大於噴頭的共用管道長度。"

msgctxt "support_interface_priority description"
msgid "How support interface and support will interact when they overlap. Currently only implemented for support roof."
msgstr ""

msgctxt "support_tree_min_height_to_model description"
msgid "How tall a branch has to be if it is placed on the model. Prevents small blobs of support. This setting is ignored when a branch is supporting a support roof."
msgstr ""

msgctxt "bridge_skin_support_threshold description"
msgid "If a skin region is supported for less than this percentage of its area, print it using the bridge settings. Otherwise it is printed using the normal skin settings."
msgstr "假如表層區域受支撐的面積小於此百分比，使用橋樑設定列印。否則用一般的表層設定列印。"

msgctxt "meshfix_fluid_motion_angle description"
msgid "If a toolpath-segment deviates more than this angle from the general motion it is smoothed."
msgstr ""

msgctxt "bridge_enable_more_layers description"
msgid "If enabled, the second and third layers above the air are printed using the following settings. Otherwise, those layers are printed using the normal settings."
msgstr "假如啟用此功能，橋樑上的第二層和第三層使用下列的設定列印。否則這些層以一般設定列印。"

msgctxt "wall_transition_filter_distance description"
msgid "If it would be transitioning back and forth between different numbers of walls in quick succession, don't transition at all. Remove transitions if they are closer together than this distance."
msgstr ""

msgctxt "raft_margin description"
msgid "If the raft is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "如果啟用了木筏，在模型周圍也會增加額外邊緣。增大這個邊緣將產生更強的木筏，不過也會使用更多的線材，並減少印表機的可列印面積。"

msgctxt "meshfix_union_all description"
msgid "Ignore the internal geometry arising from overlapping volumes within a mesh and print the volumes as one. This may cause unintended internal cavities to disappear."
msgstr "忽略因網格內部重疊產生的幾何空間，並將多個重疊體積作為一個列印。這可能會導致意外的內部孔洞消失。"

msgctxt "material_bed_temp_prepend label"
msgid "Include Build Plate Temperature"
msgstr "插入熱床溫度"

msgctxt "material_print_temp_prepend label"
msgid "Include Material Temperatures"
msgstr "插入線材溫度"

msgctxt "slicing_tolerance option inclusive"
msgid "Inclusive"
msgstr "包含"

msgctxt "infill description"
msgid "Infill"
msgstr "填充"

msgctxt "infill label"
msgid "Infill"
msgstr "填充"

msgctxt "acceleration_infill label"
msgid "Infill Acceleration"
msgstr "填充加速度"

msgctxt "infill_before_walls label"
msgid "Infill Before Walls"
msgstr "先印填充再印牆壁"

msgctxt "infill_sparse_density label"
msgid "Infill Density"
msgstr "填充密度"

msgctxt "infill_extruder_nr label"
msgid "Infill Extruder"
msgstr "填充擠出機"

msgctxt "infill_material_flow label"
msgid "Infill Flow"
msgstr "填充流量"

msgctxt "jerk_infill label"
msgid "Infill Jerk"
msgstr "填充加加速度"

msgctxt "infill_sparse_thickness label"
msgid "Infill Layer Thickness"
msgstr "填充層厚度"

msgctxt "infill_angles label"
msgid "Infill Line Directions"
msgstr "填充線條方向"

msgctxt "infill_line_distance label"
msgid "Infill Line Distance"
msgstr "填充線條距離"

msgctxt "infill_multiplier label"
msgid "Infill Line Multiplier"
msgstr "填充線倍增器"

msgctxt "infill_line_width label"
msgid "Infill Line Width"
msgstr "填充線寬"

msgctxt "infill_mesh label"
msgid "Infill Mesh"
msgstr "填充網格"

msgctxt "infill_support_angle label"
msgid "Infill Overhang Angle"
msgstr "填充突出角度"

msgctxt "infill_overlap_mm label"
msgid "Infill Overlap"
msgstr "填充重疊"

msgctxt "infill_overlap label"
msgid "Infill Overlap Percentage"
msgstr "填充重疊百分比"

msgctxt "infill_pattern label"
msgid "Infill Pattern"
msgstr "填充列印樣式"

msgctxt "speed_infill label"
msgid "Infill Speed"
msgstr "填充速度"

msgctxt "infill_support_enabled label"
msgid "Infill Support"
msgstr "填充支撐"

msgctxt "infill_enable_travel_optimization label"
msgid "Infill Travel Optimization"
msgstr "填充空跑最佳化"

msgctxt "infill_wipe_dist label"
msgid "Infill Wipe Distance"
msgstr "填充擦拭距離"

msgctxt "infill_offset_x label"
msgid "Infill X Offset"
msgstr "填充 X 軸偏移"

msgctxt "infill_offset_y label"
msgid "Infill Y Offset"
msgstr "填充 Y 軸偏移"

msgctxt "initial_bottom_layers label"
msgid "Initial Bottom Layers"
msgstr "起始底部層數"

msgctxt "cool_fan_speed_0 label"
msgid "Initial Fan Speed"
msgstr "起始層風扇轉速"

msgctxt "acceleration_layer_0 label"
msgid "Initial Layer Acceleration"
msgstr "起始層加速度"

msgctxt "skin_material_flow_layer_0 label"
msgid "Initial Layer Bottom Flow"
msgstr ""

msgctxt "support_tree_bp_diameter label"
msgid "Initial Layer Diameter"
msgstr ""

msgctxt "material_flow_layer_0 label"
msgid "Initial Layer Flow"
msgstr "起始層流量"

msgctxt "layer_height_0 label"
msgid "Initial Layer Height"
msgstr "起始層高"

msgctxt "xy_offset_layer_0 label"
msgid "Initial Layer Horizontal Expansion"
msgstr "起始層水平擴展"

msgctxt "wall_x_material_flow_layer_0 label"
msgid "Initial Layer Inner Wall Flow"
msgstr ""

msgctxt "jerk_layer_0 label"
msgid "Initial Layer Jerk"
msgstr "起始層加加速度"

msgctxt "initial_layer_line_width_factor label"
msgid "Initial Layer Line Width"
msgstr "起始層線寬"

msgctxt "wall_0_material_flow_layer_0 label"
msgid "Initial Layer Outer Wall Flow"
msgstr ""

msgctxt "acceleration_print_layer_0 label"
msgid "Initial Layer Print Acceleration"
msgstr "起始層列印加速度"

msgctxt "jerk_print_layer_0 label"
msgid "Initial Layer Print Jerk"
msgstr "起始層列印加加速度"

msgctxt "speed_print_layer_0 label"
msgid "Initial Layer Print Speed"
msgstr "起始層列印速度"

msgctxt "speed_layer_0 label"
msgid "Initial Layer Speed"
msgstr "起始層速度"

msgctxt "support_initial_layer_line_distance label"
msgid "Initial Layer Support Line Distance"
msgstr "支撐起始層線條間距"

msgctxt "acceleration_travel_layer_0 label"
msgid "Initial Layer Travel Acceleration"
msgstr "起始層空跑加速度"

msgctxt "jerk_travel_layer_0 label"
msgid "Initial Layer Travel Jerk"
msgstr "起始層空跑加加速度"

msgctxt "speed_travel_layer_0 label"
msgid "Initial Layer Travel Speed"
msgstr "起始層空跑速度"

msgctxt "layer_0_z_overlap label"
msgid "Initial Layer Z Overlap"
msgstr "起始層 Z 重疊"

msgctxt "material_initial_print_temperature label"
msgid "Initial Printing Temperature"
msgstr "起始列印溫度"

msgctxt "acceleration_wall_x label"
msgid "Inner Wall Acceleration"
msgstr "內壁加速度"

msgctxt "wall_x_extruder_nr label"
msgid "Inner Wall Extruder"
msgstr "內壁擠出機"

msgctxt "jerk_wall_x label"
msgid "Inner Wall Jerk"
msgstr "內壁加加速度"

msgctxt "speed_wall_x label"
msgid "Inner Wall Speed"
msgstr "內壁速度"

msgctxt "wall_x_material_flow label"
msgid "Inner Wall(s) Flow"
msgstr "內壁流量"

msgctxt "wall_line_width_x label"
msgid "Inner Wall(s) Line Width"
msgstr "內壁線寬"

msgctxt "wall_0_inset description"
msgid "Inset applied to the path of the outer wall. If the outer wall is smaller than the nozzle, and printed after the inner walls, use this offset to get the hole in the nozzle to overlap with the inner walls instead of the outside of the model."
msgstr "內嵌是套用在外壁路徑上的功能。如果外壁小於噴頭，並且在內壁之後列印，則此偏移量將使噴頭孔內移與內壁重疊而不是行走在模型外部。"

msgctxt "inset_direction option inside_out"
msgid "Inside To Outside"
msgstr ""

msgctxt "support_interface_priority option interface_lines_overwrite_support_area"
msgid "Interface lines preferred"
msgstr ""

msgctxt "support_interface_priority option interface_area_overwrite_support_area"
msgid "Interface preferred"
msgstr ""

msgctxt "interlocking_beam_layer_count label"
msgid "Interlocking Beam Layer Count"
msgstr ""

msgctxt "interlocking_beam_width label"
msgid "Interlocking Beam Width"
msgstr ""

msgctxt "interlocking_boundary_avoidance label"
msgid "Interlocking Boundary Avoidance"
msgstr ""

msgctxt "interlocking_depth label"
msgid "Interlocking Depth"
msgstr ""

msgctxt "interlocking_orientation label"
msgid "Interlocking Structure Orientation"
msgstr ""

msgctxt "ironing_only_highest_layer label"
msgid "Iron Only Highest Layer"
msgstr "只燙平最高層"

msgctxt "acceleration_ironing label"
msgid "Ironing Acceleration"
msgstr "燙平加速度"

msgctxt "ironing_flow label"
msgid "Ironing Flow"
msgstr "燙平流量"

msgctxt "ironing_inset label"
msgid "Ironing Inset"
msgstr "燙平內嵌"

msgctxt "jerk_ironing label"
msgid "Ironing Jerk"
msgstr "燙平加加速度"

msgctxt "ironing_line_spacing label"
msgid "Ironing Line Spacing"
msgstr "燙平線條間距"

msgctxt "ironing_pattern label"
msgid "Ironing Pattern"
msgstr "燙平列印樣式"

msgctxt "speed_ironing label"
msgid "Ironing Speed"
msgstr "燙平速度"

msgctxt "machine_center_is_zero label"
msgid "Is Center Origin"
msgstr "原點是否位於中心"

msgctxt "material_is_support_material label"
msgid "Is support material"
msgstr ""

msgctxt "material_crystallinity description"
msgid "Is this material the type that breaks off cleanly when heated (crystalline), or is it the type that produces long intertwined polymer chains (non-crystalline)?"
msgstr "這種線材高溫時是脆斷的類型（晶狀），還是拉絲的類型（非晶狀）？"

msgctxt "material_is_support_material description"
msgid "Is this material typically used as a support material during printing."
msgstr ""

msgctxt "magic_fuzzy_skin_outside_only description"
msgid "Jitter only the parts' outlines and not the parts' holes."
msgstr "只在列印外側時隨機抖動，內部孔洞不抖動。"

msgctxt "meshfix_keep_open_polygons label"
msgid "Keep Disconnected Faces"
msgstr "保持斷開表面"

msgctxt "layer_height label"
msgid "Layer Height"
msgstr "層高"

msgctxt "layer_start_x label"
msgid "Layer Start X"
msgstr "每層列印起始點的 X 座標"

msgctxt "layer_start_y label"
msgid "Layer Start Y"
msgstr "每層列印起始點的 Y 座標"

msgctxt "raft_base_thickness description"
msgid "Layer thickness of the base raft layer. This should be a thick layer which sticks firmly to the printer build plate."
msgstr "木筏底部的層厚。本層應為與印表機列印平台穩固附著厚實的一層。"

msgctxt "raft_interface_thickness description"
msgid "Layer thickness of the middle raft layer."
msgstr "木筏中層的層厚。"

msgctxt "raft_surface_thickness description"
msgid "Layer thickness of the top raft layers."
msgstr "木筏頂部單層的厚度。"

msgctxt "support_skip_zag_per_mm description"
msgid "Leave out a connection between support lines once every N millimeter to make the support structure easier to break away."
msgstr "每隔 N 毫米省略一次連接線，讓支撐結構更容易拆除。"

msgctxt "z_seam_position option left"
msgid "Left"
msgstr "左方"

msgctxt "cool_lift_head label"
msgid "Lift Head"
msgstr "噴頭抬升"

msgctxt "infill_pattern option lightning"
msgid "Lightning"
msgstr "閃電形"

msgctxt "lightning_infill_overhang_angle label"
msgid "Lightning Infill Overhang Angle"
msgstr "閃電形填充突出角度"

msgctxt "lightning_infill_prune_angle label"
msgid "Lightning Infill Prune Angle"
msgstr "閃電形填充生成角度"

msgctxt "lightning_infill_straightening_angle label"
msgid "Lightning Infill Straightening Angle"
msgstr "閃電形填充層間垂直堆疊角度"

msgctxt "lightning_infill_support_angle label"
msgid "Lightning Infill Support Angle"
msgstr "閃電形填充支撐堆疊角度"

msgctxt "support_tree_limit_branch_reach label"
msgid "Limit Branch Reach"
msgstr ""

msgctxt "support_tree_limit_branch_reach description"
msgid "Limit how far each branch should travel from the point it supports. This can make the support more sturdy, but will increase the amount of branches (and because of that material usage/print time)"
msgstr ""

msgctxt "cutting_mesh description"
msgid "Limit the volume of this mesh to within other meshes. You can use this to make certain areas of one mesh print with different settings and with a whole different extruder."
msgstr "將此網格的體積限制在其他網格內。你可以使用它來制作採用不同的設定以及完全不同的擠出機的網格列印的特定區域。"

msgctxt "draft_shield_height_limitation option limited"
msgid "Limited"
msgstr "限制"

msgctxt "line_width label"
msgid "Line Width"
msgstr "線寬"

msgctxt "infill_pattern option lines"
msgid "Lines"
msgstr "直線"

msgctxt "roofing_pattern option lines"
msgid "Lines"
msgstr "線條"

msgctxt "support_bottom_pattern option lines"
msgid "Lines"
msgstr "線條"

msgctxt "support_interface_pattern option lines"
msgid "Lines"
msgstr "線條"

msgctxt "support_pattern option lines"
msgid "Lines"
msgstr "線條"

msgctxt "support_roof_pattern option lines"
msgid "Lines"
msgstr "直線"

msgctxt "top_bottom_pattern option lines"
msgid "Lines"
msgstr "直線"

msgctxt "top_bottom_pattern_0 option lines"
msgid "Lines"
msgstr "直線"

msgctxt "machine_gcode_flavor option MACH3"
msgid "Mach3"
msgstr "Mach3"

msgctxt "machine_settings label"
msgid "Machine"
msgstr "機器"

msgctxt "machine_depth label"
msgid "Machine Depth"
msgstr "機器深度"

msgctxt "machine_head_with_fans_polygon label"
msgid "Machine Head & Fan Polygon"
msgstr "機器頭和風扇多邊形"

msgctxt "machine_height label"
msgid "Machine Height"
msgstr "機器高度"

msgctxt "machine_name label"
msgid "Machine Type"
msgstr "機器類型"

msgctxt "machine_width label"
msgid "Machine Width"
msgstr "機器寬度"

msgctxt "machine_settings description"
msgid "Machine specific settings"
msgstr "機器詳細設定"

msgctxt "conical_overhang_enabled label"
msgid "Make Overhang Printable"
msgstr "使突出可列印"

msgctxt "multiple_mesh_overlap description"
msgid "Make meshes which are touching each other overlap a bit. This makes them bond together better."
msgstr "使彼此接觸的網格稍微重疊。使他們能更緊密地結合在一起。"

msgctxt "support_conical_enabled description"
msgid "Make support areas smaller at the bottom than at the overhang."
msgstr "讓底部的支撐區域小於突出部分的支撐區域。"

msgctxt "support_mesh_drop_down description"
msgid "Make support everywhere below the support mesh, so that there's no overhang in the support mesh."
msgstr "在支撐網格下方的所有位置進行支撐，讓支撐網格中没有突出部分。"

msgctxt "extruder_prime_pos_abs description"
msgid "Make the extruder prime position absolute rather than relative to the last-known location of the head."
msgstr "擠出機的控制參數使用絕對位置，而不是與前次位置的相對位移。"

msgctxt "layer_0_z_overlap description"
msgid "Make the first and second layer of the model overlap in the Z direction to compensate for the filament lost in the airgap. All models above the first model layer will be shifted down by this amount."
msgstr "使模型的第一層和第二層在 Z 方向上重疊以補償在空隙中損失的線材。第一個模型層上方的所有模型將向下移動此重疊量。"

msgctxt "meshfix description"
msgid "Make the meshes more suited for 3D printing."
msgstr "讓網格更適合 3D 列印。"

msgctxt "machine_gcode_flavor option Makerbot"
msgid "Makerbot"
msgstr "Makerbot"

msgctxt "machine_gcode_flavor option RepRap (Marlin/Sprinter)"
msgid "Marlin"
msgstr "Marlin"

msgctxt "machine_gcode_flavor option RepRap (Volumetric)"
msgid "Marlin (Volumetric)"
msgstr "Marlin（容積）"

msgctxt "material description"
msgid "Material"
msgstr "線材"

msgctxt "material label"
msgid "Material"
msgstr "線材"

msgctxt "material_guid label"
msgid "Material GUID"
msgstr "線材 GUID"

msgctxt "max_extrusion_before_wipe label"
msgid "Material Volume Between Wipes"
msgstr "擦拭線材體積"

msgctxt "retraction_combing_max_distance label"
msgid "Max Comb Distance With No Retract"
msgstr "不回抽的最大梳理距離"

msgctxt "machine_max_acceleration_x label"
msgid "Maximum Acceleration X"
msgstr "X 軸最大加速度"

msgctxt "machine_max_acceleration_y label"
msgid "Maximum Acceleration Y"
msgstr "Y 軸最大加速度"

msgctxt "machine_max_acceleration_z label"
msgid "Maximum Acceleration Z"
msgstr "Z 軸最大加速度"

msgctxt "support_tree_angle label"
msgid "Maximum Branch Angle"
msgstr ""

msgctxt "meshfix_maximum_deviation label"
msgid "Maximum Deviation"
msgstr "最大偏差值"

msgctxt "meshfix_maximum_extrusion_area_deviation label"
msgid "Maximum Extrusion Area Deviation"
msgstr ""

msgctxt "cool_fan_speed_max label"
msgid "Maximum Fan Speed"
msgstr "最大風扇轉速"

msgctxt "machine_max_acceleration_e label"
msgid "Maximum Filament Acceleration"
msgstr "擠出馬達最大加速度"

msgctxt "conical_overhang_angle label"
msgid "Maximum Model Angle"
msgstr "最大模型角度"

msgctxt "conical_overhang_hole_size label"
msgid "Maximum Overhang Hole Area"
msgstr "最大突出孔洞面積"

msgctxt "material_maximum_park_duration label"
msgid "Maximum Park Duration"
msgstr "最長停放時間"

msgctxt "meshfix_maximum_resolution label"
msgid "Maximum Resolution"
msgstr "最高解析度"

msgctxt "retraction_count_max label"
msgid "Maximum Retraction Count"
msgstr "最大回抽次數"

msgctxt "max_skin_angle_for_expansion label"
msgid "Maximum Skin Angle for Expansion"
msgstr "最大延伸表層角度"

msgctxt "machine_max_feedrate_e label"
msgid "Maximum Speed E"
msgstr "E 軸最大速度"

msgctxt "machine_max_feedrate_x label"
msgid "Maximum Speed X"
msgstr "X 軸最大速度"

msgctxt "machine_max_feedrate_y label"
msgid "Maximum Speed Y"
msgstr "Y 軸最大速度"

msgctxt "machine_max_feedrate_z label"
msgid "Maximum Speed Z"
msgstr "Z 軸最大速度"

msgctxt "support_tower_maximum_supported_diameter label"
msgid "Maximum Tower-Supported Diameter"
msgstr "最大塔型支撐直徑"

msgctxt "meshfix_maximum_travel_resolution label"
msgid "Maximum Travel Resolution"
msgstr "最大空跑解析度"

msgctxt "machine_max_acceleration_x description"
msgid "Maximum acceleration for the motor of the X-direction"
msgstr "X 軸方向馬達的最大加速度"

msgctxt "machine_max_acceleration_y description"
msgid "Maximum acceleration for the motor of the Y-direction."
msgstr "Y 軸方向馬達的最大加速度。"

msgctxt "machine_max_acceleration_z description"
msgid "Maximum acceleration for the motor of the Z-direction."
msgstr "Z 軸方向馬達的最大加速度。"

msgctxt "machine_max_acceleration_e description"
msgid "Maximum acceleration for the motor of the filament."
msgstr "擠出馬達的最大加速度。"

msgctxt "bridge_sparse_infill_max_density description"
msgid "Maximum density of infill considered to be sparse. Skin over sparse infill is considered to be unsupported and so may be treated as a bridge skin."
msgstr "低於此密度的填充被視為稀疏填充。位於稀疏填充上的表層被視為沒有受到支撐，因此會被當作橋樑處理。"

msgctxt "support_tower_maximum_supported_diameter description"
msgid "Maximum diameter in the X/Y directions of a small area which is to be supported by a specialized support tower."
msgstr "塔型支撐使用的區域在 X/Y 方向的最大直徑。"

msgctxt "max_extrusion_before_wipe description"
msgid "Maximum material that can be extruded before another nozzle wipe is initiated. If this value is less than the volume of material required in a layer, the setting has no effect in this layer, i.e. it is limited to one wipe per layer."
msgstr "在另一次擦拭噴頭前可擠出的最大線材量。假如此值小於列印此層所需的線材量，則此設定對此層無效，也就是說，每層只會擦拭一次。"

msgctxt "multiple_mesh_overlap label"
msgid "Merged Meshes Overlap"
msgstr "合併網格重疊"

msgctxt "meshfix label"
msgid "Mesh Fixes"
msgstr "網格修復"

msgctxt "mesh_position_x label"
msgid "Mesh Position X"
msgstr "網格位置 X"

msgctxt "mesh_position_y label"
msgid "Mesh Position Y"
msgstr "網格位置 Y"

msgctxt "mesh_position_z label"
msgid "Mesh Position Z"
msgstr "網格位置 Z"

msgctxt "infill_mesh_order label"
msgid "Mesh Processing Rank"
msgstr "網格處理等級"

msgctxt "mesh_rotation_matrix label"
msgid "Mesh Rotation Matrix"
msgstr "網格旋轉矩陣"

msgctxt "slicing_tolerance option middle"
msgid "Middle"
msgstr "中間"

msgctxt "mold_width label"
msgid "Minimal Mold Width"
msgstr "最小模具寬度"

msgctxt "machine_min_cool_heat_time_window label"
msgid "Minimal Time Standby Temperature"
msgstr "待機溫度最短時間"

msgctxt "bridge_wall_min_length label"
msgid "Minimum Bridge Wall Length"
msgstr "最小橋樑牆壁長度"

msgctxt "min_even_wall_line_width label"
msgid "Minimum Even Wall Line Width"
msgstr ""

msgctxt "retraction_extrusion_window label"
msgid "Minimum Extrusion Distance Window"
msgstr "最小擠出距離範圍"

msgctxt "min_feature_size label"
msgid "Minimum Feature Size"
msgstr ""

msgctxt "machine_minimum_feedrate label"
msgid "Minimum Feedrate"
msgstr "最小進料速率"

msgctxt "support_tree_min_height_to_model label"
msgid "Minimum Height To Model"
msgstr ""

msgctxt "min_infill_area label"
msgid "Minimum Infill Area"
msgstr "最小填充面積"

msgctxt "cool_min_layer_time label"
msgid "Minimum Layer Time"
msgstr "最短單層列印時間"

msgctxt "min_odd_wall_line_width label"
msgid "Minimum Odd Wall Line Width"
msgstr ""

msgctxt "minimum_polygon_circumference label"
msgid "Minimum Polygon Circumference"
msgstr "最小多邊形周長"

msgctxt "min_skin_width_for_expansion label"
msgid "Minimum Skin Width for Expansion"
msgstr "最小延伸表層寬度"

msgctxt "cool_min_speed label"
msgid "Minimum Speed"
msgstr "最低列印速度"

msgctxt "minimum_support_area label"
msgid "Minimum Support Area"
msgstr "最小支撐面積"

msgctxt "minimum_bottom_area label"
msgid "Minimum Support Floor Area"
msgstr "最小支撐底板面積"

msgctxt "minimum_interface_area label"
msgid "Minimum Support Interface Area"
msgstr "最小支撐介面面積"

msgctxt "minimum_roof_area label"
msgid "Minimum Support Roof Area"
msgstr "最小支撐頂板面積"

msgctxt "support_xy_distance_overhang label"
msgid "Minimum Support X/Y Distance"
msgstr "最小支撐 X/Y 間距"

msgctxt "min_bead_width label"
msgid "Minimum Thin Wall Line Width"
msgstr ""

msgctxt "coasting_min_volume label"
msgid "Minimum Volume Before Coasting"
msgstr "滑行前最小體積"

msgctxt "min_wall_line_width label"
msgid "Minimum Wall Line Width"
msgstr ""

msgctxt "minimum_interface_area description"
msgid "Minimum area size for support interface polygons. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "支撐介面區域的最小面積大小。面積小於此值的區域將列印一般支撐。"

msgctxt "minimum_support_area description"
msgid "Minimum area size for support polygons. Polygons which have an area smaller than this value will not be generated."
msgstr "支撐區域的最小面積大小。面積小於此值的區域將不會產生支撐。"

msgctxt "minimum_bottom_area description"
msgid "Minimum area size for the floors of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "支撐底板區域的最小面積大小。面積小於此值的區域將列印一般支撐。"

msgctxt "minimum_roof_area description"
msgid "Minimum area size for the roofs of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "支撐頂板區域的最小面積大小。面積小於此值的區域將列印一般支撐。"

msgctxt "min_feature_size description"
msgid "Minimum thickness of thin features. Model features that are thinner than this value will not be printed, while features thicker than the Minimum Feature Size will be widened to the Minimum Wall Line Width."
msgstr ""

msgctxt "support_conical_min_width description"
msgid "Minimum width to which the base of the conical support area is reduced. Small widths can lead to unstable support structures."
msgstr "錐形支撐區域底部的最小寬度。寬度較小可能導致不穩定的支撐結構。"

msgctxt "mold_enabled label"
msgid "Mold"
msgstr "模具"

msgctxt "mold_angle label"
msgid "Mold Angle"
msgstr "模具角度"

msgctxt "mold_roof_height label"
msgid "Mold Roof Height"
msgstr "模具頂板高度"

msgctxt "ironing_monotonic label"
msgid "Monotonic Ironing Order"
msgstr "單一化燙平順序"

msgctxt "roofing_monotonic label"
msgid "Monotonic Top Surface Order"
msgstr "頂層表面單一化列印順序"

msgctxt "skin_monotonic label"
msgid "Monotonic Top/Bottom Order"
msgstr "單一化列印 頂層/底層 順序"

msgctxt "skirt_line_count description"
msgid "Multiple skirt lines help to prime your extrusion better for small models. Setting this to 0 will disable the skirt."
msgstr "多條外圍線條有助你在列印小型模型時，更好地裝填的擠出機組。將其設為 0 將關閉外圍。"

msgctxt "initial_layer_line_width_factor description"
msgid "Multiplier of the line width on the first layer. Increasing this could improve bed adhesion."
msgstr "第一層線寬倍數。增大此倍數可改善熱床附著。"

msgctxt "material_no_load_move_factor label"
msgid "No Load Move Factor"
msgstr "空載移動係數"

msgctxt "skin_no_small_gaps_heuristic label"
msgid "No Skin in Z Gaps"
msgstr "Z 間隙無表層"

msgctxt "blackmagic description"
msgid "Non-traditional ways to print your models."
msgstr "以非傳統的方式列印你的模型。"

msgctxt "adhesion_type option none"
msgid "None"
msgstr "無"

msgctxt "z_seam_corner option z_seam_corner_none"
msgid "None"
msgstr "無"

msgctxt "magic_mesh_surface_mode option normal"
msgid "Normal"
msgstr "正常"

msgctxt "support_structure option normal"
msgid "Normal"
msgstr "正常"

msgctxt "meshfix_keep_open_polygons description"
msgid "Normally Cura tries to stitch up small holes in the mesh and remove parts of a layer with big holes. Enabling this option keeps those parts which cannot be stitched. This option should be used as a last resort option when everything else fails to produce proper g-code."
msgstr "通常 Cura 會嘗試縫合網格中的小孔，並移除大的孔洞部分。啟用此選項可保留那些無法縫合的部分。此選項應該做為其他方法都無法產生適當 g-code 時的最後選擇。"

msgctxt "retraction_combing option noskin"
msgid "Not in Skin"
msgstr "表層以外區域"

msgctxt "retraction_combing option no_outer_surfaces"
msgid "Not on Outer Surface"
msgstr "不在外表面上"

msgctxt "machine_nozzle_expansion_angle label"
msgid "Nozzle Angle"
msgstr "噴頭角度"

msgctxt "machine_nozzle_size label"
msgid "Nozzle Diameter"
msgstr "噴頭直徑"

msgctxt "nozzle_disallowed_areas label"
msgid "Nozzle Disallowed Areas"
msgstr "噴頭禁入區域"

msgctxt "machine_nozzle_id label"
msgid "Nozzle ID"
msgstr "噴頭 ID"

msgctxt "machine_nozzle_head_distance label"
msgid "Nozzle Length"
msgstr "噴頭長度"

msgctxt "switch_extruder_extra_prime_amount label"
msgid "Nozzle Switch Extra Prime Amount"
msgstr "噴頭切換額外裝填量"

msgctxt "switch_extruder_prime_speed label"
msgid "Nozzle Switch Prime Speed"
msgstr "噴頭切換裝填速度"

msgctxt "switch_extruder_retraction_speed label"
msgid "Nozzle Switch Retract Speed"
msgstr "噴頭切換回抽速度"

msgctxt "switch_extruder_retraction_amount label"
msgid "Nozzle Switch Retraction Distance"
msgstr "噴頭切換回抽距離"

msgctxt "switch_extruder_retraction_speeds label"
msgid "Nozzle Switch Retraction Speed"
msgstr "噴頭切換回抽速度"

msgctxt "machine_extruder_count label"
msgid "Number of Extruders"
msgstr "擠出機數目"

msgctxt "extruders_enabled_count label"
msgid "Number of Extruders That Are Enabled"
msgstr "已啟用擠出機的數量"

msgctxt "speed_slowdown_layers label"
msgid "Number of Slower Layers"
msgstr "慢速列印層數"

msgctxt "extruders_enabled_count description"
msgid "Number of extruder trains that are enabled; automatically set in software"
msgstr "啟用擠出機的數量；軟體自動設定"

msgctxt "machine_extruder_count description"
msgid "Number of extruder trains. An extruder train is the combination of a feeder, bowden tube, and nozzle."
msgstr "擠出機組數目。擠出機組是指進料裝置、喉管和噴頭的組合。"

msgctxt "wipe_repeat_count description"
msgid "Number of times to move the nozzle across the brush."
msgstr "將噴頭移動經過刷子的次數。"

msgctxt "gradual_infill_steps description"
msgid "Number of times to reduce the infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Infill Density."
msgstr "由模型頂部往下，填充密度減半的次數。愈接近頂部的填充密度愈高，直到所設定的填充密度。"

msgctxt "gradual_support_infill_steps description"
msgid "Number of times to reduce the support infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Support Infill Density."
msgstr "從支撐頂層往下，填充密度減半的次數。越靠近頂層的填充密度越高，最高密度為支撐填充密度。"

msgctxt "infill_pattern option tetrahedral"
msgid "Octet"
msgstr "八面體"

msgctxt "retraction_combing option off"
msgid "Off"
msgstr "關"

msgctxt "mesh_position_x description"
msgid "Offset applied to the object in the x direction."
msgstr "套用在模型 x 方向上的偏移量。"

msgctxt "mesh_position_y description"
msgid "Offset applied to the object in the y direction."
msgstr "套用在模型 y 方向上的偏移量。"

msgctxt "mesh_position_z description"
msgid "Offset applied to the object in the z direction. With this you can perform what was used to be called 'Object Sink'."
msgstr "套用在模型 z 方向上的偏移量。利用此選項，你可以執行過去被稱為“模型沉降”的操作。"

msgctxt "machine_use_extruder_offset_to_offset_coords label"
msgid "Offset with Extruder"
msgstr "擠出機偏移量"

msgctxt "support_tree_rest_preference option buildplate"
msgid "On buildplate when possible"
msgstr ""

msgctxt "support_tree_rest_preference option graceful"
msgid "On model if required"
msgstr ""

msgctxt "print_sequence option one_at_a_time"
msgid "One at a Time"
msgstr "排隊列印"

msgctxt "retraction_hop_only_when_collides description"
msgid "Only perform a Z Hop when moving over printed parts which cannot be avoided by horizontal motion by Avoid Printed Parts when Traveling."
msgstr "僅在移動到無法通過“空跑時避開已列印部分”選項避開的已列印部分上方時執行 Z 抬升。"

msgctxt "ironing_only_highest_layer description"
msgid "Only perform ironing on the very last layer of the mesh. This saves time if the lower layers don't need a smooth surface finish."
msgstr "只在網格的最後一層進行燙平處理。 如果下層不需要光滑的表面，可啟用此選項以節省時間。"

msgctxt "brim_outside_only description"
msgid "Only print the brim on the outside of the model. This reduces the amount of brim you need to remove afterwards, while it doesn't reduce the bed adhesion that much."
msgstr "僅在模型外部列印邊緣。這會減少你之後需要移除的邊緣量，而不會過度影響列印平台附著。"

msgctxt "ooze_shield_angle label"
msgid "Ooze Shield Angle"
msgstr "擦拭牆角度"

msgctxt "ooze_shield_dist label"
msgid "Ooze Shield Distance"
msgstr "擦拭牆距離"

msgctxt "support_tree_branch_reach_limit label"
msgid "Optimal Branch Range"
msgstr ""

msgctxt "optimize_wall_printing_order label"
msgid "Optimize Wall Printing Order"
msgstr "最佳化牆壁列印順序"

msgctxt "optimize_wall_printing_order description"
msgid "Optimize the order in which walls are printed so as to reduce the number of retractions and the distance travelled. Most parts will benefit from this being enabled but some may actually take longer so please compare the print time estimates with and without optimization. First layer is not optimized when choosing brim as build plate adhesion type."
msgstr "最佳化列印牆壁的順序，以減少縮回次數和行進距離。啟用此功能對大多數的零件是有益的，但有些零件可能反而會更花時間，因此請比較列印時間的估計值評估是否進行最佳化。當列印平台附著類型設定為邊緣時，第一層不會進行最佳化。"

msgctxt "machine_nozzle_tip_outer_diameter label"
msgid "Outer Nozzle Diameter"
msgstr "噴頭外徑"

msgctxt "acceleration_wall_0 label"
msgid "Outer Wall Acceleration"
msgstr "外壁加速度"

msgctxt "wall_0_extruder_nr label"
msgid "Outer Wall Extruder"
msgstr "外壁擠出機"

msgctxt "wall_0_material_flow label"
msgid "Outer Wall Flow"
msgstr "外壁流量"

msgctxt "wall_0_inset label"
msgid "Outer Wall Inset"
msgstr "外壁內嵌"

msgctxt "jerk_wall_0 label"
msgid "Outer Wall Jerk"
msgstr "外壁加加速度"

msgctxt "wall_line_width_0 label"
msgid "Outer Wall Line Width"
msgstr "線寬（外壁）"

msgctxt "speed_wall_0 label"
msgid "Outer Wall Speed"
msgstr "外壁速度"

msgctxt "wall_0_wipe_dist label"
msgid "Outer Wall Wipe Distance"
msgstr "外壁擦拭噴頭長度"

msgctxt "group_outer_walls description"
msgid "Outer walls of different islands in the same layer are printed in sequence. When enabled the amount of flow changes is limited because walls are printed one type at a time, when disabled the number of travels between islands is reduced because walls in the same islands are grouped."
msgstr ""

msgctxt "inset_direction option outside_in"
msgid "Outside To Inside"
msgstr ""

msgctxt "wall_overhang_angle label"
msgid "Overhanging Wall Angle"
msgstr "突出牆壁角度"

msgctxt "wall_overhang_speed_factor label"
msgid "Overhanging Wall Speed"
msgstr "突出牆壁速度"

msgctxt "wall_overhang_speed_factor description"
msgid "Overhanging walls will be printed at this percentage of their normal print speed."
msgstr "突出牆壁將會以正常速度的此百分比值列印。"

msgctxt "wipe_pause description"
msgid "Pause after the unretract."
msgstr "若無回抽，擦拭後暫停。"

msgctxt "bridge_fan_speed description"
msgid "Percentage fan speed to use when printing bridge walls and skin."
msgstr "列印橋樑牆壁和表層時，風扇轉速的百分比。"

msgctxt "bridge_fan_speed_2 description"
msgid "Percentage fan speed to use when printing the second bridge skin layer."
msgstr "列印橋樑表層第二層時，風扇轉速的百分比。"

msgctxt "support_supported_skin_fan_speed description"
msgid "Percentage fan speed to use when printing the skin regions immediately above the support. Using a high fan speed can make the support easier to remove."
msgstr "在支撐上方列印表層區域時使用的風扇轉速百分比。使用高風扇轉速可以使支撐更容易移除。"

msgctxt "bridge_fan_speed_3 description"
msgid "Percentage fan speed to use when printing the third bridge skin layer."
msgstr "列印橋樑表層第三層時，風扇轉速的百分比。"

msgctxt "minimum_polygon_circumference description"
msgid "Polygons in sliced layers that have a circumference smaller than this amount will be filtered out. Lower values lead to higher resolution mesh at the cost of slicing time. It is meant mostly for high resolution SLA printers and very tiny 3D models with a lot of details."
msgstr "切片層中周長小於此值的多邊形將被過濾掉。設定較低的值會花費較多的切片時間，以獲得較高解析度的網格。它主要用於高解析度的 SLA 印表機和具有大量細節的微小 3D 模型。"

msgctxt "support_tree_angle_slow label"
msgid "Preferred Branch Angle"
msgstr ""

msgctxt "wall_transition_filter_deviation description"
msgid "Prevent transitioning back and forth between one extra wall and one less. This margin extends the range of line widths which follow to [Minimum Wall Line Width - Margin, 2 * Minimum Wall Line Width + Margin]. Increasing this margin reduces the number of transitions, which reduces the number of extrusion starts/stops and travel time. However, large line width variation can lead to under- or overextrusion problems."
msgstr ""

msgctxt "acceleration_prime_tower label"
msgid "Prime Tower Acceleration"
msgstr "換料塔加速度"

msgctxt "prime_tower_brim_enable label"
msgid "Prime Tower Base"
msgstr ""

msgctxt "prime_tower_base_height label"
msgid "Prime Tower Base Height"
msgstr ""

msgctxt "prime_tower_base_size label"
msgid "Prime Tower Base Size"
msgstr ""

msgctxt "prime_tower_base_curve_magnitude label"
msgid "Prime Tower Base Slope"
msgstr ""

msgctxt "prime_tower_flow label"
msgid "Prime Tower Flow"
msgstr "換料塔流量"

msgctxt "jerk_prime_tower label"
msgid "Prime Tower Jerk"
msgstr "換料塔加加速度"

msgctxt "prime_tower_line_width label"
msgid "Prime Tower Line Width"
msgstr "換料塔線寬"

msgctxt "prime_tower_min_volume label"
msgid "Prime Tower Minimum Volume"
msgstr "換料塔最小體積"

msgctxt "prime_tower_raft_base_line_spacing label"
msgid "Prime Tower Raft Line Spacing"
msgstr ""

msgctxt "prime_tower_size label"
msgid "Prime Tower Size"
msgstr "換料塔尺寸"

msgctxt "speed_prime_tower label"
msgid "Prime Tower Speed"
msgstr "換料塔速度"

msgctxt "prime_tower_position_x label"
msgid "Prime Tower X Position"
msgstr "換料塔 X 位置"

msgctxt "prime_tower_position_y label"
msgid "Prime Tower Y Position"
msgstr "換料塔 Y 位置"

msgctxt "acceleration_print label"
msgid "Print Acceleration"
msgstr "列印加速度"

msgctxt "jerk_print label"
msgid "Print Jerk"
msgstr "列印加加速度"

msgctxt "print_sequence label"
msgid "Print Sequence"
msgstr "列印順序"

msgctxt "speed_print label"
msgid "Print Speed"
msgstr "列印速度"

msgctxt "fill_outline_gaps label"
msgid "Print Thin Walls"
msgstr "列印薄壁"

msgctxt "prime_tower_enable description"
msgid "Print a tower next to the print which serves to prime the material after each nozzle switch."
msgstr "在列印件旁邊印一個塔，用在每次切換噴頭後填充線材。"

msgctxt "infill_support_enabled description"
msgid "Print infill structures only where tops of the model should be supported. Enabling this reduces print time and material usage, but leads to ununiform object strength."
msgstr "只在模型頂部需要支撐的地方才列印填充。啟用此功能可減少列印時間和線材用量，但會導致物件強度不均勻。"

msgctxt "ironing_monotonic description"
msgid "Print ironing lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "再使用燙平列印線條時，命令相鄰線條於單方向重疊。雖然會花更多時間列印，但會使平面更平整."

msgctxt "mold_enabled description"
msgid "Print models as a mold, which can be cast in order to get a model which resembles the models on the build plate."
msgstr "將模型作為模具列印，可進行鑄造，以便獲取與列印平台上的模型類似的模型。"

msgctxt "fill_outline_gaps description"
msgid "Print pieces of the model which are horizontally thinner than the nozzle size."
msgstr "列印在水平面上比噴頭尺寸更薄的模型部件。"

msgctxt "bridge_skin_speed_2 description"
msgid "Print speed to use when printing the second bridge skin layer."
msgstr "列印橋樑表層區域第二層時的速度。"

msgctxt "bridge_skin_speed_3 description"
msgid "Print speed to use when printing the third bridge skin layer."
msgstr "列印橋樑表層區域第三層時的速度。"

msgctxt "infill_before_walls description"
msgid "Print the infill before printing the walls. Printing the walls first may lead to more accurate walls, but overhangs print worse. Printing the infill first leads to sturdier walls, but the infill pattern might sometimes show through the surface."
msgstr "列印牆壁前先列印填充。先列印牆壁可以產生更精確的牆壁，但突出部分列印品質會較差。先列印填充會產生更牢固的牆壁，但有時候填充的列印樣式會透過表面顯現出來。"

msgctxt "roofing_monotonic description"
msgid "Print top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "再列印頂層表面線條時，命令相鄰線條於單方向重疊. 雖然會花更多時間列印，但會使平面更平整."

msgctxt "skin_monotonic description"
msgid "Print top/bottom lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "再列印頂層/底層線條時，命令相鄰線條於單方向重疊. 雖然會花更多時間列印，但會使平面更平整."

msgctxt "material_print_temperature label"
msgid "Printing Temperature"
msgstr "列印溫度"

msgctxt "material_print_temperature_layer_0 label"
msgid "Printing Temperature Initial Layer"
msgstr "列印溫度起始層"

msgctxt "skirt_height description"
msgid "Printing the innermost skirt line with multiple layers makes it easy to remove the skirt."
msgstr ""

msgctxt "alternate_extra_perimeter description"
msgid "Prints an extra wall at every other layer. This way infill gets caught between these extra walls, resulting in stronger prints."
msgstr "每兩層建立一個額外牆壁，這些額外的牆壁能更緊密地抓填充部分，產生較強壯的模型。"

msgctxt "resolution label"
msgid "Quality"
msgstr "品質"

msgctxt "infill_pattern option quarter_cubic"
msgid "Quarter Cubic"
msgstr "四分立方體"

msgctxt "adhesion_type option raft"
msgid "Raft"
msgstr "木筏"

msgctxt "raft_airgap label"
msgid "Raft Air Gap"
msgstr "木筏間隙"

msgctxt "raft_base_extruder_nr label"
msgid "Raft Base Extruder"
msgstr ""

msgctxt "raft_base_fan_speed label"
msgid "Raft Base Fan Speed"
msgstr "木筏底部風扇轉速"

msgctxt "raft_base_line_spacing label"
msgid "Raft Base Line Spacing"
msgstr "木筏底部間距"

msgctxt "raft_base_line_width label"
msgid "Raft Base Line Width"
msgstr "木筏底部線寬"

msgctxt "raft_base_acceleration label"
msgid "Raft Base Print Acceleration"
msgstr "木筏底部列印加速度"

msgctxt "raft_base_jerk label"
msgid "Raft Base Print Jerk"
msgstr "木筏底部列印加加速度"

msgctxt "raft_base_speed label"
msgid "Raft Base Print Speed"
msgstr "木筏底部列印速度"

msgctxt "raft_base_thickness label"
msgid "Raft Base Thickness"
msgstr "木筏底部厚度"

msgctxt "raft_base_wall_count label"
msgid "Raft Base Wall Count"
msgstr ""

msgctxt "raft_margin label"
msgid "Raft Extra Margin"
msgstr "木筏額外邊緣"

msgctxt "raft_fan_speed label"
msgid "Raft Fan Speed"
msgstr "木筏風扇轉速"

msgctxt "raft_interface_extruder_nr label"
msgid "Raft Middle Extruder"
msgstr ""

msgctxt "raft_interface_fan_speed label"
msgid "Raft Middle Fan Speed"
msgstr "木筏中層風扇轉速"

msgctxt "raft_interface_layers label"
msgid "Raft Middle Layers"
msgstr ""

msgctxt "raft_interface_line_width label"
msgid "Raft Middle Line Width"
msgstr "木筏中層線寬"

msgctxt "raft_interface_acceleration label"
msgid "Raft Middle Print Acceleration"
msgstr "木筏中層列印加速度"

msgctxt "raft_interface_jerk label"
msgid "Raft Middle Print Jerk"
msgstr "木筏中層列印加加速度"

msgctxt "raft_interface_speed label"
msgid "Raft Middle Print Speed"
msgstr "木筏中層列印速度"

msgctxt "raft_interface_line_spacing label"
msgid "Raft Middle Spacing"
msgstr "木筏中層間距"

msgctxt "raft_interface_thickness label"
msgid "Raft Middle Thickness"
msgstr "木筏中層厚度"

msgctxt "raft_acceleration label"
msgid "Raft Print Acceleration"
msgstr "木筏列印加速度"

msgctxt "raft_jerk label"
msgid "Raft Print Jerk"
msgstr "木筏列印加加速度"

msgctxt "raft_speed label"
msgid "Raft Print Speed"
msgstr "木筏列印速度"

msgctxt "raft_smoothing label"
msgid "Raft Smoothing"
msgstr "木筏平滑處理"

msgctxt "raft_surface_extruder_nr label"
msgid "Raft Top Extruder"
msgstr ""

msgctxt "raft_surface_fan_speed label"
msgid "Raft Top Fan Speed"
msgstr "木筏頂部風扇轉速"

msgctxt "raft_surface_thickness label"
msgid "Raft Top Layer Thickness"
msgstr "木筏頂部層厚"

msgctxt "raft_surface_layers label"
msgid "Raft Top Layers"
msgstr "木筏頂部層數"

msgctxt "raft_surface_line_width label"
msgid "Raft Top Line Width"
msgstr "木筏頂部線寬"

msgctxt "raft_surface_acceleration label"
msgid "Raft Top Print Acceleration"
msgstr "木筏頂部列印加速度"

msgctxt "raft_surface_jerk label"
msgid "Raft Top Print Jerk"
msgstr "木筏頂部列印加加速度"

msgctxt "raft_surface_speed label"
msgid "Raft Top Print Speed"
msgstr "木筏頂部列印速度"

msgctxt "raft_surface_line_spacing label"
msgid "Raft Top Spacing"
msgstr "木筏頂部間距"

msgctxt "z_seam_type option random"
msgid "Random"
msgstr "隨機"

msgctxt "infill_randomize_start_location label"
msgid "Randomize Infill Start"
msgstr "隨機填充起始位置"

msgctxt "infill_randomize_start_location description"
msgid "Randomize which infill line is printed first. This prevents one segment becoming the strongest, but it does so at the cost of an additional travel move."
msgstr "隨機選擇第一條填充線列印。 這可以防止強度集中在某一個部分，但會花費額外的空跑。"

msgctxt "magic_fuzzy_skin_enabled description"
msgid "Randomly jitter while printing the outer wall, so that the surface has a rough and fuzzy look."
msgstr "在列印外牆時隨機抖動，使表面具有粗糙和毛絨絨的外觀。"

msgctxt "machine_shape option rectangular"
msgid "Rectangular"
msgstr "矩形"

msgctxt "cool_fan_speed_min label"
msgid "Regular Fan Speed"
msgstr "標準風扇轉速"

msgctxt "cool_fan_full_at_height label"
msgid "Regular Fan Speed at Height"
msgstr "標準風扇轉速（高度）"

msgctxt "cool_fan_full_layer label"
msgid "Regular Fan Speed at Layer"
msgstr "標準風扇轉速（層）"

msgctxt "cool_min_layer_time_fan_speed_max label"
msgid "Regular/Maximum Fan Speed Threshold"
msgstr "標準風扇轉速門檻值"

msgctxt "relative_extrusion label"
msgid "Relative Extrusion"
msgstr "相對模式擠出"

msgctxt "meshfix_union_all_remove_holes label"
msgid "Remove All Holes"
msgstr "移除所有孔洞"

msgctxt "remove_empty_first_layers label"
msgid "Remove Empty First Layers"
msgstr "移除空的第一層"

msgctxt "carve_multiple_volumes label"
msgid "Remove Mesh Intersection"
msgstr "刪除網格交集部分"

msgctxt "raft_remove_inside_corners label"
msgid "Remove Raft Inside Corners"
msgstr ""

msgctxt "carve_multiple_volumes description"
msgid "Remove areas where multiple meshes are overlapping with each other. This may be used if merged dual material objects overlap with each other."
msgstr "刪除多個網格彼此重疊的區域。如果合併的雙重線材對象彼此重疊，則可以使用此選項。"

msgctxt "remove_empty_first_layers description"
msgid "Remove empty layers beneath the first printed layer if they are present. Disabling this setting can cause empty first layers if the Slicing Tolerance setting is set to Exclusive or Middle."
msgstr "如果可列印的第一層下方有空的層，將其移除。假如「切片公差」設定為「排除」或「中間」，關閉此設定可能會導致空的第一層。"

msgctxt "raft_remove_inside_corners description"
msgid "Remove inside corners from the raft, causing the raft to become convex."
msgstr ""

msgctxt "meshfix_union_all_remove_holes description"
msgid "Remove the holes in each layer and keep only the outside shape. This will ignore any invisible internal geometry. However, it also ignores layer holes which can be viewed from above or below."
msgstr "移除每層的孔洞，僅保留外部形狀。這會忽略任何不可見的內部幾何。但是，也會忽略可從上方或下方看到的層孔洞。"

msgctxt "machine_gcode_flavor option RepRap (RepRap)"
msgid "RepRap"
msgstr "RepRap"

msgctxt "machine_gcode_flavor option Repetier"
msgid "Repetier"
msgstr "Repetier"

msgctxt "skin_outline_count description"
msgid "Replaces the outermost part of the top/bottom pattern with a number of concentric lines. Using one or two lines improves roofs that start on infill material."
msgstr "用多個同心線代替頂部/底部列印樣式的最外面部分。使用一條或兩條線可以改善列印在填充上的頂板。"

msgctxt "support_tree_rest_preference label"
msgid "Rest Preference"
msgstr ""

msgctxt "travel_retract_before_outer_wall label"
msgid "Retract Before Outer Wall"
msgstr "列印外壁前先進行回抽"

msgctxt "retract_at_layer_change label"
msgid "Retract at Layer Change"
msgstr "列印下一層時回抽"

msgctxt "retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "當噴頭移動經過非列印區域時回抽線材。"

msgctxt "wipe_retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "當噴頭移動經過非列印區域時回抽線材。"

msgctxt "retract_at_layer_change description"
msgid "Retract the filament when the nozzle is moving to the next layer."
msgstr "當噴頭移動到下一層時回抽線材。"

msgctxt "retraction_amount label"
msgid "Retraction Distance"
msgstr "回抽距離"

msgctxt "retraction_extra_prime_amount label"
msgid "Retraction Extra Prime Amount"
msgstr "回抽額外裝填量"

msgctxt "retraction_min_travel label"
msgid "Retraction Minimum Travel"
msgstr "回抽最小空跑距離"

msgctxt "retraction_prime_speed label"
msgid "Retraction Prime Speed"
msgstr "回抽裝填速度"

msgctxt "retraction_retract_speed label"
msgid "Retraction Retract Speed"
msgstr "回抽速度"

msgctxt "retraction_speed label"
msgid "Retraction Speed"
msgstr "回抽速度"

msgctxt "z_seam_position option right"
msgid "Right"
msgstr "右方"

msgctxt "machine_scale_fan_speed_zero_to_one label"
msgid "Scale Fan Speed To 0-1"
msgstr ""

msgctxt "machine_scale_fan_speed_zero_to_one description"
msgid "Scale the fan speed to be between 0 and 1 instead of between 0 and 256."
msgstr ""

msgctxt "material_shrinkage_percentage label"
msgid "Scaling Factor Shrinkage Compensation"
msgstr "收縮補償放大倍率"

msgctxt "support_meshes_present label"
msgid "Scene Has Support Meshes"
msgstr "場景具有支撐網格"

msgctxt "z_seam_corner label"
msgid "Seam Corner Preference"
msgstr "接縫偏好設定"

msgctxt "draft_shield_height_limitation description"
msgid "Set the height of the draft shield. Choose to print the draft shield at the full height of the model or at a limited height."
msgstr "設定防風罩的高度。選擇防風罩與模型同高或只列印到限制的高度。"

msgctxt "dual description"
msgid "Settings used for printing with multiple extruders."
msgstr "用於多擠出機情況下的設定。"

msgctxt "command_line_settings description"
msgid "Settings which are only used if CuraEngine isn't called from the Cura frontend."
msgstr "未從 Cura 前端調用 CuraEngine 時使用的設定。"

msgctxt "machine_extruders_shared_nozzle_initial_retraction label"
msgid "Shared Nozzle Initial Retraction"
msgstr "共用噴頭初始回抽"

msgctxt "z_seam_type option sharpest_corner"
msgid "Sharpest Corner"
msgstr "最尖銳的轉角"

msgctxt "shell description"
msgid "Shell"
msgstr "外殼"

msgctxt "z_seam_type option shortest"
msgid "Shortest"
msgstr "最短"

msgctxt "machine_show_variants label"
msgid "Show Machine Variants"
msgstr "顯示印表機型號"

msgctxt "skin_edge_support_layers label"
msgid "Skin Edge Support Layers"
msgstr "表層邊緣支撐層數"

msgctxt "skin_edge_support_thickness label"
msgid "Skin Edge Support Thickness"
msgstr "表層邊緣支撐厚度"

msgctxt "expand_skins_expand_distance label"
msgid "Skin Expand Distance"
msgstr "表層延伸距離"

msgctxt "skin_overlap_mm label"
msgid "Skin Overlap"
msgstr "表層重疊"

msgctxt "skin_overlap label"
msgid "Skin Overlap Percentage"
msgstr "表層重疊百分比"

msgctxt "skin_preshrink label"
msgid "Skin Removal Width"
msgstr "表層移除寬度"

msgctxt "min_skin_width_for_expansion description"
msgid "Skin areas narrower than this are not expanded. This avoids expanding the narrow skin areas that are created when the model surface has a slope close to the vertical."
msgstr "如果表層區域寬度小於此值，則不會延伸。這會避免延伸在模型表面的斜度接近垂直時所形成的狹窄表層區域。"

msgctxt "support_zag_skip_count description"
msgid "Skip one in every N connection lines to make the support structure easier to break away."
msgstr "每隔 N 個連接線省略一次，讓支撐結構更容易拆除。"

msgctxt "support_skip_some_zags description"
msgid "Skip some support line connections to make the support structure easier to break away. This setting is applicable to the Zig Zag support infill pattern."
msgstr "省略支撐的部分連接線，讓支撐結構更容易拆除。此設定適用於鋸齒狀的支撐樣式。"

msgctxt "adhesion_type option skirt"
msgid "Skirt"
msgstr "外圍"

msgctxt "skirt_gap label"
msgid "Skirt Distance"
msgstr "外圍間距"

msgctxt "skirt_height label"
msgid "Skirt Height"
msgstr ""

msgctxt "skirt_line_count label"
msgid "Skirt Line Count"
msgstr "外圍線條數量"

msgctxt "acceleration_skirt_brim label"
msgid "Skirt/Brim Acceleration"
msgstr "外圍/邊緣加速度"

msgctxt "skirt_brim_extruder_nr label"
msgid "Skirt/Brim Extruder"
msgstr ""

msgctxt "skirt_brim_material_flow label"
msgid "Skirt/Brim Flow"
msgstr "外圍/邊緣流量"

msgctxt "jerk_skirt_brim label"
msgid "Skirt/Brim Jerk"
msgstr "外圍/邊緣加加速度"

msgctxt "skirt_brim_line_width label"
msgid "Skirt/Brim Line Width"
msgstr "外圍/邊緣線寬"

msgctxt "skirt_brim_minimal_length label"
msgid "Skirt/Brim Minimum Length"
msgstr "外圍/邊緣最小長度"

msgctxt "skirt_brim_speed label"
msgid "Skirt/Brim Speed"
msgstr "外圍/邊緣速度"

msgctxt "slicing_tolerance label"
msgid "Slicing Tolerance"
msgstr "切片公差"

msgctxt "small_feature_speed_factor_0 label"
msgid "Small Feature Initial Layer Speed"
msgstr "細部模式起始層速度"

msgctxt "small_feature_max_length label"
msgid "Small Feature Max Length"
msgstr "細部模式最大長度"

msgctxt "small_feature_speed_factor label"
msgid "Small Feature Speed"
msgstr "細部模式速度"

msgctxt "small_hole_max_size label"
msgid "Small Hole Max Size"
msgstr "細部模式最大直徑"

msgctxt "cool_min_temperature label"
msgid "Small Layer Printing Temperature"
msgstr "最終列印溫度"

msgctxt "small_skin_on_surface label"
msgid "Small Top/Bottom On Surface"
msgstr ""

msgctxt "small_skin_width label"
msgid "Small Top/Bottom Width"
msgstr ""

msgctxt "small_feature_speed_factor_0 description"
msgid "Small features on the first layer will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "第一層的細部模式將以正常列印速度的此百分比值列印。 較慢的列印有助於黏合和精度。"

msgctxt "small_feature_speed_factor description"
msgid "Small features will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "細部模式將以正常列印速度的此百分比值列印。 較慢的列印有助於黏合和精度。"

msgctxt "small_skin_width description"
msgid "Small top/bottom regions are filled with walls instead of the default top/bottom pattern. This helps to avoids jerky motions. Off for the topmost (air-exposed) layer by default (see 'Small Top/Bottom On Surface')."
msgstr ""

msgctxt "brim_smart_ordering label"
msgid "Smart Brim"
msgstr ""

msgctxt "z_seam_corner option z_seam_corner_weighted"
msgid "Smart Hiding"
msgstr "智慧隱藏"

msgctxt "smooth_spiralized_contours label"
msgid "Smooth Spiralized Contours"
msgstr "平滑螺旋輪廓"

msgctxt "smooth_spiralized_contours description"
msgid "Smooth the spiralized contours to reduce the visibility of the Z seam (the Z seam should be barely visible on the print but will still be visible in the layer view). Note that smoothing will tend to blur fine surface details."
msgstr "平滑螺旋輪廓可以減少 Z 縫的出現（Z 縫應在列印品上幾乎看不到，但在分層檢視中仍然可見）。請注意，平滑操作將傾向於模糊精細的表面細節。"

msgctxt "retraction_extra_prime_amount description"
msgid "Some material can ooze away during a travel move, which can be compensated for here."
msgstr "有些線材可能會在空跑過程中滲出，可以在這裡對其進行補償。"

msgctxt "wipe_retraction_extra_prime_amount description"
msgid "Some material can ooze away during a wipe travel moves, which can be compensated for here."
msgstr "有些線材可能會在擦拭過程中滲出，可以在這裡對其進行補償。"

msgctxt "blackmagic label"
msgid "Special Modes"
msgstr "特殊模式"

msgctxt "speed description"
msgid "Speed"
msgstr "速度"

msgctxt "speed label"
msgid "Speed"
msgstr "速度"

msgctxt "wipe_hop_speed description"
msgid "Speed to move the z-axis during the hop."
msgstr "抬升時移動 Z 軸的速度。"

msgctxt "magic_spiralize label"
msgid "Spiralize Outer Contour"
msgstr "螺旋列印外輪廓"

msgctxt "magic_spiralize description"
msgid "Spiralize smooths out the Z move of the outer edge. This will create a steady Z increase over the whole print. This feature turns a solid model into a single walled print with a solid bottom. This feature should only be enabled when each layer only contains a single part."
msgstr "螺旋列印實現外部邊緣的平滑 Z 移動。這會在整個列印上改成 Z 軸穩定增動。該功能會將一個實心模型轉變為具有實體底部的單壁列印。只有在當每一層只包含一個封閉面時才應啟用此功能。"

msgctxt "material_standby_temperature label"
msgid "Standby Temperature"
msgstr "待機溫度"

msgctxt "machine_start_gcode label"
msgid "Start G-code"
msgstr "起始 G-code"

msgctxt "z_seam_type description"
msgid "Starting point of each path in a layer. When paths in consecutive layers start at the same point a vertical seam may show on the print. When aligning these near a user specified location, the seam is easiest to remove. When placed randomly the inaccuracies at the paths' start will be less noticeable. When taking the shortest path the print will be quicker."
msgstr "一層中每條路徑的起點。當連續多層的路徑從相同點開始時，則列印物上會顯示一條垂直縫隙。如果將這些路徑靠近一個使用者指定的位置對齊，則縫隙最容易移除。如果隨機放置，則路徑起點的不精準度將較不明顯。採用最短的路徑時，列印將更為快速。"

msgctxt "machine_steps_per_mm_e label"
msgid "Steps per Millimeter (E)"
msgstr "每毫米的步數（E）"

msgctxt "machine_steps_per_mm_x label"
msgid "Steps per Millimeter (X)"
msgstr "每毫米的步數（X）"

msgctxt "machine_steps_per_mm_y label"
msgid "Steps per Millimeter (Y)"
msgstr "每毫米的步數（Y）"

msgctxt "machine_steps_per_mm_z label"
msgid "Steps per Millimeter (Z)"
msgstr "每毫米的步數（Z）"

msgctxt "support description"
msgid "Support"
msgstr "支撐"

msgctxt "support label"
msgid "Support"
msgstr "支撐"

msgctxt "acceleration_support label"
msgid "Support Acceleration"
msgstr "支撐加速度"

msgctxt "support_bottom_distance label"
msgid "Support Bottom Distance"
msgstr "支撐底部間距"

msgctxt "support_bottom_wall_count label"
msgid "Support Bottom Wall Line Count"
msgstr "支撐牆壁線條數量"

msgctxt "support_brim_line_count label"
msgid "Support Brim Line Count"
msgstr "支撐邊緣線條數量"

msgctxt "support_brim_width label"
msgid "Support Brim Width"
msgstr "支撐邊緣寬度"

msgctxt "support_zag_skip_count label"
msgid "Support Chunk Line Count"
msgstr "支撐塊線條數"

msgctxt "support_skip_zag_per_mm label"
msgid "Support Chunk Size"
msgstr "支撐塊大小"

msgctxt "support_infill_rate label"
msgid "Support Density"
msgstr "支撐密度"

msgctxt "support_xy_overrides_z label"
msgid "Support Distance Priority"
msgstr "支撐間距優先權"

msgctxt "support_extruder_nr label"
msgid "Support Extruder"
msgstr "支撐用擠出機"

msgctxt "acceleration_support_bottom label"
msgid "Support Floor Acceleration"
msgstr "支撐底板加速度"

msgctxt "support_bottom_density label"
msgid "Support Floor Density"
msgstr "支撐底板密度"

msgctxt "support_bottom_extruder_nr label"
msgid "Support Floor Extruder"
msgstr "支撐底板擠出機"

msgctxt "support_bottom_material_flow label"
msgid "Support Floor Flow"
msgstr "支撐底板流量"

msgctxt "support_bottom_offset label"
msgid "Support Floor Horizontal Expansion"
msgstr "支撐底板水平擴展"

msgctxt "jerk_support_bottom label"
msgid "Support Floor Jerk"
msgstr "支撐底板加加速度"

msgctxt "support_bottom_angles label"
msgid "Support Floor Line Directions"
msgstr "支撐底板線條方向"

msgctxt "support_bottom_line_distance label"
msgid "Support Floor Line Distance"
msgstr "支撐底板線條距離"

msgctxt "support_bottom_line_width label"
msgid "Support Floor Line Width"
msgstr "支撐底板線寬"

msgctxt "support_bottom_pattern label"
msgid "Support Floor Pattern"
msgstr "支撐底板列印樣式"

msgctxt "speed_support_bottom label"
msgid "Support Floor Speed"
msgstr "支撐底板速度"

msgctxt "support_bottom_height label"
msgid "Support Floor Thickness"
msgstr "支撐底板厚度"

msgctxt "support_material_flow label"
msgid "Support Flow"
msgstr "支撐流量"

msgctxt "support_offset label"
msgid "Support Horizontal Expansion"
msgstr "支撐水平擴展"

msgctxt "acceleration_support_infill label"
msgid "Support Infill Acceleration"
msgstr "支撐填充加速度"

msgctxt "support_infill_extruder_nr label"
msgid "Support Infill Extruder"
msgstr "支撐填充擠出機"

msgctxt "jerk_support_infill label"
msgid "Support Infill Jerk"
msgstr "支撐填充加加速度"

msgctxt "support_infill_sparse_thickness label"
msgid "Support Infill Layer Thickness"
msgstr "支撐填充層厚度"

msgctxt "support_infill_angles label"
msgid "Support Infill Line Directions"
msgstr "支撐填充線條方向"

msgctxt "speed_support_infill label"
msgid "Support Infill Speed"
msgstr "支撐填充速度"

msgctxt "acceleration_support_interface label"
msgid "Support Interface Acceleration"
msgstr "支撐介面加速度"

msgctxt "support_interface_density label"
msgid "Support Interface Density"
msgstr "支撐介面密度"

msgctxt "support_interface_extruder_nr label"
msgid "Support Interface Extruder"
msgstr "支撐介面擠出機"

msgctxt "support_interface_material_flow label"
msgid "Support Interface Flow"
msgstr "支撐介面流量"

msgctxt "support_interface_offset label"
msgid "Support Interface Horizontal Expansion"
msgstr "支撐介面水平擴展"

msgctxt "jerk_support_interface label"
msgid "Support Interface Jerk"
msgstr "支撐介面加加速度"

msgctxt "support_interface_angles label"
msgid "Support Interface Line Directions"
msgstr "支撐介面線條方向"

msgctxt "support_interface_line_width label"
msgid "Support Interface Line Width"
msgstr "支撐介面線寬"

msgctxt "support_interface_pattern label"
msgid "Support Interface Pattern"
msgstr "支撐介面列印樣式"

msgctxt "support_interface_priority label"
msgid "Support Interface Priority"
msgstr ""

msgctxt "support_interface_skip_height label"
msgid "Support Interface Resolution"
msgstr "支撐介面解析度"

msgctxt "speed_support_interface label"
msgid "Support Interface Speed"
msgstr "支撐介面速度"

msgctxt "support_interface_height label"
msgid "Support Interface Thickness"
msgstr "支撐介面厚度"

msgctxt "support_interface_wall_count label"
msgid "Support Interface Wall Line Count"
msgstr "支撐牆壁線條數量"

msgctxt "jerk_support label"
msgid "Support Jerk"
msgstr "支撐加加速度"

msgctxt "support_join_distance label"
msgid "Support Join Distance"
msgstr "支撐結合距離"

msgctxt "support_line_distance label"
msgid "Support Line Distance"
msgstr "支撐線條間距"

msgctxt "support_line_width label"
msgid "Support Line Width"
msgstr "支撐線寬"

msgctxt "support_mesh label"
msgid "Support Mesh"
msgstr "支撐網格"

msgctxt "support_angle label"
msgid "Support Overhang Angle"
msgstr "支撐突出角度"

msgctxt "support_pattern label"
msgid "Support Pattern"
msgstr "支撐列印樣式"

msgctxt "support_type label"
msgid "Support Placement"
msgstr "支撐位置"

msgctxt "acceleration_support_roof label"
msgid "Support Roof Acceleration"
msgstr "支撐頂板加速度"

msgctxt "support_roof_density label"
msgid "Support Roof Density"
msgstr "支撐頂板密度"

msgctxt "support_roof_extruder_nr label"
msgid "Support Roof Extruder"
msgstr "支撐頂板擠出機"

msgctxt "support_roof_material_flow label"
msgid "Support Roof Flow"
msgstr "支撐頂板流量"

msgctxt "support_roof_offset label"
msgid "Support Roof Horizontal Expansion"
msgstr "支撐頂板水平擴展"

msgctxt "jerk_support_roof label"
msgid "Support Roof Jerk"
msgstr "支撐頂板加加速度"

msgctxt "support_roof_angles label"
msgid "Support Roof Line Directions"
msgstr "支撐頂板線條方向"

msgctxt "support_roof_line_distance label"
msgid "Support Roof Line Distance"
msgstr "支撐頂板線條距離"

msgctxt "support_roof_line_width label"
msgid "Support Roof Line Width"
msgstr "支撐頂板線寬"

msgctxt "support_roof_pattern label"
msgid "Support Roof Pattern"
msgstr "支撐頂板列印樣式"

msgctxt "speed_support_roof label"
msgid "Support Roof Speed"
msgstr "支撐頂板速度"

msgctxt "support_roof_height label"
msgid "Support Roof Thickness"
msgstr "支撐頂板厚度"

msgctxt "support_roof_wall_count label"
msgid "Support Roof Wall Line Count"
msgstr "支撐牆壁線條數量"

msgctxt "speed_support label"
msgid "Support Speed"
msgstr "支撐速度"

msgctxt "support_bottom_stair_step_height label"
msgid "Support Stair Step Height"
msgstr "支撐階梯高度"

msgctxt "support_bottom_stair_step_width label"
msgid "Support Stair Step Maximum Width"
msgstr "支撐階梯最大寬度"

msgctxt "support_bottom_stair_step_min_slope label"
msgid "Support Stair Step Minimum Slope Angle"
msgstr "支撐階梯最小傾角"

msgctxt "support_structure label"
msgid "Support Structure"
msgstr "支撐結構"

msgctxt "support_top_distance label"
msgid "Support Top Distance"
msgstr "支撐頂部間距"

msgctxt "support_wall_count label"
msgid "Support Wall Line Count"
msgstr "支撐牆壁線條數量"

msgctxt "support_xy_distance label"
msgid "Support X/Y Distance"
msgstr "支撐 X/Y 間距"

msgctxt "support_z_distance label"
msgid "Support Z Distance"
msgstr "支撐 Z 間距"

msgctxt "support_interface_priority option support_lines_overwrite_interface_area"
msgid "Support lines preferred"
msgstr ""

msgctxt "support_interface_priority option support_area_overwrite_interface_area"
msgid "Support preferred"
msgstr ""

msgctxt "support_supported_skin_fan_speed label"
msgid "Supported Skin Fan Speed"
msgstr "受支撐表層風扇轉速"

msgctxt "magic_mesh_surface_mode option surface"
msgid "Surface"
msgstr "表面"

msgctxt "material_surface_energy label"
msgid "Surface Energy"
msgstr "表面能量"

msgctxt "magic_mesh_surface_mode label"
msgid "Surface Mode"
msgstr "表面模式"

msgctxt "material_adhesion_tendency description"
msgid "Surface adhesion tendency."
msgstr "表面附著趨勢。"

msgctxt "material_surface_energy description"
msgid "Surface energy."
msgstr "表面能量。"

msgctxt "brim_smart_ordering description"
msgid "Swap print order of the innermost and second innermost brim lines. This improves brim removal."
msgstr ""

msgctxt "alternate_carve_order description"
msgid "Switch to which mesh intersecting volumes will belong with every layer, so that the overlapping meshes become interwoven. Turning this setting off will cause one of the meshes to obtain all of the volume in the overlap, while it is removed from the other meshes."
msgstr "將網格重疊的部分，交互的在每一層中歸屬到不同的網格，以便重疊的網格交織在一起。關閉此設定將使其中一個網格物體獲得重疊中的所有體積，而從其他網格物體中移除。"

msgctxt "adaptive_layer_height_threshold description"
msgid "Target horizontal distance between two adjacent layers. Reducing this setting causes thinner layers to be used to bring the edges of the layers closer together."
msgstr "兩個相鄰層之間的目標水平距離。 減少此設定將導致使用較薄的層高以使各層的邊緣更靠近。"

msgctxt "layer_start_x description"
msgid "The X coordinate of the position near where to find the part to start printing each layer."
msgstr "每一層列印起始點附近位置的 X 坐標。"

msgctxt "z_seam_x description"
msgid "The X coordinate of the position near where to start printing each part in a layer."
msgstr "位置的 X 軸座標，在該位置附近開始列印層中各個部分。"

msgctxt "extruder_prime_pos_x description"
msgid "The X coordinate of the position where the nozzle primes at the start of printing."
msgstr "列印開始時，噴頭在 X 軸上初始位置。"

msgctxt "layer_start_y description"
msgid "The Y coordinate of the position near where to find the part to start printing each layer."
msgstr "每一層列印起始點附近位置的 Y 坐標。"

msgctxt "z_seam_y description"
msgid "The Y coordinate of the position near where to start printing each part in a layer."
msgstr "位置的 Y 軸座標，在該位置附近開始列印層中各個部分。"

msgctxt "extruder_prime_pos_y description"
msgid "The Y coordinate of the position where the nozzle primes at the start of printing."
msgstr "列印開始時，噴頭在 Y 軸座標上初始位置。"

msgctxt "extruder_prime_pos_z description"
msgid "The Z coordinate of the position where the nozzle primes at the start of printing."
msgstr "列印開始時，噴頭在 Z 軸座標上的起始位置."

msgctxt "acceleration_print_layer_0 description"
msgid "The acceleration during the printing of the initial layer."
msgstr "列印起始層時的加速度。"

msgctxt "acceleration_layer_0 description"
msgid "The acceleration for the initial layer."
msgstr "起始層的加速度。"

msgctxt "acceleration_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "起始層中的空跑加速度。"

msgctxt "jerk_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "起始層中的空跑加速度。"

msgctxt "acceleration_wall_x description"
msgid "The acceleration with which all inner walls are printed."
msgstr "列印所有內壁的加速度。"

msgctxt "acceleration_infill description"
msgid "The acceleration with which infill is printed."
msgstr "列印填充的加速度。"

msgctxt "acceleration_ironing description"
msgid "The acceleration with which ironing is performed."
msgstr "執行燙平的加速度。"

msgctxt "acceleration_print description"
msgid "The acceleration with which printing happens."
msgstr "列印發生的加速度。"

msgctxt "raft_base_acceleration description"
msgid "The acceleration with which the base raft layer is printed."
msgstr "列印木筏底部的加速度。"

msgctxt "acceleration_support_bottom description"
msgid "The acceleration with which the floors of support are printed. Printing them at lower acceleration can improve adhesion of support on top of your model."
msgstr "列印支撐底板的加速度。以較低的加速度列印可以改善支撐在模型頂部的附著。"

msgctxt "acceleration_support_infill description"
msgid "The acceleration with which the infill of support is printed."
msgstr "列印支撐填充的加速度。"

msgctxt "raft_interface_acceleration description"
msgid "The acceleration with which the middle raft layer is printed."
msgstr "列印木筏中層的加速度。"

msgctxt "acceleration_wall_0 description"
msgid "The acceleration with which the outermost walls are printed."
msgstr "列印最外壁的加速度。"

msgctxt "acceleration_prime_tower description"
msgid "The acceleration with which the prime tower is printed."
msgstr "列印換料塔的加速度。"

msgctxt "raft_acceleration description"
msgid "The acceleration with which the raft is printed."
msgstr "列印木筏的加速度。"

msgctxt "acceleration_support_interface description"
msgid "The acceleration with which the roofs and floors of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "列印支撐頂板和底板的加速度。以較低的加速度列印可以改善突出部分的品質。"

msgctxt "acceleration_support_roof description"
msgid "The acceleration with which the roofs of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "列印支撐頂板的加速度。以較低的加速度列印可以改善突出部分的品質。"

msgctxt "acceleration_skirt_brim description"
msgid "The acceleration with which the skirt and brim are printed. Normally this is done with the initial layer acceleration, but sometimes you might want to print the skirt or brim at a different acceleration."
msgstr "列印外圍和邊緣的加速度。一般情况是以起始層加速度列印這些部分，但有時候你可能想要以不同加速度來列印外圍或邊緣。"

msgctxt "acceleration_support description"
msgid "The acceleration with which the support structure is printed."
msgstr "列印支撐的加速度。"

msgctxt "raft_surface_acceleration description"
msgid "The acceleration with which the top raft layers are printed."
msgstr "列印木筏頂部的加速度。"

msgctxt "acceleration_wall_x_roofing description"
msgid "The acceleration with which the top surface inner walls are printed."
msgstr ""

msgctxt "acceleration_wall_0_roofing description"
msgid "The acceleration with which the top surface outermost walls are printed."
msgstr ""

msgctxt "acceleration_wall description"
msgid "The acceleration with which the walls are printed."
msgstr "列印牆壁的加速度。"

msgctxt "acceleration_roofing description"
msgid "The acceleration with which top surface skin layers are printed."
msgstr "列印頂部表層的加速度。"

msgctxt "acceleration_topbottom description"
msgid "The acceleration with which top/bottom layers are printed."
msgstr "列印頂部/底部層的加速度。"

msgctxt "acceleration_travel description"
msgid "The acceleration with which travel moves are made."
msgstr "進行空跑的加速度。"

msgctxt "ironing_flow description"
msgid "The amount of material, relative to a normal skin line, to extrude during ironing. Keeping the nozzle filled helps filling some of the crevices of the top surface, but too much results in overextrusion and blips on the side of the surface."
msgstr "燙平期間相對於正常表層線條的擠出線材量。保持噴頭填充狀态有助於填充頂部表面的一些縫隙，但如填充過多則會導致表面上過度擠出和光點。"

msgctxt "infill_overlap description"
msgid "The amount of overlap between the infill and the walls as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "填充與牆壁的重疊量佔填充線寬的百分比。輕微的重疊能讓填充與牆壁牢固地連接。"

msgctxt "infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls. A slight overlap allows the walls to connect firmly to the infill."
msgstr "填充和牆壁之間的重疊量。稍微重疊可讓各個壁與填充牢固連接。"

msgctxt "switch_extruder_retraction_amount description"
msgid "The amount of retraction when switching extruders. Set to 0 for no retraction at all. This should generally be the same as the length of the heat zone."
msgstr "切換擠出機時的回抽量。設定為 0 表示沒有回抽。這值通常和加熱區的長度相同。"

msgctxt "machine_nozzle_expansion_angle description"
msgid "The angle between the horizontal plane and the conical part right above the tip of the nozzle."
msgstr "水平面與噴頭尖端上部圓錐形之間的角度。"

msgctxt "support_tower_roof_angle description"
msgid "The angle of a rooftop of a tower. A higher value results in pointed tower roofs, a lower value results in flattened tower roofs."
msgstr "塔頂角度。該值越高，塔頂越尖，值越低，塔頂越平。"

msgctxt "mold_angle description"
msgid "The angle of overhang of the outer walls created for the mold. 0° will make the outer shell of the mold vertical, while 90° will make the outside of the model follow the contour of the model."
msgstr "為模具創建的外壁的突出角度。0° 將使模具的外殼垂直，而 90° 將使模型的外部遵循模型的輪廓。"

msgctxt "support_tree_branch_diameter_angle description"
msgid "The angle of the branches' diameter as they gradually become thicker towards the bottom. An angle of 0 will cause the branches to have uniform thickness over their length. A bit of an angle can increase stability of the tree support."
msgstr "樹枝向底部逐漸變粗時，外徑變化的角度。設為 0 可讓整條樹枝的粗細一致, 而有點角度可增加樹狀支撐的穩定性。"

msgctxt "support_conical_angle description"
msgid "The angle of the tilt of conical support. With 0 degrees being vertical, and 90 degrees being horizontal. Smaller angles cause the support to be more sturdy, but consist of more material. Negative angles cause the base of the support to be wider than the top."
msgstr "錐形支撐的傾斜角度。角度 0 度時為垂直，角度 90 度時為水平。較小的角度會讓支撐更為牢固，但需要更多線材。負值會讓支撐底座比頂部寬。"

msgctxt "magic_fuzzy_skin_point_density description"
msgid "The average density of points introduced on each polygon in a layer. Note that the original points of the polygon are discarded, so a low density results in a reduction of the resolution."
msgstr "在每一層中，每個多邊形上改變的點的平均密度。注意，多邊形的原始點會被捨棄，因此低密度導致解析度降低。"

msgctxt "magic_fuzzy_skin_point_dist description"
msgid "The average distance between the random points introduced on each line segment. Note that the original points of the polygon are discarded, so a high smoothness results in a reduction of the resolution. This value must be higher than half the Fuzzy Skin Thickness."
msgstr "在每個線條部分改變的隨機點之間的平均距離。注意，多邊形的原始點會被捨棄，因此高平滑度導致解析度降低。該值必須大於絨毛皮膚厚度的一半。"

msgctxt "machine_acceleration description"
msgid "The default acceleration of print head movement."
msgstr "列印頭移動的預設加速度。"

msgctxt "default_material_print_temperature description"
msgid "The default temperature used for printing. This should be the \"base\" temperature of a material. All other print temperatures should use offsets based on this value"
msgstr "用於列印的預設溫度。應為線材的溫度\"基礎值\"。其他列印溫度將以此值為基準計算偏移"

msgctxt "default_material_bed_temperature description"
msgid "The default temperature used for the heated build plate. This should be the \"base\" temperature of a build plate. All other print temperatures should use offsets based on this value"
msgstr "列印平台加熱的預設溫度。這會是列印平台的溫度\"基礎值\"。其他列印溫度將以此值為基準計算偏移"

msgctxt "bridge_skin_density description"
msgid "The density of the bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "橋樑表層的密度。當值小於 100 時會增加表層線條的間隙。"

msgctxt "support_bottom_density description"
msgid "The density of the floors of the support structure. A higher value results in better adhesion of the support on top of the model."
msgstr "支撐結構底板的密度。較高的值會讓支撐更容易附著在模型上。"

msgctxt "support_roof_density description"
msgid "The density of the roofs of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "支撐結構頂板的密度。較高的值會讓突出部分印得更好，但支撐將更加難以移除。"

msgctxt "bridge_skin_density_2 description"
msgid "The density of the second bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "橋樑表層第二層的密度。當值小於 100 時會增加表層線條的間隙。"

msgctxt "bridge_skin_density_3 description"
msgid "The density of the third bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "橋樑表層第三層的密度。當值小於 100 時會增加表層線條的間隙。"

msgctxt "machine_depth description"
msgid "The depth (Y-direction) of the printable area."
msgstr "機器可列印區域深度（Y 座標）"

msgctxt "support_tower_diameter description"
msgid "The diameter of a special tower."
msgstr "特殊塔的直徑。"

msgctxt "support_tree_branch_diameter description"
msgid "The diameter of the thinnest branches of tree support. Thicker branches are more sturdy. Branches towards the base will be thicker than this."
msgstr "樹狀支撐中最細樹枝的直徑。越粗的樹枝越堅固。底部的樹枝會比這更粗。"

msgctxt "support_tree_tip_diameter description"
msgid "The diameter of the top of the tip of the branches of tree support."
msgstr ""

msgctxt "machine_feeder_wheel_diameter description"
msgid "The diameter of the wheel that drives the material in the feeder."
msgstr "帶動進料器中線材的輪子的直徑。"

msgctxt "support_tree_max_diameter description"
msgid "The diameter of the widest branches of tree support. A thicker trunk is more sturdy; a thinner trunk takes up less space on the build plate."
msgstr ""

msgctxt "adaptive_layer_height_variation_step description"
msgid "The difference in height of the next layer height compared to the previous one."
msgstr "下一列印層與前一列印層的層高差。"

msgctxt "ironing_line_spacing description"
msgid "The distance between the lines of ironing."
msgstr "燙平線條之間的距離。"

msgctxt "travel_avoid_distance description"
msgid "The distance between the nozzle and already printed parts when avoiding during travel moves."
msgstr "噴頭和已列印部分之間在空跑時避開的距離。"

msgctxt "raft_base_line_spacing description"
msgid "The distance between the raft lines for the base raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "木筏底部線條之間的距離。寬間距方便讓木筏從列印平台移除。"

msgctxt "raft_interface_line_spacing description"
msgid "The distance between the raft lines for the middle raft layer. The spacing of the middle should be quite wide, while being dense enough to support the top raft layers."
msgstr "木筏中層線條之間的距離。中層的間距應足夠寬，同時也要足夠密集，以便支撐木筏頂部。"

msgctxt "raft_surface_line_spacing description"
msgid "The distance between the raft lines for the top raft layers. The spacing should be equal to the line width, so that the surface is solid."
msgstr "木筏頂部線條之間的距離。間距應等於線寬，以便打造堅固表面。"

msgctxt "prime_tower_raft_base_line_spacing description"
msgid "The distance between the raft lines for the unique prime tower raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr ""

msgctxt "interlocking_depth description"
msgid "The distance from the boundary between models to generate interlocking structure, measured in cells. Too few cells will result in poor adhesion."
msgstr ""

msgctxt "brim_width description"
msgid "The distance from the model to the outermost brim line. A larger brim enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "模型到最外側邊緣線的距離。較大的邊緣可增强與列印平台的附著，但也會減少有效列印區域。"

msgctxt "interlocking_boundary_avoidance description"
msgid "The distance from the outside of a model where interlocking structures will not be generated, measured in cells."
msgstr "與噴頭尖端的距離，當不再使用擠出機時會將耗材停放在此區域。"

msgctxt "machine_heat_zone_length description"
msgid "The distance from the tip of the nozzle in which heat from the nozzle is transferred to the filament."
msgstr "與噴頭尖端的距離，噴頭產生的熱量在這段距離內傳遞到線材中。"

msgctxt "bottom_skin_expand_distance description"
msgid "The distance the bottom skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the skin adhere better to the walls on the layer below. Lower values save amount of material used."
msgstr "底部表層延伸進入填充的距離。值愈高表層與填充之間的附著愈好，並使下方的牆壁與表層黏得更緊。而較低的值可以節省線材的使用。"

msgctxt "expand_skins_expand_distance description"
msgid "The distance the skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on neighboring layers adhere better to the skin. Lower values save amount of material used."
msgstr "表層延伸進入填充的距離。值愈高表層與填充之間的附著愈好，並使相鄰層的牆壁與表層黏得更緊。而較低的值可以節省線材的使用。"

msgctxt "top_skin_expand_distance description"
msgid "The distance the top skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on the layer above adhere better to the skin. Lower values save amount of material used."
msgstr "頂部表層延伸進入填充的距離。值愈高表層與填充之間的附著愈好，並使上方的牆壁與表層黏得更緊。而較低的值可以節省線材的使用。"

msgctxt "wipe_move_distance description"
msgid "The distance to move the head back and forth across the brush."
msgstr "將噴頭來回移動經過刷子的距離。"

msgctxt "lightning_infill_prune_angle description"
msgid "The endpoints of infill lines are shortened to save on material. This setting is the angle of overhang of the endpoints of these lines."
msgstr "內部填充線的端點已被縮減以節省線材. 這個設定用於調整突出線的角度."

msgctxt "material_extrusion_cool_down_speed description"
msgid "The extra speed by which the nozzle cools while extruding. The same value is used to signify the heat up speed lost when heating up while extruding."
msgstr "解決在擠料的同時因為噴頭冷卻所造成的影響的額外速度修正。相同的值被用於表示在擠壓時所失去的升溫速度。"

msgctxt "support_extruder_nr_layer_0 description"
msgid "The extruder train to use for printing the first layer of support infill. This is used in multi-extrusion."
msgstr "用於列印支撐填充第一層的擠出機組。在多擠出機情況下適用。"

msgctxt "raft_base_extruder_nr description"
msgid "The extruder train to use for printing the first layer of the raft. This is used in multi-extrusion."
msgstr ""

msgctxt "support_bottom_extruder_nr description"
msgid "The extruder train to use for printing the floors of the support. This is used in multi-extrusion."
msgstr "用於列印支撐底板的擠出機組。在多擠出機情況下適用。"

msgctxt "support_infill_extruder_nr description"
msgid "The extruder train to use for printing the infill of the support. This is used in multi-extrusion."
msgstr "用於列印支撐填充的擠出機組。在多擠出機情況下適用。"

msgctxt "raft_interface_extruder_nr description"
msgid "The extruder train to use for printing the middle layer of the raft. This is used in multi-extrusion."
msgstr ""

msgctxt "support_interface_extruder_nr description"
msgid "The extruder train to use for printing the roofs and floors of the support. This is used in multi-extrusion."
msgstr "用於列印支撐頂板和底板的擠出機組。在多擠出機情況下適用。"

msgctxt "support_roof_extruder_nr description"
msgid "The extruder train to use for printing the roofs of the support. This is used in multi-extrusion."
msgstr "用於列印支撐頂板的擠出機組。在多擠出機情況下適用。"

msgctxt "skirt_brim_extruder_nr description"
msgid "The extruder train to use for printing the skirt or brim. This is used in multi-extrusion."
msgstr ""

msgctxt "adhesion_extruder_nr description"
msgid "The extruder train to use for printing the skirt/brim/raft. This is used in multi-extrusion."
msgstr "用於列印外圍/邊緣/木筏的擠出機組。在多擠出機情況下適用。"

msgctxt "support_extruder_nr description"
msgid "The extruder train to use for printing the support. This is used in multi-extrusion."
msgstr "用於列印支撐的擠出機組。在多擠出機情況下適用。"

msgctxt "raft_surface_extruder_nr description"
msgid "The extruder train to use for printing the top layer(s) of the raft. This is used in multi-extrusion."
msgstr ""

msgctxt "infill_extruder_nr description"
msgid "The extruder train used for printing infill. This is used in multi-extrusion."
msgstr "用於列印填充的擠出機組。在多擠出機情況下適用。"

msgctxt "wall_x_extruder_nr description"
msgid "The extruder train used for printing the inner walls. This is used in multi-extrusion."
msgstr "用於列印內壁的擠出機組。在多擠出機情況下適用。"

msgctxt "wall_0_extruder_nr description"
msgid "The extruder train used for printing the outer wall. This is used in multi-extrusion."
msgstr "用於列印外壁的擠出機組。在多擠出機情況下適用。"

msgctxt "top_bottom_extruder_nr description"
msgid "The extruder train used for printing the top and bottom skin. This is used in multi-extrusion."
msgstr "用於列印頂部和底部表層的擠出機組。在多擠出機情況下適用。"

msgctxt "roofing_extruder_nr description"
msgid "The extruder train used for printing the top most skin. This is used in multi-extrusion."
msgstr "用於列印最頂部表層的擠出機組。在多擠出機情況下適用。"

msgctxt "wall_extruder_nr description"
msgid "The extruder train used for printing the walls. This is used in multi-extrusion."
msgstr "用於列印牆壁的擠出機組。在多擠出機情況下適用。"

msgctxt "raft_base_fan_speed description"
msgid "The fan speed for the base raft layer."
msgstr "木筏底部的風扇轉速。"

msgctxt "raft_interface_fan_speed description"
msgid "The fan speed for the middle raft layer."
msgstr "木筏中層的風扇轉速。"

msgctxt "raft_fan_speed description"
msgid "The fan speed for the raft."
msgstr "木筏的風扇轉速。"

msgctxt "raft_surface_fan_speed description"
msgid "The fan speed for the top raft layers."
msgstr "木筏頂部的風扇轉速。"

msgctxt "cross_infill_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the infill of the print."
msgstr "圖片檔案位置，該圖片的亮度值決定最小密度在填充中對應的位置。"

msgctxt "cross_support_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the support."
msgstr "圖片檔案位置，該圖片的亮度值決定最小密度在支撐中對應的位置。"

msgctxt "speed_slowdown_layers description"
msgid "The first few layers are printed slower than the rest of the model, to get better adhesion to the build plate and improve the overall success rate of prints. The speed is gradually increased over these layers."
msgstr "前幾層的列印速度比模型的其他層慢，以便實現與列印平台的更好附著，並改善整體的列印成功率。該速度在這些層中會逐漸增加。"

msgctxt "raft_airgap description"
msgid "The gap between the final raft layer and the first layer of the model. Only the first layer is raised by this amount to lower the bonding between the raft layer and the model. Makes it easier to peel off the raft."
msgstr "木筏最後一層與模型第一層之間的間隙。只有第一層被提高了這個距離，以便降低木筏和模型之間的附著。讓木筏更容易剝離。"

msgctxt "machine_height description"
msgid "The height (Z-direction) of the printable area."
msgstr "機器可列印區域高度（Z 座標）"

msgctxt "mold_roof_height description"
msgid "The height above horizontal parts in your model which to print mold."
msgstr "用於列印模具的模型水平部分上方的高度。"

msgctxt "cool_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "使用標準風扇轉速的高度。風扇轉速會從起始轉速逐漸增加，在此高度達到標準風扇轉速。"

msgctxt "gantry_height description"
msgid "The height difference between the tip of the nozzle and the gantry system (X and Y axes)."
msgstr "噴頭尖端與吊車之間的高度差。"

msgctxt "machine_nozzle_head_distance description"
msgid "The height difference between the tip of the nozzle and the lowest part of the print head."
msgstr "噴頭尖端與列印頭最低部分之間的高度差。"

msgctxt "retraction_hop_after_extruder_switch_height description"
msgid "The height difference when performing a Z Hop after extruder switch."
msgstr "擠出機切換後進行 Z 抬升的高度差。"

msgctxt "retraction_hop description"
msgid "The height difference when performing a Z Hop."
msgstr "執行 Z 抬升的高度差。"

msgctxt "wipe_hop_amount description"
msgid "The height difference when performing a Z Hop."
msgstr "執行 Z 抬升的高度差。"

msgctxt "layer_height description"
msgid "The height of each layer in mm. Higher values produce faster prints in lower resolution, lower values produce slower prints in higher resolution."
msgstr "每層的高度（以毫米為單位）。值越高，則列印速度越快，解析度越低；值越低，則列印速度越慢，解析度越高。"

msgctxt "gradual_infill_step_height description"
msgid "The height of infill of a given density before switching to half the density."
msgstr "減半填充密度的高度。"

msgctxt "gradual_support_infill_step_height description"
msgid "The height of support infill of a given density before switching to half the density."
msgstr "支撐層密度減半的厚度。"

msgctxt "interlocking_beam_layer_count description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr ""

msgctxt "interlocking_orientation description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr ""

msgctxt "layer_height_0 description"
msgid "The height of the initial layer in mm. A thicker initial layer makes adhesion to the build plate easier."
msgstr "起始層高（以毫米為單位）。起始層越厚，與列印平台的附著越輕鬆。"

msgctxt "prime_tower_base_height description"
msgid "The height of the prime tower base. Increasing this value will result in a more sturdy prime tower because the base will be wider. If this setting is too low, the prime tower will not have a sturdy base."
msgstr ""

msgctxt "support_bottom_stair_step_height description"
msgid "The height of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures. Set to zero to turn off the stair-like behaviour."
msgstr "模型上的支撐階梯狀底部的階梯高度。較低的值會使支撐更難於移除，但過高的值可能導致不穩定的支撐結構。設為零可以關閉階梯狀行為。"

msgctxt "brim_gap description"
msgid "The horizontal distance between the first brim line and the outline of the first layer of the print. A small gap can make the brim easier to remove while still providing the thermal benefits."
msgstr "第一條邊緣線和列印品第一層輪廓之間的水平距離。 一個小間隙可以讓邊緣更容易移除，同時仍然具有散熱優點。"

msgctxt "skirt_gap description"
msgid ""
"The horizontal distance between the skirt and the first layer of the print.\n"
"This is the minimum distance. Multiple skirt lines will extend outwards from this distance."
msgstr ""
"外圍和列印第一層之間的水平距離。\n"
"這是最小距離，多個外圍線條將從此距離向外延伸。"

msgctxt "lightning_infill_straightening_angle description"
msgid "The infill lines are straightened out to save on printing time. This is the maximum angle of overhang allowed across the length of the infill line."
msgstr "填充線被拉直用以節省列印時間. 這是填充線長度上允許的最大突出角度."

msgctxt "infill_offset_x description"
msgid "The infill pattern is moved this distance along the X axis."
msgstr "填充樣式在 X 軸方向平移此距離。"

msgctxt "infill_offset_y description"
msgid "The infill pattern is moved this distance along the Y axis."
msgstr "填充樣式在 Y 軸方向平移此距離。"

msgctxt "machine_nozzle_size description"
msgid "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size."
msgstr "噴頭內徑，在使用非標準噴頭尺寸時需更改此設定。"

msgctxt "raft_base_jerk description"
msgid "The jerk with which the base raft layer is printed."
msgstr "列印木筏底部的加加速度。"

msgctxt "raft_interface_jerk description"
msgid "The jerk with which the middle raft layer is printed."
msgstr "列印木筏中層的加加速度。"

msgctxt "raft_jerk description"
msgid "The jerk with which the raft is printed."
msgstr "列印木筏的加加速度。"

msgctxt "raft_surface_jerk description"
msgid "The jerk with which the top raft layers are printed."
msgstr "列印木筏頂部的加加速度。"

msgctxt "bottom_skin_preshrink description"
msgid "The largest width of bottom skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing bottom skin at slanted surfaces in the model."
msgstr "要移除底部表層區域的最大寬度。寬度小於此值的底部表層區域將會消失。這有助於減少在列印模型傾斜的底部表層所花費的時間和線材。"

msgctxt "skin_preshrink description"
msgid "The largest width of skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top/bottom skin at slanted surfaces in the model."
msgstr "要移除表層區域的最大寬度。寬度小於此值的表層區域將會消失。這有助於減少在列印模型傾斜的頂部表層和底部表層所花費的時間和線材。"

msgctxt "top_skin_preshrink description"
msgid "The largest width of top skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top skin at slanted surfaces in the model."
msgstr "要移除頂部表層區域的最大寬度。寬度小於此值的頂部表層區域將會消失。這有助於減少在列印模型傾斜的頂部表層所花費的時間和線材。"

msgctxt "cool_fan_full_layer description"
msgid "The layer at which the fans spin on regular fan speed. If regular fan speed at height is set, this value is calculated and rounded to a whole number."
msgstr "要使用標準風扇轉速的層。如果標準風扇轉速高度已被設定，這個值將使用計算出來後取四捨五入的整數值。"

msgctxt "cool_min_layer_time_fan_speed_max description"
msgid "The layer time which sets the threshold between regular fan speed and maximum fan speed. Layers that print slower than this time use regular fan speed. For faster layers the fan speed gradually increases towards the maximum fan speed."
msgstr "使用標準風扇轉速的單層列印時間門檻值。如果單層列印時間大於這個門檻值，就使用標準風扇轉速。如果單層列印時間比這個門檻值短，系統會根據單層列印時間決定使用的風扇轉速。列印時間愈短，所使用的風扇轉速愈快，但不會超過最大風扇轉速。"

msgctxt "retraction_amount description"
msgid "The length of material retracted during a retraction move."
msgstr "回抽移動期間回抽的線材長度。"

msgctxt "prime_tower_base_curve_magnitude description"
msgid "The magnitude factor used for the slope of the prime tower base. If you increase this value, the base will become slimmer. If you decrease it, the base will become thicker."
msgstr ""

msgctxt "machine_buildplate_type description"
msgid "The material of the build plate installed on the printer."
msgstr "印表機上列印平台的材質。"

msgctxt "adaptive_layer_height_variation description"
msgid "The maximum allowed height different from the base layer height."
msgstr "允許與底層高度差異的最大值。"

msgctxt "ooze_shield_angle description"
msgid "The maximum angle a part in the ooze shield will have. With 0 degrees being vertical, and 90 degrees being horizontal. A smaller angle leads to less failed ooze shields, but more material."
msgstr "擦拭牆中的一部分的最大角度。0度為垂直，90度為水平。較小的角度擦拭效果較好，但是要用更多的線材。"

msgctxt "conical_overhang_angle description"
msgid "The maximum angle of overhangs after the they have been made printable. At a value of 0° all overhangs are replaced by a piece of model connected to the build plate, 90° will not change the model in any way."
msgstr "在突出部分變得可列印後突出的最大角度。當該值為 0° 時，所有突出部分將被與列印平台連接的模型的一個部分替代，如果為 90° 時，不會以任何方式更改模型。"

msgctxt "support_tree_angle description"
msgid "The maximum angle of the branches while they grow around the model. Use a lower angle to make them more vertical and more stable. Use a higher angle to be able to have more reach."
msgstr ""

msgctxt "conical_overhang_hole_size description"
msgid "The maximum area of a hole in the base of the model before it's removed by Make Overhang Printable.  Holes smaller than this will be retained.  A value of 0 mm² will fill all holes in the models base."
msgstr "啟用\"使突出可列印\"前之模型底層孔洞最大面積. 如孔洞面積低於此值會被保留, 如設為0會填補所有模型底層孔洞."

msgctxt "meshfix_maximum_deviation description"
msgid "The maximum deviation allowed when reducing the resolution for the Maximum Resolution setting. If you increase this, the print will be less accurate, but the g-code will be smaller. Maximum Deviation is a limit for Maximum Resolution, so if the two conflict the Maximum Deviation will always be held true."
msgstr "降低「最高解析度」設定時允許的最大偏差範圍。假如你增加這個設定值，列印精度會降低，但 G-code 會較小。最大偏差是最高解析度的限制，所以當兩者衝突時，會以最大偏差成立為優先。"

msgctxt "support_join_distance description"
msgid "The maximum distance between support structures in the X/Y directions. When separate structures are closer together than this value, the structures merge into one."
msgstr "支撐結構間在 X/Y 方向的最大距離。當結構與結構靠近到小於此值時，這些結構將合併為一個。"

msgctxt "flow_rate_max_extrusion_offset description"
msgid "The maximum distance in mm to move the filament to compensate for changes in flow rate."
msgstr "流速補償時線材可移動的最大距離（以毫米為單位）。"

msgctxt "meshfix_maximum_extrusion_area_deviation description"
msgid "The maximum extrusion area deviation allowed when removing intermediate points from a straight line. An intermediate point may serve as width-changing point in a long straight line. Therefore, if it is removed, it will cause the line to have a uniform width and, as a result, lose (or gain) a bit of extrusion area. If you increase this you may notice slight under- (or over-) extrusion in between straight parallel walls, as more intermediate width-changing points will be allowed to be removed. Your print will be less accurate, but the g-code will be smaller."
msgstr ""

msgctxt "jerk_print_layer_0 description"
msgid "The maximum instantaneous velocity change during the printing of the initial layer."
msgstr "列印起始層時的最大瞬時速度變化。"

msgctxt "jerk_print description"
msgid "The maximum instantaneous velocity change of the print head."
msgstr "列印頭的最大瞬時速度變化。"

msgctxt "jerk_ironing description"
msgid "The maximum instantaneous velocity change while performing ironing."
msgstr "執行燙平時的最大瞬時速度變化。"

msgctxt "jerk_wall_x description"
msgid "The maximum instantaneous velocity change with which all inner walls are printed."
msgstr "列印所有內壁時的最大瞬時速度變化。"

msgctxt "jerk_infill description"
msgid "The maximum instantaneous velocity change with which infill is printed."
msgstr "列印填充時的最大瞬時速度變化。"

msgctxt "jerk_support_bottom description"
msgid "The maximum instantaneous velocity change with which the floors of support are printed."
msgstr "列印支撐底板時的最大瞬時速度變化。"

msgctxt "jerk_support_infill description"
msgid "The maximum instantaneous velocity change with which the infill of support is printed."
msgstr "列印支撐填充時的最大瞬時速度變化。"

msgctxt "jerk_wall_0 description"
msgid "The maximum instantaneous velocity change with which the outermost walls are printed."
msgstr "列印最外壁時的最大瞬時速度變化。"

msgctxt "jerk_prime_tower description"
msgid "The maximum instantaneous velocity change with which the prime tower is printed."
msgstr "列印換料塔時的最大瞬時速度變化。"

msgctxt "jerk_support_interface description"
msgid "The maximum instantaneous velocity change with which the roofs and floors of support are printed."
msgstr "列印支撐頂板和底板的最大瞬時速度變化。"

msgctxt "jerk_support_roof description"
msgid "The maximum instantaneous velocity change with which the roofs of support are printed."
msgstr "列印支撐頂板的最大瞬時速度變化。"

msgctxt "jerk_skirt_brim description"
msgid "The maximum instantaneous velocity change with which the skirt and brim are printed."
msgstr "列印外圍和邊緣時的最大瞬時速度變化。"

msgctxt "jerk_support description"
msgid "The maximum instantaneous velocity change with which the support structure is printed."
msgstr "列印支撐結構時的最大瞬時速度變化。"

msgctxt "jerk_wall_x_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface inner walls are printed."
msgstr ""

msgctxt "jerk_wall_0_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface outermost walls are printed."
msgstr ""

msgctxt "jerk_wall description"
msgid "The maximum instantaneous velocity change with which the walls are printed."
msgstr "列印牆壁時的最大瞬時速度變化。"

msgctxt "jerk_roofing description"
msgid "The maximum instantaneous velocity change with which top surface skin layers are printed."
msgstr "列印頂部表層時的最大瞬時速度變化。"

msgctxt "jerk_topbottom description"
msgid "The maximum instantaneous velocity change with which top/bottom layers are printed."
msgstr "列印頂部/底部層時的最大瞬時速度變化。"

msgctxt "jerk_travel description"
msgid "The maximum instantaneous velocity change with which travel moves are made."
msgstr "進行空跑時的最大瞬時速度變化。"

msgctxt "machine_max_feedrate_x description"
msgid "The maximum speed for the motor of the X-direction."
msgstr "X 軸方向馬達的最大速度。"

msgctxt "machine_max_feedrate_y description"
msgid "The maximum speed for the motor of the Y-direction."
msgstr "Y 軸方向馬達的最大速度。"

msgctxt "machine_max_feedrate_z description"
msgid "The maximum speed for the motor of the Z-direction."
msgstr "Z 軸方向馬達的最大速度。"

msgctxt "machine_max_feedrate_e description"
msgid "The maximum speed of the filament."
msgstr "線材的最大速度。"

msgctxt "support_bottom_stair_step_width description"
msgid "The maximum width of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures."
msgstr "停留在模型上的支撐階梯狀底部的最大階梯寬度。較低的值會使支撐更難於移除，但過高的值可能導致不穩定的支撐結構。"

msgctxt "mold_width description"
msgid "The minimal distance between the outside of the mold and the outside of the model."
msgstr "模具外部與模型外部的最小距離."

msgctxt "machine_minimum_feedrate description"
msgid "The minimal movement speed of the print head."
msgstr "列印頭的最低移動速度。"

msgctxt "material_initial_print_temperature description"
msgid "The minimal temperature while heating up to the Printing Temperature at which printing can already start."
msgstr "加熱到可以開始列印的列印溫度時的最低溫度。"

msgctxt "machine_min_cool_heat_time_window description"
msgid "The minimal time an extruder has to be inactive before the nozzle is cooled. Only when an extruder is not used for longer than this time will it be allowed to cool down to the standby temperature."
msgstr "擠出機必須保持不活動以便噴頭冷卻的最短時間。擠出機必須停用超過此時間，才可以冷卻到待機溫度。"

msgctxt "infill_support_angle description"
msgid "The minimum angle of internal overhangs for which infill is added. At a value of 0° objects are totally filled with infill, 90° will not provide any infill."
msgstr "添加填充的最小向內突出角度。設為 0° 時，物件將完全填充，設為 90° 時，不提供任何填充。"

msgctxt "support_angle description"
msgid "The minimum angle of overhangs for which support is added. At a value of 0° all overhangs are supported, 90° will not provide any support."
msgstr "添加支撐的最小突出角度。當角度為 0° 時，將支撐所有突出部分，當角度為 90° 時，不提供任何支撐。"

msgctxt "retraction_min_travel description"
msgid "The minimum distance of travel needed for a retraction to happen at all. This helps to get fewer retractions in a small area."
msgstr "觸發回抽所需的最小空跑距離。這有助於減少小區域內的回抽次數。"

msgctxt "skirt_brim_minimal_length description"
msgid "The minimum length of the skirt or brim. If this length is not reached by all skirt or brim lines together, more skirt or brim lines will be added until the minimum length is reached. Note: If the line count is set to 0 this is ignored."
msgstr "外圍或邊緣的最小長度。如果所有外圍或邊緣線條之和都没有達到此長度，則將添加更多外圍或邊緣線條直至達到最小長度。注意：如果線條計數設為 0，則將忽略此選項。"

msgctxt "min_odd_wall_line_width description"
msgid "The minimum line width for middle line gap filler polyline walls. This setting determines at which model thickness we switch from printing two wall lines, to printing two outer walls and a single central wall in the middle. A higher Minimum Odd Wall Line Width leads to a higher maximum even wall line width. The maximum odd wall line width is calculated as 2 * Minimum Even Wall Line Width."
msgstr ""

msgctxt "min_even_wall_line_width description"
msgid "The minimum line width for normal polygonal walls. This setting determines at which model thickness we switch from printing a single thin wall line, to printing two wall lines. A higher Minimum Even Wall Line Width leads to a higher maximum odd wall line width. The maximum even wall line width is calculated as Outer Wall Line Width + 0.5 * Minimum Odd Wall Line Width."
msgstr ""

msgctxt "cool_min_speed description"
msgid "The minimum print speed, despite slowing down due to the minimum layer time. When the printer would slow down too much, the pressure in the nozzle would be too low and result in bad print quality."
msgstr "列印速度的下限，限制因“最短單層列印時間”的減速。當印表機減速過多時，噴頭中的壓力將過低並導致較差的列印品質。"

msgctxt "meshfix_maximum_resolution description"
msgid "The minimum size of a line segment after slicing. If you increase this, the mesh will have a lower resolution. This may allow the printer to keep up with the speed it has to process g-code and will increase slice speed by removing details of the mesh that it can't process anyway."
msgstr "切片後線段的最小尺寸。 如果你增加此設定值，網格的解析度將較低。 這允許印表機保持處理 G-code 的速度，並通過移除無法處理的網格細節來增加切片速度。"

msgctxt "meshfix_maximum_travel_resolution description"
msgid "The minimum size of a travel line segment after slicing. If you increase this, the travel moves will have less smooth corners. This may allow the printer to keep up with the speed it has to process g-code, but it may cause model avoidance to become less accurate."
msgstr "切片後空跑線段的最小尺寸。如果你增加此設定值，空跑移動時的轉角較不圓滑。這允許印表機快速的處理 G-code，但可能造成噴頭迴避模型時較不精確。"

msgctxt "support_bottom_stair_step_min_slope description"
msgid "The minimum slope of the area for stair-stepping to take effect. Low values should make support easier to remove on shallower slopes, but really low values may result in some very counter-intuitive results on other parts of the model."
msgstr "階梯作用區域的最小斜率。較低的值應能讓較淺的斜面上的支撐更容易移除，但過低的值可能會在模型的其它部位導致與直覺相反的結果。"

msgctxt "cool_min_layer_time description"
msgid "The minimum time spent in a layer. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "單層列印時間的下限。這會迫使印表機減速，以便在單層列印中消耗此處所規定的時間。這會讓模型充分冷卻後再列印下一層。如果“噴頭抬升”功能被關閉，為了不違反“最低列印速度”，單層列印時間仍有可能低於此設定值。"

msgctxt "prime_tower_min_volume description"
msgid "The minimum volume for each layer of the prime tower in order to purge enough material."
msgstr "為了清除足夠的線材，換料塔每層的最小體積。"

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model description"
msgid "The most the diameter of a branch that has to connect to the model may increase by merging with branches that could reach the buildplate. Increasing this reduces print time, but increases the area of support that rests on model"
msgstr ""

msgctxt "machine_name description"
msgid "The name of your 3D printer model."
msgstr "你的 3D 印表機型號的名稱。"

msgctxt "machine_nozzle_id description"
msgid "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\"."
msgstr "擠出機組的噴頭 ID，比如\"AA 0.4\"和\"BB 0.8\"。"

msgctxt "travel_avoid_other_parts description"
msgid "The nozzle avoids already printed parts when traveling. This option is only available when combing is enabled."
msgstr "噴頭會在空跑時避開已列印的部分。此選項僅在啟用梳理功能時可用。"

msgctxt "travel_avoid_supports description"
msgid "The nozzle avoids already printed supports when traveling. This option is only available when combing is enabled."
msgstr "噴頭在空跑時避開已列印的支撐。此選項僅在啟用梳理功能時可用。"

msgctxt "bottom_layers description"
msgid "The number of bottom layers. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "底部列印層數，當由底部厚度來計算時層數時，會四捨五入為一個整數值。"

msgctxt "raft_base_wall_count description"
msgid "The number of contours to print around the linear pattern in the base layer of the raft."
msgstr ""

msgctxt "skin_edge_support_layers description"
msgid "The number of infill layers that supports skin edges."
msgstr "支撐表層邊緣的額外填充的層數。"

msgctxt "initial_bottom_layers description"
msgid "The number of initial bottom layers, from the build-plate upwards. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "從列印平台列印的起始底部列印層數，當由底部厚度來計算時層數時，會四捨五入為一個整數值。"

msgctxt "raft_interface_layers description"
msgid "The number of layers between the base and the surface of the raft. These comprise the main thickness of the raft. Increasing this creates a thicker, sturdier raft."
msgstr ""

msgctxt "brim_line_count description"
msgid "The number of lines used for a brim. More brim lines enhance adhesion to the build plate, but also reduces the effective print area."
msgstr "邊緣所用線條數量。更多邊緣線條可增强與列印平台的附著，但也會減少有效列印區域。"

msgctxt "support_brim_line_count description"
msgid "The number of lines used for the support brim. More brim lines enhance adhesion to the build plate, at the cost of some extra material."
msgstr "支撐邊緣所使用的線條數量。邊緣使用較多的線條會加強對列印平台的附著力，但會需要一些額外的線材。"

msgctxt "raft_surface_layers description"
msgid "The number of top layers on top of the 2nd raft layer. These are fully filled layers that the model sits on. 2 layers result in a smoother top surface than 1."
msgstr "位於木筏中層上方的頂部層數。這是承載模型的完全填充層。兩層會產生比一層更平滑的頂部表面。"

msgctxt "top_layers description"
msgid "The number of top layers. When calculated by the top thickness, this value is rounded to a whole number."
msgstr "頂部列印層數，當由頂部厚度來計算時層數時，會四捨五入為一個整數值。"

msgctxt "roofing_layer_count description"
msgid "The number of top most skin layers. Usually only one top most layer is sufficient to generate higher quality top surfaces."
msgstr "最頂部表層層數。通常只需一層最頂部就足以產生較高品質的頂部表面。"

msgctxt "support_wall_count description"
msgid "The number of walls with which to surround support infill. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "支撐填充的牆壁數。增加牆壁能讓支撐填充更加可靠並能更佳的支撐突出部分，但會增長列印時間和使用的線材。"

msgctxt "support_bottom_wall_count description"
msgid "The number of walls with which to surround support interface floor. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "支撐填充的牆壁數。增加牆壁能讓支撐填充更加可靠並能更佳的支撐突出部分，但會增長列印時間和使用的線材。"

msgctxt "support_roof_wall_count description"
msgid "The number of walls with which to surround support interface roof. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "支撐填充的牆壁數。增加牆壁能讓支撐填充更加可靠並能更佳的支撐突出部分，但會增長列印時間和使用的線材。"

msgctxt "support_interface_wall_count description"
msgid "The number of walls with which to surround support interface. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "支撐填充的牆壁數。增加牆壁能讓支撐填充更加可靠並能更佳的支撐突出部分，但會增長列印時間和使用的線材。"

msgctxt "wall_distribution_count description"
msgid "The number of walls, counted from the center, over which the variation needs to be spread. Lower values mean that the outer walls don't change in width."
msgstr ""

msgctxt "wall_line_count description"
msgid "The number of walls. When calculated by the wall thickness, this value is rounded to a whole number."
msgstr "牆壁的線條圈數，如果由壁厚計算，會四捨五入為一個整數值。"

msgctxt "machine_nozzle_tip_outer_diameter description"
msgid "The outer diameter of the tip of the nozzle."
msgstr "噴頭尖端的外徑。"

msgctxt "infill_pattern description"
msgid "The pattern of the infill material of the print. The line and zig zag infill swap direction on alternate layers, reducing material cost. The grid, triangle, tri-hexagon, cubic, octet, quarter cubic, cross and concentric patterns are fully printed every layer. Gyroid, cubic, quarter cubic and octet infill change with every layer to provide a more equal distribution of strength over each direction. Lightning infill tries to minimize the infill, by only supporting the ceiling of the object."
msgstr "內部填充層的圖案。線形、鋸齒形填充在交替層間交換方向，已降低材料成本。網格、三角形、三角-六邊形、立方體、八面體、四分立方體、十字和同心圖案每個層間皆有列印。螺旋型、立方體、四分立方體和八面體的填充隨著每一層而變化，以在每個方向上提供更均勻的強度分佈。閃電型填充透過僅支撐物體的頂層來最小化填充。"

msgctxt "support_pattern description"
msgid "The pattern of the support structures of the print. The different options available result in sturdy or easy to remove support."
msgstr "支撐結構的列印樣式。有不同的選項可產生堅固的或容易清除的支撐。"

msgctxt "roofing_pattern description"
msgid "The pattern of the top most layers."
msgstr "最頂部列印樣式。"

msgctxt "top_bottom_pattern description"
msgid "The pattern of the top/bottom layers."
msgstr "頂部/底部的列印樣式。"

msgctxt "top_bottom_pattern_0 description"
msgid "The pattern on the bottom of the print on the first layer."
msgstr "列印件底部第一層的列印樣式。"

msgctxt "ironing_pattern description"
msgid "The pattern to use for ironing top surfaces."
msgstr "用於燙平頂部表面的列印樣式。"

msgctxt "support_bottom_pattern description"
msgid "The pattern with which the floors of the support are printed."
msgstr "列印支撐底板的列印樣式。"

msgctxt "support_interface_pattern description"
msgid "The pattern with which the interface of the support with the model is printed."
msgstr "支撐與模型之間介面的列印樣式。"

msgctxt "support_roof_pattern description"
msgid "The pattern with which the roofs of the support are printed."
msgstr "列印支撐頂板的列印樣式。"

msgctxt "z_seam_position description"
msgid "The position near where to start printing each part in a layer."
msgstr "每一層開始列印位置要靠近哪個方向。"

msgctxt "support_tree_angle_slow description"
msgid "The preferred angle of the branches, when they do not have to avoid the model. Use a lower angle to make them more vertical and more stable. Use a higher angle for branches to merge faster."
msgstr ""

msgctxt "support_tree_rest_preference description"
msgid "The preferred placement of the support structures. If structures can't be placed at the preferred location, they will be place elsewhere, even if that means placing them on the model."
msgstr ""

msgctxt "jerk_layer_0 description"
msgid "The print maximum instantaneous velocity change for the initial layer."
msgstr "起始層的列印最大瞬時速度變化。"

msgctxt "machine_shape description"
msgid "The shape of the build plate without taking unprintable areas into account."
msgstr "列印平台形狀（不計算不可列印區域）。"

msgctxt "machine_head_with_fans_polygon description"
msgid "The shape of the print head. These are coordinates relative to the position of the print head, which is usually the position of its first extruder. The dimensions left and in front of the print head must be negative coordinates."
msgstr ""

msgctxt "cross_infill_pocket_size description"
msgid "The size of pockets at four-way crossings in the cross 3D pattern at heights where the pattern is touching itself."
msgstr "立體十字形在樣式閉合的高度處，中央十字交叉的氣囊大小。"

msgctxt "coasting_min_volume description"
msgid "The smallest volume an extrusion path should have before allowing coasting. For smaller extrusion paths, less pressure has been built up in the bowden tube and so the coasted volume is scaled linearly. This value should always be larger than the Coasting Volume."
msgstr "可以進行滑行前，擠出路徑應有的最小體積。對於較小的擠出路徑，喉管內累積的壓力較少，因此滑行體積採用線性比率縮小。該值應大於滑行體積。"

msgctxt "machine_nozzle_cool_down_speed description"
msgid "The speed (°C/s) by which the nozzle cools down averaged over the window of normal printing temperatures and the standby temperature."
msgstr "噴頭從列印溫度冷卻到待機溫度的平均速度（℃/ s）。"

msgctxt "machine_nozzle_heat_up_speed description"
msgid "The speed (°C/s) by which the nozzle heats up averaged over the window of normal printing temperatures and the standby temperature."
msgstr "噴頭從待機溫度加熱到列印溫度的平均速度（℃/ s）。"

msgctxt "speed_wall_x description"
msgid "The speed at which all inner walls are printed. Printing the inner wall faster than the outer wall will reduce printing time. It works well to set this in between the outer wall speed and the infill speed."
msgstr "列印所有內壁的速度。以比外壁更快的速度列印內壁將減少列印時間。將該值設為外壁速度和填充速度之間也可行。"

msgctxt "bridge_skin_speed description"
msgid "The speed at which bridge skin regions are printed."
msgstr "列印橋樑表層區域時的速度。"

msgctxt "speed_infill description"
msgid "The speed at which infill is printed."
msgstr "列印填充的速度。"

msgctxt "speed_print description"
msgid "The speed at which printing happens."
msgstr "開始列印時的速度。"

msgctxt "raft_base_speed description"
msgid "The speed at which the base raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "列印木筏底部的速度。這些層應以很慢的速度列印，因為噴頭所出的線材量非常高。"

msgctxt "bridge_wall_speed description"
msgid "The speed at which the bridge walls are printed."
msgstr "列印橋樑牆壁時的速度。"

msgctxt "cool_fan_speed_0 description"
msgid "The speed at which the fans spin at the start of the print. In subsequent layers the fan speed is gradually increased up to the layer corresponding to Regular Fan Speed at Height."
msgstr "列印起始層時的風扇轉速。在隨後的層中，風扇轉速會逐漸增加到對應層所設定的速度。"

msgctxt "cool_fan_speed_min description"
msgid "The speed at which the fans spin before hitting the threshold. When a layer prints faster than the threshold, the fan speed gradually inclines towards the maximum fan speed."
msgstr "在單層列印時間大於門檻值時，風扇運轉的速度。當單層列印時間小於門檻值時，系統會根據單層列印時間決定使用的風扇轉速。列印時間愈短，所使用的風扇轉速愈快，但不會超過最大風扇轉速。"

msgctxt "cool_fan_speed_max description"
msgid "The speed at which the fans spin on the minimum layer time. The fan speed gradually increases between the regular fan speed and maximum fan speed when the threshold is hit."
msgstr "在“最短單層列印時間”時，風扇運轉的速度。當單層列印時間小於門檻值時，系統會根據單層列印時間決定使用的風扇轉速。列印時間愈短，所使用的風扇轉速愈快，但不會超過最大風扇轉速。"

msgctxt "retraction_prime_speed description"
msgid "The speed at which the filament is primed during a retraction move."
msgstr "回抽移動期間線材裝填的速度。"

msgctxt "wipe_retraction_prime_speed description"
msgid "The speed at which the filament is primed during a wipe retraction move."
msgstr "擦拭過程中線材裝填的速度。"

msgctxt "switch_extruder_prime_speed description"
msgid "The speed at which the filament is pushed back after a nozzle switch retraction."
msgstr "噴頭切換回抽後線材被推回的速度。"

msgctxt "retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a retraction move."
msgstr "回抽移動期間線材回抽和裝填的速度。"

msgctxt "wipe_retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a wipe retraction move."
msgstr "擦拭過程中線材回抽和裝填的速度。"

msgctxt "switch_extruder_retraction_speed description"
msgid "The speed at which the filament is retracted during a nozzle switch retract."
msgstr "噴頭切換回抽期間線材回抽的速度。"

msgctxt "retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a retraction move."
msgstr "回抽移動期間線材回抽的速度。"

msgctxt "wipe_retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a wipe retraction move."
msgstr "擦拭過程中線材回抽的速度。"

msgctxt "switch_extruder_retraction_speeds description"
msgid "The speed at which the filament is retracted. A higher retraction speed works better, but a very high retraction speed can lead to filament grinding."
msgstr "回抽線材的速度。較高的回抽速度效果較好，但回抽速度過高可能導致線材磨損。"

msgctxt "speed_support_bottom description"
msgid "The speed at which the floor of support is printed. Printing it at lower speed can improve adhesion of support on top of your model."
msgstr "列印支撐底板的速度。以較低的速度列印可以改善支撐在模型頂部的附著。"

msgctxt "speed_support_infill description"
msgid "The speed at which the infill of support is printed. Printing the infill at lower speeds improves stability."
msgstr "列印支撐填充的速度。以較低的速度列印填充可改善穩定性。"

msgctxt "raft_interface_speed description"
msgid "The speed at which the middle raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "列印木筏中層的速度。這些層應以很慢的速度列印，因為噴頭所出的線材量非常高。"

msgctxt "speed_wall_0 description"
msgid "The speed at which the outermost walls are printed. Printing the outer wall at a lower speed improves the final skin quality. However, having a large difference between the inner wall speed and the outer wall speed will affect quality in a negative way."
msgstr "列印最外壁的速度。以較低速度列印外壁可改善最終表層品質。但是，如果內壁速度和外壁速度差距過大，則將對品質產生負面影響。"

msgctxt "speed_prime_tower description"
msgid "The speed at which the prime tower is printed. Printing the prime tower slower can make it more stable when the adhesion between the different filaments is suboptimal."
msgstr "列印換料塔的速度。當不同線材之間的黏合力不佳時，較慢地列印速度可以讓它更穩定。"

msgctxt "cool_fan_speed description"
msgid "The speed at which the print cooling fans spin."
msgstr "列印冷卻風扇旋轉的速度。"

msgctxt "raft_speed description"
msgid "The speed at which the raft is printed."
msgstr "列印木筏的速度。"

msgctxt "speed_support_interface description"
msgid "The speed at which the roofs and floors of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "列印支撐頂板和底板的速度。以較低的速度列印可以改善突出部分的品質。"

msgctxt "speed_support_roof description"
msgid "The speed at which the roofs of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "列印支撐頂板的速度。以較低的速度列印可以改善突出部分的品質。"

msgctxt "skirt_brim_speed description"
msgid "The speed at which the skirt and brim are printed. Normally this is done at the initial layer speed, but sometimes you might want to print the skirt or brim at a different speed."
msgstr "列印外圍和邊緣的速度。一般情况是以起始層速度列印這些部分，但有時候你可能想要以不同速度來列印外圍或邊緣。"

msgctxt "speed_support description"
msgid "The speed at which the support structure is printed. Printing support at higher speeds can greatly reduce printing time. The surface quality of the support structure is not important since it is removed after printing."
msgstr "在列印支撐結構時的速度。以更高的速度列印支撐可以大大減少列印時間。因為支撐在列印後會被清除，所以表面品質並不重要。"

msgctxt "raft_surface_speed description"
msgid "The speed at which the top raft layers are printed. These should be printed a bit slower, so that the nozzle can slowly smooth out adjacent surface lines."
msgstr "列印木筏頂部的速度。這些層應以稍慢的速度列印，以便噴頭緩慢地整平臨近的表面線條。"

msgctxt "speed_wall_x_roofing description"
msgid "The speed at which the top surface inner walls are printed."
msgstr ""

msgctxt "speed_wall_0_roofing description"
msgid "The speed at which the top surface outermost wall is printed."
msgstr ""

msgctxt "speed_z_hop description"
msgid "The speed at which the vertical Z movement is made for Z Hops. This is typically lower than the print speed since the build plate or machine's gantry is harder to move."
msgstr "Z 抬升時 Z 軸垂直移動的速度。這通常低於列印速度，因為列印平台或機器的吊車較難移動。"

msgctxt "speed_wall description"
msgid "The speed at which the walls are printed."
msgstr "列印牆壁的速度。"

msgctxt "speed_ironing description"
msgid "The speed at which to pass over the top surface."
msgstr "通過頂部表面的速度。"

msgctxt "material_break_speed description"
msgid "The speed at which to retract the filament in order to break it cleanly."
msgstr "要讓線材脆斷要回抽多快。"

msgctxt "speed_roofing description"
msgid "The speed at which top surface skin layers are printed."
msgstr "列印頂部表層的速度。"

msgctxt "speed_topbottom description"
msgid "The speed at which top/bottom layers are printed."
msgstr "列印頂部/底部層的速度。"

msgctxt "speed_travel description"
msgid "The speed at which travel moves are made."
msgstr "噴頭在非列印時的移動速度。"

msgctxt "coasting_speed description"
msgid "The speed by which to move during coasting, relative to the speed of the extrusion path. A value slightly under 100% is advised, since during the coasting move the pressure in the bowden tube drops."
msgstr "滑行期間相對於擠出路徑的移動速度。建議採用略低於 100% 的值，因為在滑行移動期間喉管中的壓力會下降。"

msgctxt "speed_layer_0 description"
msgid "The speed for the initial layer. A lower value is advised to improve adhesion to the build plate. Does not affect the build plate adhesion structures themselves, like brim and raft."
msgstr "初始層速度, 建議以一個較低的值去改善列印平台的附著. 並不影響模型的附著方式,例如開啟邊緣或木筏."

msgctxt "speed_print_layer_0 description"
msgid "The speed of printing for the initial layer. A lower value is advised to improve adhesion to the build plate."
msgstr "列印起始層的速度。建議採用較低的值以便改善與列印平台的附著。"

msgctxt "speed_travel_layer_0 description"
msgid "The speed of travel moves in the initial layer. A lower value is advised to prevent pulling previously printed parts away from the build plate. The value of this setting can automatically be calculated from the ratio between the Travel Speed and the Print Speed."
msgstr "起始層中的空跑速度。建議採用較低的值，以防止將之前列印的部分從列印平台上拉離。該設定的值可以根據空跑速度和列印速度的比率自動計算得出。"

msgctxt "material_break_temperature description"
msgid "The temperature at which the filament is broken for a clean break."
msgstr "要讓線材脆斷所需的溫度。"

msgctxt "build_volume_temperature description"
msgid "The temperature of the environment to print in. If this is 0, the build volume temperature will not be adjusted."
msgstr "列印的環境溫度。如果設為 0，則不會調整列印空間溫度。"

msgctxt "material_standby_temperature description"
msgid "The temperature of the nozzle when another nozzle is currently used for printing."
msgstr "當另一個噴頭進行列印時，這個噴頭要保持的溫度。"

msgctxt "material_final_print_temperature description"
msgid "The temperature to which to already start cooling down just before the end of printing."
msgstr "列印結束前開始冷卻的溫度。"

msgctxt "material_print_temperature_layer_0 description"
msgid "The temperature used for printing the first layer."
msgstr ""

msgctxt "material_print_temperature description"
msgid "The temperature used for printing."
msgstr "用於列印的溫度。"

msgctxt "material_bed_temperature_layer_0 description"
msgid "The temperature used for the heated build plate at the first layer. If this is 0, the build plate is left unheated during the first layer."
msgstr "設定列印第一層時列印平台的溫度。如果設定為 0，就列印第一層時不會加熱列印平台。"

msgctxt "material_bed_temperature description"
msgid "The temperature used for the heated build plate. If this is 0, the build plate is left unheated."
msgstr "設定列印平台的溫度。如果設定為 0，就不會加熱列印平台。"

msgctxt "material_break_preparation_temperature description"
msgid "The temperature used to purge material, should be roughly equal to the highest possible printing temperature."
msgstr "清洗線材的溫度，應該約等於可能的最高列印溫度。"

msgctxt "bottom_thickness description"
msgid "The thickness of the bottom layers in the print. This value divided by the layer height defines the number of bottom layers."
msgstr "列印模型中底部的厚度。此值除以層高決定底部的層數。"

msgctxt "skin_edge_support_thickness description"
msgid "The thickness of the extra infill that supports skin edges."
msgstr "支撐表層邊緣的額外填充的厚度。"

msgctxt "support_interface_height description"
msgid "The thickness of the interface of the support where it touches with the model on the bottom or the top."
msgstr "支撐與模型在底部或頂部接觸的介面厚度。"

msgctxt "support_bottom_height description"
msgid "The thickness of the support floors. This controls the number of dense layers that are printed on top of places of a model on which support rests."
msgstr "支撐底板的厚度。這會控制座落在模型上的支撐底部密集層的數量。"

msgctxt "support_roof_height description"
msgid "The thickness of the support roofs. This controls the amount of dense layers at the top of the support on which the model rests."
msgstr "支撐頂板的厚度。這會控制承載模型的支撐頂部密集層的數量。"

msgctxt "top_thickness description"
msgid "The thickness of the top layers in the print. This value divided by the layer height defines the number of top layers."
msgstr "列印模型中頂部的厚度。該值除以層高決定頂部的層數。"

msgctxt "top_bottom_thickness description"
msgid "The thickness of the top/bottom layers in the print. This value divided by the layer height defines the number of top/bottom layers."
msgstr "列印模型中頂部/底部的厚度。該值除以層高決定頂部/底部的層數。"

msgctxt "wall_thickness description"
msgid "The thickness of the walls in the horizontal direction. This value divided by the wall line width defines the number of walls."
msgstr "水平方向的牆壁厚度。此值除以壁線寬度決定牆壁數量。"

msgctxt "infill_sparse_thickness description"
msgid "The thickness per layer of infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "每層填充的厚度。此值應該是層高度的倍數，並且否則會四捨五入。"

msgctxt "support_infill_sparse_thickness description"
msgid "The thickness per layer of support infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "支撐填充線材每層的厚度。該值應為層高的倍數，否則數值會被四捨五入到層高的倍數。"

msgctxt "machine_gcode_flavor description"
msgid "The type of g-code to be generated."
msgstr "產生 G-code 的類型。"

msgctxt "coasting_volume description"
msgid "The volume otherwise oozed. This value should generally be close to the nozzle diameter cubed."
msgstr "不進行滑行時，會滲出的體積。該值一般應接近噴頭直徑的立方。"

msgctxt "machine_width description"
msgid "The width (X-direction) of the printable area."
msgstr "機器可列印區域寬度（X 座標）"

msgctxt "support_brim_width description"
msgid "The width of the brim to print underneath the support. A larger brim enhances adhesion to the build plate, at the cost of some extra material."
msgstr "列印在支撐下面邊緣的寬度。較大的邊緣會加強對列印平台的附著力，但會需要一些額外的線材。"

msgctxt "interlocking_beam_width description"
msgid "The width of the interlocking structure beams."
msgstr "換料塔的寬度。"

msgctxt "prime_tower_base_size description"
msgid "The width of the prime tower brim/base. A larger base enhances adhesion to the build plate, but also reduces the effective print area."
msgstr ""

msgctxt "prime_tower_size description"
msgid "The width of the prime tower."
msgstr "換料塔的寬度。"

msgctxt "magic_fuzzy_skin_thickness description"
msgid "The width within which to jitter. It's advised to keep this below the outer wall width, since the inner walls are unaltered."
msgstr "進行抖動的寬度。建議讓此值低於外壁寬度，因為內壁不會更改。"

msgctxt "retraction_extrusion_window description"
msgid "The window in which the maximum retraction count is enforced. This value should be approximately the same as the retraction distance, so that effectively the number of times a retraction passes the same patch of material is limited."
msgstr "最大回抽次數範圍。此值應大致與回抽距離相等，從而有效地限制在同一段線材上的回抽次數。"

msgctxt "prime_tower_position_x description"
msgid "The x coordinate of the position of the prime tower."
msgstr "換料塔位置的 X 座標。"

msgctxt "prime_tower_position_y description"
msgid "The y coordinate of the position of the prime tower."
msgstr "換料塔位置的 Y 座標。"

msgctxt "support_meshes_present description"
msgid "There are support meshes present in the scene. This setting is controlled by Cura."
msgstr "場景中有支撐網格存在。此設定由 Cura 控制。"

msgctxt "bridge_wall_coast description"
msgid "This controls the distance the extruder should coast immediately before a bridge wall begins. Coasting before the bridge starts can reduce the pressure in the nozzle and may produce a flatter bridge."
msgstr "這可以控制擠出機在開始列印橋樑牆壁前滑行的距離。在橋樑開始之前進行滑行可以減小噴頭中的壓力並可能產生更平坦的橋樑。"

msgctxt "raft_smoothing description"
msgid "This setting controls how much inner corners in the raft outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "此設定控制木筏輪廓凹角導圓角的量。向內的轉角會被導為圓弧，其半徑等於此設定值。此設定同時可以移除木筏輪廓中半徑小於此設定值的圓孔。"

msgctxt "retraction_count_max description"
msgid "This setting limits the number of retractions occurring within the minimum extrusion distance window. Further retractions within this window will be ignored. This avoids retracting repeatedly on the same piece of filament, as that can flatten the filament and cause grinding issues."
msgstr "此設定限制在最小擠出距離範圍內發生的回抽數。此範圍內的額外回抽將會忽略。這避免了在同一件線材上重複回抽，從而導致線材變扁並引起磨損問題。"

msgctxt "draft_shield_enabled description"
msgid "This will create a wall around the model, which traps (hot) air and shields against exterior airflow. Especially useful for materials which warp easily."
msgstr "這將在模型周圍建立一個牆壁留住（熱）空氣並遮住外部氣流。對於容易翹曲的線材非常有用。"

msgctxt "support_tree_tip_diameter label"
msgid "Tip Diameter"
msgstr ""

msgctxt "material_shrinkage_percentage_xy description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the XY-direction (horizontally)."
msgstr ""

msgctxt "material_shrinkage_percentage_z description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the Z-direction (vertically)."
msgstr ""

msgctxt "material_shrinkage_percentage description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor."
msgstr "為了補償線材在冷卻時的收縮，模型會依此比例放大列印。"

msgctxt "top_layers label"
msgid "Top Layers"
msgstr "頂部層數"

msgctxt "top_skin_expand_distance label"
msgid "Top Skin Expand Distance"
msgstr "頂部表層延伸距離"

msgctxt "top_skin_preshrink label"
msgid "Top Skin Removal Width"
msgstr "頂部表層移除寬度"

msgctxt "acceleration_wall_x_roofing label"
msgid "Top Surface Inner Wall Acceleration"
msgstr ""

msgctxt "jerk_wall_x_roofing label"
msgid "Top Surface Inner Wall Jerk"
msgstr ""

msgctxt "speed_wall_x_roofing label"
msgid "Top Surface Inner Wall Speed"
msgstr ""

msgctxt "wall_x_material_flow_roofing label"
msgid "Top Surface Inner Wall(s) Flow"
msgstr ""

msgctxt "acceleration_wall_0_roofing label"
msgid "Top Surface Outer Wall Acceleration"
msgstr ""

msgctxt "wall_0_material_flow_roofing label"
msgid "Top Surface Outer Wall Flow"
msgstr ""

msgctxt "jerk_wall_0_roofing label"
msgid "Top Surface Outer Wall Jerk"
msgstr ""

msgctxt "speed_wall_0_roofing label"
msgid "Top Surface Outer Wall Speed"
msgstr ""

msgctxt "acceleration_roofing label"
msgid "Top Surface Skin Acceleration"
msgstr "頂部表層加速度"

msgctxt "roofing_extruder_nr label"
msgid "Top Surface Skin Extruder"
msgstr "頂部表層擠出機"

msgctxt "roofing_material_flow label"
msgid "Top Surface Skin Flow"
msgstr "頂部表層流量"

msgctxt "jerk_roofing label"
msgid "Top Surface Skin Jerk"
msgstr "頂部表層加加速度"

msgctxt "roofing_layer_count label"
msgid "Top Surface Skin Layers"
msgstr "頂部表層"

msgctxt "roofing_angles label"
msgid "Top Surface Skin Line Directions"
msgstr "頂部表層線條方向"

msgctxt "roofing_line_width label"
msgid "Top Surface Skin Line Width"
msgstr "頂部表層線寬"

msgctxt "roofing_pattern label"
msgid "Top Surface Skin Pattern"
msgstr "頂部表層列印樣式"

msgctxt "speed_roofing label"
msgid "Top Surface Skin Speed"
msgstr "頂部表層速度"

msgctxt "top_thickness label"
msgid "Top Thickness"
msgstr "頂部厚度"

msgctxt "max_skin_angle_for_expansion description"
msgid "Top and/or bottom surfaces of your object with an angle larger than this setting, won't have their top/bottom skin expanded. This avoids expanding the narrow skin areas that are created when the model surface has a near vertical slope. An angle of 0° is horizontal and will cause no skin to be expanded, while an angle of 90° is vertical and will cause all skin to be expanded."
msgstr "當物件頂部與底部表面角度大於此設定值時，將不會產生表層延伸。當模型表面斜率接近垂直時，可避免產生狹小的表層延伸區域。角度0°為水平，將不會產生表層延伸。而角度90°為垂直，所有表層將被延伸。"

msgctxt "top_bottom description"
msgid "Top/Bottom"
msgstr "頂層/底層"

msgctxt "top_bottom label"
msgid "Top/Bottom"
msgstr "頂層/底層"

msgctxt "acceleration_topbottom label"
msgid "Top/Bottom Acceleration"
msgstr "頂部/底部加速度"

msgctxt "top_bottom_extruder_nr label"
msgid "Top/Bottom Extruder"
msgstr "頂部/底部擠出機"

msgctxt "skin_material_flow label"
msgid "Top/Bottom Flow"
msgstr "頂部/底部流量"

msgctxt "jerk_topbottom label"
msgid "Top/Bottom Jerk"
msgstr "頂部/底部加加速度"

msgctxt "skin_angles label"
msgid "Top/Bottom Line Directions"
msgstr "頂部/底部線條方向"

msgctxt "skin_line_width label"
msgid "Top/Bottom Line Width"
msgstr "頂部/底部線寬"

msgctxt "top_bottom_pattern label"
msgid "Top/Bottom Pattern"
msgstr "頂部/底部填充樣式"

msgctxt "speed_topbottom label"
msgid "Top/Bottom Speed"
msgstr "頂部/底部速度"

msgctxt "top_bottom_thickness label"
msgid "Top/Bottom Thickness"
msgstr "頂部 / 底部厚度"

msgctxt "support_type option buildplate"
msgid "Touching Buildplate"
msgstr "接觸列印平台"

msgctxt "support_tower_diameter label"
msgid "Tower Diameter"
msgstr "塔直徑"

msgctxt "support_tower_roof_angle label"
msgid "Tower Roof Angle"
msgstr "塔頂板角度"

msgctxt "mesh_rotation_matrix description"
msgid "Transformation matrix to be applied to the model when loading it from file."
msgstr "在將模型從檔案中載入時套用在模型上的轉換矩陣。"

msgctxt "travel label"
msgid "Travel"
msgstr "空跑"

msgctxt "acceleration_travel label"
msgid "Travel Acceleration"
msgstr "空跑加速度"

msgctxt "travel_avoid_distance label"
msgid "Travel Avoid Distance"
msgstr "空跑避開距離"

msgctxt "jerk_travel label"
msgid "Travel Jerk"
msgstr "空跑加加速度"

msgctxt "speed_travel label"
msgid "Travel Speed"
msgstr "空跑速度"

msgctxt "magic_mesh_surface_mode description"
msgid "Treat the model as a surface only, a volume, or volumes with loose surfaces. The normal print mode only prints enclosed volumes. \"Surface\" prints a single wall tracing the mesh surface with no infill and no top/bottom skin. \"Both\" prints enclosed volumes like normal and any remaining polygons as surfaces."
msgstr "將模型作為僅表面、一個空間或多個具有鬆散表面的空間處理。“正常”僅列印封閉的空間。“表面”列印模型表面的單壁，没有填充，也没有頂部/底部表層。“兩者”將封閉空間正常列印，並將任何剩餘多邊形作為表面列印。"

msgctxt "support_structure option tree"
msgid "Tree"
msgstr "樹狀"

msgctxt "infill_pattern option trihexagon"
msgid "Tri-Hexagon"
msgstr "三角-六邊形混和"

msgctxt "infill_pattern option triangles"
msgid "Triangles"
msgstr "三角形"

msgctxt "support_bottom_pattern option triangles"
msgid "Triangles"
msgstr "三角形"

msgctxt "support_interface_pattern option triangles"
msgid "Triangles"
msgstr "三角形"

msgctxt "support_pattern option triangles"
msgid "Triangles"
msgstr "三角形"

msgctxt "support_roof_pattern option triangles"
msgid "Triangles"
msgstr "三角形"

msgctxt "support_tree_max_diameter label"
msgid "Trunk Diameter"
msgstr ""

msgctxt "machine_gcode_flavor option UltiGCode"
msgid "Ultimaker 2"
msgstr "Ultimaker 2"

msgctxt "meshfix_union_all label"
msgid "Union Overlapping Volumes"
msgstr "合併重疊體積"

msgctxt "bridge_wall_min_length description"
msgid "Unsupported walls shorter than this will be printed using the normal wall settings. Longer unsupported walls will be printed using the bridge wall settings."
msgstr "比此長度短的無支撐牆壁將以一般牆壁設定列印。較長的無支撐牆壁則以橋樑牆壁的設定列印。"

msgctxt "adaptive_layer_height_enabled label"
msgid "Use Adaptive Layers"
msgstr "使用適應性層高"

msgctxt "support_use_towers label"
msgid "Use Towers"
msgstr "使用塔型支撐"

msgctxt "acceleration_travel_enabled description"
msgid "Use a separate acceleration rate for travel moves. If disabled, travel moves will use the acceleration value of the printed line at their destination."
msgstr ""

msgctxt "jerk_travel_enabled description"
msgid "Use a separate jerk rate for travel moves. If disabled, travel moves will use the jerk value of the printed line at their destination."
msgstr ""

msgctxt "relative_extrusion description"
msgid "Use relative extrusion rather than absolute extrusion. Using relative E-steps makes for easier post-processing of the g-code. However, it's not supported by all printers and it may produce very slight deviations in the amount of deposited material compared to absolute E-steps. Irrespective of this setting, the extrusion mode will always be set to absolute before any g-code script is output."
msgstr "使用相對模式擠出而非絕對模式擠出。使用相對 E 步數在進行 g-code 後處理時可以更加輕鬆。不過並不是所有的印表機都支援此功能，而且與絕對 E 步數相比，它可能在線材的使用量上產生輕微的誤差。不管設定為何，在產生 g-code 之前都是使用絕對模式。"

msgctxt "support_use_towers description"
msgid "Use specialized towers to support tiny overhang areas. These towers have a larger diameter than the region they support. Near the overhang the towers' diameter decreases, forming a roof."
msgstr "使用專門的塔來支撐較小的突出區域。這些塔的直徑比它們所支撐的區域要大。在靠近突出部分時，塔的直徑減小，形成頂板。"

msgctxt "infill_mesh description"
msgid "Use this mesh to modify the infill of other meshes with which it overlaps. Replaces infill regions of other meshes with regions for this mesh. It's suggested to only print one Wall and no Top/Bottom Skin for this mesh."
msgstr "使用此網格修改與其重疊的其他網格的填充。利用此網格的區域替換其他網格的填充區域。建議僅為此網格列印一個壁，而不列印頂部/底部表層。"

msgctxt "support_mesh description"
msgid "Use this mesh to specify support areas. This can be used to generate support structure."
msgstr "使用此網格指定支撐區域。可用於產生支撐結構。"

msgctxt "anti_overhang_mesh description"
msgid "Use this mesh to specify where no part of the model should be detected as overhang. This can be used to remove unwanted support structure."
msgstr "使用此網格指定模型的任何部分不應被檢測為突出的區域。可用於移除不需要的支撐結構。"

msgctxt "z_seam_type option back"
msgid "User Specified"
msgstr "使用者指定"

msgctxt "material_shrinkage_percentage_z label"
msgid "Vertical Scaling Factor Shrinkage Compensation"
msgstr ""

msgctxt "slicing_tolerance description"
msgid "Vertical tolerance in the sliced layers. The contours of a layer are normally generated by taking cross sections through the middle of each layer's thickness (Middle). Alternatively each layer can have the areas which fall inside of the volume throughout the entire thickness of the layer (Exclusive) or a layer has the areas which fall inside anywhere within the layer (Inclusive). Inclusive retains the most details, Exclusive makes for the best fit and Middle stays closest to the original surface."
msgstr "切片層的垂直方向公差。切片層的輪廓通常是採「中間」的方式，取每一層厚度中間的橫切面來產生。選擇「排除」，讓列印區域在該層厚度內的所有高度都維持在模型內。或是選擇「包含」，列印區域將包住該層模型。「包含」保留了最多的細節，「排除」選擇最合身的位置，而「中間」維持最接近原始表面。"

msgctxt "material_bed_temp_wait label"
msgid "Wait for Build Plate Heatup"
msgstr "等待列印平台加熱"

msgctxt "material_print_temp_wait label"
msgid "Wait for Nozzle Heatup"
msgstr "等待噴頭加熱"

msgctxt "acceleration_wall label"
msgid "Wall Acceleration"
msgstr "牆壁加速度"

msgctxt "wall_distribution_count label"
msgid "Wall Distribution Count"
msgstr ""

msgctxt "wall_extruder_nr label"
msgid "Wall Extruder"
msgstr "牆壁擠出機"

msgctxt "wall_material_flow label"
msgid "Wall Flow"
msgstr "牆壁流量"

msgctxt "jerk_wall label"
msgid "Wall Jerk"
msgstr "牆壁加加速度"

msgctxt "wall_line_count label"
msgid "Wall Line Count"
msgstr "牆壁線條圈數"

msgctxt "wall_line_width label"
msgid "Wall Line Width"
msgstr "牆壁線寬"

msgctxt "inset_direction label"
msgid "Wall Ordering"
msgstr ""

msgctxt "speed_wall label"
msgid "Wall Speed"
msgstr "牆壁速度"

msgctxt "wall_thickness label"
msgid "Wall Thickness"
msgstr "壁厚"

msgctxt "wall_transition_length label"
msgid "Wall Transition Length"
msgstr ""

msgctxt "wall_transition_filter_distance label"
msgid "Wall Transitioning Filter Distance"
msgstr ""

msgctxt "wall_transition_filter_deviation label"
msgid "Wall Transitioning Filter Margin"
msgstr ""

msgctxt "wall_transition_angle label"
msgid "Wall Transitioning Threshold Angle"
msgstr ""

msgctxt "shell label"
msgid "Walls"
msgstr "牆"

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either."
msgstr "牆壁突出的角度大於此值時，將使用突出牆壁的設定列印。當此值設定為 90 時，所有牆壁都不會被當作突出牆壁。被支撐的突出牆壁也將不不會被當作突出牆壁。"

msgctxt "support_interface_skip_height description"
msgid "When checking where there's model above and below the support, take steps of the given height. Lower values will slice slower, while higher values may cause normal support to be printed in some places where there should have been support interface."
msgstr "在檢查支撐上方或下方是否有模型時，所採用步階的高度。值越低切片速度越慢，而較高的值會導致在部分應有支撐介面的位置列印一般的支撐。"

msgctxt "meshfix_fluid_motion_enabled description"
msgid "When enabled tool paths are corrected for printers with smooth motion planners. Small movements that deviate from the general tool path direction are smoothed to improve fluid motions."
msgstr ""

msgctxt "infill_enable_travel_optimization description"
msgid "When enabled, the order in which the infill lines are printed is optimized to reduce the distance travelled. The reduction in travel time achieved very much depends on the model being sliced, infill pattern, density, etc. Note that, for some models that have many small areas of infill, the time to slice the model may be greatly increased."
msgstr "當功能啟用時，填充線條的列印順序會對降低空跑距離做最佳化。所能減少的空跑時間取決於模型、填充樣式、填充密度等。請注意，對於有很多小型填充區域的模型，切片時間可能會大量增加。"

msgctxt "support_fan_enable description"
msgid "When enabled, the print cooling fan speed is altered for the skin regions immediately above the support."
msgstr "啟用後，列印支撐上方表層的風扇轉速會發生變化。"

msgctxt "z_seam_relative description"
msgid "When enabled, the z seam coordinates are relative to each part's centre. When disabled, the coordinates define an absolute position on the build plate."
msgstr "啟用時，Z 接縫座標為相對於各個部分中心的值。關閉時，座標固定在列印平台上的一個絕對位置。"

msgctxt "retraction_combing_max_distance description"
msgid "When greater than zero, combing travel moves that are longer than this distance will use retraction. If set to zero, there is no maximum and combing moves will not use retraction."
msgstr "觸發回抽時之最小距離，如大於此數值，便開啟回抽；如設置為0，則關閉回抽."

msgctxt "hole_xy_offset_max_diameter description"
msgid "When greater than zero, the Hole Horizontal Expansion is gradually applied on small holes (small holes are expanded more). When set to zero the Hole Horizontal Expansion will be applied to all holes. Holes larger than the Hole Horizontal Expansion Max Diameter are not expanded."
msgstr ""

msgctxt "hole_xy_offset description"
msgid "When greater than zero, the Hole Horizontal Expansion is the amount of offset applied to all holes in each layer. Positive values increase the size of the holes, negative values reduce the size of the holes. When this setting is enabled it can be further tuned with Hole Horizontal Expansion Max Diameter."
msgstr ""

msgctxt "bridge_skin_material_flow description"
msgid "When printing bridge skin regions, the amount of material extruded is multiplied by this value."
msgstr "列印橋樑表層區域時，擠出的線材量乘以此值。"

msgctxt "bridge_wall_material_flow description"
msgid "When printing bridge walls, the amount of material extruded is multiplied by this value."
msgstr "列印橋樑牆壁時，擠出的線材量乘以此值。"

msgctxt "bridge_skin_material_flow_2 description"
msgid "When printing the second bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "列印橋樑表層區域第二層時，擠出的線材量乘以此值。"

msgctxt "bridge_skin_material_flow_3 description"
msgid "When printing the third bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "列印橋樑表層區域第三層時，擠出的線材量乘以此值。"

msgctxt "cool_lift_head description"
msgid "When the minimum speed is hit because of minimum layer time, lift the head away from the print and wait the extra time until the minimum layer time is reached."
msgstr "當“最短單層列印時間”受到“最低列印速度”限制時，將噴頭從模型上抬高，並等候達到最短單層列印時間。"

msgctxt "skin_no_small_gaps_heuristic description"
msgid "When the model has small vertical gaps of only a few layers, there should normally be skin around those layers in the narrow space. Enable this setting to not generate skin if the vertical gap is very small. This improves printing time and slicing time, but technically leaves infill exposed to the air."
msgstr "當模型具有僅幾層的小垂直間隙時，通常在那些層周圍的狹窄空間中應該存在表層。如果垂直間隙非常小，啟用此設定會停止自動產生表層。這樣可以縮短列印時間和切片時間，但技術上會使填充暴露出來。"

msgctxt "wall_transition_angle description"
msgid "When to create transitions between even and odd numbers of walls. A wedge shape with an angle greater than this setting will not have transitions and no walls will be printed in the center to fill the remaining space. Reducing this setting reduces the number and length of these center walls, but may leave gaps or overextrude."
msgstr ""

msgctxt "wall_transition_length description"
msgid "When transitioning between different numbers of walls as the part becomes thinner, a certain amount of space is allotted to split or join the wall lines."
msgstr ""

msgctxt "wipe_hop_enable description"
msgid "When wiping, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "擦拭時列印平台會降低以便在噴頭和列印品之間形成空隙。它可以防止噴頭在空跑過程中撞到列印品，降低將列印品從列印平台撞掉的幾率。"

msgctxt "retraction_hop_enabled description"
msgid "Whenever a retraction is done, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "每當回抽完成時，列印平台會降低以便在噴頭和列印品之間形成空隙。它可以防止噴頭在空跑過程中撞到列印品，降低將列印品從列印平台撞掉的幾率。"

msgctxt "support_xy_overrides_z description"
msgid "Whether the Support X/Y Distance overrides the Support Z Distance or vice versa. When X/Y overrides Z the X/Y distance can push away the support from the model, influencing the actual Z distance to the overhang. We can disable this by not applying the X/Y distance around overhangs."
msgstr "支撐 X/Y 間距是否優先於支撐 Z 間距的設定。當 X/Y 間距優先於 Z 時，X/Y 間距可將支撐從模型上推離，同時影響與突出部分之間的實際 Z 間距。我們可以通過不在突出部分周圍套用 X/Y 間距來關閉此選項。"

msgctxt "machine_center_is_zero description"
msgid "Whether the X/Y coordinates of the zero position of the printer is at the center of the printable area."
msgstr "印表機的 X/Y 座標原點是否位於可列印區域的中心。"

msgctxt "machine_endstop_positive_direction_x description"
msgid "Whether the endstop of the X axis is in the positive direction (high X coordinate) or negative (low X coordinate)."
msgstr "X 軸的限位開關位於正向（X 座標值大）還是負向（X 座標值小）。"

msgctxt "machine_endstop_positive_direction_y description"
msgid "Whether the endstop of the Y axis is in the positive direction (high Y coordinate) or negative (low Y coordinate)."
msgstr "Y 軸的限位開關位於正向（Y 座標值大）還是負向（Y 座標值小）。"

msgctxt "machine_endstop_positive_direction_z description"
msgid "Whether the endstop of the Z axis is in the positive direction (high Z coordinate) or negative (low Z coordinate)."
msgstr "Z 軸的限位開關位於正向（Z 座標值大）還是負向（Z 座標值小）。"

msgctxt "machine_extruders_share_heater description"
msgid "Whether the extruders share a single heater rather than each extruder having its own heater."
msgstr "擠出機共用一個加熱器，而不是每個擠出機都有獨立的加熱器。"

msgctxt "machine_extruders_share_nozzle description"
msgid "Whether the extruders share a single nozzle rather than each extruder having its own nozzle. When set to true, it is expected that the printer-start gcode script properly sets up all extruders in an initial retraction state that is known and mutually compatible (either zero or one filament not retracted); in that case the initial retraction status is described, per extruder, by the 'machine_extruders_shared_nozzle_initial_retraction' parameter."
msgstr "擠出機共用一個噴頭，而不是每個擠出機都有獨立的噴頭。當設置為\"同意\"時,預期\"Printer-start G-code\"已妥善設定擠出機之初始回抽值互相相容(設置為0或其一線材不回抽); 在此種情況下,每個擠出機之初始回抽值已透過機器.擠出機.共用噴頭.初始回抽值等參數描述設定."

msgctxt "machine_heated_bed description"
msgid "Whether the machine has a heated build plate present."
msgstr "機器是否有熱床。"

msgctxt "machine_heated_build_volume description"
msgid "Whether the machine is able to stabilize the build volume temperature."
msgstr "機器是否能夠穩定列印空間溫度。"

msgctxt "center_object description"
msgid "Whether to center the object on the middle of the build platform (0,0), instead of using the coordinate system in which the object was saved."
msgstr "是否將模型放置在列印平台中心 (0,0)，而不是使用模型內儲存的座標系統。"

msgctxt "machine_nozzle_temp_enabled description"
msgid "Whether to control temperature from Cura. Turn this off to control nozzle temperature from outside of Cura."
msgstr "是否從 Cura 控制溫度。若要從 Cura 外部控制噴頭溫度，關閉此選項。"

msgctxt "material_bed_temp_prepend description"
msgid "Whether to include build plate temperature commands at the start of the gcode. When the start_gcode already contains build plate temperature commands Cura frontend will automatically disable this setting."
msgstr "是否需要在 G-code 開始部分插入熱床溫度的命令。當起始 G-code 包含熱床溫度命令時，Cura 前端將自動關閉此設定。"

msgctxt "material_print_temp_prepend description"
msgid "Whether to include nozzle temperature commands at the start of the gcode. When the start_gcode already contains nozzle temperature commands Cura frontend will automatically disable this setting."
msgstr "是否在 G-code 開始部分插入噴頭溫度命令。當起始 G-code 已包含噴頭溫度命令時，Cura 前端將自動關閉此設定。"

msgctxt "clean_between_layers description"
msgid "Whether to include nozzle wipe G-Code between layers (maximum 1 per layer). Enabling this setting could influence behavior of retract at layer change. Please use Wipe Retraction settings to control retraction at layers where the wipe script will be working."
msgstr "是否在層與層之間加入擦拭噴頭的 G-code（每層最多一次）。啟用此設定會影響換層時的回抽行為。請用「擦拭回抽」設定來控制何處使用擦拭腳本。"

msgctxt "material_bed_temp_wait description"
msgid "Whether to insert a command to wait until the build plate temperature is reached at the start."
msgstr "是否插入一條命令，在開始時等待列印平台達到設定溫度。"

msgctxt "prime_blob_enable description"
msgid "Whether to prime the filament with a blob before printing. Turning this setting on will ensure that the extruder will have material ready at the nozzle before printing. Printing Brim or Skirt can act like priming too, in which case turning this setting off saves some time."
msgstr "列印前是否裝填少量的線材。開啟此設定將確保列印前擠出機的噴頭處已準備好線材。列印邊緣或外圍也可作為裝填用途，這種情况下關閉此設定可以節省時間。"

msgctxt "print_sequence description"
msgid "Whether to print all models one layer at a time or to wait for one model to finish, before moving on to the next. One at a time mode is possible if a) only one extruder is enabled and b) all models are separated in such a way that the whole print head can move in between and all models are lower than the distance between the nozzle and the X/Y axes."
msgstr "選擇一次列印一層中的所有模型或等待一個模型完成後再轉到下一個模型。只有在 a) 只使用一個擠出機而且 b) 所有模型以整個列印頭可以在各個模型之間移動的方式分隔開，且所有模型都低於噴頭和 X / Y 軸之間距離的情况下，排隊列印才可使用。"

msgctxt "machine_show_variants description"
msgid "Whether to show the different variants of this machine, which are described in separate json files."
msgstr "是否顯示這台印表機在不同的 JSON 檔案中所描述的型號。"

msgctxt "machine_firmware_retract description"
msgid "Whether to use firmware retract commands (G10/G11) instead of using the E property in G1 commands to retract the material."
msgstr "是否使用韌體回抽命令（G10/G11）取代 G1 命令的 E 參數來回抽線材。"

msgctxt "material_print_temp_wait description"
msgid "Whether to wait until the nozzle temperature is reached at the start."
msgstr "是否在開始時等待噴頭達到設定溫度。"

msgctxt "infill_line_width description"
msgid "Width of a single infill line."
msgstr "單一填充線寬。"

msgctxt "support_interface_line_width description"
msgid "Width of a single line of support roof or floor."
msgstr "支撐頂板或底板單一線寬。"

msgctxt "roofing_line_width description"
msgid "Width of a single line of the areas at the top of the print."
msgstr "列印頂部區域單一線寬。"

msgctxt "line_width description"
msgid "Width of a single line. Generally, the width of each line should correspond to the width of the nozzle. However, slightly reducing this value could produce better prints."
msgstr "單一線寬。一般而言，每條線條的寬度應與噴頭的寬度對應。但是，稍微降低此值可以產生更好的列印成果。"

msgctxt "prime_tower_line_width description"
msgid "Width of a single prime tower line."
msgstr "單一換料塔線寬。"

msgctxt "skirt_brim_line_width description"
msgid "Width of a single skirt or brim line."
msgstr "單一外圍或邊緣的線寬。"

msgctxt "support_bottom_line_width description"
msgid "Width of a single support floor line."
msgstr "單一支撐底板線寬。"

msgctxt "support_roof_line_width description"
msgid "Width of a single support roof line."
msgstr "單一支撐頂板線寬。"

msgctxt "support_line_width description"
msgid "Width of a single support structure line."
msgstr "單一支撐線寬。"

msgctxt "skin_line_width description"
msgid "Width of a single top/bottom line."
msgstr "單一頂部/底部線寬。"

msgctxt "wall_line_width_x description"
msgid "Width of a single wall line for all wall lines except the outermost one."
msgstr "除了外壁以外牆壁的線寬。"

msgctxt "wall_line_width description"
msgid "Width of a single wall line."
msgstr "單層牆壁線寬。"

msgctxt "raft_base_line_width description"
msgid "Width of the lines in the base raft layer. These should be thick lines to assist in build plate adhesion."
msgstr "木筏底部的線寬。這些線條應該是粗線，以便協助列印平台附著。"

msgctxt "raft_interface_line_width description"
msgid "Width of the lines in the middle raft layer. Making the second layer extrude more causes the lines to stick to the build plate."
msgstr "木筏中層的線寬。第二層擠出多一些會讓線條附著在列印平台上。"

msgctxt "raft_surface_line_width description"
msgid "Width of the lines in the top surface of the raft. These can be thin lines so that the top of the raft becomes smooth."
msgstr "木筏頂部表面的線寬。這些線條可以是細線，以便讓木筏頂部變得平滑。"

msgctxt "wall_line_width_0 description"
msgid "Width of the outermost wall line. By lowering this value, higher levels of detail can be printed."
msgstr "最外側牆壁的線寬。降低此值，可列印出更高水準的細節。"

msgctxt "min_bead_width description"
msgid "Width of the wall that will replace thin features (according to the Minimum Feature Size) of the model. If the Minimum Wall Line Width is thinner than the thickness of the feature, the wall will become as thick as the feature itself."
msgstr ""

msgctxt "wipe_brush_pos_x label"
msgid "Wipe Brush X Position"
msgstr "擦拭刷 X 軸位置"

msgctxt "wipe_hop_speed label"
msgid "Wipe Hop Speed"
msgstr "擦拭 Z 抬升速度"

msgctxt "prime_tower_wipe_enabled label"
msgid "Wipe Inactive Nozzle on Prime Tower"
msgstr "在換料塔上擦拭非作用中的噴頭"

msgctxt "wipe_move_distance label"
msgid "Wipe Move Distance"
msgstr "擦拭移動距離"

msgctxt "clean_between_layers label"
msgid "Wipe Nozzle Between Layers"
msgstr "換層時擦拭噴頭"

msgctxt "wipe_pause label"
msgid "Wipe Pause"
msgstr "擦拭暫停"

msgctxt "wipe_repeat_count label"
msgid "Wipe Repeat Count"
msgstr "擦拭重覆次數"

msgctxt "wipe_retraction_amount label"
msgid "Wipe Retraction Distance"
msgstr "擦拭回抽距離"

msgctxt "wipe_retraction_enable label"
msgid "Wipe Retraction Enable"
msgstr "擦拭回抽啟用"

msgctxt "wipe_retraction_extra_prime_amount label"
msgid "Wipe Retraction Extra Prime Amount"
msgstr "擦拭回抽額外裝填量"

msgctxt "wipe_retraction_prime_speed label"
msgid "Wipe Retraction Prime Speed"
msgstr "擦拭回抽裝填速度"

msgctxt "wipe_retraction_retract_speed label"
msgid "Wipe Retraction Retract Speed"
msgstr "擦拭回抽回抽速度"

msgctxt "wipe_retraction_speed label"
msgid "Wipe Retraction Speed"
msgstr "擦拭回抽速度"

msgctxt "wipe_hop_enable label"
msgid "Wipe Z Hop"
msgstr "擦拭 Z 抬升"

msgctxt "wipe_hop_amount label"
msgid "Wipe Z Hop Height"
msgstr "擦拭 Z 抬升高度"

msgctxt "retraction_combing option infill"
msgid "Within Infill"
msgstr "內部填充"

msgctxt "machine_always_write_active_tool description"
msgid "Write active tool after sending temp commands to inactive tool. Required for Dual Extruder printing with Smoothie or other firmware with modal tool commands."
msgstr "向未啟用工具發送溫度命令後寫入啟用工具。使用 Smoothie 或是使用 modal tool 命令的韌體做雙擠出機列印時，此功能是必需開啟的。"

msgctxt "machine_endstop_positive_direction_x label"
msgid "X Endstop in Positive Direction"
msgstr "X 限位開關位於正向"

msgctxt "wipe_brush_pos_x description"
msgid "X location where wipe script will start."
msgstr "擦拭動作開始的 X 位置。"

msgctxt "support_xy_overrides_z option xy_overrides_z"
msgid "X/Y overrides Z"
msgstr "X/Y 優先 Z"

msgctxt "machine_endstop_positive_direction_y label"
msgid "Y Endstop in Positive Direction"
msgstr "Y 限位開關位於正向"

msgctxt "machine_endstop_positive_direction_z label"
msgid "Z Endstop in Positive Direction"
msgstr "Z 限位開關位於正向"

msgctxt "retraction_hop_after_extruder_switch label"
msgid "Z Hop After Extruder Switch"
msgstr "擠出機切換後的 Z 抬升"

msgctxt "retraction_hop_after_extruder_switch_height label"
msgid "Z Hop After Extruder Switch Height"
msgstr "擠出機切換後的 Z 抬升高度"

msgctxt "retraction_hop label"
msgid "Z Hop Height"
msgstr "Z 抬升高度"

msgctxt "retraction_hop_only_when_collides label"
msgid "Z Hop Only Over Printed Parts"
msgstr "僅在已列印部分上 Z 抬升"

msgctxt "speed_z_hop label"
msgid "Z Hop Speed"
msgstr "Z 抬升速度"

msgctxt "retraction_hop_enabled label"
msgid "Z Hop When Retracted"
msgstr "回抽時 Z 抬升"

msgctxt "z_seam_type label"
msgid "Z Seam Alignment"
msgstr "Z 接縫對齊"

msgctxt "z_seam_position label"
msgid "Z Seam Position"
msgstr "Z 接縫位置"

msgctxt "z_seam_relative label"
msgid "Z Seam Relative"
msgstr "Z 接縫相對"

msgctxt "z_seam_x label"
msgid "Z Seam X"
msgstr "Z 接縫 X 座標"

msgctxt "z_seam_y label"
msgid "Z Seam Y"
msgstr "Z 接縫 Y 座標"

msgctxt "support_xy_overrides_z option z_overrides_xy"
msgid "Z overrides X/Y"
msgstr "Z 優先 X/Y"

msgctxt "infill_pattern option zigzag"
msgid "Zig Zag"
msgstr "鋸齒狀"

msgctxt "ironing_pattern option zigzag"
msgid "Zig Zag"
msgstr "鋸齒狀"

msgctxt "roofing_pattern option zigzag"
msgid "Zig Zag"
msgstr "鋸齒狀"

msgctxt "support_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "鋸齒狀"

msgctxt "support_interface_pattern option zigzag"
msgid "Zig Zag"
msgstr "鋸齒狀"

msgctxt "support_pattern option zigzag"
msgid "Zig Zag"
msgstr "鋸齒狀"

msgctxt "support_roof_pattern option zigzag"
msgid "Zig Zag"
msgstr "鋸齒狀"

msgctxt "top_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "鋸齒狀"

msgctxt "top_bottom_pattern_0 option zigzag"
msgid "Zig Zag"
msgstr "鋸齒狀"

msgctxt "travel description"
msgid "travel"
msgstr "空跑"

msgctxt "gradual_flow_discretisation_step_size description"
msgid "Duration of each step in the gradual flow change"
msgstr ""

msgctxt "gradual_flow_enabled description"
msgid "Enable gradual flow changes. When enabled, the flow is gradually increased/decreased to the target flow. This is useful for printers with a bowden tube where the flow is not immediately changed when the extruder motor starts/stops."
msgstr ""

msgctxt "reset_flow_duration description"
msgid "For any travel move longer than this value, the material flow is reset to the paths target flow"
msgstr ""

msgctxt "gradual_flow_discretisation_step_size label"
msgid "Gradual flow discretisation step size"
msgstr ""

msgctxt "gradual_flow_enabled label"
msgid "Gradual flow enabled"
msgstr ""

msgctxt "max_flow_acceleration label"
msgid "Gradual flow max acceleration"
msgstr ""

msgctxt "layer_0_max_flow_acceleration label"
msgid "Initial layer max flow acceleration"
msgstr ""

msgctxt "max_flow_acceleration description"
msgid "Maximum acceleration for gradual flow changes"
msgstr ""

msgctxt "layer_0_max_flow_acceleration description"
msgid "Minimum speed for gradual flow changes for the first layer"
msgstr ""

msgctxt "reset_flow_duration label"
msgid "Reset flow duration"
msgstr ""
