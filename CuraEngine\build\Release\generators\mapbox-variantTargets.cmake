# Load the debug and release variables
file(GLOB DATA_FILES "${CMAKE_CURRENT_LIST_DIR}/mapbox-variant-*-data.cmake")

foreach(f ${DATA_FILES})
    include(${f})
endforeach()

# Create the targets for all the components
foreach(_COMPONENT ${mapbox-variant_COMPONENT_NAMES} )
    if(NOT TARGET ${_COMPONENT})
        add_library(${_COMPONENT} INTERFACE IMPORTED)
        message(${mapbox-variant_MESSAGE_MODE} "Conan: Component target declared '${_COMPONENT}'")
    endif()
endforeach()

if(NOT TARGET mapbox-variant::mapbox-variant)
    add_library(mapbox-variant::mapbox-variant INTERFACE IMPORTED)
    message(${mapbox-variant_MESSAGE_MODE} "Conan: Target declared 'mapbox-variant::mapbox-variant'")
endif()
# Load the debug and release library finders
file(GLOB CONFIG_FILES "${CMAKE_CURRENT_LIST_DIR}/mapbox-variant-Target-*.cmake")

foreach(f ${CONFIG_FILES})
    include(${f})
endforeach()