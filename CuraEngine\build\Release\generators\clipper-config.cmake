########## MACROS ###########################################################################
#############################################################################################

# Requires CMake > 3.15
if(${CMAKE_VERSION} VERSION_LESS "3.15")
    message(FATAL_ERROR "The 'CMakeDeps' generator only works with CMake >= 3.15")
endif()

if(clipper_FIND_QUIETLY)
    set(clipper_MESSAGE_MODE VERBOSE)
else()
    set(clipper_MESSAGE_MODE STATUS)
endif()

include(${CMAKE_CURRENT_LIST_DIR}/cmakedeps_macros.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/clipperTargets.cmake)
include(CMakeFindDependencyMacro)

check_build_type_defined()

foreach(_DEPENDENCY ${clipper_FIND_DEPENDENCY_NAMES} )
    # Check that we have not already called a find_package with the transitive dependency
    if(NOT ${_DEPENDENCY}_FOUND)
        find_dependency(${_DEPENDENCY} REQUIRED ${${_DEPENDENCY}_FIND_MODE})
    endif()
endforeach()

set(clipper_VERSION_STRING "6.4.2")
set(clipper_INCLUDE_DIRS ${clipper_INCLUDE_DIRS_RELEASE} )
set(clipper_INCLUDE_DIR ${clipper_INCLUDE_DIRS_RELEASE} )
set(clipper_LIBRARIES ${clipper_LIBRARIES_RELEASE} )
set(clipper_DEFINITIONS ${clipper_DEFINITIONS_RELEASE} )


# Only the last installed configuration BUILD_MODULES are included to avoid the collision
foreach(_BUILD_MODULE ${clipper_BUILD_MODULES_PATHS_RELEASE} )
    message(${clipper_MESSAGE_MODE} "Conan: Including build module from '${_BUILD_MODULE}'")
    include(${_BUILD_MODULE})
endforeach()


