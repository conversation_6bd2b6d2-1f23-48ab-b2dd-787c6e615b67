message(STATUS "Conan: Using CMakeDeps conandeps_legacy.cmake aggregator via include()")
message(STATUS "Conan: It is recommended to use explicit find_package() per dependency instead")

find_package(scripta)
find_package(arcus)
find_package(semver)
find_package(curaengine_grpc_definitions)
find_package(clipper)
find_package(Boost)
find_package(RapidJSON)
find_package(stb)
find_package(spdlog)
find_package(fmt)
find_package(range-v3)
find_package(ZLIB)
find_package(mapbox-wagyu)
find_package(standardprojectsettings)

set(CONANDEPS_LEGACY  scripta::scripta  arcus::arcus  semver::semver  curaengine_grpc_definitions::curaengine_grpc_definitions  clipper::clipper  boost::boost  rapidjson  stb::stb  spdlog::spdlog  fmt::fmt  range-v3::range-v3  ZLIB::ZLIB  mapbox-wagyu::mapbox-wagyu  standardprojectsettings::standardprojectsettings )