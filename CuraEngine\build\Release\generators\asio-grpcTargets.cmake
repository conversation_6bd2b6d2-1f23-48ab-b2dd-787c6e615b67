# Load the debug and release variables
file(GLOB DATA_FILES "${CMAKE_CURRENT_LIST_DIR}/asio-grpc-*-data.cmake")

foreach(f ${DATA_FILES})
    include(${f})
endforeach()

# Create the targets for all the components
foreach(_COMPONENT ${asio-grpc_COMPONENT_NAMES} )
    if(NOT TARGET ${_COMPONENT})
        add_library(${_COMPONENT} INTERFACE IMPORTED)
        message(${asio-grpc_MESSAGE_MODE} "Conan: Component target declared '${_COMPONENT}'")
    endif()
endforeach()

if(NOT TARGET asio-grpc::asio-grpc)
    add_library(asio-grpc::asio-grpc INTERFACE IMPORTED)
    message(${asio-grpc_MESSAGE_MODE} "Conan: Target declared 'asio-grpc::asio-grpc'")
endif()
# Load the debug and release library finders
file(GLOB CONFIG_FILES "${CMAKE_CURRENT_LIST_DIR}/asio-grpc-Target-*.cmake")

foreach(f ${CONFIG_FILES})
    include(${f})
endforeach()