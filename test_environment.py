#!/usr/bin/env python3
"""
Cura Development Environment Test Script
测试Cura开发环境是否正确配置
"""

import sys
import os
import subprocess
from pathlib import Path

def test_python_version():
    """测试Python版本"""
    print("=== Python版本测试 ===")
    version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"Python版本: {version}")
    
    if sys.version_info >= (3, 12):
        print("✅ Python版本符合要求 (3.12+)")
        return True
    else:
        print("❌ Python版本不符合要求，需要3.12+")
        return False

def test_virtual_environment():
    """测试虚拟环境"""
    print("\n=== 虚拟环境测试 ===")
    
    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 运行在虚拟环境中")
        print(f"虚拟环境路径: {sys.prefix}")
        return True
    else:
        print("❌ 未在虚拟环境中运行")
        return False

def test_cura_imports():
    """测试Cura相关模块导入"""
    print("\n=== Cura模块导入测试 ===")
    
    modules_to_test = [
        ("PyQt6", "PyQt6"),
        ("numpy", "numpy"),
        ("scipy", "scipy"),
        ("trimesh", "trimesh"),
        ("shapely", "shapely"),
        ("requests", "requests"),
        ("twisted", "twisted"),
        ("cryptography", "cryptography"),
        ("pySavitar", "pySavitar"),
        ("pyArcus", "pyArcus"),
        ("pynest2d", "pynest2d"),
    ]
    
    success_count = 0
    for display_name, module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {display_name}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {display_name}: {e}")
    
    print(f"\n模块导入成功率: {success_count}/{len(modules_to_test)}")
    return success_count == len(modules_to_test)

def test_uranium_import():
    """测试Uranium模块导入"""
    print("\n=== Uranium模块导入测试 ===")
    
    try:
        import UM
        print("✅ Uranium (UM) 模块导入成功")
        
        # 测试一些核心Uranium模块
        from UM.Application import Application
        from UM.Scene.Scene import Scene
        from UM.Settings.ContainerRegistry import ContainerRegistry
        print("✅ Uranium核心模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ Uranium模块导入失败: {e}")
        return False

def test_cura_import():
    """测试Cura模块导入"""
    print("\n=== Cura模块导入测试 ===")
    
    try:
        import cura
        print("✅ Cura模块导入成功")
        
        # 测试一些核心Cura模块
        from cura.CuraApplication import CuraApplication
        from cura.Settings.CuraStackBuilder import CuraStackBuilder
        print("✅ Cura核心模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ Cura模块导入失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n=== 文件结构测试 ===")
    
    required_files = [
        "cura_app.py",
        "build_windows/generators/cura_venv",
        "build_windows/generators/virtual_python_env.bat",
        ".run/cura.run.xml",
    ]
    
    success_count = 0
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
            success_count += 1
        else:
            print(f"❌ {file_path} 不存在")
    
    print(f"\n文件结构检查: {success_count}/{len(required_files)}")
    return success_count == len(required_files)

def test_uranium_editable():
    """测试Uranium可编辑模式"""
    print("\n=== Uranium可编辑模式测试 ===")
    
    try:
        result = subprocess.run(['conan', 'editable', 'list'], 
                              capture_output=True, text=True, check=True)
        
        if 'uranium/5.11.0-alpha.0' in result.stdout:
            print("✅ Uranium已设置为可编辑模式")
            return True
        else:
            print("❌ Uranium未设置为可编辑模式")
            return False
    except subprocess.CalledProcessError as e:
        print(f"❌ 无法检查Uranium可编辑模式: {e}")
        return False

def main():
    """主函数"""
    print("Cura开发环境测试")
    print("=" * 50)
    
    tests = [
        test_python_version(),
        test_virtual_environment(),
        test_file_structure(),
        test_cura_imports(),
        test_uranium_import(),
        test_cura_import(),
        test_uranium_editable(),
    ]
    
    passed = sum(tests)
    total = len(tests)
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！Cura开发环境配置成功！")
        print("\n下一步:")
        print("1. 在PyCharm中打开Cura项目")
        print("2. 配置Python解释器为: build_windows/generators/cura_venv/Scripts/python.exe")
        print("3. 使用.run文件夹中的运行配置")
        print("4. 开始开发！")
        return True
    else:
        print(f"❌ {total - passed} 项测试失败，请检查配置")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        sys.exit(1)
