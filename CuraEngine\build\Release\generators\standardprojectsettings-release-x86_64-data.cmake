########### AGGREGATED COMPONENTS AND DEPENDENCIES FOR THE MULTI CONFIG #####################
#############################################################################################

set(standardprojectsettings_COMPONENT_NAMES "")
if(DEFINED standardprojectsettings_FIND_DEPENDENCY_NAMES)
  list(APPEND standardprojectsettings_FIND_DEPENDENCY_NAMES )
  list(REMOVE_DUPLICATES standardprojectsettings_FIND_DEPENDENCY_NAMES)
else()
  set(standardprojectsettings_FIND_DEPENDENCY_NAMES )
endif()

########### VARIABLES #######################################################################
#############################################################################################
set(standardprojectsettings_PACKAGE_FOLDER_RELEASE "C:/Users/<USER>/.conan2/p/standb1cf9a2ad7ef9/p")
set(standardprojectsettings_BUILD_MODULES_PATHS_RELEASE "${standardprojectsettings_PACKAGE_FOLDER_RELEASE}/res/cmake/StandardProjectSettings.cmake")


set(standardprojectsettings_INCLUDE_DIRS_RELEASE )
set(standardprojectsettings_RES_DIRS_RELEASE )
set(standardprojectsettings_DEFINITIONS_RELEASE )
set(standardprojectsettings_SHARED_LINK_FLAGS_RELEASE )
set(standardprojectsettings_EXE_LINK_FLAGS_RELEASE )
set(standardprojectsettings_OBJECTS_RELEASE )
set(standardprojectsettings_COMPILE_DEFINITIONS_RELEASE )
set(standardprojectsettings_COMPILE_OPTIONS_C_RELEASE )
set(standardprojectsettings_COMPILE_OPTIONS_CXX_RELEASE )
set(standardprojectsettings_LIB_DIRS_RELEASE )
set(standardprojectsettings_BIN_DIRS_RELEASE )
set(standardprojectsettings_LIBRARY_TYPE_RELEASE UNKNOWN)
set(standardprojectsettings_IS_HOST_WINDOWS_RELEASE 0)
set(standardprojectsettings_LIBS_RELEASE )
set(standardprojectsettings_SYSTEM_LIBS_RELEASE )
set(standardprojectsettings_FRAMEWORK_DIRS_RELEASE )
set(standardprojectsettings_FRAMEWORKS_RELEASE )
set(standardprojectsettings_BUILD_DIRS_RELEASE )
set(standardprojectsettings_NO_SONAME_MODE_RELEASE FALSE)


# COMPOUND VARIABLES
set(standardprojectsettings_COMPILE_OPTIONS_RELEASE
    "$<$<COMPILE_LANGUAGE:CXX>:${standardprojectsettings_COMPILE_OPTIONS_CXX_RELEASE}>"
    "$<$<COMPILE_LANGUAGE:C>:${standardprojectsettings_COMPILE_OPTIONS_C_RELEASE}>")
set(standardprojectsettings_LINKER_FLAGS_RELEASE
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,SHARED_LIBRARY>:${standardprojectsettings_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,MODULE_LIBRARY>:${standardprojectsettings_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,EXECUTABLE>:${standardprojectsettings_EXE_LINK_FLAGS_RELEASE}>")


set(standardprojectsettings_COMPONENTS_RELEASE )