########### AGGREGATED COMPONENTS AND DEPENDENCIES FOR THE MULTI CONFIG #####################
#############################################################################################

set(mapbox-geometry_COMPONENT_NAMES "")
if(DEFINED mapbox-geometry_FIND_DEPENDENCY_NAMES)
  list(APPEND mapbox-geometry_FIND_DEPENDENCY_NAMES mapbox-variant)
  list(REMOVE_DUPLICATES mapbox-geometry_FIND_DEPENDENCY_NAMES)
else()
  set(mapbox-geometry_FIND_DEPENDENCY_NAMES mapbox-variant)
endif()
set(mapbox-variant_FIND_MODE "NO_MODULE")

########### VARIABLES #######################################################################
#############################################################################################
set(mapbox-geometry_PACKAGE_FOLDER_RELEASE "C:/Users/<USER>/.conan2/p/mapbofe72fb50cf7c4/p")
set(mapbox-geometry_BUILD_MODULES_PATHS_RELEASE )


set(mapbox-geometry_INCLUDE_DIRS_RELEASE "${mapbox-geometry_PACKAGE_FOLDER_RELEASE}/include")
set(mapbox-geometry_RES_DIRS_RELEASE )
set(mapbox-geometry_DEFINITIONS_RELEASE )
set(mapbox-geometry_SHARED_LINK_FLAGS_RELEASE )
set(mapbox-geometry_EXE_LINK_FLAGS_RELEASE )
set(mapbox-geometry_OBJECTS_RELEASE )
set(mapbox-geometry_COMPILE_DEFINITIONS_RELEASE )
set(mapbox-geometry_COMPILE_OPTIONS_C_RELEASE )
set(mapbox-geometry_COMPILE_OPTIONS_CXX_RELEASE )
set(mapbox-geometry_LIB_DIRS_RELEASE )
set(mapbox-geometry_BIN_DIRS_RELEASE )
set(mapbox-geometry_LIBRARY_TYPE_RELEASE UNKNOWN)
set(mapbox-geometry_IS_HOST_WINDOWS_RELEASE 1)
set(mapbox-geometry_LIBS_RELEASE )
set(mapbox-geometry_SYSTEM_LIBS_RELEASE )
set(mapbox-geometry_FRAMEWORK_DIRS_RELEASE )
set(mapbox-geometry_FRAMEWORKS_RELEASE )
set(mapbox-geometry_BUILD_DIRS_RELEASE )
set(mapbox-geometry_NO_SONAME_MODE_RELEASE FALSE)


# COMPOUND VARIABLES
set(mapbox-geometry_COMPILE_OPTIONS_RELEASE
    "$<$<COMPILE_LANGUAGE:CXX>:${mapbox-geometry_COMPILE_OPTIONS_CXX_RELEASE}>"
    "$<$<COMPILE_LANGUAGE:C>:${mapbox-geometry_COMPILE_OPTIONS_C_RELEASE}>")
set(mapbox-geometry_LINKER_FLAGS_RELEASE
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,SHARED_LIBRARY>:${mapbox-geometry_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,MODULE_LIBRARY>:${mapbox-geometry_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,EXECUTABLE>:${mapbox-geometry_EXE_LINK_FLAGS_RELEASE}>")


set(mapbox-geometry_COMPONENTS_RELEASE )