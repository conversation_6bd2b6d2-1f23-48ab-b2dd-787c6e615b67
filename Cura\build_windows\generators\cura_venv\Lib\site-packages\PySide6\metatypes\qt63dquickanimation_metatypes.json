[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "AbstractClipAnimator"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QAbstractClipAnimator"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "QAbstractClipAnimator is abstract"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QAbstractClipAnimatorForeign", "gadget": true, "lineNumber": 51, "qualifiedClassName": "QAbstractClipAnimatorForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ClipA<PERSON><PERSON><PERSON>"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QClipAnimator"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QClipAnimatorForeign", "gadget": true, "lineNumber": 60, "qualifiedClassName": "QClipAnimatorForeign"}, {"classInfos": [{"name": "QML.Element", "value": "BlendedClipAnimator"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QBlendedClipAnimator"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QBlendedClipAnimatorForeign", "gadget": true, "lineNumber": 68, "qualifiedClassName": "QBlendedClipAnimatorForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ChannelMapping"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QChannelMapping"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QChannelMappingForeign", "gadget": true, "lineNumber": 76, "qualifiedClassName": "QChannelMappingForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AbstractAnimationClip"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QAbstractAnimationClip"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "QAbstractAnimationClip is abstract"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QAbstractAnimationClipForeign", "gadget": true, "lineNumber": 84, "qualifiedClassName": "QAbstractAnimationClipForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AnimationClipLoader"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QAnimationClipLoader"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QAnimationClipLoaderForeign", "gadget": true, "lineNumber": 93, "qualifiedClassName": "QAnimationClipLoaderForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AnimationClip"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QAnimationClip"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QAnimationClipForeign", "gadget": true, "lineNumber": 101, "qualifiedClassName": "QAnimationClipForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ChannelMapper"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QChannelMapper"}, {"name": "QML.Extended", "value": "Qt3DAnimation::Animation::Quick::Quick3DChannelMapper"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QChannelMapperForeign", "gadget": true, "lineNumber": 109, "qualifiedClassName": "QChannelMapperForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AbstractClipBlendNode"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QAbstractClipBlendNode"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "QAbstractClipBlendNode is abstract"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QAbstractClipBlendNodeForeign", "gadget": true, "lineNumber": 118, "qualifiedClassName": "QAbstractClipBlendNodeForeign"}, {"classInfos": [{"name": "QML.Element", "value": "LerpClipBlend"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QLerpClipBlend"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QLerpClipBlendForeign", "gadget": true, "lineNumber": 127, "qualifiedClassName": "QLerpClipBlendForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AdditiveClipBlend"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QAdditiveClipBlend"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QAdditiveClipBlendForeign", "gadget": true, "lineNumber": 135, "qualifiedClassName": "QAdditiveClipBlendForeign"}, {"classInfos": [{"name": "QML.Element", "value": "ClipBlendValue"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QClipBlendValue"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QClipBlendValueForeign", "gadget": true, "lineNumber": 143, "qualifiedClassName": "QClipBlendValueForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Clock"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QClock"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QClockForeign", "gadget": true, "lineNumber": 151, "qualifiedClassName": "QClockForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AbstractAnimation"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QAbstractAnimation"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "AbstractAnimation is abstract"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QAbstractAnimationForeign", "gadget": true, "lineNumber": 159, "qualifiedClassName": "QAbstractAnimationForeign"}, {"classInfos": [{"name": "QML.Element", "value": "KeyframeAnimation"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QKeyframeAnimation"}, {"name": "QML.Extended", "value": "Qt3DAnimation::Quick::QQuick3DKeyframeAnimation"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QKeyframeAnimationForeign", "gadget": true, "lineNumber": 168, "qualifiedClassName": "QKeyframeAnimationForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AnimationGroup"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QAnimationGroup"}, {"name": "QML.Extended", "value": "Qt3DAnimation::Quick::QQuick3DAnimationGroup"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QAnimationGroupForeign", "gadget": true, "lineNumber": 177, "qualifiedClassName": "QAnimationGroupForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AnimationController"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QAnimationController"}, {"name": "QML.Extended", "value": "Qt3DAnimation::Quick::QQuick3DAnimationController"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QAnimationControllerForeign", "gadget": true, "lineNumber": 186, "qualifiedClassName": "QAnimationControllerForeign"}, {"classInfos": [{"name": "QML.Element", "value": "MorphingAnimation"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QMorphingAnimation"}, {"name": "QML.Extended", "value": "Qt3DAnimation::Quick::QQuick3DMorphingAnimation"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QMorphingAnimationForeign", "gadget": true, "lineNumber": 195, "qualifiedClassName": "QMorphingAnimationForeign"}, {"classInfos": [{"name": "QML.Element", "value": "MorphTarget"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QMorphTarget"}, {"name": "QML.Extended", "value": "Qt3DAnimation::Quick::QQuick3DMorphTarget"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QMorphTargetForeign", "gadget": true, "lineNumber": 204, "qualifiedClassName": "QMorphTargetForeign"}, {"classInfos": [{"name": "QML.Element", "value": "VertexBlendAnimation"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QVertexBlendAnimation"}, {"name": "QML.Extended", "value": "Qt3DAnimation::Quick::QQuick3DVertexBlendAnimation"}, {"name": "QML.AddedInVersion", "value": "521"}], "className": "QVertexBlendAnimationForeign", "gadget": true, "lineNumber": 213, "qualifiedClassName": "QVertexBlendAnimationForeign"}, {"classInfos": [{"name": "QML.Element", "value": "AbstractChannelMapping"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QAbstractChannelMapping"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "QAbstractChannelMapping is abstract"}, {"name": "QML.AddedInVersion", "value": "522"}], "className": "QAbstractChannelMappingForeign", "gadget": true, "lineNumber": 222, "qualifiedClassName": "QAbstractChannelMappingForeign"}, {"classInfos": [{"name": "QML.Element", "value": "SkeletonMapping"}, {"name": "QML.Foreign", "value": "Qt3DAnimation::QSkeletonMapping"}, {"name": "QML.AddedInVersion", "value": "522"}], "className": "QSkeletonMappingForeign", "gadget": true, "lineNumber": 231, "qualifiedClassName": "QSkeletonMappingForeign"}], "inputFile": "qt3dquickanimationforeign_p.h", "outputRevision": 69}, {"classes": [{"className": "QQuick3DAnimationController", "lineNumber": 27, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "animationGroups", "read": "animationGroups", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DAnimation::QAnimationGroup>", "user": false}], "qualifiedClassName": "Qt3DAnimation::Quick::QQuick3DAnimationController", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3danimationcontroller_p.h", "outputRevision": 69}, {"classes": [{"className": "QQuick3DAnimationGroup", "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "animations", "read": "animations", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DAnimation::QAbstractAnimation>", "user": false}], "qualifiedClassName": "Qt3DAnimation::Quick::QQuick3DAnimationGroup", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3danimationgroup_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "DefaultProperty", "value": "mappings"}], "className": "Quick3DChannelMapper", "lineNumber": 29, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "mappings", "read": "qmlMappings", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DAnimation::QAbstractChannelMapping>", "user": false}], "qualifiedClassName": "Qt3DAnimation::Animation::Quick::Quick3DChannelMapper", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dchannelmapper_p.h", "outputRevision": 69}, {"classes": [{"className": "QQuick3DKeyframeAnimation", "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "keyframes", "read": "keyframes", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DCore::QTransform>", "user": false}], "qualifiedClassName": "Qt3DAnimation::Quick::QQuick3DKeyframeAnimation", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dkeyframeanimation_p.h", "outputRevision": 69}, {"classes": [{"className": "QQuick3DMorphingAnimation", "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "morphTargets", "read": "morphTargets", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DAnimation::QMorphTarget>", "user": false}], "qualifiedClassName": "Qt3DAnimation::Quick::QQuick3DMorphingAnimation", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dmorphinganimation_p.h", "outputRevision": 69}, {"classes": [{"className": "QQuick3DMorphTarget", "lineNumber": 27, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "attributes", "read": "attributes", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DCore::QAttribute>", "user": false}], "qualifiedClassName": "Qt3DAnimation::Quick::QQuick3DMorphTarget", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dmorphtarget_p.h", "outputRevision": 69}, {"classes": [{"className": "QQuick3DVertexBlendAnimation", "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "morphTargets", "read": "morphTargets", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<Qt3DAnimation::QMorphTarget>", "user": false}], "qualifiedClassName": "Qt3DAnimation::Quick::QQuick3DVertexBlendAnimation", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "quick3dvertexblendanimation_p.h", "outputRevision": 69}]