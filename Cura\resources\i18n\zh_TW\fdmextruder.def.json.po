# Cura JSON setting files
# Copyright (C) 2022 Ultimaker B.V.
# This file is distributed under the same license as the Cura package.
# <AUTHOR> <EMAIL>, 2022.
#
msgid ""
msgstr ""
"Project-Id-Version: Cura 5.1\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2023-06-08 16:32+0000\n"
"PO-Revision-Date: 2022-01-02 19:59+0800\n"
"Last-Translator: <PERSON><PERSON>  <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON> <<EMAIL>>\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 3.0\n"

msgctxt "platform_adhesion description"
msgid "Adhesion"
msgstr "附著"

msgctxt "material_diameter description"
msgid "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament."
msgstr "調整所用耗材的直徑。調整此值與所用耗材的直徑相匹配。"

msgctxt "platform_adhesion label"
msgid "Build Plate Adhesion"
msgstr "列印平台附著"

msgctxt "material_diameter label"
msgid "Diameter"
msgstr "直徑"

msgctxt "machine_extruder_end_code description"
msgid "End g-code to execute when switching away from this extruder."
msgstr "從此擠出機切換到其它擠出機時，要執行的結束 G-code。"

msgctxt "extruder_nr label"
msgid "Extruder"
msgstr "擠出機"

msgctxt "machine_extruder_end_code label"
msgid "Extruder End G-Code"
msgstr "擠出機結束 Gcode"

msgctxt "machine_extruder_end_pos_abs label"
msgid "Extruder End Position Absolute"
msgstr "擠出機終點絕對位置"

msgctxt "machine_extruder_end_pos_x label"
msgid "Extruder End Position X"
msgstr "擠出機結束位置 X 座標"

msgctxt "machine_extruder_end_pos_y label"
msgid "Extruder End Position Y"
msgstr "擠出機終點位置 Y 座標"

msgctxt "extruder_prime_pos_x label"
msgid "Extruder Prime X Position"
msgstr "擠出機 X 軸座標"

msgctxt "extruder_prime_pos_y label"
msgid "Extruder Prime Y Position"
msgstr "擠出機 Y 軸起始位置"

msgctxt "extruder_prime_pos_z label"
msgid "Extruder Prime Z Position"
msgstr "擠出機初始 Z 軸位置"

msgctxt "machine_extruder_cooling_fan_number label"
msgid "Extruder Print Cooling Fan"
msgstr "擠出機列印冷卻風扇"

msgctxt "machine_extruder_start_code label"
msgid "Extruder Start G-Code"
msgstr "擠出機起始 G-code"

msgctxt "machine_extruder_start_pos_abs label"
msgid "Extruder Start Position Absolute"
msgstr "擠出機起點絕對位置"

msgctxt "machine_extruder_start_pos_x label"
msgid "Extruder Start Position X"
msgstr "擠出機起始位置 X 座標"

msgctxt "machine_extruder_start_pos_y label"
msgid "Extruder Start Position Y"
msgstr "擠出機起始位置 Y 座標"

msgctxt "machine_settings label"
msgid "Machine"
msgstr "機型"

msgctxt "machine_settings description"
msgid "Machine specific settings"
msgstr "機器詳細設定"

msgctxt "machine_extruder_end_pos_abs description"
msgid "Make the extruder ending position absolute rather than relative to the last-known location of the head."
msgstr "讓擠出機以絕對位置為終點，而不是與前一次位置的相對位置。"

msgctxt "machine_extruder_start_pos_abs description"
msgid "Make the extruder starting position absolute rather than relative to the last-known location of the head."
msgstr "讓擠出機以絕對位置做為起點，而不是與前一次位置的相對位置。"

msgctxt "material description"
msgid "Material"
msgstr "線材"

msgctxt "material label"
msgid "Material"
msgstr "線材"

msgctxt "machine_nozzle_size label"
msgid "Nozzle Diameter"
msgstr "噴頭直徑"

msgctxt "machine_nozzle_id label"
msgid "Nozzle ID"
msgstr "噴頭 ID"

msgctxt "machine_nozzle_offset_x label"
msgid "Nozzle X Offset"
msgstr "噴頭 X 軸偏移量"

msgctxt "machine_nozzle_offset_y label"
msgid "Nozzle Y Offset"
msgstr "噴頭 Y 軸偏移量"

msgctxt "machine_extruder_start_code description"
msgid "Start g-code to execute when switching to this extruder."
msgstr "切換到此擠出機時，要執行的啟動 G-code。"

msgctxt "extruder_prime_pos_x description"
msgid "The X coordinate of the position where the nozzle primes at the start of printing."
msgstr "列印開始時，噴頭在 X 軸上初始位置。"

msgctxt "extruder_prime_pos_y description"
msgid "The Y coordinate of the position where the nozzle primes at the start of printing."
msgstr "列印開始時，噴頭在 Y 軸座標上初始位置。"

msgctxt "extruder_prime_pos_z description"
msgid "The Z coordinate of the position where the nozzle primes at the start of printing."
msgstr "列印開始時，噴頭在 Z 軸座標上的起始位置."

msgctxt "extruder_nr description"
msgid "The extruder train used for printing. This is used in multi-extrusion."
msgstr "用於列印的擠出機，在多擠出機情況下適用。"

msgctxt "machine_nozzle_size description"
msgid "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size."
msgstr "噴頭內徑，在使用非標準噴頭尺寸時需更改此設定。"

msgctxt "machine_nozzle_id description"
msgid "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\"."
msgstr "擠出機組的噴頭 ID，比如 \"AA 0.4\" 和 \"BB 0.8\"。"

msgctxt "machine_extruder_cooling_fan_number description"
msgid "The number of the print cooling fan associated with this extruder. Only change this from the default value of 0 when you have a different print cooling fan for each extruder."
msgstr "與此擠出機關聯的列印冷卻風扇的數量。只有當每個擠出機的列印冷卻風扇數量不同時，才需更改此值為正確數量，否則保持預設值 0 即可。"

msgctxt "machine_extruder_end_pos_x description"
msgid "The x-coordinate of the ending position when turning the extruder off."
msgstr "關閉擠出機時的終止位置的 X 座標。"

msgctxt "machine_nozzle_offset_x description"
msgid "The x-coordinate of the offset of the nozzle."
msgstr "噴頭 X 軸座標偏移。"

msgctxt "machine_extruder_start_pos_x description"
msgid "The x-coordinate of the starting position when turning the extruder on."
msgstr "打開擠出機時起始位置的 X 座標。"

msgctxt "machine_extruder_end_pos_y description"
msgid "The y-coordinate of the ending position when turning the extruder off."
msgstr "關閉擠出機時的終止位置的 Y 座標。"

msgctxt "machine_nozzle_offset_y description"
msgid "The y-coordinate of the offset of the nozzle."
msgstr "噴頭 Y 軸座標偏移。"

msgctxt "machine_extruder_start_pos_y description"
msgid "The y-coordinate of the starting position when turning the extruder on."
msgstr "打開擠出機時的起始位置 Y 座標。"

#~ msgctxt "machine_extruder_end_code description"
#~ msgid "End g-code to execute whenever turning the extruder off."
#~ msgstr "在關閉擠出機時，執行結束 G-code。"

#~ msgctxt "machine_extruder_start_code description"
#~ msgid "Start g-code to execute whenever turning the extruder on."
#~ msgstr "打開擠出機將執行此段 G-code。"
