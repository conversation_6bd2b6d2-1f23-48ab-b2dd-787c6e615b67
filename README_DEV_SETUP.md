# Cura & Uranium 并行开发环境设置指南

本指南将帮助您设置Cura和Uranium的跨平台并行开发环境，支持macOS和Windows同时开发，避免文件冲突。

## 特性

- ✅ **跨平台兼容**: 支持macOS和Windows同时开发
- ✅ **系统后缀**: 虚拟环境和构建文件夹使用系统后缀避免冲突
- ✅ **CuraEngine排除**: 已排除CuraEngine依赖，支持独立开发
- ✅ **PyCharm集成**: 自动生成PyCharm运行配置
- ✅ **Uranium并行开发**: 支持Uranium和Cura同时修改
- ✅ **自动化脚本**: 一键设置开发环境

## 系统要求

### Windows
- Windows 10 或更高版本
- Visual Studio with MSVC 2022 或更高版本
- Python 3.12 或更高版本
- PowerShell 5.0 或更高版本

### macOS
- macOS 11 或更高版本
- Xcode 12 或更高版本
- Python 3.12 或更高版本
- Bash shell

## 快速开始

### 1. 检查Python版本

确保您安装了Python 3.12或更高版本：

```bash
python --version  # 或 python3 --version (macOS/Linux)
```

如果版本不符合要求：
- **Windows**: 从 [python.org](https://python.org) 下载安装
- **macOS**: 使用 `brew install python@3.12`

### 2. 运行设置脚本

#### Windows (PowerShell)
```powershell
.\setup_cura_dev_env.ps1
```

强制重新创建环境：
```powershell
.\setup_cura_dev_env.ps1 -Force
```

#### macOS/Linux (Bash)
```bash
./setup_cura_dev_env.sh
```

强制重新创建环境：
```bash
./setup_cura_dev_env.sh --force
```

### 3. 设置PyCharm

1. 打开PyCharm，选择 "Open" 打开Cura项目文件夹
2. 在PyCharm中，选择 File > Open，选择Uranium文件夹并点击 "Attach"
3. 配置Python解释器：
   - File > Settings > Project > Python Interpreter
   - 选择现有解释器，浏览到：
     - **Windows**: `Cura\build_windows\generators\cura_venv_windows\Scripts\python.exe`
     - **macOS**: `Cura/build_macos/generators/cura_venv_macos/bin/python`
4. 使用自动生成的运行配置

## 项目结构

设置完成后，您的项目结构将如下：

```
CuraProject/
├── Cura/
│   ├── build_windows/          # Windows构建文件
│   ├── build_macos/            # macOS构建文件
│   ├── venv_windows/           # Windows虚拟环境
│   ├── venv_macos/             # macOS虚拟环境
│   └── ...
├── Uranium/
│   ├── build_windows/          # Windows构建文件
│   ├── build_macos/            # macOS构建文件
│   ├── venv_windows/           # Windows虚拟环境
│   ├── venv_macos/             # macOS虚拟环境
│   └── ...
├── CuraEngine/                 # 独立开发，不参与构建
├── cura_conan_venv_windows/    # Windows Conan环境
├── cura_conan_venv_macos/      # macOS Conan环境
└── setup_cura_dev_env.*        # 设置脚本
```

## 手动运行Cura

如果需要手动启动Cura：

### Windows
```powershell
cd Cura
.\build_windows\generators\virtual_python_env.ps1
python cura_app.py
```

### macOS
```bash
cd Cura
source build_macos/generators/virtual_python_env.sh
python cura_app.py
```

## Uranium并行开发

Uranium已设置为可编辑模式，这意味着：

1. 您可以直接修改Uranium源代码
2. 修改会立即反映在Cura中
3. 无需重新安装Uranium包

要验证Uranium并行开发是否工作：

1. 删除 `Uranium/UM/Application.py` 文件
2. 尝试启动Cura - 应该会失败
3. 恢复文件，Cura应该能正常启动

## 故障排除

### Conan相关问题

1. **版本错误**: 确保使用Conan 2.7.0
   ```bash
   pip install --force-reinstall "conan==2.7.0"
   ```

2. **缓存问题**: 清除Conan缓存
   ```bash
   conan remove "*" -f
   ```

3. **权限问题**: 不要使用sudo运行conan install

### Python版本问题

如果遇到Python版本问题：
1. 确保Python 3.12+已正确安装
2. 检查PATH环境变量
3. 重新运行设置脚本

### PyCharm配置问题

1. 确保选择了正确的Python解释器
2. 检查项目结构是否正确
3. 重新导入项目

## 开发工作流

1. **修改Cura代码**: 直接在Cura文件夹中修改
2. **修改Uranium代码**: 直接在Uranium文件夹中修改
3. **测试**: 使用PyCharm运行配置或手动启动
4. **调试**: 使用PyCharm调试器
5. **提交**: 分别为Cura和Uranium创建提交

## 注意事项

- 虚拟环境和构建文件夹使用系统后缀，确保跨平台兼容性
- CuraEngine已从依赖中排除，可以独立开发
- 首次运行conan install可能需要30分钟以上
- 确保网络连接稳定，用于下载依赖包

## 支持

如果遇到问题，请检查：
1. 官方文档: [Cura Wiki](https://github.com/Ultimaker/Cura/wiki)
2. 系统要求是否满足
3. 网络连接是否正常
4. Python和Conan版本是否正确
